# Sort the member alphabetically.
aliases:
  emeritus-approvers: # do not assign it in any OWNERS file.
    - tonyxuqqi # 2024-05
    - kevin-<PERSON><PERSON>iu # 2024-08
  sig-approvers: # default approvers for the repo on rest folders.
    - cfzjywxk
    - lance6716
    - overvenus
    - you06
    - YuJuncen
  sig-approvers-pb: # default approvers for `proto` folder
    - yudongusa
    - zhangjinpeng87
  sig-approvers-pb-autoid: [bb7133, tian<PERSON><PERSON><PERSON>]
  sig-approvers-pb-br: [BornChang<PERSON>, overvenus]
  sig-approvers-pb-import: [<PERSON><PERSON><PERSON><PERSON>, D3<PERSON>unt<PERSON>, lance6716]
  sig-approvers-pb-cdc: [flowbehappy, overvenus]
  sig-approvers-pb-config: [overvenus]
  sig-approvers-pb-coprocessor: [cfzjywxk]
  sig-approvers-pb-deadlock: [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, cfzjywxk]
  sig-approvers-pb-debug: [cfzjywxk]
  sig-approvers-pb-pd: [ni<PERSON><PERSON>, rleungx, nolouch]
  sig-approvers-pb-raftstore: [zhangjinpeng87, overvenus]
  sig-approvers-pb-tikv: [zhangjinpeng87]
  sig-approvers-pb-error: [cfzjywxk]
  sig-approvers-pb-kvrpc: [cfzjywxk]
  sig-approvers-pb-trace: [you06]
