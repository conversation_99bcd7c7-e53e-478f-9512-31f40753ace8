syntax = "proto3";
package debugpb;

import "eraftpb.proto";
import "kvrpcpb.proto";
import "raft_serverpb.proto";

import "gogoproto/gogo.proto";
import "rustproto.proto";

option (gogoproto.marshaler_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.goproto_unkeyed_all) = false;
option (gogoproto.goproto_unrecognized_all) = false;
option (gogoproto.goproto_sizecache_all) = false;
option (rustproto.lite_runtime_all) = true;

option java_package = "org.tikv.kvproto";

// Debug service for TiKV.
//
// Errors are defined as follow:
//   - OK: Okay, we are good!
//   - UNKNOWN: For unknown error.
//   - INVALID_ARGUMENT: Something goes wrong within requests.
//   - NOT_FOUND: It is key or region not found, it's based on context, detailed
//                reason can be found in grpc message.
// Note: It bypasses raft layer.
service Debug {
    // Read a value arbitrarily for a key.
    // Note: Server uses key directly w/o any encoding.
    rpc Get(GetRequest) returns (GetResponse) {}

    // Read raft info.
    rpc RaftLog(RaftLogRequest) returns (RaftLogResponse) {}
    rpc RegionInfo(RegionInfoRequest) returns (RegionInfoResponse) {}

    // Calculate size of a region.
    // Note: DO NOT CALL IT IN PRODUCTION, it's really expensive.
    rpc RegionSize(RegionSizeRequest) returns (RegionSizeResponse) {}

    // Scan a specific range.
    // Note: DO NOT CALL IT IN PRODUCTION, it's really expensive.
    //       Server uses keys directly w/o any encoding.
    rpc ScanMvcc(ScanMvccRequest) returns (stream ScanMvccResponse) {}

    // Compact a column family in a specified range.
    // Note: Server uses keys directly w/o any encoding.
    rpc Compact(CompactRequest) returns (CompactResponse) {}

    // Inject a fail point. Currently, it's only used in tests.
    // Note: DO NOT CALL IT IN PRODUCTION.
    rpc InjectFailPoint(InjectFailPointRequest) returns (InjectFailPointResponse) {}
    // Recover from a fail point.
    rpc RecoverFailPoint(RecoverFailPointRequest) returns (RecoverFailPointResponse) {}
    // List all fail points.
    rpc ListFailPoints(ListFailPointsRequest) returns (ListFailPointsResponse) {}

    // Get Metrics
    rpc GetMetrics(GetMetricsRequest) returns (GetMetricsResponse){}

    // Do a consistent check for a region.
    rpc CheckRegionConsistency(RegionConsistencyCheckRequest) returns (RegionConsistencyCheckResponse) {}

    // dynamically modify tikv's config
    rpc ModifyTikvConfig(ModifyTikvConfigRequest) returns (ModifyTikvConfigResponse) {}

    // Get region properties
    rpc GetRegionProperties(GetRegionPropertiesRequest) returns (GetRegionPropertiesResponse) {}

    // Get store ID
    rpc GetStoreInfo(GetStoreInfoRequest) returns (GetStoreInfoResponse) {}

    // Get cluster ID
    rpc GetClusterInfo(GetClusterInfoRequest) returns (GetClusterInfoResponse) {}

    // Get all region IDs in the store
    rpc GetAllRegionsInStore(GetAllRegionsInStoreRequest) returns (GetAllRegionsInStoreResponse) {}

    // Make this TiKV node return to the status on this node to certain ts.
    rpc ResetToVersion(ResetToVersionRequest) returns (ResetToVersionResponse) {}

    // Get range properties
    rpc GetRangeProperties(GetRangePropertiesRequest) returns (GetRangePropertiesResponse) {}

    // Flashback given key range to a specified version.
    rpc FlashbackToVersion(FlashbackToVersionRequest) returns (FlashbackToVersionResponse) {}

    // GetRegionReadProgress returns the some useful info in RegionReadProgress
    rpc GetRegionReadProgress(GetRegionReadProgressRequest) returns (GetRegionReadProgressResponse) {}
}

enum DB {
    INVALID = 0;
    KV = 1;
    RAFT = 2;
}

enum MODULE {
    UNUSED = 0;
    KVDB = 1;
    RAFTDB = 2;
    READPOOL = 3;
    SERVER = 4;
    STORAGE = 5;
    PD = 6;
    METRIC = 7;
    COPROCESSOR = 8;
    SECURITY = 9;
    IMPORT = 10;
}

message GetRequest {
    DB db = 1;
    string cf = 2;
    bytes key = 3;
}

message GetResponse {
    bytes value = 1;
}

message RaftLogRequest {
    uint64 region_id = 1;
    uint64 log_index = 2;
}

message RaftLogResponse {
    eraftpb.Entry entry = 1;
}

message RegionInfoRequest {
    uint64 region_id = 1;
}

message RegionInfoResponse {
    raft_serverpb.RaftLocalState raft_local_state = 1;
    raft_serverpb.RaftApplyState raft_apply_state = 2;
    raft_serverpb.RegionLocalState region_local_state = 3;
}

message RegionSizeRequest {
    uint64 region_id = 1;
    repeated string cfs = 2;
}

message RegionSizeResponse {
    message Entry {
        string cf = 1;
        uint64 size = 2;
    }

    repeated Entry entries = 1;
}

message ScanMvccRequest {
    bytes from_key = 1;
    bytes to_key = 2;
    uint64 limit = 3;
}

message ScanMvccResponse {
    bytes key = 1;
    kvrpcpb.MvccInfo info = 2;
}

enum BottommostLevelCompaction {
    // Skip bottommost level compaction
    Skip = 0;
    // Force bottommost level compaction
    Force = 1;
    // Compact bottommost level if there is a compaction filter.
    IfHaveCompactionFilter = 2;
}

message CompactRequest {
    DB db = 1;
    string cf = 2;
    bytes from_key = 3;
    bytes to_key = 4;
    uint32 threads = 5;
    BottommostLevelCompaction bottommost_level_compaction = 6;
}

message CompactResponse {
}

message InjectFailPointRequest {
    string name = 1;
    string actions = 2;
}

message InjectFailPointResponse {
}

message RecoverFailPointRequest {
    string name = 1;
}

message RecoverFailPointResponse {
}

message ListFailPointsRequest {
}

message ListFailPointsResponse {
    message Entry {
        string name = 1;
        string actions = 2;
    }

    repeated Entry entries = 1;
}

message GetMetricsRequest {
   bool all = 1;
}

message GetMetricsResponse {
    string prometheus = 1;
    string rocksdb_kv = 2;
    string rocksdb_raft = 3;
    string jemalloc = 4;
    uint64 store_id = 5;
}

message RegionConsistencyCheckRequest {
    uint64 region_id = 1;
}

message RegionConsistencyCheckResponse {
}

message ModifyTikvConfigRequest {
    MODULE module = 1;
    string config_name = 2;
    string config_value = 3;
}

message ModifyTikvConfigResponse {
}

message Property {
    string name = 1;
    string value = 2;
}

message GetRegionPropertiesRequest {
    uint64 region_id = 1;
}

message GetRegionPropertiesResponse {
    repeated Property props = 1;
}

message GetStoreInfoRequest {
}

message GetStoreInfoResponse {
    uint64 store_id = 1;
    kvrpcpb.APIVersion api_version = 2;
}

message GetClusterInfoRequest {
}

message GetClusterInfoResponse {
    uint64 cluster_id = 1;
}

message GetAllRegionsInStoreRequest {
}

message GetAllRegionsInStoreResponse {
    repeated uint64 regions = 1;
}

message ResetToVersionRequest {
    uint64 ts = 1;
}

message ResetToVersionResponse {
}

message GetRangePropertiesRequest {
    bytes start_key = 1;
    bytes end_key = 2;
}

message GetRangePropertiesResponse {
    message RangeProperty {
        string key = 1;
        string value = 2;
    }

    repeated RangeProperty properties = 1;
}

message FlashbackToVersionRequest {
    kvrpcpb.Context context = 1;
    uint64 version = 2;
    uint64 region_id = 3;
    bytes start_key = 4;
    bytes end_key = 5;
    uint64 start_ts = 6;
    uint64 commit_ts = 7;
}

message FlashbackToVersionResponse {
    string error = 1;
}

message GetRegionReadProgressRequest {
    uint64 region_id = 1;
    bool log_locks = 2; // when set to true, print a log of the locks with min start_ts in the resolver.
    uint64 min_start_ts = 3; // only print locks whose start_ts >= min_start_ts. Can be used to find certain transaction.
}

message GetRegionReadProgressResponse {
    // below are from region_read_progress module
    uint64 safe_ts = 1;
    uint64 applied_index = 2;
    uint64 pending_front_applied_index = 3;
    uint64 pending_front_ts = 4;
    uint64 pending_back_applied_index = 5;
    uint64 pending_back_ts = 6;
    bool region_read_progress_paused = 7;
    uint64 duration_to_last_update_safe_ts_ms = 8;
    uint64 duration_to_last_consume_leader_ms = 9;
    bool region_read_progress_exist = 10;
    uint64 read_state_ts = 18;
    uint64 read_state_apply_index = 19;
    bool discard = 20;

    // below are from resolved-ts module
    uint64 resolved_ts = 11;
    uint64 resolver_tracked_index = 12;
    bool resolver_exist = 13;
    bool resolver_stopped = 14;
    uint64 num_locks = 16;
    uint64 num_transactions = 17;

    string error = 15;
}