syntax = "proto3";
package tracepb;

import "gogoproto/gogo.proto";
import "rustproto.proto";

option (gogoproto.sizer_all) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.goproto_unkeyed_all) = false;
option (gogoproto.goproto_unrecognized_all) = false;
option (gogoproto.goproto_sizecache_all) = false;
option (rustproto.lite_runtime_all) = true;

option java_package = "org.tikv.kvproto";

service TraceRecordPubSub {
    // Subscribe the Trace records generated on this service. The service will periodically (e.g. per minute)
    // publishes Trace records to clients via gRPC stream.
    rpc Subscribe(TraceRecordRequest) returns (stream TraceRecord) {}
}

message TraceRecordRequest {}

message TraceRecord {
    oneof record_oneof {
        Report report = 1;
        NotifyCollect notify_collect = 2;
    }
}

message RemoteParentSpan {
    // A unique id to identify the request. It's usually a UUID.
    uint64 trace_id = 1;
    // The span of remote caller that is awaiting the request.
    uint64 span_id = 2;
}

// The context of the request to be traced.
message TraceContext {
    repeated RemoteParentSpan remote_parent_spans = 1;
    // Report the trace records only if the duration of handling the request exceeds the threshold.
    uint32 duration_threshold_ms = 2;
}

// Report the spans collected when handling a request on a service.
message Report {
    repeated RemoteParentSpan remote_parent_spans = 1;
    repeated Span spans = 2;
}

// Notify the subscriber to persis the spans of the trace.
message NotifyCollect {
    uint64 trace_id = 1;
}

message Span {
    // The unique span id within the spans with the same `trace_id`.
    // The most significant 32 bits should be random number generated by each service instance.
    uint64 span_id = 1;
    uint64 parent_id = 2;
    uint64 begin_unix_ns = 3;
    uint64 duration_ns = 4;
    string event = 5;
    repeated Property properties = 6;
}

message Property {
    string key = 1;
    string value = 2;
}
