syntax = "proto3";
package coprocessor;

import "errorpb.proto";
import "kvrpcpb.proto";
import "metapb.proto";

import "gogoproto/gogo.proto";
import "rustproto.proto";

option (gogoproto.marshaler_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.goproto_unkeyed_all) = false;
option (gogoproto.goproto_unrecognized_all) = false;
option (gogoproto.goproto_sizecache_all) = false;
option (rustproto.lite_runtime_all) = true;

option java_package = "org.tikv.kvproto";


// [start, end)
message KeyRange {
    bytes start = 1;
    bytes end = 2;
}

message Request {
    kvrpcpb.Context context = 1;
    int64 tp = 2;
    bytes data = 3;
    uint64 start_ts = 7;
    repeated KeyRange ranges = 4;

    // If cache is enabled, TiKV returns cache hit instead of data if
    // its last version matches this `cache_if_match_version`.
    bool is_cache_enabled = 5;
    uint64 cache_if_match_version = 6;
    // Any schema-ful storage to validate schema correctness if necessary.
    int64 schema_ver = 8;
    bool is_trace_enabled = 9;
    // paging_size is 0 when it's disabled, otherwise, it should be a positive number.
    uint64 paging_size = 10;
    // tasks stores the batched coprocessor tasks sent to the same tikv store.
    repeated StoreBatchTask tasks = 11;
    uint64 connection_id = 12; // This is the session id between a client and tidb
    string connection_alias = 13; // This is the session alias between a client and tidb

    repeated TableShardInfos table_shard_infos= 14; // Shard infos for FTS index, used by TiFlash reading TiCI.
}

message Response {
    bytes data = 1 [(gogoproto.customtype) = "github.com/pingcap/kvproto/pkg/sharedbytes.SharedBytes", (gogoproto.nullable) = false];
    errorpb.Error region_error = 2;
    kvrpcpb.LockInfo locked = 3;
    string other_error = 4;
    KeyRange range = 5;

    // This field is always filled for compatibility consideration. However
    // newer TiDB should respect `exec_details_v2` field instead.
    kvrpcpb.ExecDetails exec_details = 6;
    // This field is provided in later versions, containing more detailed
    // information.
    kvrpcpb.ExecDetailsV2 exec_details_v2 = 11;

    bool is_cache_hit = 7;
    uint64 cache_last_version = 8;
    bool can_be_cached = 9;

    reserved 10;

    // Contains the latest buckets version of the region.
    // Clients should query PD to update buckets in cache if its is stale.
    uint64 latest_buckets_version = 12;

    // StoreBatchTaskResponse is the collection of batch task responses.
    repeated StoreBatchTaskResponse batch_responses = 13;
}

message RegionInfo {
    uint64 region_id = 1;
    metapb.RegionEpoch region_epoch = 2;
    repeated KeyRange ranges = 3;
}

message ShardInfo {
    uint64 shard_id = 1;
    uint64 shard_epoch = 2;
    repeated KeyRange ranges = 3;
}

message TableShardInfos {
    // The executor ID is used to identify the tici executor.
    string executor_id = 1;
    // The shard_infos contains the shard information for each tici executor.
    repeated ShardInfo shard_infos = 2;
}

message TableRegions {
    int64 physical_table_id = 1;
    repeated RegionInfo regions = 2;
}

message BatchRequest {
    kvrpcpb.Context context = 1;
    int64 tp = 2;
    bytes data = 3;
    repeated RegionInfo regions = 4;
    uint64 start_ts = 5;
    // Any schema-ful storage to validate schema correctness if necessary.
    int64 schema_ver = 6;
    // Used for partition table scan
    repeated TableRegions table_regions = 7;
    string log_id = 8;
    uint64 connection_id = 9; // This is the session id between a client and tidb
    string connection_alias = 10; // This is the session alias between a client and tidb
    repeated TableShardInfos table_shard_infos= 11; // Shard infos for FTS index, used by TiFlash reading TiCI.
}

message BatchResponse {
    bytes data = 1 [(gogoproto.customtype) = "github.com/pingcap/kvproto/pkg/sharedbytes.SharedBytes", (gogoproto.nullable) = false];
    string other_error = 2;
    kvrpcpb.ExecDetails exec_details = 3;
    repeated metapb.Region retry_regions = 4;
}

message StoreBatchTask {
    uint64 region_id = 1;
    metapb.RegionEpoch region_epoch = 2;
    metapb.Peer peer = 3;
    repeated KeyRange ranges = 4;
    uint64 task_id = 5;
}

message StoreBatchTaskResponse {
    bytes data = 1 [(gogoproto.customtype) = "github.com/pingcap/kvproto/pkg/sharedbytes.SharedBytes", (gogoproto.nullable) = false];
    errorpb.Error region_error = 2;
    kvrpcpb.LockInfo locked = 3;
    string other_error = 4;
    uint64 task_id = 5;
    kvrpcpb.ExecDetailsV2 exec_details_v2 = 6;
}

message DelegateRequest {
    kvrpcpb.Context context = 1;
    uint64 start_ts = 2;
    repeated KeyRange ranges = 3;
    // Used for avoid redundant mem-table copying.
    // If the sequence is the same, tikv-server will not return the mem-table.
    uint64 mem_table_sequence = 4;

    // Used for avoid redundant snapshot copying.
    // If the sequence is the same, tikv-server will not return the snapshot.
    uint64 snapshot_sequence = 5;
}

message DelegateResponse {
    bytes mem_table_data = 1;
    bytes snapshot = 2;
    errorpb.Error region_error = 3;
    kvrpcpb.LockInfo locked = 4;
    string other_error = 5;
    // Used for avoid redundant mem-table copying.
    uint64 mem_table_sequence = 6;
}
