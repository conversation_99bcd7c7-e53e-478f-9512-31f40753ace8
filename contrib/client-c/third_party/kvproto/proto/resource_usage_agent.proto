syntax = "proto3";

package resource_usage_agent;

import "gogoproto/gogo.proto";
import "rustproto.proto";

option (gogoproto.sizer_all) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.goproto_unkeyed_all) = false;
option (gogoproto.goproto_unrecognized_all) = false;
option (gogoproto.goproto_sizecache_all) = false;
option (rustproto.lite_runtime_all) = true;

option java_package = "org.tikv.kvproto";

// ResourceUsageAgent is the service for storing resource usage records.
service ResourceUsageAgent {
    // Report the resource usage records. By default, the records with the same
    // resource group tag will be batched by minute.
    rpc Report(stream ResourceUsageRecord) returns (EmptyResponse) {}
}

// TiKV implements ResourceMeteringPubSub service for clients to subscribe to resource metering records.
service ResourceMeteringPubSub {
    // Clients subscribe to resource metering records through this RPC, and TiKV periodically (e.g. per minute)
    // publishes resource metering records to clients via gRPC stream.
    rpc Subscribe(ResourceMeteringRequest) returns (stream ResourceUsageRecord) {}
}

message ResourceMeteringRequest {}

message EmptyResponse {}

message ResourceUsageRecord {
    oneof record_oneof {
        GroupTagRecord record = 1;
    }
}

// GroupTagRecord is a set of resource usage data grouped by resource_group_tag.
message GroupTagRecord {
    bytes resource_group_tag = 1;
    repeated GroupTagRecordItem items = 2;
}

message GroupTagRecordItem {
    uint64 timestamp_sec = 1;
    uint32 cpu_time_ms = 2;
    uint32 read_keys = 3;
    uint32 write_keys = 4;
}
