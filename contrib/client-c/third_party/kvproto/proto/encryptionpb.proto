// These encryption protobufs are not sent over the network.
// Protobufs are used to define a stable backwards compatible persistent storage format.
// These definitions are used by both PD and TiKV to keep their implementations similar.

syntax = "proto3";
package encryptionpb;

import "gogoproto/gogo.proto";
import "rustproto.proto";

option (gogoproto.sizer_all) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.goproto_unkeyed_all) = false;
option (gogoproto.goproto_unrecognized_all) = false;
option (gogoproto.goproto_sizecache_all) = false;
option (rustproto.lite_runtime_all) = true;

option java_package = "org.tikv.kvproto";

// General encryption metadata for any data type.
message EncryptionMeta {
    // ID of the key used to encrypt the data.
    uint64 key_id = 1;
    // Initialization vector (IV) of the data.
    bytes iv = 2;
}

// Information about an encrypted file.
message FileInfo {
    // ID of the key used to encrypt the file.
    uint64 key_id = 1;
    // Initialization vector (IV) of the file.
    bytes iv = 2;
    // Method of encryption algorithm used to encrypted the file.
    EncryptionMethod method = 3;
}

message FileDictionary {
    // A map of file name to file info.
    map<string, FileInfo> files = 1;
}

enum EncryptionMethod {
    UNKNOWN = 0;
    PLAINTEXT = 1;
    AES128_CTR = 2;
    AES192_CTR = 3;
    AES256_CTR = 4;
    SM4_CTR = 5;
}

// The key used to encrypt the user data.
message DataKey {
    // A sequence of secret bytes used to encrypt data.
    bytes key = 1;
    // Method of encryption algorithm used to encrypted data.
    EncryptionMethod method = 2;
    // Creation time of the key.
    uint64 creation_time = 3;
    // A flag for the key have ever been exposed.
    bool was_exposed = 4;
}

message KeyDictionary {
    // A map of key ID to dat key.
    map<uint64, DataKey> keys = 1;
    // ID of a key currently in use.
    uint64 current_key_id = 2;
}

// Master key config.
message MasterKey {
    oneof backend {
        MasterKeyPlaintext plaintext = 1;
        MasterKeyFile file = 2;
        MasterKeyKms kms = 3;
    }
}

// MasterKeyPlaintext indicates content is stored as plaintext.
message MasterKeyPlaintext {}

// MasterKeyFile is a master key backed by a file containing encryption key in human-readable
// hex format.
message MasterKeyFile {
    // Local file path.
    string path = 1;
}

// MasterKeyKms is a master key backed by KMS service that manages the encryption key,
// and provide API to encrypt and decrypt a data key, which is used to encrypt the content.
message MasterKeyKms {
    // KMS vendor.
    string vendor = 1;
    // KMS key id.
    string key_id = 2;
    // KMS region.
    string region = 3;
    // KMS endpoint. Normally not needed.
    string endpoint = 4;
    // optional, used to set up azure master key backend
    AzureKms azure_kms = 5;
    // optional, used to set up gcp master key backend
    GcpKms gcp_kms = 6;
    // optional, used to set up aws master key backend
    AwsKms aws_kms = 7;
}

message AzureKms {
    string tenant_id = 1;
    string client_id = 2;
    string client_secret = 3;
    // Key vault to encrypt/decrypt data key.
    string key_vault_url = 4;
    // optional hsm used to generate data key
    string hsm_name = 5;
    string hsm_url = 6;
    string client_certificate = 7;
    string client_certificate_path = 8;
    string client_certificate_password = 9;

}

message GcpKms {
    string credential = 1;
}

message AwsKms {
    string access_key = 1;
    string secret_access_key = 2;
}

message EncryptedContent {
    // Metadata of the encrypted content.
    // Eg. IV, method and KMS key ID
    // It is preferred to define new fields for extra metadata than using this metadata map.
    map<string, bytes> metadata = 1;
    // Encrypted content.
    bytes content = 2;
    // Master key used to encrypt the content.
    MasterKey master_key = 3;
    // Initilization vector (IV) used.
    bytes iv = 4;
    // Encrypted data key generated by KMS and used to actually encrypt data.
    // Valid only when KMS is used.
    bytes ciphertext_key = 5;
}

message FileEncryptionInfo {
    oneof mode {
        PlainTextDataKey plain_text_data_key = 1;
        MasterKeyBased master_key_based = 2;
    }
    // file encryption method
    encryptionpb.EncryptionMethod encryption_method = 3;
    // iv to encrypt the file by data key
    bytes file_iv = 4;
    // file checksum after encryption, optional if using GCM
    bytes checksum = 5;
}

// not recommended in production.
// user needs to pass back the same data key for restore.
message PlainTextDataKey {}

message MasterKeyBased {
    // encrypted data key with metadata
    repeated encryptionpb.EncryptedContent data_key_encrypted_content = 1;
}
