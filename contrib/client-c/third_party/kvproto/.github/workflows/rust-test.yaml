name: Rust Test
on:
  push:
    branches:
      - master
      - release-6.0
      - release-5.0
      - release-4.0
      - release-3.0
  pull_request:
    branches:
      - master
      - release-6.0
      - release-5.0
      - release-4.0
      - release-3.0
jobs:
  rust-test:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Update package lists
        run: sudo apt update
      - name: Install dependencies (LLVM and compilers)
        run: sudo apt install -y cmake clang libclang-dev llvm llvm-dev
      - name: Install dependencies (protocol buffers compiler)
        uses: arduino/setup-protoc@v1
        with:
          version: '3.8.0'
      - name: Test Rust
        env:
          RUST_TEST_THREADS: "1"
          RUST_BACKTRACE: "1"
          RUSTFLAGS: "-Dwarnings"
          CPLUS_INCLUDE_PATH: "/usr/lib/gcc/x86_64-linux-gnu/6/include"
        run: make rust
