// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: raft_cmdpb.proto

package raft_cmdpb

import (
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	eraftpb "github.com/pingcap/kvproto/pkg/eraftpb"
	errorpb "github.com/pingcap/kvproto/pkg/errorpb"
	import_sstpb "github.com/pingcap/kvproto/pkg/import_sstpb"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
	raft_serverpb "github.com/pingcap/kvproto/pkg/raft_serverpb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CmdType int32

const (
	CmdType_Invalid     CmdType = 0
	CmdType_Get         CmdType = 1
	CmdType_Put         CmdType = 3
	CmdType_Delete      CmdType = 4
	CmdType_Snap        CmdType = 5
	CmdType_Prewrite    CmdType = 6
	CmdType_DeleteRange CmdType = 7
	CmdType_IngestSST   CmdType = 8
	CmdType_ReadIndex   CmdType = 9
)

var CmdType_name = map[int32]string{
	0: "Invalid",
	1: "Get",
	3: "Put",
	4: "Delete",
	5: "Snap",
	6: "Prewrite",
	7: "DeleteRange",
	8: "IngestSST",
	9: "ReadIndex",
}

var CmdType_value = map[string]int32{
	"Invalid":     0,
	"Get":         1,
	"Put":         3,
	"Delete":      4,
	"Snap":        5,
	"Prewrite":    6,
	"DeleteRange": 7,
	"IngestSST":   8,
	"ReadIndex":   9,
}

func (x CmdType) String() string {
	return proto.EnumName(CmdType_name, int32(x))
}

func (CmdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{0}
}

type AdminCmdType int32

const (
	AdminCmdType_InvalidAdmin AdminCmdType = 0
	AdminCmdType_ChangePeer   AdminCmdType = 1
	// Use `BatchSplit` instead.
	AdminCmdType_Split              AdminCmdType = 2 // Deprecated: Do not use.
	AdminCmdType_CompactLog         AdminCmdType = 3
	AdminCmdType_TransferLeader     AdminCmdType = 4
	AdminCmdType_ComputeHash        AdminCmdType = 5
	AdminCmdType_VerifyHash         AdminCmdType = 6
	AdminCmdType_PrepareMerge       AdminCmdType = 7
	AdminCmdType_CommitMerge        AdminCmdType = 8
	AdminCmdType_RollbackMerge      AdminCmdType = 9
	AdminCmdType_BatchSplit         AdminCmdType = 10
	AdminCmdType_ChangePeerV2       AdminCmdType = 11
	AdminCmdType_PrepareFlashback   AdminCmdType = 12
	AdminCmdType_FinishFlashback    AdminCmdType = 13
	AdminCmdType_BatchSwitchWitness AdminCmdType = 14
	// Command that updates RegionLocalState.gc_peers
	AdminCmdType_UpdateGcPeer AdminCmdType = 15
)

var AdminCmdType_name = map[int32]string{
	0:  "InvalidAdmin",
	1:  "ChangePeer",
	2:  "Split",
	3:  "CompactLog",
	4:  "TransferLeader",
	5:  "ComputeHash",
	6:  "VerifyHash",
	7:  "PrepareMerge",
	8:  "CommitMerge",
	9:  "RollbackMerge",
	10: "BatchSplit",
	11: "ChangePeerV2",
	12: "PrepareFlashback",
	13: "FinishFlashback",
	14: "BatchSwitchWitness",
	15: "UpdateGcPeer",
}

var AdminCmdType_value = map[string]int32{
	"InvalidAdmin":       0,
	"ChangePeer":         1,
	"Split":              2,
	"CompactLog":         3,
	"TransferLeader":     4,
	"ComputeHash":        5,
	"VerifyHash":         6,
	"PrepareMerge":       7,
	"CommitMerge":        8,
	"RollbackMerge":      9,
	"BatchSplit":         10,
	"ChangePeerV2":       11,
	"PrepareFlashback":   12,
	"FinishFlashback":    13,
	"BatchSwitchWitness": 14,
	"UpdateGcPeer":       15,
}

func (x AdminCmdType) String() string {
	return proto.EnumName(AdminCmdType_name, int32(x))
}

func (AdminCmdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{1}
}

type StatusCmdType int32

const (
	StatusCmdType_InvalidStatus StatusCmdType = 0
	StatusCmdType_RegionLeader  StatusCmdType = 1
	StatusCmdType_RegionDetail  StatusCmdType = 2
)

var StatusCmdType_name = map[int32]string{
	0: "InvalidStatus",
	1: "RegionLeader",
	2: "RegionDetail",
}

var StatusCmdType_value = map[string]int32{
	"InvalidStatus": 0,
	"RegionLeader":  1,
	"RegionDetail":  2,
}

func (x StatusCmdType) String() string {
	return proto.EnumName(StatusCmdType_name, int32(x))
}

func (StatusCmdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{2}
}

type GetRequest struct {
	Cf  string `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Key []byte `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (m *GetRequest) Reset()         { *m = GetRequest{} }
func (m *GetRequest) String() string { return proto.CompactTextString(m) }
func (*GetRequest) ProtoMessage()    {}
func (*GetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{0}
}
func (m *GetRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRequest.Merge(m, src)
}
func (m *GetRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRequest proto.InternalMessageInfo

func (m *GetRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *GetRequest) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

type GetResponse struct {
	Value []byte `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *GetResponse) Reset()         { *m = GetResponse{} }
func (m *GetResponse) String() string { return proto.CompactTextString(m) }
func (*GetResponse) ProtoMessage()    {}
func (*GetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{1}
}
func (m *GetResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResponse.Merge(m, src)
}
func (m *GetResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetResponse proto.InternalMessageInfo

func (m *GetResponse) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type PutRequest struct {
	Cf    string `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Key   []byte `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Value []byte `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *PutRequest) Reset()         { *m = PutRequest{} }
func (m *PutRequest) String() string { return proto.CompactTextString(m) }
func (*PutRequest) ProtoMessage()    {}
func (*PutRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{2}
}
func (m *PutRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PutRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutRequest.Merge(m, src)
}
func (m *PutRequest) XXX_Size() int {
	return m.Size()
}
func (m *PutRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PutRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PutRequest proto.InternalMessageInfo

func (m *PutRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *PutRequest) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *PutRequest) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type PutResponse struct {
}

func (m *PutResponse) Reset()         { *m = PutResponse{} }
func (m *PutResponse) String() string { return proto.CompactTextString(m) }
func (*PutResponse) ProtoMessage()    {}
func (*PutResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{3}
}
func (m *PutResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PutResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutResponse.Merge(m, src)
}
func (m *PutResponse) XXX_Size() int {
	return m.Size()
}
func (m *PutResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PutResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PutResponse proto.InternalMessageInfo

type DeleteRequest struct {
	Cf  string `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Key []byte `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (m *DeleteRequest) Reset()         { *m = DeleteRequest{} }
func (m *DeleteRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRequest) ProtoMessage()    {}
func (*DeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{4}
}
func (m *DeleteRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRequest.Merge(m, src)
}
func (m *DeleteRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRequest proto.InternalMessageInfo

func (m *DeleteRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *DeleteRequest) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

type DeleteResponse struct {
}

func (m *DeleteResponse) Reset()         { *m = DeleteResponse{} }
func (m *DeleteResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteResponse) ProtoMessage()    {}
func (*DeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{5}
}
func (m *DeleteResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResponse.Merge(m, src)
}
func (m *DeleteResponse) XXX_Size() int {
	return m.Size()
}
func (m *DeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResponse proto.InternalMessageInfo

type DeleteRangeRequest struct {
	Cf         string `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	StartKey   []byte `protobuf:"bytes,2,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey     []byte `protobuf:"bytes,3,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	NotifyOnly bool   `protobuf:"varint,4,opt,name=notify_only,json=notifyOnly,proto3" json:"notify_only,omitempty"`
}

func (m *DeleteRangeRequest) Reset()         { *m = DeleteRangeRequest{} }
func (m *DeleteRangeRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRangeRequest) ProtoMessage()    {}
func (*DeleteRangeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{6}
}
func (m *DeleteRangeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteRangeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteRangeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteRangeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRangeRequest.Merge(m, src)
}
func (m *DeleteRangeRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteRangeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRangeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRangeRequest proto.InternalMessageInfo

func (m *DeleteRangeRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *DeleteRangeRequest) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *DeleteRangeRequest) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (m *DeleteRangeRequest) GetNotifyOnly() bool {
	if m != nil {
		return m.NotifyOnly
	}
	return false
}

type DeleteRangeResponse struct {
}

func (m *DeleteRangeResponse) Reset()         { *m = DeleteRangeResponse{} }
func (m *DeleteRangeResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteRangeResponse) ProtoMessage()    {}
func (*DeleteRangeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{7}
}
func (m *DeleteRangeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteRangeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteRangeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteRangeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRangeResponse.Merge(m, src)
}
func (m *DeleteRangeResponse) XXX_Size() int {
	return m.Size()
}
func (m *DeleteRangeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRangeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRangeResponse proto.InternalMessageInfo

type SnapRequest struct {
}

func (m *SnapRequest) Reset()         { *m = SnapRequest{} }
func (m *SnapRequest) String() string { return proto.CompactTextString(m) }
func (*SnapRequest) ProtoMessage()    {}
func (*SnapRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{8}
}
func (m *SnapRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapRequest.Merge(m, src)
}
func (m *SnapRequest) XXX_Size() int {
	return m.Size()
}
func (m *SnapRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SnapRequest proto.InternalMessageInfo

type SnapResponse struct {
	Region *metapb.Region `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
}

func (m *SnapResponse) Reset()         { *m = SnapResponse{} }
func (m *SnapResponse) String() string { return proto.CompactTextString(m) }
func (*SnapResponse) ProtoMessage()    {}
func (*SnapResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{9}
}
func (m *SnapResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapResponse.Merge(m, src)
}
func (m *SnapResponse) XXX_Size() int {
	return m.Size()
}
func (m *SnapResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SnapResponse proto.InternalMessageInfo

func (m *SnapResponse) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

type PrewriteRequest struct {
	Key   []byte `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Lock  []byte `protobuf:"bytes,3,opt,name=lock,proto3" json:"lock,omitempty"`
}

func (m *PrewriteRequest) Reset()         { *m = PrewriteRequest{} }
func (m *PrewriteRequest) String() string { return proto.CompactTextString(m) }
func (*PrewriteRequest) ProtoMessage()    {}
func (*PrewriteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{10}
}
func (m *PrewriteRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrewriteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrewriteRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrewriteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrewriteRequest.Merge(m, src)
}
func (m *PrewriteRequest) XXX_Size() int {
	return m.Size()
}
func (m *PrewriteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PrewriteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PrewriteRequest proto.InternalMessageInfo

func (m *PrewriteRequest) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *PrewriteRequest) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *PrewriteRequest) GetLock() []byte {
	if m != nil {
		return m.Lock
	}
	return nil
}

type PrewriteResponse struct {
}

func (m *PrewriteResponse) Reset()         { *m = PrewriteResponse{} }
func (m *PrewriteResponse) String() string { return proto.CompactTextString(m) }
func (*PrewriteResponse) ProtoMessage()    {}
func (*PrewriteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{11}
}
func (m *PrewriteResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrewriteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrewriteResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrewriteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrewriteResponse.Merge(m, src)
}
func (m *PrewriteResponse) XXX_Size() int {
	return m.Size()
}
func (m *PrewriteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PrewriteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PrewriteResponse proto.InternalMessageInfo

type IngestSSTRequest struct {
	Sst *import_sstpb.SSTMeta `protobuf:"bytes,1,opt,name=sst,proto3" json:"sst,omitempty"`
}

func (m *IngestSSTRequest) Reset()         { *m = IngestSSTRequest{} }
func (m *IngestSSTRequest) String() string { return proto.CompactTextString(m) }
func (*IngestSSTRequest) ProtoMessage()    {}
func (*IngestSSTRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{12}
}
func (m *IngestSSTRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IngestSSTRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IngestSSTRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IngestSSTRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IngestSSTRequest.Merge(m, src)
}
func (m *IngestSSTRequest) XXX_Size() int {
	return m.Size()
}
func (m *IngestSSTRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IngestSSTRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IngestSSTRequest proto.InternalMessageInfo

func (m *IngestSSTRequest) GetSst() *import_sstpb.SSTMeta {
	if m != nil {
		return m.Sst
	}
	return nil
}

type IngestSSTResponse struct {
}

func (m *IngestSSTResponse) Reset()         { *m = IngestSSTResponse{} }
func (m *IngestSSTResponse) String() string { return proto.CompactTextString(m) }
func (*IngestSSTResponse) ProtoMessage()    {}
func (*IngestSSTResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{13}
}
func (m *IngestSSTResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IngestSSTResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IngestSSTResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IngestSSTResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IngestSSTResponse.Merge(m, src)
}
func (m *IngestSSTResponse) XXX_Size() int {
	return m.Size()
}
func (m *IngestSSTResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IngestSSTResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IngestSSTResponse proto.InternalMessageInfo

type ReadIndexRequest struct {
	// In replica read, leader uses start_ts and key_ranges to check memory locks.
	StartTs   uint64              `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	KeyRanges []*kvrpcpb.KeyRange `protobuf:"bytes,2,rep,name=key_ranges,json=keyRanges,proto3" json:"key_ranges,omitempty"`
}

func (m *ReadIndexRequest) Reset()         { *m = ReadIndexRequest{} }
func (m *ReadIndexRequest) String() string { return proto.CompactTextString(m) }
func (*ReadIndexRequest) ProtoMessage()    {}
func (*ReadIndexRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{14}
}
func (m *ReadIndexRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadIndexRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadIndexRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadIndexRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadIndexRequest.Merge(m, src)
}
func (m *ReadIndexRequest) XXX_Size() int {
	return m.Size()
}
func (m *ReadIndexRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadIndexRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReadIndexRequest proto.InternalMessageInfo

func (m *ReadIndexRequest) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *ReadIndexRequest) GetKeyRanges() []*kvrpcpb.KeyRange {
	if m != nil {
		return m.KeyRanges
	}
	return nil
}

type ReadIndexResponse struct {
	ReadIndex uint64 `protobuf:"varint,1,opt,name=read_index,json=readIndex,proto3" json:"read_index,omitempty"`
	// The memory lock blocking this read at the leader
	Locked *kvrpcpb.LockInfo `protobuf:"bytes,2,opt,name=locked,proto3" json:"locked,omitempty"`
}

func (m *ReadIndexResponse) Reset()         { *m = ReadIndexResponse{} }
func (m *ReadIndexResponse) String() string { return proto.CompactTextString(m) }
func (*ReadIndexResponse) ProtoMessage()    {}
func (*ReadIndexResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{15}
}
func (m *ReadIndexResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadIndexResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadIndexResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadIndexResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadIndexResponse.Merge(m, src)
}
func (m *ReadIndexResponse) XXX_Size() int {
	return m.Size()
}
func (m *ReadIndexResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadIndexResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReadIndexResponse proto.InternalMessageInfo

func (m *ReadIndexResponse) GetReadIndex() uint64 {
	if m != nil {
		return m.ReadIndex
	}
	return 0
}

func (m *ReadIndexResponse) GetLocked() *kvrpcpb.LockInfo {
	if m != nil {
		return m.Locked
	}
	return nil
}

type Request struct {
	CmdType     CmdType             `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.CmdType" json:"cmd_type,omitempty"`
	Get         *GetRequest         `protobuf:"bytes,2,opt,name=get,proto3" json:"get,omitempty"`
	Put         *PutRequest         `protobuf:"bytes,4,opt,name=put,proto3" json:"put,omitempty"`
	Delete      *DeleteRequest      `protobuf:"bytes,5,opt,name=delete,proto3" json:"delete,omitempty"`
	Snap        *SnapRequest        `protobuf:"bytes,6,opt,name=snap,proto3" json:"snap,omitempty"`
	Prewrite    *PrewriteRequest    `protobuf:"bytes,7,opt,name=prewrite,proto3" json:"prewrite,omitempty"`
	DeleteRange *DeleteRangeRequest `protobuf:"bytes,8,opt,name=delete_range,json=deleteRange,proto3" json:"delete_range,omitempty"`
	IngestSst   *IngestSSTRequest   `protobuf:"bytes,9,opt,name=ingest_sst,json=ingestSst,proto3" json:"ingest_sst,omitempty"`
	ReadIndex   *ReadIndexRequest   `protobuf:"bytes,10,opt,name=read_index,json=readIndex,proto3" json:"read_index,omitempty"`
}

func (m *Request) Reset()         { *m = Request{} }
func (m *Request) String() string { return proto.CompactTextString(m) }
func (*Request) ProtoMessage()    {}
func (*Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{16}
}
func (m *Request) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Request.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Request.Merge(m, src)
}
func (m *Request) XXX_Size() int {
	return m.Size()
}
func (m *Request) XXX_DiscardUnknown() {
	xxx_messageInfo_Request.DiscardUnknown(m)
}

var xxx_messageInfo_Request proto.InternalMessageInfo

func (m *Request) GetCmdType() CmdType {
	if m != nil {
		return m.CmdType
	}
	return CmdType_Invalid
}

func (m *Request) GetGet() *GetRequest {
	if m != nil {
		return m.Get
	}
	return nil
}

func (m *Request) GetPut() *PutRequest {
	if m != nil {
		return m.Put
	}
	return nil
}

func (m *Request) GetDelete() *DeleteRequest {
	if m != nil {
		return m.Delete
	}
	return nil
}

func (m *Request) GetSnap() *SnapRequest {
	if m != nil {
		return m.Snap
	}
	return nil
}

func (m *Request) GetPrewrite() *PrewriteRequest {
	if m != nil {
		return m.Prewrite
	}
	return nil
}

func (m *Request) GetDeleteRange() *DeleteRangeRequest {
	if m != nil {
		return m.DeleteRange
	}
	return nil
}

func (m *Request) GetIngestSst() *IngestSSTRequest {
	if m != nil {
		return m.IngestSst
	}
	return nil
}

func (m *Request) GetReadIndex() *ReadIndexRequest {
	if m != nil {
		return m.ReadIndex
	}
	return nil
}

type Response struct {
	CmdType    CmdType              `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.CmdType" json:"cmd_type,omitempty"`
	Get        *GetResponse         `protobuf:"bytes,2,opt,name=get,proto3" json:"get,omitempty"`
	Put        *PutResponse         `protobuf:"bytes,4,opt,name=put,proto3" json:"put,omitempty"`
	Delete     *DeleteResponse      `protobuf:"bytes,5,opt,name=delete,proto3" json:"delete,omitempty"`
	Snap       *SnapResponse        `protobuf:"bytes,6,opt,name=snap,proto3" json:"snap,omitempty"`
	Prewrite   *PrewriteResponse    `protobuf:"bytes,7,opt,name=prewrite,proto3" json:"prewrite,omitempty"`
	DelteRange *DeleteRangeResponse `protobuf:"bytes,8,opt,name=delte_range,json=delteRange,proto3" json:"delte_range,omitempty"`
	IngestSst  *IngestSSTResponse   `protobuf:"bytes,9,opt,name=ingest_sst,json=ingestSst,proto3" json:"ingest_sst,omitempty"`
	ReadIndex  *ReadIndexResponse   `protobuf:"bytes,10,opt,name=read_index,json=readIndex,proto3" json:"read_index,omitempty"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{17}
}
func (m *Response) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Response.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(m, src)
}
func (m *Response) XXX_Size() int {
	return m.Size()
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetCmdType() CmdType {
	if m != nil {
		return m.CmdType
	}
	return CmdType_Invalid
}

func (m *Response) GetGet() *GetResponse {
	if m != nil {
		return m.Get
	}
	return nil
}

func (m *Response) GetPut() *PutResponse {
	if m != nil {
		return m.Put
	}
	return nil
}

func (m *Response) GetDelete() *DeleteResponse {
	if m != nil {
		return m.Delete
	}
	return nil
}

func (m *Response) GetSnap() *SnapResponse {
	if m != nil {
		return m.Snap
	}
	return nil
}

func (m *Response) GetPrewrite() *PrewriteResponse {
	if m != nil {
		return m.Prewrite
	}
	return nil
}

func (m *Response) GetDelteRange() *DeleteRangeResponse {
	if m != nil {
		return m.DelteRange
	}
	return nil
}

func (m *Response) GetIngestSst() *IngestSSTResponse {
	if m != nil {
		return m.IngestSst
	}
	return nil
}

func (m *Response) GetReadIndex() *ReadIndexResponse {
	if m != nil {
		return m.ReadIndex
	}
	return nil
}

type ChangePeerRequest struct {
	// This can be only called in internal RaftStore now.
	ChangeType eraftpb.ConfChangeType `protobuf:"varint,1,opt,name=change_type,json=changeType,proto3,enum=eraftpb.ConfChangeType" json:"change_type,omitempty"`
	Peer       *metapb.Peer           `protobuf:"bytes,2,opt,name=peer,proto3" json:"peer,omitempty"`
}

func (m *ChangePeerRequest) Reset()         { *m = ChangePeerRequest{} }
func (m *ChangePeerRequest) String() string { return proto.CompactTextString(m) }
func (*ChangePeerRequest) ProtoMessage()    {}
func (*ChangePeerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{18}
}
func (m *ChangePeerRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangePeerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangePeerRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangePeerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangePeerRequest.Merge(m, src)
}
func (m *ChangePeerRequest) XXX_Size() int {
	return m.Size()
}
func (m *ChangePeerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangePeerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChangePeerRequest proto.InternalMessageInfo

func (m *ChangePeerRequest) GetChangeType() eraftpb.ConfChangeType {
	if m != nil {
		return m.ChangeType
	}
	return eraftpb.ConfChangeType_AddNode
}

func (m *ChangePeerRequest) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

type ChangePeerResponse struct {
	Region *metapb.Region `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
}

func (m *ChangePeerResponse) Reset()         { *m = ChangePeerResponse{} }
func (m *ChangePeerResponse) String() string { return proto.CompactTextString(m) }
func (*ChangePeerResponse) ProtoMessage()    {}
func (*ChangePeerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{19}
}
func (m *ChangePeerResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangePeerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangePeerResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangePeerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangePeerResponse.Merge(m, src)
}
func (m *ChangePeerResponse) XXX_Size() int {
	return m.Size()
}
func (m *ChangePeerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangePeerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChangePeerResponse proto.InternalMessageInfo

func (m *ChangePeerResponse) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

type ChangePeerV2Request struct {
	Changes []*ChangePeerRequest `protobuf:"bytes,1,rep,name=changes,proto3" json:"changes,omitempty"`
}

func (m *ChangePeerV2Request) Reset()         { *m = ChangePeerV2Request{} }
func (m *ChangePeerV2Request) String() string { return proto.CompactTextString(m) }
func (*ChangePeerV2Request) ProtoMessage()    {}
func (*ChangePeerV2Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{20}
}
func (m *ChangePeerV2Request) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangePeerV2Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangePeerV2Request.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangePeerV2Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangePeerV2Request.Merge(m, src)
}
func (m *ChangePeerV2Request) XXX_Size() int {
	return m.Size()
}
func (m *ChangePeerV2Request) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangePeerV2Request.DiscardUnknown(m)
}

var xxx_messageInfo_ChangePeerV2Request proto.InternalMessageInfo

func (m *ChangePeerV2Request) GetChanges() []*ChangePeerRequest {
	if m != nil {
		return m.Changes
	}
	return nil
}

type ChangePeerV2Response struct {
	Region *metapb.Region `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
}

func (m *ChangePeerV2Response) Reset()         { *m = ChangePeerV2Response{} }
func (m *ChangePeerV2Response) String() string { return proto.CompactTextString(m) }
func (*ChangePeerV2Response) ProtoMessage()    {}
func (*ChangePeerV2Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{21}
}
func (m *ChangePeerV2Response) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangePeerV2Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangePeerV2Response.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangePeerV2Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangePeerV2Response.Merge(m, src)
}
func (m *ChangePeerV2Response) XXX_Size() int {
	return m.Size()
}
func (m *ChangePeerV2Response) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangePeerV2Response.DiscardUnknown(m)
}

var xxx_messageInfo_ChangePeerV2Response proto.InternalMessageInfo

func (m *ChangePeerV2Response) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

type SplitRequest struct {
	// This can be only called in internal RaftStore now.
	// The split_key must be in the been splitting region.
	SplitKey []byte `protobuf:"bytes,1,opt,name=split_key,json=splitKey,proto3" json:"split_key,omitempty"`
	// We split the region into two, first uses the origin
	// parent region id, and the second uses the new_region_id.
	// We must guarantee that the new_region_id is global unique.
	NewRegionId uint64 `protobuf:"varint,2,opt,name=new_region_id,json=newRegionId,proto3" json:"new_region_id,omitempty"`
	// The peer ids for the new split region.
	NewPeerIds []uint64 `protobuf:"varint,3,rep,packed,name=new_peer_ids,json=newPeerIds,proto3" json:"new_peer_ids,omitempty"`
	// If true, right region derive the origin region_id,
	// left region use new_region_id.
	// Will be ignored in batch split, use `BatchSplitRequest::right_derive` instead.
	RightDerive bool `protobuf:"varint,4,opt,name=right_derive,json=rightDerive,proto3" json:"right_derive,omitempty"` // Deprecated: Do not use.
	// It should be false iff the region split by user key such as split table or create partion table etc,
	// the new region's will not share the source region size, so it's region size is zero.
	// It should be true iff the region's load reaches the threshold such as size, keys, load check etc,
	// the new region's size will share the origin region, so it's region size is half of the source region.
	ShareSourceRegionSize bool `protobuf:"varint,5,opt,name=share_source_region_size,json=shareSourceRegionSize,proto3" json:"share_source_region_size,omitempty"`
}

func (m *SplitRequest) Reset()         { *m = SplitRequest{} }
func (m *SplitRequest) String() string { return proto.CompactTextString(m) }
func (*SplitRequest) ProtoMessage()    {}
func (*SplitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{22}
}
func (m *SplitRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SplitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SplitRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SplitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitRequest.Merge(m, src)
}
func (m *SplitRequest) XXX_Size() int {
	return m.Size()
}
func (m *SplitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SplitRequest proto.InternalMessageInfo

func (m *SplitRequest) GetSplitKey() []byte {
	if m != nil {
		return m.SplitKey
	}
	return nil
}

func (m *SplitRequest) GetNewRegionId() uint64 {
	if m != nil {
		return m.NewRegionId
	}
	return 0
}

func (m *SplitRequest) GetNewPeerIds() []uint64 {
	if m != nil {
		return m.NewPeerIds
	}
	return nil
}

// Deprecated: Do not use.
func (m *SplitRequest) GetRightDerive() bool {
	if m != nil {
		return m.RightDerive
	}
	return false
}

func (m *SplitRequest) GetShareSourceRegionSize() bool {
	if m != nil {
		return m.ShareSourceRegionSize
	}
	return false
}

type SplitResponse struct {
	Left  *metapb.Region `protobuf:"bytes,1,opt,name=left,proto3" json:"left,omitempty"`
	Right *metapb.Region `protobuf:"bytes,2,opt,name=right,proto3" json:"right,omitempty"`
}

func (m *SplitResponse) Reset()         { *m = SplitResponse{} }
func (m *SplitResponse) String() string { return proto.CompactTextString(m) }
func (*SplitResponse) ProtoMessage()    {}
func (*SplitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{23}
}
func (m *SplitResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SplitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SplitResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SplitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitResponse.Merge(m, src)
}
func (m *SplitResponse) XXX_Size() int {
	return m.Size()
}
func (m *SplitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SplitResponse proto.InternalMessageInfo

func (m *SplitResponse) GetLeft() *metapb.Region {
	if m != nil {
		return m.Left
	}
	return nil
}

func (m *SplitResponse) GetRight() *metapb.Region {
	if m != nil {
		return m.Right
	}
	return nil
}

type BatchSplitRequest struct {
	Requests []*SplitRequest `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	// If true, the last region derive the origin region_id,
	// other regions use new ids.
	RightDerive bool `protobuf:"varint,2,opt,name=right_derive,json=rightDerive,proto3" json:"right_derive,omitempty"`
	// It should be false iff the region split by user key such as split table or create partion table etc,
	// the new region's will not share the source region size, so it's region size is zero.
	// It should be true iff the region's load reaches the threshold such as size, keys, load check etc,
	// the new region's size will share the origin region, so it's region size is half of the source region.
	ShareSourceRegionSize bool `protobuf:"varint,3,opt,name=share_source_region_size,json=shareSourceRegionSize,proto3" json:"share_source_region_size,omitempty"`
}

func (m *BatchSplitRequest) Reset()         { *m = BatchSplitRequest{} }
func (m *BatchSplitRequest) String() string { return proto.CompactTextString(m) }
func (*BatchSplitRequest) ProtoMessage()    {}
func (*BatchSplitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{24}
}
func (m *BatchSplitRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchSplitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchSplitRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchSplitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSplitRequest.Merge(m, src)
}
func (m *BatchSplitRequest) XXX_Size() int {
	return m.Size()
}
func (m *BatchSplitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSplitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSplitRequest proto.InternalMessageInfo

func (m *BatchSplitRequest) GetRequests() []*SplitRequest {
	if m != nil {
		return m.Requests
	}
	return nil
}

func (m *BatchSplitRequest) GetRightDerive() bool {
	if m != nil {
		return m.RightDerive
	}
	return false
}

func (m *BatchSplitRequest) GetShareSourceRegionSize() bool {
	if m != nil {
		return m.ShareSourceRegionSize
	}
	return false
}

type BatchSplitResponse struct {
	Regions []*metapb.Region `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty"`
}

func (m *BatchSplitResponse) Reset()         { *m = BatchSplitResponse{} }
func (m *BatchSplitResponse) String() string { return proto.CompactTextString(m) }
func (*BatchSplitResponse) ProtoMessage()    {}
func (*BatchSplitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{25}
}
func (m *BatchSplitResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchSplitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchSplitResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchSplitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSplitResponse.Merge(m, src)
}
func (m *BatchSplitResponse) XXX_Size() int {
	return m.Size()
}
func (m *BatchSplitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSplitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSplitResponse proto.InternalMessageInfo

func (m *BatchSplitResponse) GetRegions() []*metapb.Region {
	if m != nil {
		return m.Regions
	}
	return nil
}

type CompactLogRequest struct {
	CompactIndex         uint64 `protobuf:"varint,1,opt,name=compact_index,json=compactIndex,proto3" json:"compact_index,omitempty"`
	CompactTerm          uint64 `protobuf:"varint,2,opt,name=compact_term,json=compactTerm,proto3" json:"compact_term,omitempty"`
	VoterReplicatedIndex uint64 `protobuf:"varint,3,opt,name=voter_replicated_index,json=voterReplicatedIndex,proto3" json:"voter_replicated_index,omitempty"`
}

func (m *CompactLogRequest) Reset()         { *m = CompactLogRequest{} }
func (m *CompactLogRequest) String() string { return proto.CompactTextString(m) }
func (*CompactLogRequest) ProtoMessage()    {}
func (*CompactLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{26}
}
func (m *CompactLogRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactLogRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CompactLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactLogRequest.Merge(m, src)
}
func (m *CompactLogRequest) XXX_Size() int {
	return m.Size()
}
func (m *CompactLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CompactLogRequest proto.InternalMessageInfo

func (m *CompactLogRequest) GetCompactIndex() uint64 {
	if m != nil {
		return m.CompactIndex
	}
	return 0
}

func (m *CompactLogRequest) GetCompactTerm() uint64 {
	if m != nil {
		return m.CompactTerm
	}
	return 0
}

func (m *CompactLogRequest) GetVoterReplicatedIndex() uint64 {
	if m != nil {
		return m.VoterReplicatedIndex
	}
	return 0
}

type CompactLogResponse struct {
}

func (m *CompactLogResponse) Reset()         { *m = CompactLogResponse{} }
func (m *CompactLogResponse) String() string { return proto.CompactTextString(m) }
func (*CompactLogResponse) ProtoMessage()    {}
func (*CompactLogResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{27}
}
func (m *CompactLogResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactLogResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactLogResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CompactLogResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactLogResponse.Merge(m, src)
}
func (m *CompactLogResponse) XXX_Size() int {
	return m.Size()
}
func (m *CompactLogResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactLogResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CompactLogResponse proto.InternalMessageInfo

type TransferLeaderRequest struct {
	Peer  *metapb.Peer   `protobuf:"bytes,1,opt,name=peer,proto3" json:"peer,omitempty"`
	Peers []*metapb.Peer `protobuf:"bytes,2,rep,name=peers,proto3" json:"peers,omitempty"`
}

func (m *TransferLeaderRequest) Reset()         { *m = TransferLeaderRequest{} }
func (m *TransferLeaderRequest) String() string { return proto.CompactTextString(m) }
func (*TransferLeaderRequest) ProtoMessage()    {}
func (*TransferLeaderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{28}
}
func (m *TransferLeaderRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransferLeaderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransferLeaderRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TransferLeaderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferLeaderRequest.Merge(m, src)
}
func (m *TransferLeaderRequest) XXX_Size() int {
	return m.Size()
}
func (m *TransferLeaderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferLeaderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TransferLeaderRequest proto.InternalMessageInfo

func (m *TransferLeaderRequest) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

func (m *TransferLeaderRequest) GetPeers() []*metapb.Peer {
	if m != nil {
		return m.Peers
	}
	return nil
}

type TransferLeaderResponse struct {
}

func (m *TransferLeaderResponse) Reset()         { *m = TransferLeaderResponse{} }
func (m *TransferLeaderResponse) String() string { return proto.CompactTextString(m) }
func (*TransferLeaderResponse) ProtoMessage()    {}
func (*TransferLeaderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{29}
}
func (m *TransferLeaderResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransferLeaderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransferLeaderResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TransferLeaderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferLeaderResponse.Merge(m, src)
}
func (m *TransferLeaderResponse) XXX_Size() int {
	return m.Size()
}
func (m *TransferLeaderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferLeaderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TransferLeaderResponse proto.InternalMessageInfo

type ComputeHashRequest struct {
	Context []byte `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
}

func (m *ComputeHashRequest) Reset()         { *m = ComputeHashRequest{} }
func (m *ComputeHashRequest) String() string { return proto.CompactTextString(m) }
func (*ComputeHashRequest) ProtoMessage()    {}
func (*ComputeHashRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{30}
}
func (m *ComputeHashRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ComputeHashRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ComputeHashRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ComputeHashRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ComputeHashRequest.Merge(m, src)
}
func (m *ComputeHashRequest) XXX_Size() int {
	return m.Size()
}
func (m *ComputeHashRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ComputeHashRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ComputeHashRequest proto.InternalMessageInfo

func (m *ComputeHashRequest) GetContext() []byte {
	if m != nil {
		return m.Context
	}
	return nil
}

type VerifyHashRequest struct {
	Index   uint64 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Hash    []byte `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
	Context []byte `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
}

func (m *VerifyHashRequest) Reset()         { *m = VerifyHashRequest{} }
func (m *VerifyHashRequest) String() string { return proto.CompactTextString(m) }
func (*VerifyHashRequest) ProtoMessage()    {}
func (*VerifyHashRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{31}
}
func (m *VerifyHashRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *VerifyHashRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_VerifyHashRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *VerifyHashRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyHashRequest.Merge(m, src)
}
func (m *VerifyHashRequest) XXX_Size() int {
	return m.Size()
}
func (m *VerifyHashRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyHashRequest.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyHashRequest proto.InternalMessageInfo

func (m *VerifyHashRequest) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *VerifyHashRequest) GetHash() []byte {
	if m != nil {
		return m.Hash
	}
	return nil
}

func (m *VerifyHashRequest) GetContext() []byte {
	if m != nil {
		return m.Context
	}
	return nil
}

type VerifyHashResponse struct {
}

func (m *VerifyHashResponse) Reset()         { *m = VerifyHashResponse{} }
func (m *VerifyHashResponse) String() string { return proto.CompactTextString(m) }
func (*VerifyHashResponse) ProtoMessage()    {}
func (*VerifyHashResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{32}
}
func (m *VerifyHashResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *VerifyHashResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_VerifyHashResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *VerifyHashResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyHashResponse.Merge(m, src)
}
func (m *VerifyHashResponse) XXX_Size() int {
	return m.Size()
}
func (m *VerifyHashResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyHashResponse.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyHashResponse proto.InternalMessageInfo

type PrepareMergeRequest struct {
	MinIndex uint64         `protobuf:"varint,1,opt,name=min_index,json=minIndex,proto3" json:"min_index,omitempty"`
	Target   *metapb.Region `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
}

func (m *PrepareMergeRequest) Reset()         { *m = PrepareMergeRequest{} }
func (m *PrepareMergeRequest) String() string { return proto.CompactTextString(m) }
func (*PrepareMergeRequest) ProtoMessage()    {}
func (*PrepareMergeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{33}
}
func (m *PrepareMergeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrepareMergeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrepareMergeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrepareMergeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareMergeRequest.Merge(m, src)
}
func (m *PrepareMergeRequest) XXX_Size() int {
	return m.Size()
}
func (m *PrepareMergeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareMergeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareMergeRequest proto.InternalMessageInfo

func (m *PrepareMergeRequest) GetMinIndex() uint64 {
	if m != nil {
		return m.MinIndex
	}
	return 0
}

func (m *PrepareMergeRequest) GetTarget() *metapb.Region {
	if m != nil {
		return m.Target
	}
	return nil
}

type PrepareMergeResponse struct {
}

func (m *PrepareMergeResponse) Reset()         { *m = PrepareMergeResponse{} }
func (m *PrepareMergeResponse) String() string { return proto.CompactTextString(m) }
func (*PrepareMergeResponse) ProtoMessage()    {}
func (*PrepareMergeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{34}
}
func (m *PrepareMergeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrepareMergeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrepareMergeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrepareMergeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareMergeResponse.Merge(m, src)
}
func (m *PrepareMergeResponse) XXX_Size() int {
	return m.Size()
}
func (m *PrepareMergeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareMergeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareMergeResponse proto.InternalMessageInfo

type PrepareFlashbackRequest struct {
	// The start_ts that the current flashback progress is using.
	StartTs uint64 `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
}

func (m *PrepareFlashbackRequest) Reset()         { *m = PrepareFlashbackRequest{} }
func (m *PrepareFlashbackRequest) String() string { return proto.CompactTextString(m) }
func (*PrepareFlashbackRequest) ProtoMessage()    {}
func (*PrepareFlashbackRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{35}
}
func (m *PrepareFlashbackRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrepareFlashbackRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrepareFlashbackRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrepareFlashbackRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareFlashbackRequest.Merge(m, src)
}
func (m *PrepareFlashbackRequest) XXX_Size() int {
	return m.Size()
}
func (m *PrepareFlashbackRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareFlashbackRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareFlashbackRequest proto.InternalMessageInfo

func (m *PrepareFlashbackRequest) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

type PrepareFlashbackResponse struct {
}

func (m *PrepareFlashbackResponse) Reset()         { *m = PrepareFlashbackResponse{} }
func (m *PrepareFlashbackResponse) String() string { return proto.CompactTextString(m) }
func (*PrepareFlashbackResponse) ProtoMessage()    {}
func (*PrepareFlashbackResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{36}
}
func (m *PrepareFlashbackResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrepareFlashbackResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrepareFlashbackResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrepareFlashbackResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrepareFlashbackResponse.Merge(m, src)
}
func (m *PrepareFlashbackResponse) XXX_Size() int {
	return m.Size()
}
func (m *PrepareFlashbackResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PrepareFlashbackResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PrepareFlashbackResponse proto.InternalMessageInfo

type FinishFlashbackRequest struct {
}

func (m *FinishFlashbackRequest) Reset()         { *m = FinishFlashbackRequest{} }
func (m *FinishFlashbackRequest) String() string { return proto.CompactTextString(m) }
func (*FinishFlashbackRequest) ProtoMessage()    {}
func (*FinishFlashbackRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{37}
}
func (m *FinishFlashbackRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FinishFlashbackRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FinishFlashbackRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FinishFlashbackRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishFlashbackRequest.Merge(m, src)
}
func (m *FinishFlashbackRequest) XXX_Size() int {
	return m.Size()
}
func (m *FinishFlashbackRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishFlashbackRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FinishFlashbackRequest proto.InternalMessageInfo

type FinishFlashbackResponse struct {
}

func (m *FinishFlashbackResponse) Reset()         { *m = FinishFlashbackResponse{} }
func (m *FinishFlashbackResponse) String() string { return proto.CompactTextString(m) }
func (*FinishFlashbackResponse) ProtoMessage()    {}
func (*FinishFlashbackResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{38}
}
func (m *FinishFlashbackResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FinishFlashbackResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FinishFlashbackResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FinishFlashbackResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishFlashbackResponse.Merge(m, src)
}
func (m *FinishFlashbackResponse) XXX_Size() int {
	return m.Size()
}
func (m *FinishFlashbackResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishFlashbackResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FinishFlashbackResponse proto.InternalMessageInfo

type CommitMergeRequest struct {
	Source  *metapb.Region   `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Commit  uint64           `protobuf:"varint,2,opt,name=commit,proto3" json:"commit,omitempty"`
	Entries []*eraftpb.Entry `protobuf:"bytes,3,rep,name=entries,proto3" json:"entries,omitempty"`
	// Used in v2. When it's present, `source` and `commit` will not be set.
	SourceState *raft_serverpb.RegionLocalState `protobuf:"bytes,4,opt,name=source_state,json=sourceState,proto3" json:"source_state,omitempty"`
	SourceMeta  []byte                          `protobuf:"bytes,100,opt,name=source_meta,json=sourceMeta,proto3" json:"source_meta,omitempty"`
}

func (m *CommitMergeRequest) Reset()         { *m = CommitMergeRequest{} }
func (m *CommitMergeRequest) String() string { return proto.CompactTextString(m) }
func (*CommitMergeRequest) ProtoMessage()    {}
func (*CommitMergeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{39}
}
func (m *CommitMergeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommitMergeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommitMergeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommitMergeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitMergeRequest.Merge(m, src)
}
func (m *CommitMergeRequest) XXX_Size() int {
	return m.Size()
}
func (m *CommitMergeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitMergeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommitMergeRequest proto.InternalMessageInfo

func (m *CommitMergeRequest) GetSource() *metapb.Region {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *CommitMergeRequest) GetCommit() uint64 {
	if m != nil {
		return m.Commit
	}
	return 0
}

func (m *CommitMergeRequest) GetEntries() []*eraftpb.Entry {
	if m != nil {
		return m.Entries
	}
	return nil
}

func (m *CommitMergeRequest) GetSourceState() *raft_serverpb.RegionLocalState {
	if m != nil {
		return m.SourceState
	}
	return nil
}

func (m *CommitMergeRequest) GetSourceMeta() []byte {
	if m != nil {
		return m.SourceMeta
	}
	return nil
}

type CommitMergeResponse struct {
}

func (m *CommitMergeResponse) Reset()         { *m = CommitMergeResponse{} }
func (m *CommitMergeResponse) String() string { return proto.CompactTextString(m) }
func (*CommitMergeResponse) ProtoMessage()    {}
func (*CommitMergeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{40}
}
func (m *CommitMergeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommitMergeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommitMergeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommitMergeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommitMergeResponse.Merge(m, src)
}
func (m *CommitMergeResponse) XXX_Size() int {
	return m.Size()
}
func (m *CommitMergeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommitMergeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommitMergeResponse proto.InternalMessageInfo

type RollbackMergeRequest struct {
	Commit uint64 `protobuf:"varint,1,opt,name=commit,proto3" json:"commit,omitempty"`
}

func (m *RollbackMergeRequest) Reset()         { *m = RollbackMergeRequest{} }
func (m *RollbackMergeRequest) String() string { return proto.CompactTextString(m) }
func (*RollbackMergeRequest) ProtoMessage()    {}
func (*RollbackMergeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{41}
}
func (m *RollbackMergeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RollbackMergeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RollbackMergeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RollbackMergeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollbackMergeRequest.Merge(m, src)
}
func (m *RollbackMergeRequest) XXX_Size() int {
	return m.Size()
}
func (m *RollbackMergeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RollbackMergeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RollbackMergeRequest proto.InternalMessageInfo

func (m *RollbackMergeRequest) GetCommit() uint64 {
	if m != nil {
		return m.Commit
	}
	return 0
}

type RollbackMergeResponse struct {
}

func (m *RollbackMergeResponse) Reset()         { *m = RollbackMergeResponse{} }
func (m *RollbackMergeResponse) String() string { return proto.CompactTextString(m) }
func (*RollbackMergeResponse) ProtoMessage()    {}
func (*RollbackMergeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{42}
}
func (m *RollbackMergeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RollbackMergeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RollbackMergeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RollbackMergeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollbackMergeResponse.Merge(m, src)
}
func (m *RollbackMergeResponse) XXX_Size() int {
	return m.Size()
}
func (m *RollbackMergeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RollbackMergeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RollbackMergeResponse proto.InternalMessageInfo

type SwitchWitnessRequest struct {
	PeerId    uint64 `protobuf:"varint,1,opt,name=peer_id,json=peerId,proto3" json:"peer_id,omitempty"`
	IsWitness bool   `protobuf:"varint,2,opt,name=is_witness,json=isWitness,proto3" json:"is_witness,omitempty"`
}

func (m *SwitchWitnessRequest) Reset()         { *m = SwitchWitnessRequest{} }
func (m *SwitchWitnessRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchWitnessRequest) ProtoMessage()    {}
func (*SwitchWitnessRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{43}
}
func (m *SwitchWitnessRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SwitchWitnessRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SwitchWitnessRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SwitchWitnessRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchWitnessRequest.Merge(m, src)
}
func (m *SwitchWitnessRequest) XXX_Size() int {
	return m.Size()
}
func (m *SwitchWitnessRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchWitnessRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchWitnessRequest proto.InternalMessageInfo

func (m *SwitchWitnessRequest) GetPeerId() uint64 {
	if m != nil {
		return m.PeerId
	}
	return 0
}

func (m *SwitchWitnessRequest) GetIsWitness() bool {
	if m != nil {
		return m.IsWitness
	}
	return false
}

type BatchSwitchWitnessRequest struct {
	SwitchWitnesses []*SwitchWitnessRequest `protobuf:"bytes,1,rep,name=switch_witnesses,json=switchWitnesses,proto3" json:"switch_witnesses,omitempty"`
}

func (m *BatchSwitchWitnessRequest) Reset()         { *m = BatchSwitchWitnessRequest{} }
func (m *BatchSwitchWitnessRequest) String() string { return proto.CompactTextString(m) }
func (*BatchSwitchWitnessRequest) ProtoMessage()    {}
func (*BatchSwitchWitnessRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{44}
}
func (m *BatchSwitchWitnessRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchSwitchWitnessRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchSwitchWitnessRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchSwitchWitnessRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSwitchWitnessRequest.Merge(m, src)
}
func (m *BatchSwitchWitnessRequest) XXX_Size() int {
	return m.Size()
}
func (m *BatchSwitchWitnessRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSwitchWitnessRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSwitchWitnessRequest proto.InternalMessageInfo

func (m *BatchSwitchWitnessRequest) GetSwitchWitnesses() []*SwitchWitnessRequest {
	if m != nil {
		return m.SwitchWitnesses
	}
	return nil
}

type BatchSwitchWitnessResponse struct {
}

func (m *BatchSwitchWitnessResponse) Reset()         { *m = BatchSwitchWitnessResponse{} }
func (m *BatchSwitchWitnessResponse) String() string { return proto.CompactTextString(m) }
func (*BatchSwitchWitnessResponse) ProtoMessage()    {}
func (*BatchSwitchWitnessResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{45}
}
func (m *BatchSwitchWitnessResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchSwitchWitnessResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchSwitchWitnessResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchSwitchWitnessResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSwitchWitnessResponse.Merge(m, src)
}
func (m *BatchSwitchWitnessResponse) XXX_Size() int {
	return m.Size()
}
func (m *BatchSwitchWitnessResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSwitchWitnessResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSwitchWitnessResponse proto.InternalMessageInfo

type UpdateGcPeerRequest struct {
	PeerId []uint64 `protobuf:"varint,1,rep,packed,name=peer_id,json=peerId,proto3" json:"peer_id,omitempty"`
}

func (m *UpdateGcPeerRequest) Reset()         { *m = UpdateGcPeerRequest{} }
func (m *UpdateGcPeerRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateGcPeerRequest) ProtoMessage()    {}
func (*UpdateGcPeerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{46}
}
func (m *UpdateGcPeerRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateGcPeerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateGcPeerRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateGcPeerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGcPeerRequest.Merge(m, src)
}
func (m *UpdateGcPeerRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpdateGcPeerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGcPeerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGcPeerRequest proto.InternalMessageInfo

func (m *UpdateGcPeerRequest) GetPeerId() []uint64 {
	if m != nil {
		return m.PeerId
	}
	return nil
}

type AdminRequest struct {
	CmdType          AdminCmdType               `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.AdminCmdType" json:"cmd_type,omitempty"`
	ChangePeer       *ChangePeerRequest         `protobuf:"bytes,2,opt,name=change_peer,json=changePeer,proto3" json:"change_peer,omitempty"`
	Split            *SplitRequest              `protobuf:"bytes,3,opt,name=split,proto3" json:"split,omitempty"` // Deprecated: Do not use.
	CompactLog       *CompactLogRequest         `protobuf:"bytes,4,opt,name=compact_log,json=compactLog,proto3" json:"compact_log,omitempty"`
	TransferLeader   *TransferLeaderRequest     `protobuf:"bytes,5,opt,name=transfer_leader,json=transferLeader,proto3" json:"transfer_leader,omitempty"`
	VerifyHash       *VerifyHashRequest         `protobuf:"bytes,6,opt,name=verify_hash,json=verifyHash,proto3" json:"verify_hash,omitempty"`
	PrepareMerge     *PrepareMergeRequest       `protobuf:"bytes,7,opt,name=prepare_merge,json=prepareMerge,proto3" json:"prepare_merge,omitempty"`
	CommitMerge      *CommitMergeRequest        `protobuf:"bytes,8,opt,name=commit_merge,json=commitMerge,proto3" json:"commit_merge,omitempty"`
	RollbackMerge    *RollbackMergeRequest      `protobuf:"bytes,9,opt,name=rollback_merge,json=rollbackMerge,proto3" json:"rollback_merge,omitempty"`
	Splits           *BatchSplitRequest         `protobuf:"bytes,10,opt,name=splits,proto3" json:"splits,omitempty"`
	ChangePeerV2     *ChangePeerV2Request       `protobuf:"bytes,11,opt,name=change_peer_v2,json=changePeerV2,proto3" json:"change_peer_v2,omitempty"`
	ComputeHash      *ComputeHashRequest        `protobuf:"bytes,12,opt,name=compute_hash,json=computeHash,proto3" json:"compute_hash,omitempty"`
	PrepareFlashback *PrepareFlashbackRequest   `protobuf:"bytes,13,opt,name=prepare_flashback,json=prepareFlashback,proto3" json:"prepare_flashback,omitempty"`
	FinishFlashback  *FinishFlashbackRequest    `protobuf:"bytes,14,opt,name=finish_flashback,json=finishFlashback,proto3" json:"finish_flashback,omitempty"`
	SwitchWitnesses  *BatchSwitchWitnessRequest `protobuf:"bytes,15,opt,name=switch_witnesses,json=switchWitnesses,proto3" json:"switch_witnesses,omitempty"`
	UpdateGcPeers    *UpdateGcPeerRequest       `protobuf:"bytes,16,opt,name=update_gc_peers,json=updateGcPeers,proto3" json:"update_gc_peers,omitempty"`
}

func (m *AdminRequest) Reset()         { *m = AdminRequest{} }
func (m *AdminRequest) String() string { return proto.CompactTextString(m) }
func (*AdminRequest) ProtoMessage()    {}
func (*AdminRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{47}
}
func (m *AdminRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AdminRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AdminRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AdminRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminRequest.Merge(m, src)
}
func (m *AdminRequest) XXX_Size() int {
	return m.Size()
}
func (m *AdminRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AdminRequest proto.InternalMessageInfo

func (m *AdminRequest) GetCmdType() AdminCmdType {
	if m != nil {
		return m.CmdType
	}
	return AdminCmdType_InvalidAdmin
}

func (m *AdminRequest) GetChangePeer() *ChangePeerRequest {
	if m != nil {
		return m.ChangePeer
	}
	return nil
}

// Deprecated: Do not use.
func (m *AdminRequest) GetSplit() *SplitRequest {
	if m != nil {
		return m.Split
	}
	return nil
}

func (m *AdminRequest) GetCompactLog() *CompactLogRequest {
	if m != nil {
		return m.CompactLog
	}
	return nil
}

func (m *AdminRequest) GetTransferLeader() *TransferLeaderRequest {
	if m != nil {
		return m.TransferLeader
	}
	return nil
}

func (m *AdminRequest) GetVerifyHash() *VerifyHashRequest {
	if m != nil {
		return m.VerifyHash
	}
	return nil
}

func (m *AdminRequest) GetPrepareMerge() *PrepareMergeRequest {
	if m != nil {
		return m.PrepareMerge
	}
	return nil
}

func (m *AdminRequest) GetCommitMerge() *CommitMergeRequest {
	if m != nil {
		return m.CommitMerge
	}
	return nil
}

func (m *AdminRequest) GetRollbackMerge() *RollbackMergeRequest {
	if m != nil {
		return m.RollbackMerge
	}
	return nil
}

func (m *AdminRequest) GetSplits() *BatchSplitRequest {
	if m != nil {
		return m.Splits
	}
	return nil
}

func (m *AdminRequest) GetChangePeerV2() *ChangePeerV2Request {
	if m != nil {
		return m.ChangePeerV2
	}
	return nil
}

func (m *AdminRequest) GetComputeHash() *ComputeHashRequest {
	if m != nil {
		return m.ComputeHash
	}
	return nil
}

func (m *AdminRequest) GetPrepareFlashback() *PrepareFlashbackRequest {
	if m != nil {
		return m.PrepareFlashback
	}
	return nil
}

func (m *AdminRequest) GetFinishFlashback() *FinishFlashbackRequest {
	if m != nil {
		return m.FinishFlashback
	}
	return nil
}

func (m *AdminRequest) GetSwitchWitnesses() *BatchSwitchWitnessRequest {
	if m != nil {
		return m.SwitchWitnesses
	}
	return nil
}

func (m *AdminRequest) GetUpdateGcPeers() *UpdateGcPeerRequest {
	if m != nil {
		return m.UpdateGcPeers
	}
	return nil
}

type AdminResponse struct {
	CmdType          AdminCmdType                `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.AdminCmdType" json:"cmd_type,omitempty"`
	ChangePeer       *ChangePeerResponse         `protobuf:"bytes,2,opt,name=change_peer,json=changePeer,proto3" json:"change_peer,omitempty"`
	Split            *SplitResponse              `protobuf:"bytes,3,opt,name=split,proto3" json:"split,omitempty"` // Deprecated: Do not use.
	CompactLog       *CompactLogResponse         `protobuf:"bytes,4,opt,name=compact_log,json=compactLog,proto3" json:"compact_log,omitempty"`
	TransferLeader   *TransferLeaderResponse     `protobuf:"bytes,5,opt,name=transfer_leader,json=transferLeader,proto3" json:"transfer_leader,omitempty"`
	VerifyHash       *VerifyHashResponse         `protobuf:"bytes,6,opt,name=verify_hash,json=verifyHash,proto3" json:"verify_hash,omitempty"`
	PrepareMerge     *PrepareMergeResponse       `protobuf:"bytes,7,opt,name=prepare_merge,json=prepareMerge,proto3" json:"prepare_merge,omitempty"`
	CommitMerge      *CommitMergeResponse        `protobuf:"bytes,8,opt,name=commit_merge,json=commitMerge,proto3" json:"commit_merge,omitempty"`
	RollbackMerge    *RollbackMergeResponse      `protobuf:"bytes,9,opt,name=rollback_merge,json=rollbackMerge,proto3" json:"rollback_merge,omitempty"`
	Splits           *BatchSplitResponse         `protobuf:"bytes,10,opt,name=splits,proto3" json:"splits,omitempty"`
	ChangePeerV2     *ChangePeerV2Response       `protobuf:"bytes,11,opt,name=change_peer_v2,json=changePeerV2,proto3" json:"change_peer_v2,omitempty"`
	PrepareFlashback *PrepareFlashbackResponse   `protobuf:"bytes,12,opt,name=prepare_flashback,json=prepareFlashback,proto3" json:"prepare_flashback,omitempty"`
	FinishFlashback  *FinishFlashbackResponse    `protobuf:"bytes,13,opt,name=finish_flashback,json=finishFlashback,proto3" json:"finish_flashback,omitempty"`
	SwitchWitnesses  *BatchSwitchWitnessResponse `protobuf:"bytes,14,opt,name=switch_witnesses,json=switchWitnesses,proto3" json:"switch_witnesses,omitempty"`
}

func (m *AdminResponse) Reset()         { *m = AdminResponse{} }
func (m *AdminResponse) String() string { return proto.CompactTextString(m) }
func (*AdminResponse) ProtoMessage()    {}
func (*AdminResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{48}
}
func (m *AdminResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AdminResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AdminResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AdminResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminResponse.Merge(m, src)
}
func (m *AdminResponse) XXX_Size() int {
	return m.Size()
}
func (m *AdminResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AdminResponse proto.InternalMessageInfo

func (m *AdminResponse) GetCmdType() AdminCmdType {
	if m != nil {
		return m.CmdType
	}
	return AdminCmdType_InvalidAdmin
}

func (m *AdminResponse) GetChangePeer() *ChangePeerResponse {
	if m != nil {
		return m.ChangePeer
	}
	return nil
}

// Deprecated: Do not use.
func (m *AdminResponse) GetSplit() *SplitResponse {
	if m != nil {
		return m.Split
	}
	return nil
}

func (m *AdminResponse) GetCompactLog() *CompactLogResponse {
	if m != nil {
		return m.CompactLog
	}
	return nil
}

func (m *AdminResponse) GetTransferLeader() *TransferLeaderResponse {
	if m != nil {
		return m.TransferLeader
	}
	return nil
}

func (m *AdminResponse) GetVerifyHash() *VerifyHashResponse {
	if m != nil {
		return m.VerifyHash
	}
	return nil
}

func (m *AdminResponse) GetPrepareMerge() *PrepareMergeResponse {
	if m != nil {
		return m.PrepareMerge
	}
	return nil
}

func (m *AdminResponse) GetCommitMerge() *CommitMergeResponse {
	if m != nil {
		return m.CommitMerge
	}
	return nil
}

func (m *AdminResponse) GetRollbackMerge() *RollbackMergeResponse {
	if m != nil {
		return m.RollbackMerge
	}
	return nil
}

func (m *AdminResponse) GetSplits() *BatchSplitResponse {
	if m != nil {
		return m.Splits
	}
	return nil
}

func (m *AdminResponse) GetChangePeerV2() *ChangePeerV2Response {
	if m != nil {
		return m.ChangePeerV2
	}
	return nil
}

func (m *AdminResponse) GetPrepareFlashback() *PrepareFlashbackResponse {
	if m != nil {
		return m.PrepareFlashback
	}
	return nil
}

func (m *AdminResponse) GetFinishFlashback() *FinishFlashbackResponse {
	if m != nil {
		return m.FinishFlashback
	}
	return nil
}

func (m *AdminResponse) GetSwitchWitnesses() *BatchSwitchWitnessResponse {
	if m != nil {
		return m.SwitchWitnesses
	}
	return nil
}

// For get the leader of the region.
type RegionLeaderRequest struct {
}

func (m *RegionLeaderRequest) Reset()         { *m = RegionLeaderRequest{} }
func (m *RegionLeaderRequest) String() string { return proto.CompactTextString(m) }
func (*RegionLeaderRequest) ProtoMessage()    {}
func (*RegionLeaderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{49}
}
func (m *RegionLeaderRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionLeaderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionLeaderRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionLeaderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionLeaderRequest.Merge(m, src)
}
func (m *RegionLeaderRequest) XXX_Size() int {
	return m.Size()
}
func (m *RegionLeaderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionLeaderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RegionLeaderRequest proto.InternalMessageInfo

type RegionLeaderResponse struct {
	Leader *metapb.Peer `protobuf:"bytes,1,opt,name=leader,proto3" json:"leader,omitempty"`
}

func (m *RegionLeaderResponse) Reset()         { *m = RegionLeaderResponse{} }
func (m *RegionLeaderResponse) String() string { return proto.CompactTextString(m) }
func (*RegionLeaderResponse) ProtoMessage()    {}
func (*RegionLeaderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{50}
}
func (m *RegionLeaderResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionLeaderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionLeaderResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionLeaderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionLeaderResponse.Merge(m, src)
}
func (m *RegionLeaderResponse) XXX_Size() int {
	return m.Size()
}
func (m *RegionLeaderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionLeaderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RegionLeaderResponse proto.InternalMessageInfo

func (m *RegionLeaderResponse) GetLeader() *metapb.Peer {
	if m != nil {
		return m.Leader
	}
	return nil
}

// For getting more information of the region.
// We add some admin operations (ChangePeer, Split...) into the pb job list,
// then pd server will peek the first one, handle it and then pop it from the job lib.
// But sometimes, the pd server may crash before popping. When another pd server
// starts and finds the job is running but not finished, it will first check whether
// the raft server already has handled this job.
// E,g, for ChangePeer, if we add Peer10 into region1 and find region1 has already had
// Peer10, we can think this ChangePeer is finished, and can pop this job from job list
// directly.
type RegionDetailRequest struct {
}

func (m *RegionDetailRequest) Reset()         { *m = RegionDetailRequest{} }
func (m *RegionDetailRequest) String() string { return proto.CompactTextString(m) }
func (*RegionDetailRequest) ProtoMessage()    {}
func (*RegionDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{51}
}
func (m *RegionDetailRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionDetailRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionDetailRequest.Merge(m, src)
}
func (m *RegionDetailRequest) XXX_Size() int {
	return m.Size()
}
func (m *RegionDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RegionDetailRequest proto.InternalMessageInfo

type RegionDetailResponse struct {
	Region *metapb.Region `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Leader *metapb.Peer   `protobuf:"bytes,2,opt,name=leader,proto3" json:"leader,omitempty"`
}

func (m *RegionDetailResponse) Reset()         { *m = RegionDetailResponse{} }
func (m *RegionDetailResponse) String() string { return proto.CompactTextString(m) }
func (*RegionDetailResponse) ProtoMessage()    {}
func (*RegionDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{52}
}
func (m *RegionDetailResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionDetailResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionDetailResponse.Merge(m, src)
}
func (m *RegionDetailResponse) XXX_Size() int {
	return m.Size()
}
func (m *RegionDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RegionDetailResponse proto.InternalMessageInfo

func (m *RegionDetailResponse) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *RegionDetailResponse) GetLeader() *metapb.Peer {
	if m != nil {
		return m.Leader
	}
	return nil
}

type StatusRequest struct {
	CmdType      StatusCmdType        `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.StatusCmdType" json:"cmd_type,omitempty"`
	RegionLeader *RegionLeaderRequest `protobuf:"bytes,2,opt,name=region_leader,json=regionLeader,proto3" json:"region_leader,omitempty"`
	RegionDetail *RegionDetailRequest `protobuf:"bytes,3,opt,name=region_detail,json=regionDetail,proto3" json:"region_detail,omitempty"`
}

func (m *StatusRequest) Reset()         { *m = StatusRequest{} }
func (m *StatusRequest) String() string { return proto.CompactTextString(m) }
func (*StatusRequest) ProtoMessage()    {}
func (*StatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{53}
}
func (m *StatusRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StatusRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatusRequest.Merge(m, src)
}
func (m *StatusRequest) XXX_Size() int {
	return m.Size()
}
func (m *StatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StatusRequest proto.InternalMessageInfo

func (m *StatusRequest) GetCmdType() StatusCmdType {
	if m != nil {
		return m.CmdType
	}
	return StatusCmdType_InvalidStatus
}

func (m *StatusRequest) GetRegionLeader() *RegionLeaderRequest {
	if m != nil {
		return m.RegionLeader
	}
	return nil
}

func (m *StatusRequest) GetRegionDetail() *RegionDetailRequest {
	if m != nil {
		return m.RegionDetail
	}
	return nil
}

type StatusResponse struct {
	CmdType      StatusCmdType         `protobuf:"varint,1,opt,name=cmd_type,json=cmdType,proto3,enum=raft_cmdpb.StatusCmdType" json:"cmd_type,omitempty"`
	RegionLeader *RegionLeaderResponse `protobuf:"bytes,2,opt,name=region_leader,json=regionLeader,proto3" json:"region_leader,omitempty"`
	RegionDetail *RegionDetailResponse `protobuf:"bytes,3,opt,name=region_detail,json=regionDetail,proto3" json:"region_detail,omitempty"`
}

func (m *StatusResponse) Reset()         { *m = StatusResponse{} }
func (m *StatusResponse) String() string { return proto.CompactTextString(m) }
func (*StatusResponse) ProtoMessage()    {}
func (*StatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{54}
}
func (m *StatusResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StatusResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatusResponse.Merge(m, src)
}
func (m *StatusResponse) XXX_Size() int {
	return m.Size()
}
func (m *StatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StatusResponse proto.InternalMessageInfo

func (m *StatusResponse) GetCmdType() StatusCmdType {
	if m != nil {
		return m.CmdType
	}
	return StatusCmdType_InvalidStatus
}

func (m *StatusResponse) GetRegionLeader() *RegionLeaderResponse {
	if m != nil {
		return m.RegionLeader
	}
	return nil
}

func (m *StatusResponse) GetRegionDetail() *RegionDetailResponse {
	if m != nil {
		return m.RegionDetail
	}
	return nil
}

type RaftRequestHeader struct {
	RegionId uint64       `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	Peer     *metapb.Peer `protobuf:"bytes,2,opt,name=peer,proto3" json:"peer,omitempty"`
	// true for read linearization
	ReadQuorum bool `protobuf:"varint,3,opt,name=read_quorum,json=readQuorum,proto3" json:"read_quorum,omitempty"`
	// 16 bytes, to distinguish request.
	Uuid        []byte              `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid,omitempty"`
	RegionEpoch *metapb.RegionEpoch `protobuf:"bytes,5,opt,name=region_epoch,json=regionEpoch,proto3" json:"region_epoch,omitempty"`
	Term        uint64              `protobuf:"varint,6,opt,name=term,proto3" json:"term,omitempty"`
	SyncLog     bool                `protobuf:"varint,7,opt,name=sync_log,json=syncLog,proto3" json:"sync_log,omitempty"`
	ReplicaRead bool                `protobuf:"varint,8,opt,name=replica_read,json=replicaRead,proto3" json:"replica_read,omitempty"`
	// Read requests can be responsed directly after the Raft applys to `applied_index`.
	AppliedIndex uint64 `protobuf:"varint,9,opt,name=applied_index,json=appliedIndex,proto3" json:"applied_index,omitempty"`
	// Custom flags for this raft request.
	Flags             uint64             `protobuf:"varint,10,opt,name=flags,proto3" json:"flags,omitempty"`
	FlagData          []byte             `protobuf:"bytes,11,opt,name=flag_data,json=flagData,proto3" json:"flag_data,omitempty"`
	Priority          kvrpcpb.CommandPri `protobuf:"varint,12,opt,name=priority,proto3,enum=kvrpcpb.CommandPri" json:"priority,omitempty"`
	ResourceGroupName string             `protobuf:"bytes,13,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
}

func (m *RaftRequestHeader) Reset()         { *m = RaftRequestHeader{} }
func (m *RaftRequestHeader) String() string { return proto.CompactTextString(m) }
func (*RaftRequestHeader) ProtoMessage()    {}
func (*RaftRequestHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{55}
}
func (m *RaftRequestHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftRequestHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftRequestHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftRequestHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftRequestHeader.Merge(m, src)
}
func (m *RaftRequestHeader) XXX_Size() int {
	return m.Size()
}
func (m *RaftRequestHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftRequestHeader.DiscardUnknown(m)
}

var xxx_messageInfo_RaftRequestHeader proto.InternalMessageInfo

func (m *RaftRequestHeader) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RaftRequestHeader) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

func (m *RaftRequestHeader) GetReadQuorum() bool {
	if m != nil {
		return m.ReadQuorum
	}
	return false
}

func (m *RaftRequestHeader) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

func (m *RaftRequestHeader) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *RaftRequestHeader) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

func (m *RaftRequestHeader) GetSyncLog() bool {
	if m != nil {
		return m.SyncLog
	}
	return false
}

func (m *RaftRequestHeader) GetReplicaRead() bool {
	if m != nil {
		return m.ReplicaRead
	}
	return false
}

func (m *RaftRequestHeader) GetAppliedIndex() uint64 {
	if m != nil {
		return m.AppliedIndex
	}
	return 0
}

func (m *RaftRequestHeader) GetFlags() uint64 {
	if m != nil {
		return m.Flags
	}
	return 0
}

func (m *RaftRequestHeader) GetFlagData() []byte {
	if m != nil {
		return m.FlagData
	}
	return nil
}

func (m *RaftRequestHeader) GetPriority() kvrpcpb.CommandPri {
	if m != nil {
		return m.Priority
	}
	return kvrpcpb.CommandPri_Normal
}

func (m *RaftRequestHeader) GetResourceGroupName() string {
	if m != nil {
		return m.ResourceGroupName
	}
	return ""
}

type RaftResponseHeader struct {
	Error       *errorpb.Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Uuid        []byte         `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	CurrentTerm uint64         `protobuf:"varint,3,opt,name=current_term,json=currentTerm,proto3" json:"current_term,omitempty"`
}

func (m *RaftResponseHeader) Reset()         { *m = RaftResponseHeader{} }
func (m *RaftResponseHeader) String() string { return proto.CompactTextString(m) }
func (*RaftResponseHeader) ProtoMessage()    {}
func (*RaftResponseHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{56}
}
func (m *RaftResponseHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftResponseHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftResponseHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftResponseHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftResponseHeader.Merge(m, src)
}
func (m *RaftResponseHeader) XXX_Size() int {
	return m.Size()
}
func (m *RaftResponseHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftResponseHeader.DiscardUnknown(m)
}

var xxx_messageInfo_RaftResponseHeader proto.InternalMessageInfo

func (m *RaftResponseHeader) GetError() *errorpb.Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *RaftResponseHeader) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

func (m *RaftResponseHeader) GetCurrentTerm() uint64 {
	if m != nil {
		return m.CurrentTerm
	}
	return 0
}

type CustomRequest struct {
	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (m *CustomRequest) Reset()         { *m = CustomRequest{} }
func (m *CustomRequest) String() string { return proto.CompactTextString(m) }
func (*CustomRequest) ProtoMessage()    {}
func (*CustomRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{57}
}
func (m *CustomRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CustomRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CustomRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CustomRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomRequest.Merge(m, src)
}
func (m *CustomRequest) XXX_Size() int {
	return m.Size()
}
func (m *CustomRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CustomRequest proto.InternalMessageInfo

func (m *CustomRequest) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type RaftCmdRequest struct {
	Header *RaftRequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// We can't enclose normal requests and administrator request
	// at same time.
	Requests      []*Request     `protobuf:"bytes,2,rep,name=requests,proto3" json:"requests,omitempty"`
	AdminRequest  *AdminRequest  `protobuf:"bytes,3,opt,name=admin_request,json=adminRequest,proto3" json:"admin_request,omitempty"`
	StatusRequest *StatusRequest `protobuf:"bytes,4,opt,name=status_request,json=statusRequest,proto3" json:"status_request,omitempty"`
	CustomRequest *CustomRequest `protobuf:"bytes,5,opt,name=custom_request,json=customRequest,proto3" json:"custom_request,omitempty"`
}

func (m *RaftCmdRequest) Reset()         { *m = RaftCmdRequest{} }
func (m *RaftCmdRequest) String() string { return proto.CompactTextString(m) }
func (*RaftCmdRequest) ProtoMessage()    {}
func (*RaftCmdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{58}
}
func (m *RaftCmdRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftCmdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftCmdRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftCmdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftCmdRequest.Merge(m, src)
}
func (m *RaftCmdRequest) XXX_Size() int {
	return m.Size()
}
func (m *RaftCmdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftCmdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RaftCmdRequest proto.InternalMessageInfo

func (m *RaftCmdRequest) GetHeader() *RaftRequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *RaftCmdRequest) GetRequests() []*Request {
	if m != nil {
		return m.Requests
	}
	return nil
}

func (m *RaftCmdRequest) GetAdminRequest() *AdminRequest {
	if m != nil {
		return m.AdminRequest
	}
	return nil
}

func (m *RaftCmdRequest) GetStatusRequest() *StatusRequest {
	if m != nil {
		return m.StatusRequest
	}
	return nil
}

func (m *RaftCmdRequest) GetCustomRequest() *CustomRequest {
	if m != nil {
		return m.CustomRequest
	}
	return nil
}

type RaftCmdResponse struct {
	Header         *RaftResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Responses      []*Response         `protobuf:"bytes,2,rep,name=responses,proto3" json:"responses,omitempty"`
	AdminResponse  *AdminResponse      `protobuf:"bytes,3,opt,name=admin_response,json=adminResponse,proto3" json:"admin_response,omitempty"`
	StatusResponse *StatusResponse     `protobuf:"bytes,4,opt,name=status_response,json=statusResponse,proto3" json:"status_response,omitempty"`
}

func (m *RaftCmdResponse) Reset()         { *m = RaftCmdResponse{} }
func (m *RaftCmdResponse) String() string { return proto.CompactTextString(m) }
func (*RaftCmdResponse) ProtoMessage()    {}
func (*RaftCmdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_661741b5e7485333, []int{59}
}
func (m *RaftCmdResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftCmdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftCmdResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftCmdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftCmdResponse.Merge(m, src)
}
func (m *RaftCmdResponse) XXX_Size() int {
	return m.Size()
}
func (m *RaftCmdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftCmdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RaftCmdResponse proto.InternalMessageInfo

func (m *RaftCmdResponse) GetHeader() *RaftResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *RaftCmdResponse) GetResponses() []*Response {
	if m != nil {
		return m.Responses
	}
	return nil
}

func (m *RaftCmdResponse) GetAdminResponse() *AdminResponse {
	if m != nil {
		return m.AdminResponse
	}
	return nil
}

func (m *RaftCmdResponse) GetStatusResponse() *StatusResponse {
	if m != nil {
		return m.StatusResponse
	}
	return nil
}

func init() {
	proto.RegisterEnum("raft_cmdpb.CmdType", CmdType_name, CmdType_value)
	proto.RegisterEnum("raft_cmdpb.AdminCmdType", AdminCmdType_name, AdminCmdType_value)
	proto.RegisterEnum("raft_cmdpb.StatusCmdType", StatusCmdType_name, StatusCmdType_value)
	proto.RegisterType((*GetRequest)(nil), "raft_cmdpb.GetRequest")
	proto.RegisterType((*GetResponse)(nil), "raft_cmdpb.GetResponse")
	proto.RegisterType((*PutRequest)(nil), "raft_cmdpb.PutRequest")
	proto.RegisterType((*PutResponse)(nil), "raft_cmdpb.PutResponse")
	proto.RegisterType((*DeleteRequest)(nil), "raft_cmdpb.DeleteRequest")
	proto.RegisterType((*DeleteResponse)(nil), "raft_cmdpb.DeleteResponse")
	proto.RegisterType((*DeleteRangeRequest)(nil), "raft_cmdpb.DeleteRangeRequest")
	proto.RegisterType((*DeleteRangeResponse)(nil), "raft_cmdpb.DeleteRangeResponse")
	proto.RegisterType((*SnapRequest)(nil), "raft_cmdpb.SnapRequest")
	proto.RegisterType((*SnapResponse)(nil), "raft_cmdpb.SnapResponse")
	proto.RegisterType((*PrewriteRequest)(nil), "raft_cmdpb.PrewriteRequest")
	proto.RegisterType((*PrewriteResponse)(nil), "raft_cmdpb.PrewriteResponse")
	proto.RegisterType((*IngestSSTRequest)(nil), "raft_cmdpb.IngestSSTRequest")
	proto.RegisterType((*IngestSSTResponse)(nil), "raft_cmdpb.IngestSSTResponse")
	proto.RegisterType((*ReadIndexRequest)(nil), "raft_cmdpb.ReadIndexRequest")
	proto.RegisterType((*ReadIndexResponse)(nil), "raft_cmdpb.ReadIndexResponse")
	proto.RegisterType((*Request)(nil), "raft_cmdpb.Request")
	proto.RegisterType((*Response)(nil), "raft_cmdpb.Response")
	proto.RegisterType((*ChangePeerRequest)(nil), "raft_cmdpb.ChangePeerRequest")
	proto.RegisterType((*ChangePeerResponse)(nil), "raft_cmdpb.ChangePeerResponse")
	proto.RegisterType((*ChangePeerV2Request)(nil), "raft_cmdpb.ChangePeerV2Request")
	proto.RegisterType((*ChangePeerV2Response)(nil), "raft_cmdpb.ChangePeerV2Response")
	proto.RegisterType((*SplitRequest)(nil), "raft_cmdpb.SplitRequest")
	proto.RegisterType((*SplitResponse)(nil), "raft_cmdpb.SplitResponse")
	proto.RegisterType((*BatchSplitRequest)(nil), "raft_cmdpb.BatchSplitRequest")
	proto.RegisterType((*BatchSplitResponse)(nil), "raft_cmdpb.BatchSplitResponse")
	proto.RegisterType((*CompactLogRequest)(nil), "raft_cmdpb.CompactLogRequest")
	proto.RegisterType((*CompactLogResponse)(nil), "raft_cmdpb.CompactLogResponse")
	proto.RegisterType((*TransferLeaderRequest)(nil), "raft_cmdpb.TransferLeaderRequest")
	proto.RegisterType((*TransferLeaderResponse)(nil), "raft_cmdpb.TransferLeaderResponse")
	proto.RegisterType((*ComputeHashRequest)(nil), "raft_cmdpb.ComputeHashRequest")
	proto.RegisterType((*VerifyHashRequest)(nil), "raft_cmdpb.VerifyHashRequest")
	proto.RegisterType((*VerifyHashResponse)(nil), "raft_cmdpb.VerifyHashResponse")
	proto.RegisterType((*PrepareMergeRequest)(nil), "raft_cmdpb.PrepareMergeRequest")
	proto.RegisterType((*PrepareMergeResponse)(nil), "raft_cmdpb.PrepareMergeResponse")
	proto.RegisterType((*PrepareFlashbackRequest)(nil), "raft_cmdpb.PrepareFlashbackRequest")
	proto.RegisterType((*PrepareFlashbackResponse)(nil), "raft_cmdpb.PrepareFlashbackResponse")
	proto.RegisterType((*FinishFlashbackRequest)(nil), "raft_cmdpb.FinishFlashbackRequest")
	proto.RegisterType((*FinishFlashbackResponse)(nil), "raft_cmdpb.FinishFlashbackResponse")
	proto.RegisterType((*CommitMergeRequest)(nil), "raft_cmdpb.CommitMergeRequest")
	proto.RegisterType((*CommitMergeResponse)(nil), "raft_cmdpb.CommitMergeResponse")
	proto.RegisterType((*RollbackMergeRequest)(nil), "raft_cmdpb.RollbackMergeRequest")
	proto.RegisterType((*RollbackMergeResponse)(nil), "raft_cmdpb.RollbackMergeResponse")
	proto.RegisterType((*SwitchWitnessRequest)(nil), "raft_cmdpb.SwitchWitnessRequest")
	proto.RegisterType((*BatchSwitchWitnessRequest)(nil), "raft_cmdpb.BatchSwitchWitnessRequest")
	proto.RegisterType((*BatchSwitchWitnessResponse)(nil), "raft_cmdpb.BatchSwitchWitnessResponse")
	proto.RegisterType((*UpdateGcPeerRequest)(nil), "raft_cmdpb.UpdateGcPeerRequest")
	proto.RegisterType((*AdminRequest)(nil), "raft_cmdpb.AdminRequest")
	proto.RegisterType((*AdminResponse)(nil), "raft_cmdpb.AdminResponse")
	proto.RegisterType((*RegionLeaderRequest)(nil), "raft_cmdpb.RegionLeaderRequest")
	proto.RegisterType((*RegionLeaderResponse)(nil), "raft_cmdpb.RegionLeaderResponse")
	proto.RegisterType((*RegionDetailRequest)(nil), "raft_cmdpb.RegionDetailRequest")
	proto.RegisterType((*RegionDetailResponse)(nil), "raft_cmdpb.RegionDetailResponse")
	proto.RegisterType((*StatusRequest)(nil), "raft_cmdpb.StatusRequest")
	proto.RegisterType((*StatusResponse)(nil), "raft_cmdpb.StatusResponse")
	proto.RegisterType((*RaftRequestHeader)(nil), "raft_cmdpb.RaftRequestHeader")
	proto.RegisterType((*RaftResponseHeader)(nil), "raft_cmdpb.RaftResponseHeader")
	proto.RegisterType((*CustomRequest)(nil), "raft_cmdpb.CustomRequest")
	proto.RegisterType((*RaftCmdRequest)(nil), "raft_cmdpb.RaftCmdRequest")
	proto.RegisterType((*RaftCmdResponse)(nil), "raft_cmdpb.RaftCmdResponse")
}

func init() { proto.RegisterFile("raft_cmdpb.proto", fileDescriptor_661741b5e7485333) }

var fileDescriptor_661741b5e7485333 = []byte{
	// 2864 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x5a, 0x5b, 0x73, 0xdb, 0xc6,
	0xf5, 0x17, 0x2f, 0xe2, 0xe5, 0xf0, 0x22, 0x68, 0x49, 0x4b, 0xb4, 0x9d, 0xd0, 0x32, 0xec, 0xf8,
	0xaf, 0xe4, 0xdf, 0x51, 0x1a, 0x25, 0x71, 0x32, 0x13, 0x37, 0x17, 0x4b, 0xb2, 0xe3, 0xf8, 0x52,
	0x65, 0xa5, 0x26, 0xd3, 0xce, 0x64, 0x30, 0x08, 0xb0, 0x24, 0x31, 0x22, 0x01, 0x64, 0x01, 0xca,
	0x61, 0xf2, 0xda, 0x0f, 0xd0, 0xbe, 0xf5, 0x0b, 0x74, 0xa6, 0xaf, 0xfd, 0x0a, 0x7d, 0x6a, 0xfb,
	0x94, 0x74, 0xf2, 0x90, 0xc7, 0x4e, 0xfc, 0x96, 0x4f, 0xd1, 0xd9, 0x1b, 0xb0, 0x00, 0x41, 0x5f,
	0xf2, 0x24, 0xec, 0xd9, 0x73, 0x7e, 0x7b, 0xf6, 0x9c, 0xdd, 0xdf, 0x9e, 0x5d, 0x0a, 0x0c, 0x6a,
	0x8f, 0x62, 0xcb, 0x99, 0xb9, 0xe1, 0x97, 0x7b, 0x21, 0x0d, 0xe2, 0x00, 0x41, 0x2a, 0xb9, 0xd4,
	0x9e, 0x91, 0xd8, 0x56, 0x3d, 0x97, 0x3a, 0x84, 0xd2, 0x80, 0xea, 0x4d, 0x7b, 0x14, 0xa7, 0xcd,
	0xb3, 0x73, 0x1a, 0x3a, 0x49, 0x13, 0x79, 0xb3, 0x30, 0xa0, 0xb1, 0x15, 0x45, 0xa9, 0x4a, 0x8f,
	0x43, 0x47, 0x84, 0x9e, 0x93, 0x14, 0xa6, 0x3f, 0x0e, 0xc6, 0x01, 0xff, 0x7c, 0x9d, 0x7d, 0x49,
	0xe9, 0x06, 0x9d, 0x47, 0x31, 0xff, 0x14, 0x02, 0x73, 0x0f, 0xe0, 0x2e, 0x89, 0x31, 0xf9, 0x6a,
	0x4e, 0xa2, 0x18, 0x75, 0xa1, 0xec, 0x8c, 0x06, 0xa5, 0x9d, 0xd2, 0x6e, 0x13, 0x97, 0x9d, 0x11,
	0x32, 0xa0, 0x72, 0x46, 0x16, 0x83, 0xf2, 0x4e, 0x69, 0xb7, 0x8d, 0xd9, 0xa7, 0x79, 0x0d, 0x5a,
	0x5c, 0x3f, 0x0a, 0x03, 0x3f, 0x22, 0xa8, 0x0f, 0xeb, 0xe7, 0xf6, 0x74, 0x4e, 0xb8, 0x4d, 0x1b,
	0x8b, 0x86, 0x79, 0x08, 0x70, 0x3c, 0x7f, 0x7e, 0xd0, 0x14, 0xa5, 0xa2, 0xa3, 0x74, 0xa0, 0xc5,
	0x51, 0xc4, 0x50, 0xe6, 0x1b, 0xd0, 0x39, 0x24, 0x53, 0x12, 0x93, 0xe7, 0x77, 0xd6, 0x80, 0xae,
	0x32, 0x91, 0x20, 0xdf, 0x02, 0x92, 0x12, 0xdb, 0x1f, 0xaf, 0x44, 0xba, 0x0c, 0xcd, 0x28, 0xb6,
	0x69, 0x6c, 0xa5, 0x78, 0x0d, 0x2e, 0xb8, 0x4f, 0x16, 0x68, 0x1b, 0xea, 0xc4, 0x77, 0x79, 0x97,
	0x70, 0xb7, 0x46, 0x7c, 0x97, 0x75, 0x5c, 0x81, 0x96, 0x1f, 0xc4, 0xde, 0x68, 0x61, 0x05, 0xfe,
	0x74, 0x31, 0xa8, 0xee, 0x94, 0x76, 0x1b, 0x18, 0x84, 0xe8, 0xb7, 0xfe, 0x74, 0x61, 0x5e, 0x80,
	0x5e, 0x66, 0x70, 0xe9, 0x53, 0x07, 0x5a, 0x27, 0xbe, 0x1d, 0x4a, 0x67, 0xcc, 0x9b, 0xd0, 0x16,
	0x4d, 0x19, 0xe2, 0x1b, 0x50, 0xa3, 0x64, 0xec, 0x05, 0x3e, 0x77, 0xb0, 0xb5, 0xdf, 0xdd, 0x93,
	0xab, 0x07, 0x73, 0x29, 0x96, 0xbd, 0xe6, 0x43, 0xd8, 0x38, 0xa6, 0xe4, 0x31, 0xf5, 0xd2, 0x08,
	0xc9, 0x88, 0x94, 0x0a, 0x22, 0x5d, 0xd6, 0x22, 0x8d, 0x10, 0x54, 0xa7, 0x81, 0x73, 0x26, 0xe7,
	0xc3, 0xbf, 0x4d, 0x04, 0x46, 0x0a, 0x27, 0x3d, 0x7d, 0x0f, 0x8c, 0x7b, 0xfe, 0x98, 0x44, 0xf1,
	0xc9, 0xc9, 0xa9, 0x1a, 0xe3, 0xff, 0xa0, 0x12, 0x45, 0xb1, 0xf4, 0xed, 0xc2, 0x5e, 0x66, 0x79,
	0x9e, 0x9c, 0x9c, 0x3e, 0x24, 0xb1, 0x8d, 0x99, 0x86, 0xd9, 0x83, 0x4d, 0xcd, 0x58, 0x22, 0x5a,
	0x60, 0x60, 0x62, 0xbb, 0xf7, 0x7c, 0x97, 0x7c, 0xad, 0x10, 0x2f, 0x82, 0x08, 0xb6, 0x15, 0x47,
	0x1c, 0xb6, 0x8a, 0xeb, 0xbc, 0x7d, 0x1a, 0xa1, 0x5f, 0x03, 0x9c, 0x91, 0x85, 0x45, 0x59, 0xfc,
	0xa2, 0x41, 0x79, 0xa7, 0xb2, 0xdb, 0xda, 0xdf, 0xdc, 0x53, 0x3b, 0xe4, 0x3e, 0x59, 0x88, 0xc8,
	0x36, 0xcf, 0xe4, 0x57, 0x64, 0x7e, 0x01, 0x9b, 0xda, 0x00, 0x32, 0xa4, 0x2f, 0x03, 0x50, 0x62,
	0xbb, 0x96, 0xc7, 0xa4, 0x72, 0x8c, 0x26, 0x55, 0x6a, 0xe8, 0x55, 0xa8, 0xb1, 0x10, 0x10, 0x97,
	0x47, 0x49, 0x1f, 0xe1, 0x41, 0xe0, 0x9c, 0xdd, 0xf3, 0x47, 0x01, 0x96, 0x0a, 0xe6, 0x93, 0x0a,
	0xd4, 0x95, 0xdf, 0x7b, 0xd0, 0x70, 0x66, 0xae, 0x15, 0x2f, 0x42, 0xb1, 0x1d, 0xba, 0xfb, 0xbd,
	0x3d, 0x8d, 0x06, 0x0e, 0x66, 0xee, 0xe9, 0x22, 0x24, 0xb8, 0xee, 0x88, 0x0f, 0xb4, 0x0b, 0x95,
	0x31, 0x89, 0xe5, 0x18, 0x5b, 0xba, 0x6a, 0xba, 0x23, 0x31, 0x53, 0x61, 0x9a, 0xe1, 0x3c, 0xe6,
	0x2b, 0x2a, 0xa7, 0x99, 0x6e, 0x33, 0xcc, 0x54, 0xd0, 0x1b, 0x50, 0x73, 0xf9, 0x12, 0x1b, 0xac,
	0x73, 0xe5, 0x8b, 0xba, 0x72, 0x66, 0xfb, 0x60, 0xa9, 0x88, 0xfe, 0x1f, 0xaa, 0x91, 0x6f, 0x87,
	0x83, 0x1a, 0x37, 0xd8, 0xd6, 0x0d, 0xb4, 0x65, 0x89, 0xb9, 0x12, 0x7a, 0x07, 0x1a, 0xa1, 0x5c,
	0x15, 0x83, 0x3a, 0x37, 0xb8, 0x9c, 0x71, 0x27, 0xbb, 0x00, 0x71, 0xa2, 0x8c, 0x3e, 0x82, 0xb6,
	0x18, 0x4f, 0x24, 0x6f, 0xd0, 0xe0, 0xc6, 0xc3, 0x02, 0xf7, 0xb4, 0x8d, 0x89, 0x5b, 0x6e, 0x2a,
	0x43, 0xef, 0x01, 0x78, 0x7c, 0x01, 0xb1, 0xd5, 0x35, 0x68, 0x72, 0x80, 0x97, 0x74, 0x80, 0xfc,
	0xda, 0xc4, 0x4d, 0xa1, 0x7f, 0x12, 0xc5, 0xcc, 0x58, 0x4b, 0x39, 0x2c, 0x1b, 0xe7, 0x97, 0xa1,
	0xb6, 0x20, 0xcc, 0x9f, 0x2b, 0xd0, 0x48, 0x16, 0xcf, 0x8b, 0xa6, 0xf9, 0x55, 0x3d, 0xcd, 0xdb,
	0x4b, 0x69, 0x16, 0xa8, 0x22, 0xcf, 0xaf, 0xea, 0x79, 0xde, 0x5e, 0xca, 0xb3, 0x52, 0x65, 0x89,
	0xde, 0xcf, 0x25, 0xfa, 0x52, 0x51, 0xa2, 0xa5, 0x81, 0xca, 0xf4, 0xaf, 0x32, 0x99, 0x1e, 0x2c,
	0x67, 0x5a, 0xea, 0x8b, 0x54, 0xbf, 0xbb, 0x94, 0xea, 0x97, 0x8a, 0x53, 0x2d, 0xad, 0xd2, 0x5c,
	0x7f, 0x08, 0x2c, 0x6f, 0xb9, 0x54, 0x5f, 0x59, 0x99, 0x6a, 0x69, 0x0f, 0xdc, 0x46, 0xa4, 0xfa,
	0x56, 0x41, 0xaa, 0x5f, 0x5e, 0x91, 0x6a, 0x69, 0xae, 0xe5, 0xfa, 0x56, 0x41, 0xae, 0x5f, 0x5e,
	0x91, 0x6b, 0x65, 0x9d, 0x26, 0x3b, 0x80, 0xcd, 0x83, 0x09, 0xf3, 0xe2, 0x98, 0x10, 0xaa, 0xf6,
	0xf6, 0xbb, 0xd0, 0x72, 0xb8, 0x50, 0xcf, 0xfb, 0xf6, 0x9e, 0x3a, 0xaa, 0x0f, 0x02, 0x7f, 0x24,
	0x8c, 0x78, 0xee, 0xc1, 0x49, 0xbe, 0xd1, 0x0e, 0x54, 0x43, 0x42, 0xa8, 0xcc, 0x7f, 0x5b, 0x91,
	0x37, 0x07, 0xe7, 0x3d, 0xe6, 0x2d, 0x40, 0xfa, 0x80, 0x2f, 0x48, 0xfb, 0x8f, 0xa0, 0x97, 0x5a,
	0x7f, 0xb6, 0xaf, 0x1c, 0x7e, 0x07, 0xea, 0xc2, 0x09, 0xc6, 0xa1, 0x95, 0x7c, 0x00, 0x96, 0x26,
	0x88, 0x95, 0xb6, 0xf9, 0x3e, 0xf4, 0xb3, 0x78, 0x2f, 0xe8, 0xcf, 0xf7, 0x25, 0x68, 0x9f, 0x84,
	0x53, 0x2f, 0x39, 0xfe, 0xd9, 0x61, 0xca, 0xda, 0x56, 0x7a, 0x14, 0x35, 0xb8, 0x80, 0x9d, 0x99,
	0x26, 0x74, 0x7c, 0xf2, 0xd8, 0x12, 0xb6, 0x96, 0x27, 0x18, 0xb7, 0x8a, 0x5b, 0x3e, 0x79, 0x2c,
	0x70, 0xef, 0xb9, 0x68, 0x07, 0xda, 0x4c, 0x87, 0xc5, 0xca, 0xf2, 0xdc, 0x68, 0x50, 0xd9, 0xa9,
	0xec, 0x56, 0x31, 0xf8, 0xe4, 0x31, 0x73, 0xf1, 0x9e, 0x1b, 0xa1, 0x57, 0xa0, 0x4d, 0xbd, 0xf1,
	0x24, 0xb6, 0x5c, 0x42, 0xbd, 0x73, 0x22, 0x8e, 0xde, 0xdb, 0xe5, 0x41, 0x09, 0xb7, 0xb8, 0xfc,
	0x90, 0x8b, 0xd1, 0x3b, 0x30, 0x88, 0x26, 0x36, 0x25, 0x56, 0x14, 0xcc, 0xa9, 0x43, 0xd4, 0xa8,
	0x91, 0xf7, 0x8d, 0xd8, 0x45, 0x0d, 0x7c, 0x81, 0xf7, 0x9f, 0xf0, 0x6e, 0x31, 0xfe, 0x89, 0xf7,
	0x0d, 0x31, 0x7f, 0x0f, 0x1d, 0x39, 0x25, 0x19, 0x0c, 0x13, 0xaa, 0x53, 0x32, 0x8a, 0x57, 0x84,
	0x82, 0xf7, 0xa1, 0xeb, 0xb0, 0xce, 0x07, 0x97, 0x99, 0xcf, 0x2b, 0x89, 0x4e, 0xf3, 0xaf, 0x25,
	0xd8, 0xbc, 0x6d, 0xc7, 0xce, 0x24, 0x13, 0xb3, 0xb7, 0xa0, 0x41, 0xc5, 0xa7, 0x4a, 0x5f, 0x76,
	0xb7, 0x6a, 0xba, 0x38, 0xd1, 0x44, 0x57, 0x73, 0x61, 0x28, 0xf3, 0x39, 0x3d, 0x77, 0x08, 0x2a,
	0x4f, 0x0b, 0xc1, 0xfb, 0x80, 0x74, 0x37, 0x65, 0x1c, 0x76, 0xa1, 0x2e, 0x10, 0x94, 0x9b, 0xf9,
	0x59, 0xaa, 0x6e, 0xf3, 0xcf, 0x25, 0xd8, 0x3c, 0x08, 0x66, 0xa1, 0xed, 0xc4, 0x0f, 0x82, 0xb1,
	0x9a, 0xe7, 0x35, 0xe8, 0x38, 0x42, 0x98, 0x39, 0x8b, 0xdb, 0x52, 0x28, 0x8e, 0xe3, 0xab, 0xa0,
	0xda, 0x56, 0x4c, 0xe8, 0x4c, 0x2d, 0x11, 0x29, 0x3b, 0x25, 0x74, 0x86, 0xde, 0x82, 0xad, 0xf3,
	0x20, 0x26, 0xd4, 0xa2, 0x24, 0x9c, 0x7a, 0x8e, 0x1d, 0x13, 0xb5, 0xfb, 0x2b, 0x5c, 0xb9, 0xcf,
	0x7b, 0x71, 0xd2, 0x29, 0x76, 0x7a, 0x1f, 0x90, 0xee, 0x92, 0x2c, 0x49, 0xbe, 0x80, 0x0b, 0xa7,
	0xd4, 0xf6, 0xa3, 0x11, 0xa1, 0x0f, 0x88, 0xed, 0xa6, 0x1c, 0xa0, 0x76, 0x72, 0x69, 0xd5, 0x4e,
	0x46, 0x26, 0xac, 0xb3, 0xbf, 0xaa, 0x32, 0xc9, 0xaa, 0x88, 0x2e, 0x73, 0x00, 0x5b, 0x79, 0x78,
	0x39, 0xf0, 0x9e, 0x70, 0x67, 0x1e, 0x93, 0x8f, 0xed, 0x68, 0xa2, 0x46, 0x1d, 0x40, 0xdd, 0x09,
	0xfc, 0x98, 0x7c, 0x1d, 0xcb, 0xcd, 0xa3, 0x9a, 0xe6, 0xe7, 0xb0, 0xf9, 0x19, 0xa1, 0xde, 0x68,
	0xa1, 0xab, 0xf7, 0x61, 0x5d, 0x8f, 0xa4, 0x68, 0xb0, 0x02, 0x6f, 0x62, 0x47, 0x13, 0x59, 0xf5,
	0xf1, 0x6f, 0x1d, 0xb8, 0x92, 0x05, 0xee, 0x03, 0xd2, 0x81, 0xa5, 0x7b, 0x7f, 0x80, 0xde, 0x31,
	0x25, 0xa1, 0x4d, 0xc9, 0x43, 0x42, 0xd3, 0xda, 0xf9, 0x32, 0x34, 0x67, 0x9e, 0x9f, 0x49, 0x5f,
	0x63, 0xe6, 0xf9, 0x22, 0x75, 0x37, 0xa0, 0x16, 0xdb, 0x34, 0x3d, 0xfe, 0x96, 0x48, 0x43, 0xf4,
	0x9a, 0x5b, 0xd0, 0xcf, 0x62, 0xcb, 0x31, 0xdf, 0x82, 0x6d, 0x29, 0xbf, 0x33, 0xb5, 0xa3, 0xc9,
	0x97, 0xb6, 0x73, 0xf6, 0xec, 0x2a, 0xd1, 0xbc, 0x04, 0x83, 0x65, 0x2b, 0x89, 0x38, 0x80, 0xad,
	0x3b, 0x9e, 0xef, 0x45, 0x93, 0x3c, 0xa0, 0x79, 0x11, 0xb6, 0x97, 0x7a, 0xa4, 0xd1, 0xcf, 0x25,
	0x9e, 0x9a, 0x99, 0x17, 0x67, 0xa6, 0x7e, 0x03, 0x6a, 0x62, 0x1b, 0xad, 0xa2, 0x44, 0xd1, 0x8b,
	0xb6, 0xa0, 0xe6, 0x70, 0x6b, 0xb9, 0x74, 0x65, 0x8b, 0xed, 0x1e, 0xe2, 0xc7, 0xd4, 0x23, 0x82,
	0xd3, 0x18, 0x80, 0x3a, 0x50, 0x8e, 0xfc, 0x98, 0x2e, 0xb0, 0xea, 0x46, 0xb7, 0xa1, 0x2d, 0x37,
	0x6c, 0x14, 0xdb, 0x31, 0x91, 0x15, 0x82, 0x3c, 0x52, 0x93, 0x8b, 0x9f, 0x18, 0xf6, 0x41, 0xe0,
	0xd8, 0xd3, 0x13, 0xa6, 0x86, 0x5b, 0xc2, 0x88, 0x37, 0xd8, 0xf5, 0x44, 0x62, 0x30, 0x2f, 0x07,
	0x2e, 0xcf, 0x39, 0x08, 0x11, 0xab, 0xd2, 0x3f, 0x59, 0x6f, 0x10, 0xe3, 0x5f, 0x25, 0x76, 0x4b,
	0xc9, 0xcc, 0x35, 0x59, 0x9d, 0x7d, 0x1c, 0x4c, 0xa7, 0x2c, 0x2e, 0x99, 0x20, 0xa4, 0x93, 0x2b,
	0xe9, 0x93, 0x33, 0xb7, 0xe1, 0x42, 0x4e, 0x5f, 0x02, 0x3d, 0x82, 0xfe, 0xc9, 0x63, 0x2f, 0x76,
	0x26, 0x9f, 0x7b, 0xb1, 0x4f, 0xa2, 0x48, 0x01, 0x6d, 0x43, 0x5d, 0x52, 0xbc, 0x42, 0x0a, 0x39,
	0xbd, 0xb3, 0x6a, 0xdd, 0x8b, 0xac, 0xc7, 0x42, 0x5b, 0x92, 0x5a, 0xd3, 0x8b, 0xa4, 0xb9, 0x39,
	0x81, 0x8b, 0x82, 0x99, 0x8a, 0x40, 0xef, 0x83, 0x11, 0x71, 0xb9, 0xb2, 0x4f, 0xce, 0xc3, 0x9d,
	0x0c, 0xa1, 0x16, 0xd8, 0xe2, 0x8d, 0x48, 0x97, 0x92, 0xc8, 0x7c, 0x09, 0x2e, 0x15, 0x8d, 0x94,
	0x04, 0xa8, 0xf7, 0xbb, 0xd0, 0xb5, 0x63, 0x72, 0xd7, 0xd1, 0x2b, 0x87, 0xcc, 0xb4, 0x2a, 0xe9,
	0xb4, 0xcc, 0xff, 0x34, 0xa0, 0xfd, 0x91, 0x3b, 0xf3, 0x7c, 0xa5, 0xf9, 0xe6, 0x52, 0x61, 0x99,
	0x21, 0x7d, 0xae, 0xbb, 0x54, 0x5d, 0xbe, 0x9f, 0x14, 0x26, 0x5a, 0x95, 0xf1, 0x8c, 0xb3, 0x5e,
	0x96, 0x27, 0x4c, 0x84, 0xf6, 0x61, 0x9d, 0x1f, 0xc6, 0x9c, 0x03, 0x9e, 0x72, 0xcc, 0xf0, 0xd3,
	0x54, 0xa8, 0xf2, 0x31, 0x25, 0x21, 0x4f, 0x83, 0xb1, 0x5c, 0x8c, 0xd9, 0x31, 0xf3, 0x4c, 0x8f,
	0xc1, 0x49, 0x44, 0xe8, 0x13, 0xd8, 0x88, 0x25, 0x05, 0x5a, 0x53, 0xce, 0x81, 0xb2, 0x88, 0xbd,
	0xaa, 0x63, 0x14, 0x92, 0x30, 0xee, 0xc6, 0x19, 0x31, 0xf3, 0xe5, 0x9c, 0x73, 0x95, 0xc5, 0x09,
	0xae, 0xb6, 0xec, 0xcb, 0x12, 0x47, 0x62, 0x38, 0x4f, 0x44, 0xe8, 0x10, 0x3a, 0xa1, 0xe0, 0x0a,
	0x6b, 0xc6, 0x96, 0xa9, 0x2c, 0x75, 0xaf, 0xe4, 0x4a, 0xdd, 0x3c, 0xed, 0xe1, 0x76, 0xa8, 0x09,
	0xd9, 0xed, 0x46, 0x2c, 0x7b, 0x09, 0x52, 0x70, 0xbb, 0x59, 0xe6, 0x0f, 0x7e, 0x84, 0x29, 0x19,
	0xba, 0x0b, 0x5d, 0x2a, 0xf7, 0x8b, 0x04, 0x11, 0x65, 0x6f, 0x66, 0x9d, 0x16, 0xed, 0x40, 0xdc,
	0xa1, 0xba, 0x14, 0xbd, 0x0d, 0x35, 0x9e, 0xa6, 0xa8, 0xa8, 0xf2, 0x5d, 0x2a, 0x35, 0xb0, 0x54,
	0x46, 0x47, 0xd0, 0xd5, 0x16, 0x92, 0x75, 0xbe, 0x3f, 0x68, 0x2d, 0x47, 0xa2, 0xa0, 0xd2, 0xc4,
	0x6d, 0x47, 0x13, 0xca, 0x48, 0xb0, 0x43, 0x4c, 0x24, 0xa4, 0x5d, 0x18, 0x89, 0xdc, 0x21, 0x27,
	0x0e, 0x73, 0x29, 0x43, 0xc7, 0xb0, 0xa9, 0x52, 0x32, 0x52, 0x54, 0x3c, 0xe8, 0x70, 0x9c, 0x6b,
	0x05, 0x69, 0xc9, 0x13, 0x39, 0x36, 0xc2, 0x5c, 0x07, 0x7a, 0x08, 0xc6, 0x88, 0x53, 0xbb, 0x06,
	0xd8, 0xe5, 0x80, 0xa6, 0x0e, 0x58, 0x7c, 0x30, 0xe0, 0x8d, 0x51, 0x56, 0x8e, 0x8e, 0x0b, 0x48,
	0x65, 0x83, 0xc3, 0xbd, 0xb2, 0x1c, 0xeb, 0xe7, 0x61, 0x16, 0x74, 0x17, 0x36, 0xe6, 0x9c, 0x3b,
	0xac, 0xb1, 0x63, 0x89, 0x12, 0xc2, 0x58, 0x8e, 0x7e, 0x01, 0xbd, 0xe0, 0xce, 0x5c, 0x13, 0x46,
	0xe6, 0x0f, 0x75, 0xe8, 0x48, 0x52, 0x91, 0x25, 0xda, 0x2f, 0x62, 0x95, 0x0f, 0x8a, 0x58, 0x65,
	0xb8, 0x8a, 0x55, 0xd4, 0x05, 0x4e, 0xa3, 0x95, 0x37, 0xb3, 0xb4, 0x72, 0xb1, 0x80, 0x56, 0x84,
	0x95, 0xce, 0x2b, 0x1f, 0x14, 0xf1, 0xca, 0x70, 0x15, 0xaf, 0x24, 0xa3, 0xa6, 0xc4, 0x72, 0x7f,
	0x15, 0xb1, 0x98, 0x4f, 0x23, 0x16, 0x09, 0x94, 0x67, 0x96, 0x0f, 0x8a, 0x98, 0x65, 0xb8, 0x8a,
	0x59, 0x94, 0x37, 0x1a, 0xb5, 0x1c, 0x15, 0x53, 0xcb, 0xce, 0x6a, 0x6a, 0x91, 0x20, 0x59, 0x6e,
	0xb9, 0x5d, 0xc8, 0x2d, 0x57, 0x56, 0x72, 0x8b, 0x04, 0xc9, 0x90, 0xcb, 0xc7, 0x2b, 0xc8, 0xe5,
	0xea, 0x53, 0xc8, 0x45, 0xe2, 0xe4, 0xd8, 0xe5, 0x66, 0x8e, 0x5d, 0x86, 0xab, 0xd8, 0x45, 0xbd,
	0x3d, 0x48, 0x7a, 0xb9, 0xb3, 0x82, 0x5e, 0x76, 0x56, 0xd3, 0x8b, 0x8a, 0x46, 0x86, 0x5f, 0x3e,
	0x2d, 0x22, 0x07, 0x41, 0x32, 0xd7, 0x9f, 0x4e, 0x0e, 0x12, 0x6e, 0x99, 0x1d, 0x1e, 0x15, 0xb0,
	0x43, 0x01, 0xdd, 0xac, 0x28, 0x0e, 0x97, 0xe9, 0xe1, 0xd3, 0x02, 0x7a, 0x10, 0x6c, 0x73, 0xe3,
	0x59, 0xf4, 0xa0, 0x20, 0xf3, 0x95, 0xc7, 0x05, 0xe8, 0xc9, 0xe2, 0x4e, 0x3f, 0x0c, 0xcd, 0x5b,
	0xd0, 0xcf, 0x8a, 0xe5, 0x9e, 0xbf, 0x0e, 0x35, 0xb9, 0xfc, 0x8b, 0xee, 0x2a, 0xb2, 0x2f, 0x05,
	0x3d, 0x24, 0xb1, 0xed, 0x4d, 0x15, 0xa8, 0xab, 0x40, 0x95, 0xf8, 0xc5, 0x1e, 0x00, 0xb4, 0xc1,
	0xcb, 0x4f, 0x19, 0xfc, 0xfb, 0x12, 0x74, 0x58, 0x5d, 0x3a, 0x8f, 0xb4, 0x3b, 0x6f, 0x8e, 0xa8,
	0xb2, 0xac, 0xc1, 0x95, 0x97, 0x98, 0xea, 0x10, 0x3a, 0xf2, 0x0e, 0x9b, 0x19, 0xf4, 0x4a, 0xf6,
	0xb9, 0x67, 0x29, 0x74, 0xb8, 0x4d, 0x35, 0xa1, 0x86, 0xe2, 0xf2, 0x49, 0x4b, 0xda, 0x2a, 0x40,
	0xc9, 0xc4, 0x4a, 0xa1, 0x08, 0xa1, 0xf9, 0x43, 0x09, 0xba, 0x6a, 0x4e, 0x32, 0x68, 0xbf, 0x6c,
	0x52, 0x47, 0xc5, 0x93, 0xda, 0x59, 0x3d, 0x29, 0xb5, 0x57, 0x32, 0xb3, 0x3a, 0x2a, 0x9e, 0xd5,
	0xce, 0xea, 0x59, 0x65, 0x61, 0xe4, 0xb4, 0xfe, 0x5d, 0x81, 0x4d, 0x6c, 0x8f, 0x54, 0xc5, 0xf0,
	0xb1, 0x00, 0xbf, 0x0c, 0xcd, 0xf4, 0xd5, 0x46, 0xde, 0xfb, 0x68, 0xfa, 0x64, 0xf3, 0x8c, 0x47,
	0x2f, 0x76, 0x1b, 0xe1, 0x6f, 0x74, 0x5f, 0xcd, 0x03, 0x3a, 0x9f, 0xc9, 0xb7, 0x07, 0xfe, 0x6c,
	0xf7, 0x29, 0x97, 0xb0, 0x2b, 0xeb, 0x7c, 0xee, 0xb9, 0xfc, 0x14, 0x68, 0x63, 0xfe, 0x8d, 0x6e,
	0x82, 0xf4, 0xcc, 0x22, 0x61, 0xe0, 0x4c, 0x24, 0xb9, 0xf7, 0xb2, 0x0b, 0xf1, 0x88, 0x75, 0xe1,
	0x16, 0x4d, 0x1b, 0x0c, 0x8b, 0xbf, 0x1c, 0xd4, 0xb8, 0x9b, 0xfc, 0x9b, 0xdf, 0x1f, 0x17, 0xbe,
	0xc3, 0x4f, 0x9a, 0x3a, 0x1f, 0xbd, 0xce, 0xda, 0xec, 0x18, 0xb9, 0xca, 0x86, 0xe1, 0x4f, 0x05,
	0x16, 0x73, 0x88, 0x33, 0x6e, 0x83, 0x21, 0x72, 0x19, 0x26, 0xb6, 0x8b, 0xae, 0x41, 0xc7, 0x0e,
	0xc3, 0xa9, 0x97, 0xbc, 0x33, 0x34, 0xc5, 0xc3, 0x85, 0x14, 0x8a, 0xdb, 0x6f, 0x1f, 0xd6, 0x47,
	0x53, 0x7b, 0x2c, 0xa8, 0xb2, 0x8a, 0x45, 0x83, 0x05, 0x8e, 0x7d, 0x58, 0xae, 0x1d, 0xdb, 0x9c,
	0x04, 0xdb, 0xb8, 0xc1, 0x04, 0x87, 0x76, 0x6c, 0xa3, 0xd7, 0xa1, 0x11, 0x52, 0x2f, 0xa0, 0x5e,
	0xbc, 0xe0, 0xac, 0xd6, 0xdd, 0xef, 0x25, 0x3f, 0x3e, 0x30, 0x96, 0xb7, 0x7d, 0xf7, 0x98, 0x7a,
	0x38, 0x51, 0x42, 0x7b, 0xd0, 0xa3, 0x44, 0xde, 0xeb, 0xc6, 0x34, 0x98, 0x87, 0x96, 0x6f, 0xcf,
	0x08, 0xe7, 0xaf, 0x26, 0xde, 0x54, 0x5d, 0x77, 0x59, 0xcf, 0x23, 0x7b, 0x46, 0xcc, 0xaf, 0x00,
	0x89, 0x5c, 0x8a, 0x54, 0xcb, 0x64, 0x5e, 0x87, 0x75, 0xfe, 0x23, 0x64, 0xb2, 0xb5, 0xd5, 0x4f,
	0x92, 0x47, 0xec, 0x2f, 0x16, 0x9d, 0x49, 0x4a, 0xca, 0x5a, 0x4a, 0xae, 0x42, 0xdb, 0x99, 0x53,
	0x4a, 0x7c, 0xf9, 0x38, 0x53, 0x91, 0x8f, 0x33, 0x42, 0x76, 0x4a, 0xe8, 0xcc, 0xbc, 0x06, 0x9d,
	0x83, 0x79, 0x14, 0x07, 0x33, 0xb5, 0xd3, 0x11, 0x54, 0xf9, 0xe4, 0xc5, 0x7b, 0x06, 0xff, 0x36,
	0xff, 0x51, 0x86, 0x2e, 0x73, 0xec, 0x60, 0xe6, 0x2a, 0xb5, 0xb7, 0xa1, 0x36, 0xd1, 0x59, 0x2c,
	0xfb, 0x84, 0x9b, 0x5f, 0x90, 0x58, 0x2a, 0xb3, 0x10, 0x26, 0x6f, 0x67, 0xe2, 0x1d, 0xa6, 0x97,
	0x5d, 0xf0, 0xf9, 0x67, 0xb3, 0xdf, 0x40, 0xc7, 0x66, 0x55, 0x90, 0x25, 0x25, 0x45, 0x57, 0x21,
	0xfd, 0xa2, 0x86, 0xdb, 0xb6, 0x7e, 0x6d, 0xfb, 0x10, 0xba, 0x11, 0xdf, 0xc6, 0x89, 0x7d, 0xb5,
	0xa0, 0xe6, 0xd1, 0xa9, 0x0e, 0x77, 0xa2, 0x0c, 0xf3, 0x7d, 0x08, 0x5d, 0x87, 0x07, 0x28, 0x41,
	0x28, 0xf8, 0xf1, 0x26, 0x13, 0x42, 0xdc, 0x71, 0xf4, 0xe6, 0x27, 0xeb, 0x0d, 0x97, 0x5d, 0xdd,
	0xff, 0x58, 0x86, 0x8d, 0x24, 0x88, 0x92, 0x81, 0x6e, 0xe6, 0xa2, 0x38, 0x5c, 0x8e, 0xa2, 0xbe,
	0x14, 0x92, 0x30, 0xee, 0xb3, 0xfd, 0x2d, 0x7a, 0x54, 0x1c, 0xfb, 0xd9, 0x38, 0xa6, 0x4f, 0xe7,
	0x52, 0x8d, 0x4d, 0x44, 0x45, 0x52, 0x88, 0x8a, 0xca, 0xbf, 0x4c, 0x79, 0x8a, 0x3b, 0x76, 0xa6,
	0x5a, 0x3d, 0x80, 0x8d, 0x24, 0x98, 0x12, 0xa2, 0xba, 0xfc, 0xfb, 0x46, 0x96, 0x64, 0x71, 0x37,
	0xca, 0xb4, 0x5f, 0xfb, 0x16, 0xea, 0x92, 0x52, 0x51, 0x0b, 0xea, 0xf7, 0xfc, 0x73, 0x7b, 0xea,
	0xb9, 0xc6, 0x1a, 0xaa, 0x43, 0xe5, 0x2e, 0x89, 0x8d, 0x12, 0xfb, 0x38, 0x9e, 0xc7, 0x46, 0x05,
	0x01, 0xd4, 0xc4, 0x4f, 0x11, 0x46, 0x15, 0x35, 0xa0, 0x7a, 0xe2, 0xdb, 0xa1, 0xb1, 0x8e, 0xda,
	0xd0, 0x50, 0xbf, 0x6e, 0x18, 0x35, 0xb4, 0x01, 0x2d, 0xed, 0xe7, 0x0a, 0xa3, 0x8e, 0x3a, 0xd0,
	0x4c, 0x7e, 0x7e, 0x30, 0x1a, 0xac, 0x99, 0xfc, 0x9e, 0x60, 0x34, 0x5f, 0xfb, 0x7b, 0x59, 0x5e,
	0xeb, 0x95, 0x0b, 0x06, 0xb4, 0xa5, 0x0b, 0x5c, 0x6c, 0xac, 0xa1, 0x2e, 0x40, 0x5a, 0xe9, 0x18,
	0x25, 0xd4, 0x81, 0x75, 0x5e, 0x34, 0x19, 0xe5, 0x4b, 0xe5, 0x46, 0x89, 0x77, 0x27, 0x35, 0xad,
	0x51, 0x41, 0x08, 0xba, 0xd9, 0x92, 0xd5, 0xa8, 0x32, 0xa7, 0xb4, 0x6b, 0x94, 0xb1, 0xce, 0x8c,
	0xd2, 0x72, 0xd4, 0xa8, 0xb1, 0x51, 0xf5, 0xda, 0xd2, 0xa8, 0x4b, 0x13, 0x55, 0x12, 0x1a, 0x0d,
	0xb4, 0x09, 0x9d, 0x4c, 0xc9, 0x67, 0x34, 0x19, 0x4a, 0x5a, 0xc3, 0x19, 0xc0, 0x50, 0xf4, 0x9a,
	0xcc, 0x68, 0xa1, 0x3e, 0xff, 0x59, 0x38, 0x53, 0x40, 0x19, 0x6d, 0xd4, 0x83, 0x8d, 0x5c, 0x79,
	0x64, 0x74, 0xd0, 0x96, 0x7a, 0x32, 0xd6, 0x8b, 0x19, 0xa3, 0xcb, 0x40, 0xf5, 0x9b, 0x8c, 0xb1,
	0xf1, 0xda, 0x1d, 0x55, 0x0b, 0xa8, 0x98, 0x6d, 0x42, 0x47, 0xc6, 0x4c, 0xc8, 0x8d, 0x35, 0x66,
	0xa5, 0x1f, 0x79, 0x46, 0x29, 0x95, 0x88, 0x73, 0xca, 0x28, 0xdf, 0xde, 0xff, 0xf1, 0x6f, 0x8d,
	0xd2, 0x3f, 0x7f, 0x1a, 0x96, 0xbe, 0xfb, 0x69, 0x58, 0xfa, 0xef, 0x4f, 0xc3, 0xd2, 0x9f, 0x9e,
	0x0c, 0xd7, 0xfe, 0xf2, 0x64, 0xb8, 0xf6, 0xdd, 0x93, 0xe1, 0xda, 0x8f, 0x4f, 0x86, 0x6b, 0x60,
	0x04, 0x74, 0xbc, 0x17, 0x7b, 0x67, 0xe7, 0x7b, 0x67, 0xe7, 0xfc, 0x1f, 0x20, 0xbe, 0xac, 0xf1,
	0x3f, 0x6f, 0xfe, 0x2f, 0x00, 0x00, 0xff, 0xff, 0x0a, 0xe4, 0xfa, 0x91, 0xb2, 0x21, 0x00, 0x00,
}

func (m *GetRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PutRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PutRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PutResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PutResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *DeleteRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeleteResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *DeleteRangeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteRangeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteRangeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.NotifyOnly {
		i--
		if m.NotifyOnly {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeleteRangeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteRangeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteRangeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *SnapRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *SnapResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PrewriteRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrewriteRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrewriteRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Lock) > 0 {
		i -= len(m.Lock)
		copy(dAtA[i:], m.Lock)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Lock)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PrewriteResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrewriteResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrewriteResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *IngestSSTRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IngestSSTRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IngestSSTRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Sst != nil {
		{
			size, err := m.Sst.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *IngestSSTResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IngestSSTResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IngestSSTResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ReadIndexRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadIndexRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadIndexRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.KeyRanges) > 0 {
		for iNdEx := len(m.KeyRanges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.KeyRanges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.StartTs != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ReadIndexResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadIndexResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadIndexResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Locked != nil {
		{
			size, err := m.Locked.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.ReadIndex != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.ReadIndex))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Request) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Request) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Request) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ReadIndex != nil {
		{
			size, err := m.ReadIndex.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.IngestSst != nil {
		{
			size, err := m.IngestSst.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.DeleteRange != nil {
		{
			size, err := m.DeleteRange.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.Prewrite != nil {
		{
			size, err := m.Prewrite.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.Snap != nil {
		{
			size, err := m.Snap.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Delete != nil {
		{
			size, err := m.Delete.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.Put != nil {
		{
			size, err := m.Put.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Get != nil {
		{
			size, err := m.Get.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.CmdType != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Response) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Response) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Response) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ReadIndex != nil {
		{
			size, err := m.ReadIndex.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.IngestSst != nil {
		{
			size, err := m.IngestSst.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.DelteRange != nil {
		{
			size, err := m.DelteRange.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.Prewrite != nil {
		{
			size, err := m.Prewrite.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.Snap != nil {
		{
			size, err := m.Snap.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Delete != nil {
		{
			size, err := m.Delete.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.Put != nil {
		{
			size, err := m.Put.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Get != nil {
		{
			size, err := m.Get.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.CmdType != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ChangePeerRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangePeerRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangePeerRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Peer != nil {
		{
			size, err := m.Peer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.ChangeType != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.ChangeType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ChangePeerResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangePeerResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangePeerResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ChangePeerV2Request) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangePeerV2Request) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangePeerV2Request) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Changes) > 0 {
		for iNdEx := len(m.Changes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Changes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ChangePeerV2Response) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangePeerV2Response) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangePeerV2Response) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SplitRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SplitRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ShareSourceRegionSize {
		i--
		if m.ShareSourceRegionSize {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if m.RightDerive {
		i--
		if m.RightDerive {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if len(m.NewPeerIds) > 0 {
		dAtA24 := make([]byte, len(m.NewPeerIds)*10)
		var j23 int
		for _, num := range m.NewPeerIds {
			for num >= 1<<7 {
				dAtA24[j23] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j23++
			}
			dAtA24[j23] = uint8(num)
			j23++
		}
		i -= j23
		copy(dAtA[i:], dAtA24[:j23])
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(j23))
		i--
		dAtA[i] = 0x1a
	}
	if m.NewRegionId != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.NewRegionId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.SplitKey) > 0 {
		i -= len(m.SplitKey)
		copy(dAtA[i:], m.SplitKey)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.SplitKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SplitResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SplitResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Right != nil {
		{
			size, err := m.Right.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Left != nil {
		{
			size, err := m.Left.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *BatchSplitRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSplitRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchSplitRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ShareSourceRegionSize {
		i--
		if m.ShareSourceRegionSize {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.RightDerive {
		i--
		if m.RightDerive {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.Requests) > 0 {
		for iNdEx := len(m.Requests) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Requests[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BatchSplitResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSplitResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchSplitResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Regions) > 0 {
		for iNdEx := len(m.Regions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Regions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CompactLogRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactLogRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CompactLogRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.VoterReplicatedIndex != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.VoterReplicatedIndex))
		i--
		dAtA[i] = 0x18
	}
	if m.CompactTerm != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CompactTerm))
		i--
		dAtA[i] = 0x10
	}
	if m.CompactIndex != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CompactIndex))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CompactLogResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactLogResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CompactLogResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *TransferLeaderRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferLeaderRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TransferLeaderRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Peers) > 0 {
		for iNdEx := len(m.Peers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Peers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Peer != nil {
		{
			size, err := m.Peer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TransferLeaderResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransferLeaderResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TransferLeaderResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ComputeHashRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ComputeHashRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ComputeHashRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Context) > 0 {
		i -= len(m.Context)
		copy(dAtA[i:], m.Context)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Context)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *VerifyHashRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyHashRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *VerifyHashRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Context) > 0 {
		i -= len(m.Context)
		copy(dAtA[i:], m.Context)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Context)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Hash) > 0 {
		i -= len(m.Hash)
		copy(dAtA[i:], m.Hash)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Hash)))
		i--
		dAtA[i] = 0x12
	}
	if m.Index != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Index))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *VerifyHashResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyHashResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *VerifyHashResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *PrepareMergeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrepareMergeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrepareMergeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Target != nil {
		{
			size, err := m.Target.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.MinIndex != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.MinIndex))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PrepareMergeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrepareMergeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrepareMergeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *PrepareFlashbackRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrepareFlashbackRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrepareFlashbackRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StartTs != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PrepareFlashbackResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrepareFlashbackResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrepareFlashbackResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *FinishFlashbackRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FinishFlashbackRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FinishFlashbackRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *FinishFlashbackResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FinishFlashbackResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FinishFlashbackResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *CommitMergeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommitMergeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommitMergeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SourceMeta) > 0 {
		i -= len(m.SourceMeta)
		copy(dAtA[i:], m.SourceMeta)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.SourceMeta)))
		i--
		dAtA[i] = 0x6
		i--
		dAtA[i] = 0xa2
	}
	if m.SourceState != nil {
		{
			size, err := m.SourceState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.Entries) > 0 {
		for iNdEx := len(m.Entries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Entries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Commit != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Commit))
		i--
		dAtA[i] = 0x10
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CommitMergeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommitMergeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommitMergeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *RollbackMergeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RollbackMergeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RollbackMergeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Commit != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Commit))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RollbackMergeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RollbackMergeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RollbackMergeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *SwitchWitnessRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SwitchWitnessRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SwitchWitnessRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsWitness {
		i--
		if m.IsWitness {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.PeerId != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.PeerId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BatchSwitchWitnessRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSwitchWitnessRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchSwitchWitnessRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SwitchWitnesses) > 0 {
		for iNdEx := len(m.SwitchWitnesses) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.SwitchWitnesses[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BatchSwitchWitnessResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSwitchWitnessResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchSwitchWitnessResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *UpdateGcPeerRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGcPeerRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateGcPeerRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.PeerId) > 0 {
		dAtA32 := make([]byte, len(m.PeerId)*10)
		var j31 int
		for _, num := range m.PeerId {
			for num >= 1<<7 {
				dAtA32[j31] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j31++
			}
			dAtA32[j31] = uint8(num)
			j31++
		}
		i -= j31
		copy(dAtA[i:], dAtA32[:j31])
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(j31))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AdminRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AdminRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AdminRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.UpdateGcPeers != nil {
		{
			size, err := m.UpdateGcPeers.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if m.SwitchWitnesses != nil {
		{
			size, err := m.SwitchWitnesses.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x7a
	}
	if m.FinishFlashback != nil {
		{
			size, err := m.FinishFlashback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	if m.PrepareFlashback != nil {
		{
			size, err := m.PrepareFlashback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if m.ComputeHash != nil {
		{
			size, err := m.ComputeHash.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if m.ChangePeerV2 != nil {
		{
			size, err := m.ChangePeerV2.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.Splits != nil {
		{
			size, err := m.Splits.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.RollbackMerge != nil {
		{
			size, err := m.RollbackMerge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.CommitMerge != nil {
		{
			size, err := m.CommitMerge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.PrepareMerge != nil {
		{
			size, err := m.PrepareMerge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.VerifyHash != nil {
		{
			size, err := m.VerifyHash.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.TransferLeader != nil {
		{
			size, err := m.TransferLeader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.CompactLog != nil {
		{
			size, err := m.CompactLog.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Split != nil {
		{
			size, err := m.Split.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.ChangePeer != nil {
		{
			size, err := m.ChangePeer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.CmdType != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AdminResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AdminResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AdminResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SwitchWitnesses != nil {
		{
			size, err := m.SwitchWitnesses.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	if m.FinishFlashback != nil {
		{
			size, err := m.FinishFlashback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if m.PrepareFlashback != nil {
		{
			size, err := m.PrepareFlashback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if m.ChangePeerV2 != nil {
		{
			size, err := m.ChangePeerV2.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.Splits != nil {
		{
			size, err := m.Splits.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.RollbackMerge != nil {
		{
			size, err := m.RollbackMerge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.CommitMerge != nil {
		{
			size, err := m.CommitMerge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.PrepareMerge != nil {
		{
			size, err := m.PrepareMerge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.VerifyHash != nil {
		{
			size, err := m.VerifyHash.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.TransferLeader != nil {
		{
			size, err := m.TransferLeader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.CompactLog != nil {
		{
			size, err := m.CompactLog.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Split != nil {
		{
			size, err := m.Split.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.ChangePeer != nil {
		{
			size, err := m.ChangePeer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.CmdType != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionLeaderRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionLeaderRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionLeaderRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *RegionLeaderResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionLeaderResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionLeaderResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Leader != nil {
		{
			size, err := m.Leader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RegionDetailRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionDetailRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionDetailRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *RegionDetailResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionDetailResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionDetailResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Leader != nil {
		{
			size, err := m.Leader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StatusRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StatusRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StatusRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionDetail != nil {
		{
			size, err := m.RegionDetail.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionLeader != nil {
		{
			size, err := m.RegionLeader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.CmdType != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *StatusResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StatusResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StatusResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionDetail != nil {
		{
			size, err := m.RegionDetail.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionLeader != nil {
		{
			size, err := m.RegionLeader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.CmdType != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CmdType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RaftRequestHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftRequestHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftRequestHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ResourceGroupName) > 0 {
		i -= len(m.ResourceGroupName)
		copy(dAtA[i:], m.ResourceGroupName)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.ResourceGroupName)))
		i--
		dAtA[i] = 0x6a
	}
	if m.Priority != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x60
	}
	if len(m.FlagData) > 0 {
		i -= len(m.FlagData)
		copy(dAtA[i:], m.FlagData)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.FlagData)))
		i--
		dAtA[i] = 0x5a
	}
	if m.Flags != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Flags))
		i--
		dAtA[i] = 0x50
	}
	if m.AppliedIndex != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.AppliedIndex))
		i--
		dAtA[i] = 0x48
	}
	if m.ReplicaRead {
		i--
		if m.ReplicaRead {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x40
	}
	if m.SyncLog {
		i--
		if m.SyncLog {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x38
	}
	if m.Term != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.Term))
		i--
		dAtA[i] = 0x30
	}
	if m.RegionEpoch != nil {
		{
			size, err := m.RegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0x22
	}
	if m.ReadQuorum {
		i--
		if m.ReadQuorum {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.Peer != nil {
		{
			size, err := m.Peer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.RegionId != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RaftResponseHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftResponseHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftResponseHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CurrentTerm != 0 {
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(m.CurrentTerm))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0x12
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CustomRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CustomRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CustomRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintRaftCmdpb(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RaftCmdRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftCmdRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftCmdRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CustomRequest != nil {
		{
			size, err := m.CustomRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.StatusRequest != nil {
		{
			size, err := m.StatusRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.AdminRequest != nil {
		{
			size, err := m.AdminRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Requests) > 0 {
		for iNdEx := len(m.Requests) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Requests[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RaftCmdResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftCmdResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftCmdResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StatusResponse != nil {
		{
			size, err := m.StatusResponse.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.AdminResponse != nil {
		{
			size, err := m.AdminResponse.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Responses) > 0 {
		for iNdEx := len(m.Responses) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Responses[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftCmdpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintRaftCmdpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovRaftCmdpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *GetRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *GetResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *PutRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *PutResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *DeleteRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *DeleteResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *DeleteRangeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.NotifyOnly {
		n += 2
	}
	return n
}

func (m *DeleteRangeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *SnapRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *SnapResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *PrewriteRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Lock)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *PrewriteResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *IngestSSTRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Sst != nil {
		l = m.Sst.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *IngestSSTResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ReadIndexRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTs != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.StartTs))
	}
	if len(m.KeyRanges) > 0 {
		for _, e := range m.KeyRanges {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	return n
}

func (m *ReadIndexResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ReadIndex != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.ReadIndex))
	}
	if m.Locked != nil {
		l = m.Locked.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *Request) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.Get != nil {
		l = m.Get.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Put != nil {
		l = m.Put.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Delete != nil {
		l = m.Delete.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Snap != nil {
		l = m.Snap.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Prewrite != nil {
		l = m.Prewrite.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.DeleteRange != nil {
		l = m.DeleteRange.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.IngestSst != nil {
		l = m.IngestSst.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.ReadIndex != nil {
		l = m.ReadIndex.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *Response) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.Get != nil {
		l = m.Get.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Put != nil {
		l = m.Put.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Delete != nil {
		l = m.Delete.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Snap != nil {
		l = m.Snap.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Prewrite != nil {
		l = m.Prewrite.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.DelteRange != nil {
		l = m.DelteRange.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.IngestSst != nil {
		l = m.IngestSst.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.ReadIndex != nil {
		l = m.ReadIndex.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *ChangePeerRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ChangeType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.ChangeType))
	}
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *ChangePeerResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *ChangePeerV2Request) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Changes) > 0 {
		for _, e := range m.Changes {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	return n
}

func (m *ChangePeerV2Response) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *SplitRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SplitKey)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.NewRegionId != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.NewRegionId))
	}
	if len(m.NewPeerIds) > 0 {
		l = 0
		for _, e := range m.NewPeerIds {
			l += sovRaftCmdpb(uint64(e))
		}
		n += 1 + sovRaftCmdpb(uint64(l)) + l
	}
	if m.RightDerive {
		n += 2
	}
	if m.ShareSourceRegionSize {
		n += 2
	}
	return n
}

func (m *SplitResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Left != nil {
		l = m.Left.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Right != nil {
		l = m.Right.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *BatchSplitRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Requests) > 0 {
		for _, e := range m.Requests {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	if m.RightDerive {
		n += 2
	}
	if m.ShareSourceRegionSize {
		n += 2
	}
	return n
}

func (m *BatchSplitResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Regions) > 0 {
		for _, e := range m.Regions {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	return n
}

func (m *CompactLogRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CompactIndex != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CompactIndex))
	}
	if m.CompactTerm != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CompactTerm))
	}
	if m.VoterReplicatedIndex != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.VoterReplicatedIndex))
	}
	return n
}

func (m *CompactLogResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *TransferLeaderRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if len(m.Peers) > 0 {
		for _, e := range m.Peers {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	return n
}

func (m *TransferLeaderResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ComputeHashRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Context)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *VerifyHashRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Index != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.Index))
	}
	l = len(m.Hash)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Context)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *VerifyHashResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *PrepareMergeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MinIndex != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.MinIndex))
	}
	if m.Target != nil {
		l = m.Target.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *PrepareMergeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *PrepareFlashbackRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTs != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.StartTs))
	}
	return n
}

func (m *PrepareFlashbackResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *FinishFlashbackRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *FinishFlashbackResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *CommitMergeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Commit != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.Commit))
	}
	if len(m.Entries) > 0 {
		for _, e := range m.Entries {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	if m.SourceState != nil {
		l = m.SourceState.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.SourceMeta)
	if l > 0 {
		n += 2 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *CommitMergeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *RollbackMergeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Commit != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.Commit))
	}
	return n
}

func (m *RollbackMergeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *SwitchWitnessRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PeerId != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.PeerId))
	}
	if m.IsWitness {
		n += 2
	}
	return n
}

func (m *BatchSwitchWitnessRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.SwitchWitnesses) > 0 {
		for _, e := range m.SwitchWitnesses {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	return n
}

func (m *BatchSwitchWitnessResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *UpdateGcPeerRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.PeerId) > 0 {
		l = 0
		for _, e := range m.PeerId {
			l += sovRaftCmdpb(uint64(e))
		}
		n += 1 + sovRaftCmdpb(uint64(l)) + l
	}
	return n
}

func (m *AdminRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.ChangePeer != nil {
		l = m.ChangePeer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Split != nil {
		l = m.Split.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CompactLog != nil {
		l = m.CompactLog.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.TransferLeader != nil {
		l = m.TransferLeader.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.VerifyHash != nil {
		l = m.VerifyHash.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.PrepareMerge != nil {
		l = m.PrepareMerge.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CommitMerge != nil {
		l = m.CommitMerge.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.RollbackMerge != nil {
		l = m.RollbackMerge.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Splits != nil {
		l = m.Splits.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.ChangePeerV2 != nil {
		l = m.ChangePeerV2.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.ComputeHash != nil {
		l = m.ComputeHash.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.PrepareFlashback != nil {
		l = m.PrepareFlashback.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.FinishFlashback != nil {
		l = m.FinishFlashback.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.SwitchWitnesses != nil {
		l = m.SwitchWitnesses.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.UpdateGcPeers != nil {
		l = m.UpdateGcPeers.Size()
		n += 2 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *AdminResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.ChangePeer != nil {
		l = m.ChangePeer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Split != nil {
		l = m.Split.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CompactLog != nil {
		l = m.CompactLog.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.TransferLeader != nil {
		l = m.TransferLeader.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.VerifyHash != nil {
		l = m.VerifyHash.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.PrepareMerge != nil {
		l = m.PrepareMerge.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CommitMerge != nil {
		l = m.CommitMerge.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.RollbackMerge != nil {
		l = m.RollbackMerge.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Splits != nil {
		l = m.Splits.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.ChangePeerV2 != nil {
		l = m.ChangePeerV2.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.PrepareFlashback != nil {
		l = m.PrepareFlashback.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.FinishFlashback != nil {
		l = m.FinishFlashback.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.SwitchWitnesses != nil {
		l = m.SwitchWitnesses.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *RegionLeaderRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *RegionLeaderResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *RegionDetailRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *RegionDetailResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *StatusRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.RegionLeader != nil {
		l = m.RegionLeader.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.RegionDetail != nil {
		l = m.RegionDetail.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *StatusResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CmdType != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CmdType))
	}
	if m.RegionLeader != nil {
		l = m.RegionLeader.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.RegionDetail != nil {
		l = m.RegionDetail.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *RaftRequestHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.RegionId))
	}
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.ReadQuorum {
		n += 2
	}
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Term != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.Term))
	}
	if m.SyncLog {
		n += 2
	}
	if m.ReplicaRead {
		n += 2
	}
	if m.AppliedIndex != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.AppliedIndex))
	}
	if m.Flags != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.Flags))
	}
	l = len(m.FlagData)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.Priority != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.Priority))
	}
	l = len(m.ResourceGroupName)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *RaftResponseHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CurrentTerm != 0 {
		n += 1 + sovRaftCmdpb(uint64(m.CurrentTerm))
	}
	return n
}

func (m *CustomRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *RaftCmdRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if len(m.Requests) > 0 {
		for _, e := range m.Requests {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	if m.AdminRequest != nil {
		l = m.AdminRequest.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.StatusRequest != nil {
		l = m.StatusRequest.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.CustomRequest != nil {
		l = m.CustomRequest.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func (m *RaftCmdResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if len(m.Responses) > 0 {
		for _, e := range m.Responses {
			l = e.Size()
			n += 1 + l + sovRaftCmdpb(uint64(l))
		}
	}
	if m.AdminResponse != nil {
		l = m.AdminResponse.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	if m.StatusResponse != nil {
		l = m.StatusResponse.Size()
		n += 1 + l + sovRaftCmdpb(uint64(l))
	}
	return n
}

func sovRaftCmdpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozRaftCmdpb(x uint64) (n int) {
	return sovRaftCmdpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteRangeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteRangeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteRangeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NotifyOnly", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.NotifyOnly = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteRangeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteRangeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteRangeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrewriteRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrewriteRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrewriteRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lock", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Lock = append(m.Lock[:0], dAtA[iNdEx:postIndex]...)
			if m.Lock == nil {
				m.Lock = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrewriteResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrewriteResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrewriteResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IngestSSTRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IngestSSTRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IngestSSTRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Sst == nil {
				m.Sst = &import_sstpb.SSTMeta{}
			}
			if err := m.Sst.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IngestSSTResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IngestSSTResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IngestSSTResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadIndexRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadIndexRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadIndexRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyRanges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeyRanges = append(m.KeyRanges, &kvrpcpb.KeyRange{})
			if err := m.KeyRanges[len(m.KeyRanges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadIndexResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadIndexResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadIndexResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadIndex", wireType)
			}
			m.ReadIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReadIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Locked", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Locked == nil {
				m.Locked = &kvrpcpb.LockInfo{}
			}
			if err := m.Locked.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Request) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Request: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Request: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= CmdType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Get", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Get == nil {
				m.Get = &GetRequest{}
			}
			if err := m.Get.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Put", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Put == nil {
				m.Put = &PutRequest{}
			}
			if err := m.Put.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Delete", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Delete == nil {
				m.Delete = &DeleteRequest{}
			}
			if err := m.Delete.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Snap", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Snap == nil {
				m.Snap = &SnapRequest{}
			}
			if err := m.Snap.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Prewrite", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Prewrite == nil {
				m.Prewrite = &PrewriteRequest{}
			}
			if err := m.Prewrite.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeleteRange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DeleteRange == nil {
				m.DeleteRange = &DeleteRangeRequest{}
			}
			if err := m.DeleteRange.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IngestSst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.IngestSst == nil {
				m.IngestSst = &IngestSSTRequest{}
			}
			if err := m.IngestSst.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadIndex", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReadIndex == nil {
				m.ReadIndex = &ReadIndexRequest{}
			}
			if err := m.ReadIndex.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Response) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Response: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Response: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= CmdType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Get", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Get == nil {
				m.Get = &GetResponse{}
			}
			if err := m.Get.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Put", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Put == nil {
				m.Put = &PutResponse{}
			}
			if err := m.Put.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Delete", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Delete == nil {
				m.Delete = &DeleteResponse{}
			}
			if err := m.Delete.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Snap", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Snap == nil {
				m.Snap = &SnapResponse{}
			}
			if err := m.Snap.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Prewrite", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Prewrite == nil {
				m.Prewrite = &PrewriteResponse{}
			}
			if err := m.Prewrite.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DelteRange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DelteRange == nil {
				m.DelteRange = &DeleteRangeResponse{}
			}
			if err := m.DelteRange.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IngestSst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.IngestSst == nil {
				m.IngestSst = &IngestSSTResponse{}
			}
			if err := m.IngestSst.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadIndex", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReadIndex == nil {
				m.ReadIndex = &ReadIndexResponse{}
			}
			if err := m.ReadIndex.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangePeerRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangePeerRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangePeerRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangeType", wireType)
			}
			m.ChangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeType |= eraftpb.ConfChangeType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangePeerResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangePeerResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangePeerResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangePeerV2Request) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangePeerV2Request: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangePeerV2Request: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Changes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Changes = append(m.Changes, &ChangePeerRequest{})
			if err := m.Changes[len(m.Changes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangePeerV2Response) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangePeerV2Response: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangePeerV2Response: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SplitRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SplitRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SplitKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SplitKey = append(m.SplitKey[:0], dAtA[iNdEx:postIndex]...)
			if m.SplitKey == nil {
				m.SplitKey = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewRegionId", wireType)
			}
			m.NewRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewRegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRaftCmdpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.NewPeerIds = append(m.NewPeerIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRaftCmdpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRaftCmdpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthRaftCmdpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.NewPeerIds) == 0 {
					m.NewPeerIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRaftCmdpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.NewPeerIds = append(m.NewPeerIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field NewPeerIds", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RightDerive", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RightDerive = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShareSourceRegionSize", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShareSourceRegionSize = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SplitResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SplitResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Left", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Left == nil {
				m.Left = &metapb.Region{}
			}
			if err := m.Left.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Right", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Right == nil {
				m.Right = &metapb.Region{}
			}
			if err := m.Right.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSplitRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchSplitRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchSplitRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Requests", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Requests = append(m.Requests, &SplitRequest{})
			if err := m.Requests[len(m.Requests)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RightDerive", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RightDerive = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShareSourceRegionSize", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShareSourceRegionSize = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSplitResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchSplitResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchSplitResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Regions = append(m.Regions, &metapb.Region{})
			if err := m.Regions[len(m.Regions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactLogRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactLogRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactLogRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompactIndex", wireType)
			}
			m.CompactIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CompactIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompactTerm", wireType)
			}
			m.CompactTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CompactTerm |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field VoterReplicatedIndex", wireType)
			}
			m.VoterReplicatedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoterReplicatedIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactLogResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactLogResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactLogResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferLeaderRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransferLeaderRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransferLeaderRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Peers = append(m.Peers, &metapb.Peer{})
			if err := m.Peers[len(m.Peers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransferLeaderResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransferLeaderResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransferLeaderResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ComputeHashRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ComputeHashRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ComputeHashRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Context = append(m.Context[:0], dAtA[iNdEx:postIndex]...)
			if m.Context == nil {
				m.Context = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyHashRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: VerifyHashRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: VerifyHashRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hash", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hash = append(m.Hash[:0], dAtA[iNdEx:postIndex]...)
			if m.Hash == nil {
				m.Hash = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Context = append(m.Context[:0], dAtA[iNdEx:postIndex]...)
			if m.Context == nil {
				m.Context = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyHashResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: VerifyHashResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: VerifyHashResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrepareMergeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrepareMergeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrepareMergeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinIndex", wireType)
			}
			m.MinIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Target", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Target == nil {
				m.Target = &metapb.Region{}
			}
			if err := m.Target.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrepareMergeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrepareMergeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrepareMergeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrepareFlashbackRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrepareFlashbackRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrepareFlashbackRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrepareFlashbackResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrepareFlashbackResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrepareFlashbackResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FinishFlashbackRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FinishFlashbackRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FinishFlashbackRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FinishFlashbackResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FinishFlashbackResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FinishFlashbackResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommitMergeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommitMergeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommitMergeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &metapb.Region{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commit", wireType)
			}
			m.Commit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Commit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Entries = append(m.Entries, &eraftpb.Entry{})
			if err := m.Entries[len(m.Entries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SourceState == nil {
				m.SourceState = &raft_serverpb.RegionLocalState{}
			}
			if err := m.SourceState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 100:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceMeta", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceMeta = append(m.SourceMeta[:0], dAtA[iNdEx:postIndex]...)
			if m.SourceMeta == nil {
				m.SourceMeta = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommitMergeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommitMergeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommitMergeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RollbackMergeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RollbackMergeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RollbackMergeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commit", wireType)
			}
			m.Commit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Commit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RollbackMergeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RollbackMergeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RollbackMergeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SwitchWitnessRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SwitchWitnessRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SwitchWitnessRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeerId", wireType)
			}
			m.PeerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsWitness", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsWitness = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSwitchWitnessRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchSwitchWitnessRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchSwitchWitnessRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SwitchWitnesses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SwitchWitnesses = append(m.SwitchWitnesses, &SwitchWitnessRequest{})
			if err := m.SwitchWitnesses[len(m.SwitchWitnesses)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSwitchWitnessResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchSwitchWitnessResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchSwitchWitnessResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGcPeerRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateGcPeerRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateGcPeerRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRaftCmdpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.PeerId = append(m.PeerId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRaftCmdpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRaftCmdpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthRaftCmdpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.PeerId) == 0 {
					m.PeerId = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRaftCmdpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.PeerId = append(m.PeerId, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field PeerId", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AdminRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AdminRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AdminRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= AdminCmdType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeer == nil {
				m.ChangePeer = &ChangePeerRequest{}
			}
			if err := m.ChangePeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Split", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Split == nil {
				m.Split = &SplitRequest{}
			}
			if err := m.Split.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompactLog", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CompactLog == nil {
				m.CompactLog = &CompactLogRequest{}
			}
			if err := m.CompactLog.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TransferLeader == nil {
				m.TransferLeader = &TransferLeaderRequest{}
			}
			if err := m.TransferLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VerifyHash", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.VerifyHash == nil {
				m.VerifyHash = &VerifyHashRequest{}
			}
			if err := m.VerifyHash.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrepareMerge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.PrepareMerge == nil {
				m.PrepareMerge = &PrepareMergeRequest{}
			}
			if err := m.PrepareMerge.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitMerge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CommitMerge == nil {
				m.CommitMerge = &CommitMergeRequest{}
			}
			if err := m.CommitMerge.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RollbackMerge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RollbackMerge == nil {
				m.RollbackMerge = &RollbackMergeRequest{}
			}
			if err := m.RollbackMerge.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Splits", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Splits == nil {
				m.Splits = &BatchSplitRequest{}
			}
			if err := m.Splits.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeerV2", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeerV2 == nil {
				m.ChangePeerV2 = &ChangePeerV2Request{}
			}
			if err := m.ChangePeerV2.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ComputeHash", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ComputeHash == nil {
				m.ComputeHash = &ComputeHashRequest{}
			}
			if err := m.ComputeHash.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrepareFlashback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.PrepareFlashback == nil {
				m.PrepareFlashback = &PrepareFlashbackRequest{}
			}
			if err := m.PrepareFlashback.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FinishFlashback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FinishFlashback == nil {
				m.FinishFlashback = &FinishFlashbackRequest{}
			}
			if err := m.FinishFlashback.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SwitchWitnesses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SwitchWitnesses == nil {
				m.SwitchWitnesses = &BatchSwitchWitnessRequest{}
			}
			if err := m.SwitchWitnesses.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateGcPeers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.UpdateGcPeers == nil {
				m.UpdateGcPeers = &UpdateGcPeerRequest{}
			}
			if err := m.UpdateGcPeers.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AdminResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AdminResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AdminResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= AdminCmdType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeer == nil {
				m.ChangePeer = &ChangePeerResponse{}
			}
			if err := m.ChangePeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Split", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Split == nil {
				m.Split = &SplitResponse{}
			}
			if err := m.Split.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompactLog", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CompactLog == nil {
				m.CompactLog = &CompactLogResponse{}
			}
			if err := m.CompactLog.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TransferLeader == nil {
				m.TransferLeader = &TransferLeaderResponse{}
			}
			if err := m.TransferLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field VerifyHash", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.VerifyHash == nil {
				m.VerifyHash = &VerifyHashResponse{}
			}
			if err := m.VerifyHash.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrepareMerge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.PrepareMerge == nil {
				m.PrepareMerge = &PrepareMergeResponse{}
			}
			if err := m.PrepareMerge.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitMerge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CommitMerge == nil {
				m.CommitMerge = &CommitMergeResponse{}
			}
			if err := m.CommitMerge.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RollbackMerge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RollbackMerge == nil {
				m.RollbackMerge = &RollbackMergeResponse{}
			}
			if err := m.RollbackMerge.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Splits", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Splits == nil {
				m.Splits = &BatchSplitResponse{}
			}
			if err := m.Splits.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeerV2", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeerV2 == nil {
				m.ChangePeerV2 = &ChangePeerV2Response{}
			}
			if err := m.ChangePeerV2.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrepareFlashback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.PrepareFlashback == nil {
				m.PrepareFlashback = &PrepareFlashbackResponse{}
			}
			if err := m.PrepareFlashback.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FinishFlashback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FinishFlashback == nil {
				m.FinishFlashback = &FinishFlashbackResponse{}
			}
			if err := m.FinishFlashback.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SwitchWitnesses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SwitchWitnesses == nil {
				m.SwitchWitnesses = &BatchSwitchWitnessResponse{}
			}
			if err := m.SwitchWitnesses.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionLeaderRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionLeaderRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionLeaderRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionLeaderResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionLeaderResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionLeaderResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &metapb.Peer{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionDetailRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionDetailRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionDetailRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionDetailResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionDetailResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionDetailResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &metapb.Peer{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StatusRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StatusRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StatusRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= StatusCmdType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionLeader == nil {
				m.RegionLeader = &RegionLeaderRequest{}
			}
			if err := m.RegionLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionDetail == nil {
				m.RegionDetail = &RegionDetailRequest{}
			}
			if err := m.RegionDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StatusResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StatusResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StatusResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdType", wireType)
			}
			m.CmdType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdType |= StatusCmdType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionLeader == nil {
				m.RegionLeader = &RegionLeaderResponse{}
			}
			if err := m.RegionLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionDetail == nil {
				m.RegionDetail = &RegionDetailResponse{}
			}
			if err := m.RegionDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftRequestHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftRequestHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftRequestHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadQuorum", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReadQuorum = bool(v != 0)
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SyncLog", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SyncLog = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReplicaRead", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReplicaRead = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppliedIndex", wireType)
			}
			m.AppliedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppliedIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Flags", wireType)
			}
			m.Flags = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Flags |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlagData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FlagData = append(m.FlagData[:0], dAtA[iNdEx:postIndex]...)
			if m.FlagData == nil {
				m.FlagData = []byte{}
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= kvrpcpb.CommandPri(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResourceGroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResourceGroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftResponseHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftResponseHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftResponseHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &errorpb.Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentTerm", wireType)
			}
			m.CurrentTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentTerm |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CustomRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CustomRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CustomRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftCmdRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftCmdRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftCmdRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RaftRequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Requests", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Requests = append(m.Requests, &Request{})
			if err := m.Requests[len(m.Requests)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdminRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdminRequest == nil {
				m.AdminRequest = &AdminRequest{}
			}
			if err := m.AdminRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StatusRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StatusRequest == nil {
				m.StatusRequest = &StatusRequest{}
			}
			if err := m.StatusRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CustomRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CustomRequest == nil {
				m.CustomRequest = &CustomRequest{}
			}
			if err := m.CustomRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftCmdResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftCmdResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftCmdResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RaftResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Responses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Responses = append(m.Responses, &Response{})
			if err := m.Responses[len(m.Responses)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdminResponse", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdminResponse == nil {
				m.AdminResponse = &AdminResponse{}
			}
			if err := m.AdminResponse.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StatusResponse", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StatusResponse == nil {
				m.StatusResponse = &StatusResponse{}
			}
			if err := m.StatusResponse.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftCmdpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftCmdpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipRaftCmdpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRaftCmdpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRaftCmdpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthRaftCmdpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupRaftCmdpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthRaftCmdpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthRaftCmdpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRaftCmdpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupRaftCmdpb = fmt.Errorf("proto: unexpected end of group")
)
