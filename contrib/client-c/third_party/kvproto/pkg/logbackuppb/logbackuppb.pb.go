// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: logbackuppb.proto

package logbackup

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	errorpb "github.com/pingcap/kvproto/pkg/errorpb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// The minimal information for identify a region.
type RegionIdentity struct {
	Id           uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	EpochVersion uint64 `protobuf:"varint,2,opt,name=epoch_version,json=epochVersion,proto3" json:"epoch_version,omitempty"`
}

func (m *RegionIdentity) Reset()         { *m = RegionIdentity{} }
func (m *RegionIdentity) String() string { return proto.CompactTextString(m) }
func (*RegionIdentity) ProtoMessage()    {}
func (*RegionIdentity) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{0}
}
func (m *RegionIdentity) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionIdentity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionIdentity.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionIdentity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionIdentity.Merge(m, src)
}
func (m *RegionIdentity) XXX_Size() int {
	return m.Size()
}
func (m *RegionIdentity) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionIdentity.DiscardUnknown(m)
}

var xxx_messageInfo_RegionIdentity proto.InternalMessageInfo

func (m *RegionIdentity) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RegionIdentity) GetEpochVersion() uint64 {
	if m != nil {
		return m.EpochVersion
	}
	return 0
}

// The last flush ts with region information.
type RegionCheckpoint struct {
	Err        *errorpb.Error  `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	Region     *RegionIdentity `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	Checkpoint uint64          `protobuf:"varint,3,opt,name=checkpoint,proto3" json:"checkpoint,omitempty"`
}

func (m *RegionCheckpoint) Reset()         { *m = RegionCheckpoint{} }
func (m *RegionCheckpoint) String() string { return proto.CompactTextString(m) }
func (*RegionCheckpoint) ProtoMessage()    {}
func (*RegionCheckpoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{1}
}
func (m *RegionCheckpoint) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionCheckpoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionCheckpoint.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionCheckpoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionCheckpoint.Merge(m, src)
}
func (m *RegionCheckpoint) XXX_Size() int {
	return m.Size()
}
func (m *RegionCheckpoint) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionCheckpoint.DiscardUnknown(m)
}

var xxx_messageInfo_RegionCheckpoint proto.InternalMessageInfo

func (m *RegionCheckpoint) GetErr() *errorpb.Error {
	if m != nil {
		return m.Err
	}
	return nil
}

func (m *RegionCheckpoint) GetRegion() *RegionIdentity {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *RegionCheckpoint) GetCheckpoint() uint64 {
	if m != nil {
		return m.Checkpoint
	}
	return 0
}

type GetLastFlushTSOfRegionRequest struct {
	Regions []*RegionIdentity `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty"`
}

func (m *GetLastFlushTSOfRegionRequest) Reset()         { *m = GetLastFlushTSOfRegionRequest{} }
func (m *GetLastFlushTSOfRegionRequest) String() string { return proto.CompactTextString(m) }
func (*GetLastFlushTSOfRegionRequest) ProtoMessage()    {}
func (*GetLastFlushTSOfRegionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{2}
}
func (m *GetLastFlushTSOfRegionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetLastFlushTSOfRegionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetLastFlushTSOfRegionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetLastFlushTSOfRegionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastFlushTSOfRegionRequest.Merge(m, src)
}
func (m *GetLastFlushTSOfRegionRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetLastFlushTSOfRegionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastFlushTSOfRegionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastFlushTSOfRegionRequest proto.InternalMessageInfo

func (m *GetLastFlushTSOfRegionRequest) GetRegions() []*RegionIdentity {
	if m != nil {
		return m.Regions
	}
	return nil
}

type GetLastFlushTSOfRegionResponse struct {
	Checkpoints []*RegionCheckpoint `protobuf:"bytes,1,rep,name=checkpoints,proto3" json:"checkpoints,omitempty"`
}

func (m *GetLastFlushTSOfRegionResponse) Reset()         { *m = GetLastFlushTSOfRegionResponse{} }
func (m *GetLastFlushTSOfRegionResponse) String() string { return proto.CompactTextString(m) }
func (*GetLastFlushTSOfRegionResponse) ProtoMessage()    {}
func (*GetLastFlushTSOfRegionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{3}
}
func (m *GetLastFlushTSOfRegionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetLastFlushTSOfRegionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetLastFlushTSOfRegionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetLastFlushTSOfRegionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLastFlushTSOfRegionResponse.Merge(m, src)
}
func (m *GetLastFlushTSOfRegionResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetLastFlushTSOfRegionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLastFlushTSOfRegionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLastFlushTSOfRegionResponse proto.InternalMessageInfo

func (m *GetLastFlushTSOfRegionResponse) GetCheckpoints() []*RegionCheckpoint {
	if m != nil {
		return m.Checkpoints
	}
	return nil
}

type SubscribeFlushEventRequest struct {
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (m *SubscribeFlushEventRequest) Reset()         { *m = SubscribeFlushEventRequest{} }
func (m *SubscribeFlushEventRequest) String() string { return proto.CompactTextString(m) }
func (*SubscribeFlushEventRequest) ProtoMessage()    {}
func (*SubscribeFlushEventRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{4}
}
func (m *SubscribeFlushEventRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SubscribeFlushEventRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SubscribeFlushEventRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SubscribeFlushEventRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeFlushEventRequest.Merge(m, src)
}
func (m *SubscribeFlushEventRequest) XXX_Size() int {
	return m.Size()
}
func (m *SubscribeFlushEventRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeFlushEventRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeFlushEventRequest proto.InternalMessageInfo

func (m *SubscribeFlushEventRequest) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

type SubscribeFlushEventResponse struct {
	Events []*FlushEvent `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
}

func (m *SubscribeFlushEventResponse) Reset()         { *m = SubscribeFlushEventResponse{} }
func (m *SubscribeFlushEventResponse) String() string { return proto.CompactTextString(m) }
func (*SubscribeFlushEventResponse) ProtoMessage()    {}
func (*SubscribeFlushEventResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{5}
}
func (m *SubscribeFlushEventResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SubscribeFlushEventResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SubscribeFlushEventResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SubscribeFlushEventResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscribeFlushEventResponse.Merge(m, src)
}
func (m *SubscribeFlushEventResponse) XXX_Size() int {
	return m.Size()
}
func (m *SubscribeFlushEventResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscribeFlushEventResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubscribeFlushEventResponse proto.InternalMessageInfo

func (m *SubscribeFlushEventResponse) GetEvents() []*FlushEvent {
	if m != nil {
		return m.Events
	}
	return nil
}

type FlushEvent struct {
	StartKey   []byte `protobuf:"bytes,1,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey     []byte `protobuf:"bytes,2,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	Checkpoint uint64 `protobuf:"varint,3,opt,name=checkpoint,proto3" json:"checkpoint,omitempty"`
}

func (m *FlushEvent) Reset()         { *m = FlushEvent{} }
func (m *FlushEvent) String() string { return proto.CompactTextString(m) }
func (*FlushEvent) ProtoMessage()    {}
func (*FlushEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{6}
}
func (m *FlushEvent) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlushEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlushEvent.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlushEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlushEvent.Merge(m, src)
}
func (m *FlushEvent) XXX_Size() int {
	return m.Size()
}
func (m *FlushEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_FlushEvent.DiscardUnknown(m)
}

var xxx_messageInfo_FlushEvent proto.InternalMessageInfo

func (m *FlushEvent) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *FlushEvent) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (m *FlushEvent) GetCheckpoint() uint64 {
	if m != nil {
		return m.Checkpoint
	}
	return 0
}

type FlushNowRequest struct {
}

func (m *FlushNowRequest) Reset()         { *m = FlushNowRequest{} }
func (m *FlushNowRequest) String() string { return proto.CompactTextString(m) }
func (*FlushNowRequest) ProtoMessage()    {}
func (*FlushNowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{7}
}
func (m *FlushNowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlushNowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlushNowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlushNowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlushNowRequest.Merge(m, src)
}
func (m *FlushNowRequest) XXX_Size() int {
	return m.Size()
}
func (m *FlushNowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FlushNowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FlushNowRequest proto.InternalMessageInfo

type FlushNowResponse struct {
	Results []*FlushResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (m *FlushNowResponse) Reset()         { *m = FlushNowResponse{} }
func (m *FlushNowResponse) String() string { return proto.CompactTextString(m) }
func (*FlushNowResponse) ProtoMessage()    {}
func (*FlushNowResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{8}
}
func (m *FlushNowResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlushNowResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlushNowResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlushNowResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlushNowResponse.Merge(m, src)
}
func (m *FlushNowResponse) XXX_Size() int {
	return m.Size()
}
func (m *FlushNowResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FlushNowResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FlushNowResponse proto.InternalMessageInfo

func (m *FlushNowResponse) GetResults() []*FlushResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type FlushResult struct {
	TaskName     string `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	Success      bool   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	ErrorMessage string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
}

func (m *FlushResult) Reset()         { *m = FlushResult{} }
func (m *FlushResult) String() string { return proto.CompactTextString(m) }
func (*FlushResult) ProtoMessage()    {}
func (*FlushResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_9a556fe18b032662, []int{9}
}
func (m *FlushResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlushResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlushResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlushResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlushResult.Merge(m, src)
}
func (m *FlushResult) XXX_Size() int {
	return m.Size()
}
func (m *FlushResult) XXX_DiscardUnknown() {
	xxx_messageInfo_FlushResult.DiscardUnknown(m)
}

var xxx_messageInfo_FlushResult proto.InternalMessageInfo

func (m *FlushResult) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *FlushResult) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *FlushResult) GetErrorMessage() string {
	if m != nil {
		return m.ErrorMessage
	}
	return ""
}

func init() {
	proto.RegisterType((*RegionIdentity)(nil), "logbackup.RegionIdentity")
	proto.RegisterType((*RegionCheckpoint)(nil), "logbackup.RegionCheckpoint")
	proto.RegisterType((*GetLastFlushTSOfRegionRequest)(nil), "logbackup.GetLastFlushTSOfRegionRequest")
	proto.RegisterType((*GetLastFlushTSOfRegionResponse)(nil), "logbackup.GetLastFlushTSOfRegionResponse")
	proto.RegisterType((*SubscribeFlushEventRequest)(nil), "logbackup.SubscribeFlushEventRequest")
	proto.RegisterType((*SubscribeFlushEventResponse)(nil), "logbackup.SubscribeFlushEventResponse")
	proto.RegisterType((*FlushEvent)(nil), "logbackup.FlushEvent")
	proto.RegisterType((*FlushNowRequest)(nil), "logbackup.FlushNowRequest")
	proto.RegisterType((*FlushNowResponse)(nil), "logbackup.FlushNowResponse")
	proto.RegisterType((*FlushResult)(nil), "logbackup.FlushResult")
}

func init() { proto.RegisterFile("logbackuppb.proto", fileDescriptor_9a556fe18b032662) }

var fileDescriptor_9a556fe18b032662 = []byte{
	// 587 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0xcb, 0x6e, 0xd3, 0x40,
	0x14, 0x8d, 0x53, 0x94, 0x26, 0x37, 0x7d, 0x0e, 0x50, 0x82, 0x23, 0xac, 0xca, 0x08, 0x54, 0x16,
	0x84, 0x92, 0xae, 0x58, 0xb0, 0x29, 0x04, 0x54, 0x51, 0x8a, 0x34, 0xad, 0xd8, 0x5a, 0x7e, 0x5c,
	0x1c, 0xcb, 0xa9, 0xc7, 0xcc, 0x8c, 0x83, 0xfa, 0x05, 0x6c, 0x59, 0xf2, 0x09, 0x6c, 0xf8, 0x0f,
	0x96, 0x5d, 0x76, 0x89, 0x92, 0x1f, 0x41, 0x1e, 0x7b, 0x12, 0x53, 0xd2, 0x96, 0xd5, 0x8c, 0xcf,
	0x39, 0x73, 0xee, 0x99, 0xb9, 0x57, 0x86, 0xcd, 0x11, 0x0b, 0x3d, 0xd7, 0x8f, 0xb3, 0x34, 0xf5,
	0x7a, 0x29, 0x67, 0x92, 0x91, 0xd6, 0x0c, 0x32, 0x57, 0x91, 0x73, 0xc6, 0x35, 0x63, 0xde, 0x09,
	0x59, 0xc8, 0xd4, 0xf6, 0x59, 0xbe, 0x2b, 0xd1, 0x75, 0x9e, 0x09, 0xa9, 0xb6, 0x05, 0x60, 0x0f,
	0x60, 0x8d, 0x62, 0x18, 0xb1, 0xe4, 0x20, 0xc0, 0x44, 0x46, 0xf2, 0x8c, 0xac, 0x41, 0x3d, 0x0a,
	0x3a, 0xc6, 0xb6, 0xb1, 0x73, 0x8b, 0xd6, 0xa3, 0x80, 0x3c, 0x84, 0x55, 0x4c, 0x99, 0x3f, 0x74,
	0xc6, 0xc8, 0x45, 0xc4, 0x92, 0x4e, 0x5d, 0x51, 0x2b, 0x0a, 0xfc, 0x58, 0x60, 0xf6, 0x57, 0x03,
	0x36, 0x0a, 0x9f, 0x57, 0x43, 0xf4, 0xe3, 0x94, 0x45, 0x89, 0x24, 0xdb, 0xb0, 0x84, 0x9c, 0x2b,
	0xab, 0x76, 0x7f, 0xad, 0xa7, 0xf3, 0x0d, 0xf2, 0x95, 0xe6, 0x14, 0x79, 0x0e, 0x0d, 0xae, 0x4e,
	0x29, 0xd3, 0x76, 0xff, 0x7e, 0x6f, 0x76, 0x9f, 0xde, 0xdf, 0xb1, 0x68, 0x29, 0x24, 0x16, 0x80,
	0x3f, 0x2b, 0xd1, 0x59, 0x52, 0x59, 0x2a, 0x88, 0x7d, 0x02, 0x0f, 0xde, 0xa2, 0x3c, 0x74, 0x85,
	0x7c, 0x33, 0xca, 0xc4, 0xf0, 0xe4, 0xf8, 0xc3, 0xa7, 0xc2, 0x89, 0xe2, 0xe7, 0x0c, 0x85, 0x24,
	0x7b, 0xb0, 0x5c, 0x58, 0x89, 0x8e, 0xb1, 0xbd, 0x74, 0x7d, 0x51, 0xad, 0xb4, 0x1d, 0xb0, 0xae,
	0x72, 0x15, 0x29, 0x4b, 0x04, 0x92, 0x97, 0xd0, 0x9e, 0xa7, 0xd0, 0xd6, 0xdd, 0x7f, 0xac, 0xe7,
	0xcf, 0x43, 0xab, 0x7a, 0xfb, 0x05, 0x98, 0xc7, 0x99, 0x27, 0x7c, 0x1e, 0x79, 0xa8, 0x4a, 0x0c,
	0xc6, 0x98, 0x48, 0x9d, 0xb9, 0x0b, 0x2d, 0x7f, 0x14, 0x61, 0x22, 0x9d, 0xb2, 0x35, 0x2d, 0xda,
	0x2c, 0x80, 0x83, 0xc0, 0x3e, 0x84, 0xee, 0xc2, 0xa3, 0x65, 0xb0, 0xa7, 0xd0, 0xc0, 0x1c, 0xd0,
	0x99, 0xee, 0x56, 0x32, 0x55, 0xe4, 0xa5, 0xc8, 0xf6, 0x00, 0xe6, 0x68, 0x5e, 0x58, 0x48, 0x97,
	0x4b, 0x27, 0xc6, 0x33, 0x55, 0x78, 0x85, 0x36, 0x15, 0xf0, 0x0e, 0xcf, 0xc8, 0x3d, 0x58, 0xc6,
	0x24, 0x50, 0x54, 0x5d, 0x51, 0x0d, 0x4c, 0x82, 0x9c, 0xb8, 0xa9, 0x47, 0x9b, 0xb0, 0xae, 0x6a,
	0x1c, 0xb1, 0x2f, 0xe5, 0x0d, 0xed, 0xd7, 0xb0, 0x31, 0x87, 0xca, 0xe4, 0xbb, 0x79, 0xa7, 0x44,
	0x36, 0x9a, 0x45, 0xdf, 0xba, 0x1c, 0x9d, 0x2a, 0x9a, 0x6a, 0x99, 0x1d, 0x41, 0xbb, 0x82, 0xe7,
	0xe9, 0xa5, 0x2b, 0x62, 0x27, 0x71, 0x4f, 0x51, 0x3f, 0x5b, 0x0e, 0x1c, 0xb9, 0xa7, 0x48, 0x3a,
	0xb0, 0x2c, 0x32, 0xdf, 0x47, 0x21, 0x54, 0xfa, 0x26, 0xd5, 0x9f, 0x6a, 0xe2, 0xf3, 0x19, 0x75,
	0x4e, 0x51, 0x08, 0x37, 0x44, 0x75, 0x83, 0x16, 0x5d, 0x51, 0xe0, 0xfb, 0x02, 0xeb, 0xff, 0xac,
	0x43, 0xeb, 0x90, 0x85, 0xfb, 0x2a, 0x0d, 0x61, 0xb0, 0xb5, 0x78, 0x3e, 0xc8, 0x4e, 0x25, 0xf3,
	0xb5, 0x83, 0x69, 0x3e, 0xf9, 0x0f, 0x65, 0xf1, 0x32, 0x76, 0x8d, 0x0c, 0xe1, 0xf6, 0x82, 0xa6,
	0x93, 0x47, 0x15, 0x8f, 0xab, 0xe7, 0xc9, 0x7c, 0x7c, 0x93, 0x4c, 0xd7, 0xd9, 0x35, 0xc8, 0x00,
	0x9a, 0xba, 0x33, 0xc4, 0xbc, 0xdc, 0x80, 0x79, 0x07, 0xcd, 0xee, 0x42, 0x4e, 0x1b, 0xed, 0xf7,
	0x2f, 0x7e, 0x34, 0x8d, 0x5f, 0x13, 0xcb, 0x38, 0x9f, 0x58, 0xc6, 0xef, 0x89, 0x65, 0x7c, 0x9b,
	0x5a, 0xb5, 0xef, 0x53, 0xab, 0x76, 0x3e, 0xb5, 0x6a, 0x17, 0x53, 0xab, 0x06, 0x1b, 0x8c, 0x87,
	0x3d, 0x19, 0xc5, 0xe3, 0x5e, 0x3c, 0x56, 0x3f, 0x27, 0xaf, 0xa1, 0x96, 0xbd, 0x3f, 0x01, 0x00,
	0x00, 0xff, 0xff, 0xe8, 0x4e, 0x8d, 0x4c, 0xf9, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// LogBackupClient is the client API for LogBackup service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LogBackupClient interface {
	GetLastFlushTSOfRegion(ctx context.Context, in *GetLastFlushTSOfRegionRequest, opts ...grpc.CallOption) (*GetLastFlushTSOfRegionResponse, error)
	SubscribeFlushEvent(ctx context.Context, in *SubscribeFlushEventRequest, opts ...grpc.CallOption) (LogBackup_SubscribeFlushEventClient, error)
	FlushNow(ctx context.Context, in *FlushNowRequest, opts ...grpc.CallOption) (*FlushNowResponse, error)
}

type logBackupClient struct {
	cc *grpc.ClientConn
}

func NewLogBackupClient(cc *grpc.ClientConn) LogBackupClient {
	return &logBackupClient{cc}
}

func (c *logBackupClient) GetLastFlushTSOfRegion(ctx context.Context, in *GetLastFlushTSOfRegionRequest, opts ...grpc.CallOption) (*GetLastFlushTSOfRegionResponse, error) {
	out := new(GetLastFlushTSOfRegionResponse)
	err := c.cc.Invoke(ctx, "/logbackup.LogBackup/GetLastFlushTSOfRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *logBackupClient) SubscribeFlushEvent(ctx context.Context, in *SubscribeFlushEventRequest, opts ...grpc.CallOption) (LogBackup_SubscribeFlushEventClient, error) {
	stream, err := c.cc.NewStream(ctx, &_LogBackup_serviceDesc.Streams[0], "/logbackup.LogBackup/SubscribeFlushEvent", opts...)
	if err != nil {
		return nil, err
	}
	x := &logBackupSubscribeFlushEventClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type LogBackup_SubscribeFlushEventClient interface {
	Recv() (*SubscribeFlushEventResponse, error)
	grpc.ClientStream
}

type logBackupSubscribeFlushEventClient struct {
	grpc.ClientStream
}

func (x *logBackupSubscribeFlushEventClient) Recv() (*SubscribeFlushEventResponse, error) {
	m := new(SubscribeFlushEventResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *logBackupClient) FlushNow(ctx context.Context, in *FlushNowRequest, opts ...grpc.CallOption) (*FlushNowResponse, error) {
	out := new(FlushNowResponse)
	err := c.cc.Invoke(ctx, "/logbackup.LogBackup/FlushNow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LogBackupServer is the server API for LogBackup service.
type LogBackupServer interface {
	GetLastFlushTSOfRegion(context.Context, *GetLastFlushTSOfRegionRequest) (*GetLastFlushTSOfRegionResponse, error)
	SubscribeFlushEvent(*SubscribeFlushEventRequest, LogBackup_SubscribeFlushEventServer) error
	FlushNow(context.Context, *FlushNowRequest) (*FlushNowResponse, error)
}

// UnimplementedLogBackupServer can be embedded to have forward compatible implementations.
type UnimplementedLogBackupServer struct {
}

func (*UnimplementedLogBackupServer) GetLastFlushTSOfRegion(ctx context.Context, req *GetLastFlushTSOfRegionRequest) (*GetLastFlushTSOfRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastFlushTSOfRegion not implemented")
}
func (*UnimplementedLogBackupServer) SubscribeFlushEvent(req *SubscribeFlushEventRequest, srv LogBackup_SubscribeFlushEventServer) error {
	return status.Errorf(codes.Unimplemented, "method SubscribeFlushEvent not implemented")
}
func (*UnimplementedLogBackupServer) FlushNow(ctx context.Context, req *FlushNowRequest) (*FlushNowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlushNow not implemented")
}

func RegisterLogBackupServer(s *grpc.Server, srv LogBackupServer) {
	s.RegisterService(&_LogBackup_serviceDesc, srv)
}

func _LogBackup_GetLastFlushTSOfRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastFlushTSOfRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LogBackupServer).GetLastFlushTSOfRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logbackup.LogBackup/GetLastFlushTSOfRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LogBackupServer).GetLastFlushTSOfRegion(ctx, req.(*GetLastFlushTSOfRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LogBackup_SubscribeFlushEvent_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SubscribeFlushEventRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(LogBackupServer).SubscribeFlushEvent(m, &logBackupSubscribeFlushEventServer{stream})
}

type LogBackup_SubscribeFlushEventServer interface {
	Send(*SubscribeFlushEventResponse) error
	grpc.ServerStream
}

type logBackupSubscribeFlushEventServer struct {
	grpc.ServerStream
}

func (x *logBackupSubscribeFlushEventServer) Send(m *SubscribeFlushEventResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _LogBackup_FlushNow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlushNowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LogBackupServer).FlushNow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logbackup.LogBackup/FlushNow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LogBackupServer).FlushNow(ctx, req.(*FlushNowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _LogBackup_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logbackup.LogBackup",
	HandlerType: (*LogBackupServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLastFlushTSOfRegion",
			Handler:    _LogBackup_GetLastFlushTSOfRegion_Handler,
		},
		{
			MethodName: "FlushNow",
			Handler:    _LogBackup_FlushNow_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SubscribeFlushEvent",
			Handler:       _LogBackup_SubscribeFlushEvent_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "logbackuppb.proto",
}

func (m *RegionIdentity) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionIdentity) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionIdentity) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.EpochVersion != 0 {
		i = encodeVarintLogbackuppb(dAtA, i, uint64(m.EpochVersion))
		i--
		dAtA[i] = 0x10
	}
	if m.Id != 0 {
		i = encodeVarintLogbackuppb(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionCheckpoint) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionCheckpoint) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionCheckpoint) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Checkpoint != 0 {
		i = encodeVarintLogbackuppb(dAtA, i, uint64(m.Checkpoint))
		i--
		dAtA[i] = 0x18
	}
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintLogbackuppb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Err != nil {
		{
			size, err := m.Err.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintLogbackuppb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetLastFlushTSOfRegionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLastFlushTSOfRegionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetLastFlushTSOfRegionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Regions) > 0 {
		for iNdEx := len(m.Regions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Regions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogbackuppb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetLastFlushTSOfRegionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLastFlushTSOfRegionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetLastFlushTSOfRegionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Checkpoints) > 0 {
		for iNdEx := len(m.Checkpoints) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Checkpoints[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogbackuppb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SubscribeFlushEventRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SubscribeFlushEventRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SubscribeFlushEventRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ClientId) > 0 {
		i -= len(m.ClientId)
		copy(dAtA[i:], m.ClientId)
		i = encodeVarintLogbackuppb(dAtA, i, uint64(len(m.ClientId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SubscribeFlushEventResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SubscribeFlushEventResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SubscribeFlushEventResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Events) > 0 {
		for iNdEx := len(m.Events) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Events[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogbackuppb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *FlushEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlushEvent) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlushEvent) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Checkpoint != 0 {
		i = encodeVarintLogbackuppb(dAtA, i, uint64(m.Checkpoint))
		i--
		dAtA[i] = 0x18
	}
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintLogbackuppb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintLogbackuppb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FlushNowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlushNowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlushNowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *FlushNowResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlushNowResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlushNowResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Results) > 0 {
		for iNdEx := len(m.Results) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Results[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintLogbackuppb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *FlushResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlushResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlushResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ErrorMessage) > 0 {
		i -= len(m.ErrorMessage)
		copy(dAtA[i:], m.ErrorMessage)
		i = encodeVarintLogbackuppb(dAtA, i, uint64(len(m.ErrorMessage)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Success {
		i--
		if m.Success {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.TaskName) > 0 {
		i -= len(m.TaskName)
		copy(dAtA[i:], m.TaskName)
		i = encodeVarintLogbackuppb(dAtA, i, uint64(len(m.TaskName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintLogbackuppb(dAtA []byte, offset int, v uint64) int {
	offset -= sovLogbackuppb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *RegionIdentity) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovLogbackuppb(uint64(m.Id))
	}
	if m.EpochVersion != 0 {
		n += 1 + sovLogbackuppb(uint64(m.EpochVersion))
	}
	return n
}

func (m *RegionCheckpoint) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Err != nil {
		l = m.Err.Size()
		n += 1 + l + sovLogbackuppb(uint64(l))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovLogbackuppb(uint64(l))
	}
	if m.Checkpoint != 0 {
		n += 1 + sovLogbackuppb(uint64(m.Checkpoint))
	}
	return n
}

func (m *GetLastFlushTSOfRegionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Regions) > 0 {
		for _, e := range m.Regions {
			l = e.Size()
			n += 1 + l + sovLogbackuppb(uint64(l))
		}
	}
	return n
}

func (m *GetLastFlushTSOfRegionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Checkpoints) > 0 {
		for _, e := range m.Checkpoints {
			l = e.Size()
			n += 1 + l + sovLogbackuppb(uint64(l))
		}
	}
	return n
}

func (m *SubscribeFlushEventRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ClientId)
	if l > 0 {
		n += 1 + l + sovLogbackuppb(uint64(l))
	}
	return n
}

func (m *SubscribeFlushEventResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Events) > 0 {
		for _, e := range m.Events {
			l = e.Size()
			n += 1 + l + sovLogbackuppb(uint64(l))
		}
	}
	return n
}

func (m *FlushEvent) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovLogbackuppb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovLogbackuppb(uint64(l))
	}
	if m.Checkpoint != 0 {
		n += 1 + sovLogbackuppb(uint64(m.Checkpoint))
	}
	return n
}

func (m *FlushNowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *FlushNowResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Results) > 0 {
		for _, e := range m.Results {
			l = e.Size()
			n += 1 + l + sovLogbackuppb(uint64(l))
		}
	}
	return n
}

func (m *FlushResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TaskName)
	if l > 0 {
		n += 1 + l + sovLogbackuppb(uint64(l))
	}
	if m.Success {
		n += 2
	}
	l = len(m.ErrorMessage)
	if l > 0 {
		n += 1 + l + sovLogbackuppb(uint64(l))
	}
	return n
}

func sovLogbackuppb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozLogbackuppb(x uint64) (n int) {
	return sovLogbackuppb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RegionIdentity) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionIdentity: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionIdentity: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EpochVersion", wireType)
			}
			m.EpochVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EpochVersion |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionCheckpoint) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionCheckpoint: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionCheckpoint: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Err", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Err == nil {
				m.Err = &errorpb.Error{}
			}
			if err := m.Err.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &RegionIdentity{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checkpoint", wireType)
			}
			m.Checkpoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Checkpoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLastFlushTSOfRegionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetLastFlushTSOfRegionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetLastFlushTSOfRegionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Regions = append(m.Regions, &RegionIdentity{})
			if err := m.Regions[len(m.Regions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLastFlushTSOfRegionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetLastFlushTSOfRegionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetLastFlushTSOfRegionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checkpoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Checkpoints = append(m.Checkpoints, &RegionCheckpoint{})
			if err := m.Checkpoints[len(m.Checkpoints)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SubscribeFlushEventRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SubscribeFlushEventRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SubscribeFlushEventRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SubscribeFlushEventResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SubscribeFlushEventResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SubscribeFlushEventResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Events", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Events = append(m.Events, &FlushEvent{})
			if err := m.Events[len(m.Events)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlushEvent) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlushEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlushEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checkpoint", wireType)
			}
			m.Checkpoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Checkpoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlushNowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlushNowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlushNowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlushNowResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlushNowResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlushNowResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Results", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Results = append(m.Results, &FlushResult{})
			if err := m.Results[len(m.Results)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlushResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlushResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlushResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TaskName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Success", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Success = bool(v != 0)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrorMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLogbackuppb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthLogbackuppb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipLogbackuppb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowLogbackuppb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLogbackuppb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthLogbackuppb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupLogbackuppb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthLogbackuppb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthLogbackuppb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowLogbackuppb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupLogbackuppb = fmt.Errorf("proto: unexpected end of group")
)
