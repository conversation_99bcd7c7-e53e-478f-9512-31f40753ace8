// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: tracepb.proto

package tracepb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type TraceRecordRequest struct {
}

func (m *TraceRecordRequest) Reset()         { *m = TraceRecordRequest{} }
func (m *TraceRecordRequest) String() string { return proto.CompactTextString(m) }
func (*TraceRecordRequest) ProtoMessage()    {}
func (*TraceRecordRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_26aed79969e856c7, []int{0}
}
func (m *TraceRecordRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TraceRecordRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TraceRecordRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TraceRecordRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TraceRecordRequest.Merge(m, src)
}
func (m *TraceRecordRequest) XXX_Size() int {
	return m.Size()
}
func (m *TraceRecordRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TraceRecordRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TraceRecordRequest proto.InternalMessageInfo

type TraceRecord struct {
	// Types that are valid to be assigned to RecordOneof:
	//	*TraceRecord_Report
	//	*TraceRecord_NotifyCollect
	RecordOneof isTraceRecord_RecordOneof `protobuf_oneof:"record_oneof"`
}

func (m *TraceRecord) Reset()         { *m = TraceRecord{} }
func (m *TraceRecord) String() string { return proto.CompactTextString(m) }
func (*TraceRecord) ProtoMessage()    {}
func (*TraceRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_26aed79969e856c7, []int{1}
}
func (m *TraceRecord) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TraceRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TraceRecord.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TraceRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TraceRecord.Merge(m, src)
}
func (m *TraceRecord) XXX_Size() int {
	return m.Size()
}
func (m *TraceRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_TraceRecord.DiscardUnknown(m)
}

var xxx_messageInfo_TraceRecord proto.InternalMessageInfo

type isTraceRecord_RecordOneof interface {
	isTraceRecord_RecordOneof()
	MarshalTo([]byte) (int, error)
	Size() int
}

type TraceRecord_Report struct {
	Report *Report `protobuf:"bytes,1,opt,name=report,proto3,oneof" json:"report,omitempty"`
}
type TraceRecord_NotifyCollect struct {
	NotifyCollect *NotifyCollect `protobuf:"bytes,2,opt,name=notify_collect,json=notifyCollect,proto3,oneof" json:"notify_collect,omitempty"`
}

func (*TraceRecord_Report) isTraceRecord_RecordOneof()        {}
func (*TraceRecord_NotifyCollect) isTraceRecord_RecordOneof() {}

func (m *TraceRecord) GetRecordOneof() isTraceRecord_RecordOneof {
	if m != nil {
		return m.RecordOneof
	}
	return nil
}

func (m *TraceRecord) GetReport() *Report {
	if x, ok := m.GetRecordOneof().(*TraceRecord_Report); ok {
		return x.Report
	}
	return nil
}

func (m *TraceRecord) GetNotifyCollect() *NotifyCollect {
	if x, ok := m.GetRecordOneof().(*TraceRecord_NotifyCollect); ok {
		return x.NotifyCollect
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*TraceRecord) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*TraceRecord_Report)(nil),
		(*TraceRecord_NotifyCollect)(nil),
	}
}

type RemoteParentSpan struct {
	// A unique id to identify the request. It's usually a UUID.
	TraceId uint64 `protobuf:"varint,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	// The span of remote caller that is awaiting the request.
	SpanId uint64 `protobuf:"varint,2,opt,name=span_id,json=spanId,proto3" json:"span_id,omitempty"`
}

func (m *RemoteParentSpan) Reset()         { *m = RemoteParentSpan{} }
func (m *RemoteParentSpan) String() string { return proto.CompactTextString(m) }
func (*RemoteParentSpan) ProtoMessage()    {}
func (*RemoteParentSpan) Descriptor() ([]byte, []int) {
	return fileDescriptor_26aed79969e856c7, []int{2}
}
func (m *RemoteParentSpan) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RemoteParentSpan) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RemoteParentSpan.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RemoteParentSpan) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoteParentSpan.Merge(m, src)
}
func (m *RemoteParentSpan) XXX_Size() int {
	return m.Size()
}
func (m *RemoteParentSpan) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoteParentSpan.DiscardUnknown(m)
}

var xxx_messageInfo_RemoteParentSpan proto.InternalMessageInfo

func (m *RemoteParentSpan) GetTraceId() uint64 {
	if m != nil {
		return m.TraceId
	}
	return 0
}

func (m *RemoteParentSpan) GetSpanId() uint64 {
	if m != nil {
		return m.SpanId
	}
	return 0
}

// The context of the request to be traced.
type TraceContext struct {
	RemoteParentSpans []*RemoteParentSpan `protobuf:"bytes,1,rep,name=remote_parent_spans,json=remoteParentSpans,proto3" json:"remote_parent_spans,omitempty"`
	// Report the trace records only if the duration of handling the request exceeds the threshold.
	DurationThresholdMs uint32 `protobuf:"varint,2,opt,name=duration_threshold_ms,json=durationThresholdMs,proto3" json:"duration_threshold_ms,omitempty"`
}

func (m *TraceContext) Reset()         { *m = TraceContext{} }
func (m *TraceContext) String() string { return proto.CompactTextString(m) }
func (*TraceContext) ProtoMessage()    {}
func (*TraceContext) Descriptor() ([]byte, []int) {
	return fileDescriptor_26aed79969e856c7, []int{3}
}
func (m *TraceContext) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TraceContext) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TraceContext.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TraceContext) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TraceContext.Merge(m, src)
}
func (m *TraceContext) XXX_Size() int {
	return m.Size()
}
func (m *TraceContext) XXX_DiscardUnknown() {
	xxx_messageInfo_TraceContext.DiscardUnknown(m)
}

var xxx_messageInfo_TraceContext proto.InternalMessageInfo

func (m *TraceContext) GetRemoteParentSpans() []*RemoteParentSpan {
	if m != nil {
		return m.RemoteParentSpans
	}
	return nil
}

func (m *TraceContext) GetDurationThresholdMs() uint32 {
	if m != nil {
		return m.DurationThresholdMs
	}
	return 0
}

// Report the spans collected when handling a request on a service.
type Report struct {
	RemoteParentSpans []*RemoteParentSpan `protobuf:"bytes,1,rep,name=remote_parent_spans,json=remoteParentSpans,proto3" json:"remote_parent_spans,omitempty"`
	Spans             []*Span             `protobuf:"bytes,2,rep,name=spans,proto3" json:"spans,omitempty"`
}

func (m *Report) Reset()         { *m = Report{} }
func (m *Report) String() string { return proto.CompactTextString(m) }
func (*Report) ProtoMessage()    {}
func (*Report) Descriptor() ([]byte, []int) {
	return fileDescriptor_26aed79969e856c7, []int{4}
}
func (m *Report) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Report) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Report.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Report) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Report.Merge(m, src)
}
func (m *Report) XXX_Size() int {
	return m.Size()
}
func (m *Report) XXX_DiscardUnknown() {
	xxx_messageInfo_Report.DiscardUnknown(m)
}

var xxx_messageInfo_Report proto.InternalMessageInfo

func (m *Report) GetRemoteParentSpans() []*RemoteParentSpan {
	if m != nil {
		return m.RemoteParentSpans
	}
	return nil
}

func (m *Report) GetSpans() []*Span {
	if m != nil {
		return m.Spans
	}
	return nil
}

// Notify the subscriber to persis the spans of the trace.
type NotifyCollect struct {
	TraceId uint64 `protobuf:"varint,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
}

func (m *NotifyCollect) Reset()         { *m = NotifyCollect{} }
func (m *NotifyCollect) String() string { return proto.CompactTextString(m) }
func (*NotifyCollect) ProtoMessage()    {}
func (*NotifyCollect) Descriptor() ([]byte, []int) {
	return fileDescriptor_26aed79969e856c7, []int{5}
}
func (m *NotifyCollect) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NotifyCollect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NotifyCollect.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *NotifyCollect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyCollect.Merge(m, src)
}
func (m *NotifyCollect) XXX_Size() int {
	return m.Size()
}
func (m *NotifyCollect) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyCollect.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyCollect proto.InternalMessageInfo

func (m *NotifyCollect) GetTraceId() uint64 {
	if m != nil {
		return m.TraceId
	}
	return 0
}

type Span struct {
	// The unique span id within the spans with the same `trace_id`.
	// The most significant 32 bits should be random number generated by each service instance.
	SpanId      uint64      `protobuf:"varint,1,opt,name=span_id,json=spanId,proto3" json:"span_id,omitempty"`
	ParentId    uint64      `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	BeginUnixNs uint64      `protobuf:"varint,3,opt,name=begin_unix_ns,json=beginUnixNs,proto3" json:"begin_unix_ns,omitempty"`
	DurationNs  uint64      `protobuf:"varint,4,opt,name=duration_ns,json=durationNs,proto3" json:"duration_ns,omitempty"`
	Event       string      `protobuf:"bytes,5,opt,name=event,proto3" json:"event,omitempty"`
	Properties  []*Property `protobuf:"bytes,6,rep,name=properties,proto3" json:"properties,omitempty"`
}

func (m *Span) Reset()         { *m = Span{} }
func (m *Span) String() string { return proto.CompactTextString(m) }
func (*Span) ProtoMessage()    {}
func (*Span) Descriptor() ([]byte, []int) {
	return fileDescriptor_26aed79969e856c7, []int{6}
}
func (m *Span) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Span) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Span.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Span) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Span.Merge(m, src)
}
func (m *Span) XXX_Size() int {
	return m.Size()
}
func (m *Span) XXX_DiscardUnknown() {
	xxx_messageInfo_Span.DiscardUnknown(m)
}

var xxx_messageInfo_Span proto.InternalMessageInfo

func (m *Span) GetSpanId() uint64 {
	if m != nil {
		return m.SpanId
	}
	return 0
}

func (m *Span) GetParentId() uint64 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *Span) GetBeginUnixNs() uint64 {
	if m != nil {
		return m.BeginUnixNs
	}
	return 0
}

func (m *Span) GetDurationNs() uint64 {
	if m != nil {
		return m.DurationNs
	}
	return 0
}

func (m *Span) GetEvent() string {
	if m != nil {
		return m.Event
	}
	return ""
}

func (m *Span) GetProperties() []*Property {
	if m != nil {
		return m.Properties
	}
	return nil
}

type Property struct {
	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *Property) Reset()         { *m = Property{} }
func (m *Property) String() string { return proto.CompactTextString(m) }
func (*Property) ProtoMessage()    {}
func (*Property) Descriptor() ([]byte, []int) {
	return fileDescriptor_26aed79969e856c7, []int{7}
}
func (m *Property) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Property) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Property.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Property) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Property.Merge(m, src)
}
func (m *Property) XXX_Size() int {
	return m.Size()
}
func (m *Property) XXX_DiscardUnknown() {
	xxx_messageInfo_Property.DiscardUnknown(m)
}

var xxx_messageInfo_Property proto.InternalMessageInfo

func (m *Property) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Property) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func init() {
	proto.RegisterType((*TraceRecordRequest)(nil), "tracepb.TraceRecordRequest")
	proto.RegisterType((*TraceRecord)(nil), "tracepb.TraceRecord")
	proto.RegisterType((*RemoteParentSpan)(nil), "tracepb.RemoteParentSpan")
	proto.RegisterType((*TraceContext)(nil), "tracepb.TraceContext")
	proto.RegisterType((*Report)(nil), "tracepb.Report")
	proto.RegisterType((*NotifyCollect)(nil), "tracepb.NotifyCollect")
	proto.RegisterType((*Span)(nil), "tracepb.Span")
	proto.RegisterType((*Property)(nil), "tracepb.Property")
}

func init() { proto.RegisterFile("tracepb.proto", fileDescriptor_26aed79969e856c7) }

var fileDescriptor_26aed79969e856c7 = []byte{
	// 551 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x53, 0xcd, 0x6e, 0xd3, 0x4c,
	0x14, 0xf5, 0xf4, 0x27, 0x4d, 0x6e, 0xea, 0xfe, 0x4c, 0xf3, 0x7d, 0xb8, 0xad, 0x64, 0x2a, 0xb3,
	0x29, 0x2c, 0x02, 0x98, 0x07, 0x40, 0x4a, 0x25, 0xd4, 0x2c, 0x88, 0xa2, 0x49, 0x11, 0x4b, 0xcb,
	0x8e, 0xa7, 0xa9, 0x95, 0x74, 0xc6, 0xcc, 0x8c, 0xa3, 0xe4, 0x11, 0x58, 0x20, 0xb1, 0xe4, 0x11,
	0x78, 0x14, 0xc4, 0xaa, 0xcb, 0x2e, 0x51, 0xf2, 0x22, 0xc8, 0xd7, 0x89, 0xe5, 0x02, 0x62, 0xc5,
	0xca, 0x73, 0xcf, 0xb9, 0x67, 0xee, 0x3d, 0xc7, 0x1a, 0xb0, 0x8d, 0x0a, 0x87, 0x3c, 0x8d, 0xda,
	0xa9, 0x92, 0x46, 0xd2, 0x9d, 0x55, 0x79, 0xd2, 0x1a, 0xc9, 0x91, 0x44, 0xec, 0x79, 0x7e, 0x2a,
	0xe8, 0x93, 0x7d, 0x95, 0x69, 0x83, 0xc7, 0x02, 0xf0, 0x5a, 0x40, 0xaf, 0x72, 0x05, 0xe3, 0x43,
	0xa9, 0x62, 0xc6, 0x3f, 0x64, 0x5c, 0x1b, 0xef, 0x23, 0x81, 0x66, 0x05, 0xa6, 0x4f, 0xa1, 0xa6,
	0x78, 0x2a, 0x95, 0x71, 0xc8, 0x19, 0x39, 0x6f, 0xfa, 0xfb, 0xed, 0xf5, 0x54, 0x86, 0xf0, 0xa5,
	0xc5, 0x56, 0x0d, 0xf4, 0x35, 0xec, 0x09, 0x69, 0x92, 0xeb, 0x79, 0x30, 0x94, 0x93, 0x09, 0x1f,
	0x1a, 0x67, 0x03, 0x25, 0xff, 0x97, 0x92, 0x1e, 0xd2, 0x17, 0x05, 0x7b, 0x69, 0x31, 0x5b, 0x54,
	0x81, 0xce, 0x1e, 0xec, 0x2a, 0x9c, 0x1a, 0x48, 0xc1, 0xe5, 0xb5, 0xf7, 0x06, 0x0e, 0x18, 0xbf,
	0x95, 0x86, 0xf7, 0x43, 0xc5, 0x85, 0x19, 0xa4, 0xa1, 0xa0, 0xc7, 0x50, 0xc7, 0xdb, 0x82, 0x24,
	0xc6, 0x8d, 0xb6, 0x58, 0xe1, 0xbb, 0x1b, 0xd3, 0x47, 0xb0, 0xa3, 0xd3, 0x50, 0xe4, 0xcc, 0x06,
	0x32, 0xb5, 0xbc, 0xec, 0xc6, 0xde, 0x27, 0x02, 0xbb, 0xe8, 0xe9, 0x42, 0x0a, 0xc3, 0x67, 0x86,
	0x76, 0xe1, 0x48, 0xe1, 0xc5, 0x41, 0x8a, 0x37, 0x07, 0x79, 0xa3, 0x76, 0xc8, 0xd9, 0xe6, 0x79,
	0xd3, 0x3f, 0xae, 0x38, 0x7c, 0x38, 0x9c, 0x1d, 0xaa, 0x5f, 0x10, 0x4d, 0x7d, 0xf8, 0x2f, 0xce,
	0x54, 0x68, 0x12, 0x29, 0x02, 0x73, 0xa3, 0xb8, 0xbe, 0x91, 0x93, 0x38, 0xb8, 0xd5, 0xb8, 0x82,
	0xcd, 0x8e, 0xd6, 0xe4, 0xd5, 0x9a, 0x7b, 0xab, 0xbd, 0x19, 0xd4, 0x8a, 0xf0, 0xfe, 0xe5, 0x22,
	0x4f, 0x60, 0xbb, 0x10, 0x6f, 0xa0, 0xd8, 0x2e, 0xc5, 0x28, 0x28, 0x38, 0xef, 0x19, 0xd8, 0x0f,
	0xfe, 0xc1, 0x5f, 0xe2, 0xf4, 0xbe, 0x13, 0xd8, 0xc2, 0xc8, 0x2b, 0xb9, 0x92, 0x6a, 0xae, 0xf4,
	0x14, 0x1a, 0xab, 0xb5, 0xcb, 0xc8, 0xeb, 0x05, 0xd0, 0x8d, 0xa9, 0x07, 0x76, 0xc4, 0x47, 0x89,
	0x08, 0x32, 0x91, 0xcc, 0x02, 0xa1, 0x9d, 0x4d, 0x6c, 0x68, 0x22, 0xf8, 0x4e, 0x24, 0xb3, 0x9e,
	0xa6, 0x8f, 0xa1, 0x59, 0x86, 0x27, 0xb4, 0xb3, 0x85, 0x1d, 0xb0, 0x86, 0x7a, 0x9a, 0xb6, 0x60,
	0x9b, 0x4f, 0xb9, 0x30, 0xce, 0xf6, 0x19, 0x39, 0x6f, 0xb0, 0xa2, 0xa0, 0x2f, 0x01, 0x52, 0x25,
	0x53, 0xae, 0x4c, 0xc2, 0xb5, 0x53, 0x43, 0xbf, 0x87, 0xa5, 0xdf, 0x7e, 0x41, 0xcd, 0x59, 0xa5,
	0xc9, 0xf3, 0xa1, 0xbe, 0xc6, 0xe9, 0x01, 0x6c, 0x8e, 0xf9, 0x1c, 0xbd, 0x34, 0x58, 0x7e, 0xcc,
	0xc7, 0x4c, 0xc3, 0x49, 0xc6, 0xd1, 0x44, 0x83, 0x15, 0x85, 0xff, 0x1e, 0x0e, 0x2b, 0x2f, 0xa1,
	0x9f, 0x45, 0x83, 0x2c, 0xa2, 0x1d, 0x68, 0x0c, 0xb2, 0x48, 0x0f, 0x55, 0x12, 0x71, 0x7a, 0x5a,
	0x0e, 0xfd, 0xfd, 0x25, 0x9d, 0xb4, 0xfe, 0x44, 0x7a, 0xd6, 0x0b, 0xd2, 0xf1, 0xef, 0xbf, 0xd6,
	0xc9, 0xb7, 0x85, 0x4b, 0xee, 0x16, 0x2e, 0xf9, 0xb1, 0x70, 0xc9, 0xe7, 0xa5, 0x6b, 0x7d, 0x59,
	0xba, 0xd6, 0xdd, 0xd2, 0xb5, 0xee, 0x97, 0xae, 0x05, 0x07, 0x52, 0x8d, 0xda, 0x26, 0x19, 0x4f,
	0xdb, 0xe3, 0x29, 0xbe, 0xd6, 0xa8, 0x86, 0x9f, 0x57, 0x3f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x70,
	0xd5, 0x1e, 0x1c, 0xf5, 0x03, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TraceRecordPubSubClient is the client API for TraceRecordPubSub service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TraceRecordPubSubClient interface {
	// Subscribe the Trace records generated on this service. The service will periodically (e.g. per minute)
	// publishes Trace records to clients via gRPC stream.
	Subscribe(ctx context.Context, in *TraceRecordRequest, opts ...grpc.CallOption) (TraceRecordPubSub_SubscribeClient, error)
}

type traceRecordPubSubClient struct {
	cc *grpc.ClientConn
}

func NewTraceRecordPubSubClient(cc *grpc.ClientConn) TraceRecordPubSubClient {
	return &traceRecordPubSubClient{cc}
}

func (c *traceRecordPubSubClient) Subscribe(ctx context.Context, in *TraceRecordRequest, opts ...grpc.CallOption) (TraceRecordPubSub_SubscribeClient, error) {
	stream, err := c.cc.NewStream(ctx, &_TraceRecordPubSub_serviceDesc.Streams[0], "/tracepb.TraceRecordPubSub/Subscribe", opts...)
	if err != nil {
		return nil, err
	}
	x := &traceRecordPubSubSubscribeClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type TraceRecordPubSub_SubscribeClient interface {
	Recv() (*TraceRecord, error)
	grpc.ClientStream
}

type traceRecordPubSubSubscribeClient struct {
	grpc.ClientStream
}

func (x *traceRecordPubSubSubscribeClient) Recv() (*TraceRecord, error) {
	m := new(TraceRecord)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// TraceRecordPubSubServer is the server API for TraceRecordPubSub service.
type TraceRecordPubSubServer interface {
	// Subscribe the Trace records generated on this service. The service will periodically (e.g. per minute)
	// publishes Trace records to clients via gRPC stream.
	Subscribe(*TraceRecordRequest, TraceRecordPubSub_SubscribeServer) error
}

// UnimplementedTraceRecordPubSubServer can be embedded to have forward compatible implementations.
type UnimplementedTraceRecordPubSubServer struct {
}

func (*UnimplementedTraceRecordPubSubServer) Subscribe(req *TraceRecordRequest, srv TraceRecordPubSub_SubscribeServer) error {
	return status.Errorf(codes.Unimplemented, "method Subscribe not implemented")
}

func RegisterTraceRecordPubSubServer(s *grpc.Server, srv TraceRecordPubSubServer) {
	s.RegisterService(&_TraceRecordPubSub_serviceDesc, srv)
}

func _TraceRecordPubSub_Subscribe_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TraceRecordRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TraceRecordPubSubServer).Subscribe(m, &traceRecordPubSubSubscribeServer{stream})
}

type TraceRecordPubSub_SubscribeServer interface {
	Send(*TraceRecord) error
	grpc.ServerStream
}

type traceRecordPubSubSubscribeServer struct {
	grpc.ServerStream
}

func (x *traceRecordPubSubSubscribeServer) Send(m *TraceRecord) error {
	return x.ServerStream.SendMsg(m)
}

var _TraceRecordPubSub_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tracepb.TraceRecordPubSub",
	HandlerType: (*TraceRecordPubSubServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Subscribe",
			Handler:       _TraceRecordPubSub_Subscribe_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "tracepb.proto",
}

func (m *TraceRecordRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TraceRecordRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TraceRecordRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *TraceRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TraceRecord) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TraceRecord) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RecordOneof != nil {
		{
			size := m.RecordOneof.Size()
			i -= size
			if _, err := m.RecordOneof.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *TraceRecord_Report) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TraceRecord_Report) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Report != nil {
		{
			size, err := m.Report.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTracepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *TraceRecord_NotifyCollect) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TraceRecord_NotifyCollect) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.NotifyCollect != nil {
		{
			size, err := m.NotifyCollect.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTracepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *RemoteParentSpan) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoteParentSpan) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RemoteParentSpan) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SpanId != 0 {
		i = encodeVarintTracepb(dAtA, i, uint64(m.SpanId))
		i--
		dAtA[i] = 0x10
	}
	if m.TraceId != 0 {
		i = encodeVarintTracepb(dAtA, i, uint64(m.TraceId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TraceContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TraceContext) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TraceContext) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DurationThresholdMs != 0 {
		i = encodeVarintTracepb(dAtA, i, uint64(m.DurationThresholdMs))
		i--
		dAtA[i] = 0x10
	}
	if len(m.RemoteParentSpans) > 0 {
		for iNdEx := len(m.RemoteParentSpans) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RemoteParentSpans[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTracepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Report) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Report) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Report) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Spans) > 0 {
		for iNdEx := len(m.Spans) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Spans[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTracepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.RemoteParentSpans) > 0 {
		for iNdEx := len(m.RemoteParentSpans) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RemoteParentSpans[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTracepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *NotifyCollect) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotifyCollect) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *NotifyCollect) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TraceId != 0 {
		i = encodeVarintTracepb(dAtA, i, uint64(m.TraceId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Span) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Span) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Span) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Properties) > 0 {
		for iNdEx := len(m.Properties) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Properties[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTracepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Event) > 0 {
		i -= len(m.Event)
		copy(dAtA[i:], m.Event)
		i = encodeVarintTracepb(dAtA, i, uint64(len(m.Event)))
		i--
		dAtA[i] = 0x2a
	}
	if m.DurationNs != 0 {
		i = encodeVarintTracepb(dAtA, i, uint64(m.DurationNs))
		i--
		dAtA[i] = 0x20
	}
	if m.BeginUnixNs != 0 {
		i = encodeVarintTracepb(dAtA, i, uint64(m.BeginUnixNs))
		i--
		dAtA[i] = 0x18
	}
	if m.ParentId != 0 {
		i = encodeVarintTracepb(dAtA, i, uint64(m.ParentId))
		i--
		dAtA[i] = 0x10
	}
	if m.SpanId != 0 {
		i = encodeVarintTracepb(dAtA, i, uint64(m.SpanId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Property) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Property) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Property) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintTracepb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintTracepb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintTracepb(dAtA []byte, offset int, v uint64) int {
	offset -= sovTracepb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *TraceRecordRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *TraceRecord) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RecordOneof != nil {
		n += m.RecordOneof.Size()
	}
	return n
}

func (m *TraceRecord_Report) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Report != nil {
		l = m.Report.Size()
		n += 1 + l + sovTracepb(uint64(l))
	}
	return n
}
func (m *TraceRecord_NotifyCollect) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.NotifyCollect != nil {
		l = m.NotifyCollect.Size()
		n += 1 + l + sovTracepb(uint64(l))
	}
	return n
}
func (m *RemoteParentSpan) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TraceId != 0 {
		n += 1 + sovTracepb(uint64(m.TraceId))
	}
	if m.SpanId != 0 {
		n += 1 + sovTracepb(uint64(m.SpanId))
	}
	return n
}

func (m *TraceContext) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.RemoteParentSpans) > 0 {
		for _, e := range m.RemoteParentSpans {
			l = e.Size()
			n += 1 + l + sovTracepb(uint64(l))
		}
	}
	if m.DurationThresholdMs != 0 {
		n += 1 + sovTracepb(uint64(m.DurationThresholdMs))
	}
	return n
}

func (m *Report) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.RemoteParentSpans) > 0 {
		for _, e := range m.RemoteParentSpans {
			l = e.Size()
			n += 1 + l + sovTracepb(uint64(l))
		}
	}
	if len(m.Spans) > 0 {
		for _, e := range m.Spans {
			l = e.Size()
			n += 1 + l + sovTracepb(uint64(l))
		}
	}
	return n
}

func (m *NotifyCollect) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TraceId != 0 {
		n += 1 + sovTracepb(uint64(m.TraceId))
	}
	return n
}

func (m *Span) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SpanId != 0 {
		n += 1 + sovTracepb(uint64(m.SpanId))
	}
	if m.ParentId != 0 {
		n += 1 + sovTracepb(uint64(m.ParentId))
	}
	if m.BeginUnixNs != 0 {
		n += 1 + sovTracepb(uint64(m.BeginUnixNs))
	}
	if m.DurationNs != 0 {
		n += 1 + sovTracepb(uint64(m.DurationNs))
	}
	l = len(m.Event)
	if l > 0 {
		n += 1 + l + sovTracepb(uint64(l))
	}
	if len(m.Properties) > 0 {
		for _, e := range m.Properties {
			l = e.Size()
			n += 1 + l + sovTracepb(uint64(l))
		}
	}
	return n
}

func (m *Property) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovTracepb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovTracepb(uint64(l))
	}
	return n
}

func sovTracepb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozTracepb(x uint64) (n int) {
	return sovTracepb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *TraceRecordRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TraceRecordRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TraceRecordRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTracepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTracepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TraceRecord) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TraceRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TraceRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Report", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Report{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.RecordOneof = &TraceRecord_Report{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NotifyCollect", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &NotifyCollect{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.RecordOneof = &TraceRecord_NotifyCollect{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTracepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTracepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoteParentSpan) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RemoteParentSpan: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RemoteParentSpan: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TraceId", wireType)
			}
			m.TraceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TraceId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpanId", wireType)
			}
			m.SpanId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpanId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTracepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTracepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TraceContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TraceContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TraceContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RemoteParentSpans", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RemoteParentSpans = append(m.RemoteParentSpans, &RemoteParentSpan{})
			if err := m.RemoteParentSpans[len(m.RemoteParentSpans)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationThresholdMs", wireType)
			}
			m.DurationThresholdMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DurationThresholdMs |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTracepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTracepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Report) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Report: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Report: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RemoteParentSpans", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RemoteParentSpans = append(m.RemoteParentSpans, &RemoteParentSpan{})
			if err := m.RemoteParentSpans[len(m.RemoteParentSpans)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spans", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Spans = append(m.Spans, &Span{})
			if err := m.Spans[len(m.Spans)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTracepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTracepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotifyCollect) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NotifyCollect: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NotifyCollect: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TraceId", wireType)
			}
			m.TraceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TraceId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTracepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTracepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Span) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Span: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Span: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpanId", wireType)
			}
			m.SpanId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpanId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParentId", wireType)
			}
			m.ParentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BeginUnixNs", wireType)
			}
			m.BeginUnixNs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginUnixNs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationNs", wireType)
			}
			m.DurationNs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DurationNs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Event", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Event = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Properties", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Properties = append(m.Properties, &Property{})
			if err := m.Properties[len(m.Properties)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTracepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTracepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Property) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Property: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Property: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTracepb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthTracepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTracepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTracepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipTracepb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTracepb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTracepb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthTracepb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupTracepb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthTracepb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthTracepb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTracepb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupTracepb = fmt.Errorf("proto: unexpected end of group")
)
