// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: resource_usage_agent.proto

package resource_usage_agent

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ResourceMeteringRequest struct {
}

func (m *ResourceMeteringRequest) Reset()         { *m = ResourceMeteringRequest{} }
func (m *ResourceMeteringRequest) String() string { return proto.CompactTextString(m) }
func (*ResourceMeteringRequest) ProtoMessage()    {}
func (*ResourceMeteringRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_14aca67aa7bfb0f2, []int{0}
}
func (m *ResourceMeteringRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResourceMeteringRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResourceMeteringRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResourceMeteringRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourceMeteringRequest.Merge(m, src)
}
func (m *ResourceMeteringRequest) XXX_Size() int {
	return m.Size()
}
func (m *ResourceMeteringRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourceMeteringRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ResourceMeteringRequest proto.InternalMessageInfo

type EmptyResponse struct {
}

func (m *EmptyResponse) Reset()         { *m = EmptyResponse{} }
func (m *EmptyResponse) String() string { return proto.CompactTextString(m) }
func (*EmptyResponse) ProtoMessage()    {}
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_14aca67aa7bfb0f2, []int{1}
}
func (m *EmptyResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EmptyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EmptyResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EmptyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyResponse.Merge(m, src)
}
func (m *EmptyResponse) XXX_Size() int {
	return m.Size()
}
func (m *EmptyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyResponse proto.InternalMessageInfo

type ResourceUsageRecord struct {
	// Types that are valid to be assigned to RecordOneof:
	//	*ResourceUsageRecord_Record
	RecordOneof isResourceUsageRecord_RecordOneof `protobuf_oneof:"record_oneof"`
}

func (m *ResourceUsageRecord) Reset()         { *m = ResourceUsageRecord{} }
func (m *ResourceUsageRecord) String() string { return proto.CompactTextString(m) }
func (*ResourceUsageRecord) ProtoMessage()    {}
func (*ResourceUsageRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_14aca67aa7bfb0f2, []int{2}
}
func (m *ResourceUsageRecord) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResourceUsageRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResourceUsageRecord.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResourceUsageRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourceUsageRecord.Merge(m, src)
}
func (m *ResourceUsageRecord) XXX_Size() int {
	return m.Size()
}
func (m *ResourceUsageRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourceUsageRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ResourceUsageRecord proto.InternalMessageInfo

type isResourceUsageRecord_RecordOneof interface {
	isResourceUsageRecord_RecordOneof()
	MarshalTo([]byte) (int, error)
	Size() int
}

type ResourceUsageRecord_Record struct {
	Record *GroupTagRecord `protobuf:"bytes,1,opt,name=record,proto3,oneof" json:"record,omitempty"`
}

func (*ResourceUsageRecord_Record) isResourceUsageRecord_RecordOneof() {}

func (m *ResourceUsageRecord) GetRecordOneof() isResourceUsageRecord_RecordOneof {
	if m != nil {
		return m.RecordOneof
	}
	return nil
}

func (m *ResourceUsageRecord) GetRecord() *GroupTagRecord {
	if x, ok := m.GetRecordOneof().(*ResourceUsageRecord_Record); ok {
		return x.Record
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*ResourceUsageRecord) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*ResourceUsageRecord_Record)(nil),
	}
}

// GroupTagRecord is a set of resource usage data grouped by resource_group_tag.
type GroupTagRecord struct {
	ResourceGroupTag []byte                `protobuf:"bytes,1,opt,name=resource_group_tag,json=resourceGroupTag,proto3" json:"resource_group_tag,omitempty"`
	Items            []*GroupTagRecordItem `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (m *GroupTagRecord) Reset()         { *m = GroupTagRecord{} }
func (m *GroupTagRecord) String() string { return proto.CompactTextString(m) }
func (*GroupTagRecord) ProtoMessage()    {}
func (*GroupTagRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_14aca67aa7bfb0f2, []int{3}
}
func (m *GroupTagRecord) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GroupTagRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GroupTagRecord.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GroupTagRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTagRecord.Merge(m, src)
}
func (m *GroupTagRecord) XXX_Size() int {
	return m.Size()
}
func (m *GroupTagRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTagRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTagRecord proto.InternalMessageInfo

func (m *GroupTagRecord) GetResourceGroupTag() []byte {
	if m != nil {
		return m.ResourceGroupTag
	}
	return nil
}

func (m *GroupTagRecord) GetItems() []*GroupTagRecordItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type GroupTagRecordItem struct {
	TimestampSec uint64 `protobuf:"varint,1,opt,name=timestamp_sec,json=timestampSec,proto3" json:"timestamp_sec,omitempty"`
	CpuTimeMs    uint32 `protobuf:"varint,2,opt,name=cpu_time_ms,json=cpuTimeMs,proto3" json:"cpu_time_ms,omitempty"`
	ReadKeys     uint32 `protobuf:"varint,3,opt,name=read_keys,json=readKeys,proto3" json:"read_keys,omitempty"`
	WriteKeys    uint32 `protobuf:"varint,4,opt,name=write_keys,json=writeKeys,proto3" json:"write_keys,omitempty"`
}

func (m *GroupTagRecordItem) Reset()         { *m = GroupTagRecordItem{} }
func (m *GroupTagRecordItem) String() string { return proto.CompactTextString(m) }
func (*GroupTagRecordItem) ProtoMessage()    {}
func (*GroupTagRecordItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_14aca67aa7bfb0f2, []int{4}
}
func (m *GroupTagRecordItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GroupTagRecordItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GroupTagRecordItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GroupTagRecordItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupTagRecordItem.Merge(m, src)
}
func (m *GroupTagRecordItem) XXX_Size() int {
	return m.Size()
}
func (m *GroupTagRecordItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupTagRecordItem.DiscardUnknown(m)
}

var xxx_messageInfo_GroupTagRecordItem proto.InternalMessageInfo

func (m *GroupTagRecordItem) GetTimestampSec() uint64 {
	if m != nil {
		return m.TimestampSec
	}
	return 0
}

func (m *GroupTagRecordItem) GetCpuTimeMs() uint32 {
	if m != nil {
		return m.CpuTimeMs
	}
	return 0
}

func (m *GroupTagRecordItem) GetReadKeys() uint32 {
	if m != nil {
		return m.ReadKeys
	}
	return 0
}

func (m *GroupTagRecordItem) GetWriteKeys() uint32 {
	if m != nil {
		return m.WriteKeys
	}
	return 0
}

func init() {
	proto.RegisterType((*ResourceMeteringRequest)(nil), "resource_usage_agent.ResourceMeteringRequest")
	proto.RegisterType((*EmptyResponse)(nil), "resource_usage_agent.EmptyResponse")
	proto.RegisterType((*ResourceUsageRecord)(nil), "resource_usage_agent.ResourceUsageRecord")
	proto.RegisterType((*GroupTagRecord)(nil), "resource_usage_agent.GroupTagRecord")
	proto.RegisterType((*GroupTagRecordItem)(nil), "resource_usage_agent.GroupTagRecordItem")
}

func init() { proto.RegisterFile("resource_usage_agent.proto", fileDescriptor_14aca67aa7bfb0f2) }

var fileDescriptor_14aca67aa7bfb0f2 = []byte{
	// 443 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x52, 0x4d, 0x6b, 0xd4, 0x40,
	0x18, 0xce, 0xd8, 0xba, 0x74, 0xdf, 0xdd, 0x6d, 0xcb, 0x58, 0x74, 0x8d, 0x18, 0x4a, 0xea, 0x21,
	0x82, 0xae, 0x12, 0xef, 0x05, 0x0b, 0xa2, 0x22, 0x05, 0x99, 0xad, 0x37, 0x21, 0x24, 0xe9, 0xeb,
	0x30, 0x2c, 0xd9, 0x19, 0xe7, 0xa3, 0xb2, 0x17, 0x2f, 0xfe, 0x01, 0x2f, 0x82, 0x3f, 0xc1, 0x9f,
	0xe2, 0xb1, 0xc7, 0x1e, 0x65, 0xf3, 0x47, 0x24, 0x93, 0x6e, 0x61, 0x6b, 0x90, 0x9e, 0xf2, 0xf2,
	0x7c, 0xe4, 0x79, 0x66, 0xe6, 0x85, 0x50, 0xa3, 0x91, 0x4e, 0x97, 0x98, 0x39, 0x93, 0x73, 0xcc,
	0x72, 0x8e, 0x73, 0x3b, 0x51, 0x5a, 0x5a, 0x49, 0xf7, 0xba, 0xb8, 0x70, 0x8f, 0x4b, 0x2e, 0xbd,
	0xe0, 0x59, 0x33, 0xb5, 0xda, 0x70, 0x47, 0x3b, 0x63, 0xfd, 0xd8, 0x02, 0xf1, 0x7d, 0xb8, 0xc7,
	0x2e, 0xed, 0xc7, 0x68, 0x51, 0x8b, 0x39, 0x67, 0xf8, 0xd9, 0xa1, 0xb1, 0xf1, 0x0e, 0x8c, 0x5e,
	0x55, 0xca, 0x2e, 0x18, 0x1a, 0x25, 0xe7, 0x06, 0x63, 0x84, 0x3b, 0x2b, 0xed, 0x87, 0x26, 0x89,
	0x61, 0x29, 0xf5, 0x29, 0x3d, 0x84, 0x9e, 0xf6, 0xd3, 0x98, 0xec, 0x93, 0x64, 0x90, 0x3e, 0x9a,
	0x74, 0x96, 0x7d, 0xad, 0xa5, 0x53, 0x27, 0x39, 0x6f, 0x5d, 0x6f, 0x02, 0x76, 0xe9, 0x3a, 0xda,
	0x86, 0x61, 0x3b, 0x65, 0x72, 0x8e, 0xf2, 0x53, 0xfc, 0x15, 0xb6, 0xd7, 0xb5, 0xf4, 0x09, 0xd0,
	0xab, 0x5f, 0xf2, 0x86, 0xca, 0x6c, 0xce, 0x7d, 0xda, 0x90, 0xed, 0xae, 0x98, 0x95, 0x87, 0x1e,
	0xc2, 0x6d, 0x61, 0xb1, 0x32, 0xe3, 0x5b, 0xfb, 0x1b, 0xc9, 0x20, 0x4d, 0x6e, 0x52, 0xe7, 0xad,
	0xc5, 0x8a, 0xb5, 0xb6, 0xf8, 0x07, 0x01, 0xfa, 0x2f, 0x4b, 0x0f, 0x60, 0x64, 0x45, 0x85, 0xc6,
	0xe6, 0x95, 0xca, 0x0c, 0x96, 0x3e, 0x7f, 0x93, 0x0d, 0xaf, 0xc0, 0x29, 0x96, 0x34, 0x82, 0x41,
	0xa9, 0x5c, 0xd6, 0x60, 0x99, 0x6f, 0x40, 0x92, 0x11, 0xeb, 0x97, 0xca, 0x9d, 0x88, 0x0a, 0x8f,
	0x0d, 0x7d, 0x00, 0x7d, 0x8d, 0xf9, 0x69, 0x36, 0xc3, 0x85, 0x19, 0x6f, 0x78, 0x76, 0xab, 0x01,
	0xde, 0xe1, 0xc2, 0xd0, 0x87, 0x00, 0x5f, 0xb4, 0xb0, 0xd8, 0xb2, 0x9b, 0xad, 0xd7, 0x23, 0x0d,
	0x9d, 0x6a, 0xa0, 0x6b, 0xd7, 0xff, 0xb2, 0x39, 0x07, 0xfd, 0x08, 0x3d, 0x86, 0x4a, 0x6a, 0x4b,
	0x1f, 0x77, 0x1f, 0xb4, 0xe3, 0xc9, 0xc2, 0x83, 0x6e, 0xe9, 0xfa, 0x73, 0x07, 0x09, 0x49, 0xbf,
	0x11, 0xb8, 0x7b, 0x7d, 0x3f, 0xde, 0xbb, 0x62, 0xea, 0x0a, 0x2a, 0xa0, 0x3f, 0x75, 0x85, 0x29,
	0xb5, 0x28, 0x90, 0x3e, 0xfd, 0x7f, 0xf6, 0xb5, 0xd5, 0x0a, 0x6f, 0x5e, 0x35, 0x0e, 0x9e, 0x93,
	0xa3, 0xf4, 0xe2, 0xd7, 0x16, 0xf9, 0xbd, 0x8c, 0xc8, 0xf9, 0x32, 0x22, 0x7f, 0x96, 0x11, 0xf9,
	0x5e, 0x47, 0xc1, 0xcf, 0x3a, 0x0a, 0xce, 0xeb, 0x28, 0xb8, 0xa8, 0xa3, 0x00, 0x76, 0xa5, 0xe6,
	0x13, 0x2b, 0x66, 0x67, 0x93, 0xd9, 0x99, 0x5f, 0xec, 0xa2, 0xe7, 0x3f, 0x2f, 0xfe, 0x06, 0x00,
	0x00, 0xff, 0xff, 0x92, 0xa3, 0x5a, 0xf4, 0x3a, 0x03, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ResourceUsageAgentClient is the client API for ResourceUsageAgent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ResourceUsageAgentClient interface {
	// Report the resource usage records. By default, the records with the same
	// resource group tag will be batched by minute.
	Report(ctx context.Context, opts ...grpc.CallOption) (ResourceUsageAgent_ReportClient, error)
}

type resourceUsageAgentClient struct {
	cc *grpc.ClientConn
}

func NewResourceUsageAgentClient(cc *grpc.ClientConn) ResourceUsageAgentClient {
	return &resourceUsageAgentClient{cc}
}

func (c *resourceUsageAgentClient) Report(ctx context.Context, opts ...grpc.CallOption) (ResourceUsageAgent_ReportClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ResourceUsageAgent_serviceDesc.Streams[0], "/resource_usage_agent.ResourceUsageAgent/Report", opts...)
	if err != nil {
		return nil, err
	}
	x := &resourceUsageAgentReportClient{stream}
	return x, nil
}

type ResourceUsageAgent_ReportClient interface {
	Send(*ResourceUsageRecord) error
	CloseAndRecv() (*EmptyResponse, error)
	grpc.ClientStream
}

type resourceUsageAgentReportClient struct {
	grpc.ClientStream
}

func (x *resourceUsageAgentReportClient) Send(m *ResourceUsageRecord) error {
	return x.ClientStream.SendMsg(m)
}

func (x *resourceUsageAgentReportClient) CloseAndRecv() (*EmptyResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(EmptyResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ResourceUsageAgentServer is the server API for ResourceUsageAgent service.
type ResourceUsageAgentServer interface {
	// Report the resource usage records. By default, the records with the same
	// resource group tag will be batched by minute.
	Report(ResourceUsageAgent_ReportServer) error
}

// UnimplementedResourceUsageAgentServer can be embedded to have forward compatible implementations.
type UnimplementedResourceUsageAgentServer struct {
}

func (*UnimplementedResourceUsageAgentServer) Report(srv ResourceUsageAgent_ReportServer) error {
	return status.Errorf(codes.Unimplemented, "method Report not implemented")
}

func RegisterResourceUsageAgentServer(s *grpc.Server, srv ResourceUsageAgentServer) {
	s.RegisterService(&_ResourceUsageAgent_serviceDesc, srv)
}

func _ResourceUsageAgent_Report_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ResourceUsageAgentServer).Report(&resourceUsageAgentReportServer{stream})
}

type ResourceUsageAgent_ReportServer interface {
	SendAndClose(*EmptyResponse) error
	Recv() (*ResourceUsageRecord, error)
	grpc.ServerStream
}

type resourceUsageAgentReportServer struct {
	grpc.ServerStream
}

func (x *resourceUsageAgentReportServer) SendAndClose(m *EmptyResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *resourceUsageAgentReportServer) Recv() (*ResourceUsageRecord, error) {
	m := new(ResourceUsageRecord)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _ResourceUsageAgent_serviceDesc = grpc.ServiceDesc{
	ServiceName: "resource_usage_agent.ResourceUsageAgent",
	HandlerType: (*ResourceUsageAgentServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Report",
			Handler:       _ResourceUsageAgent_Report_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "resource_usage_agent.proto",
}

// ResourceMeteringPubSubClient is the client API for ResourceMeteringPubSub service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ResourceMeteringPubSubClient interface {
	// Clients subscribe to resource metering records through this RPC, and TiKV periodically (e.g. per minute)
	// publishes resource metering records to clients via gRPC stream.
	Subscribe(ctx context.Context, in *ResourceMeteringRequest, opts ...grpc.CallOption) (ResourceMeteringPubSub_SubscribeClient, error)
}

type resourceMeteringPubSubClient struct {
	cc *grpc.ClientConn
}

func NewResourceMeteringPubSubClient(cc *grpc.ClientConn) ResourceMeteringPubSubClient {
	return &resourceMeteringPubSubClient{cc}
}

func (c *resourceMeteringPubSubClient) Subscribe(ctx context.Context, in *ResourceMeteringRequest, opts ...grpc.CallOption) (ResourceMeteringPubSub_SubscribeClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ResourceMeteringPubSub_serviceDesc.Streams[0], "/resource_usage_agent.ResourceMeteringPubSub/Subscribe", opts...)
	if err != nil {
		return nil, err
	}
	x := &resourceMeteringPubSubSubscribeClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ResourceMeteringPubSub_SubscribeClient interface {
	Recv() (*ResourceUsageRecord, error)
	grpc.ClientStream
}

type resourceMeteringPubSubSubscribeClient struct {
	grpc.ClientStream
}

func (x *resourceMeteringPubSubSubscribeClient) Recv() (*ResourceUsageRecord, error) {
	m := new(ResourceUsageRecord)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ResourceMeteringPubSubServer is the server API for ResourceMeteringPubSub service.
type ResourceMeteringPubSubServer interface {
	// Clients subscribe to resource metering records through this RPC, and TiKV periodically (e.g. per minute)
	// publishes resource metering records to clients via gRPC stream.
	Subscribe(*ResourceMeteringRequest, ResourceMeteringPubSub_SubscribeServer) error
}

// UnimplementedResourceMeteringPubSubServer can be embedded to have forward compatible implementations.
type UnimplementedResourceMeteringPubSubServer struct {
}

func (*UnimplementedResourceMeteringPubSubServer) Subscribe(req *ResourceMeteringRequest, srv ResourceMeteringPubSub_SubscribeServer) error {
	return status.Errorf(codes.Unimplemented, "method Subscribe not implemented")
}

func RegisterResourceMeteringPubSubServer(s *grpc.Server, srv ResourceMeteringPubSubServer) {
	s.RegisterService(&_ResourceMeteringPubSub_serviceDesc, srv)
}

func _ResourceMeteringPubSub_Subscribe_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ResourceMeteringRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ResourceMeteringPubSubServer).Subscribe(m, &resourceMeteringPubSubSubscribeServer{stream})
}

type ResourceMeteringPubSub_SubscribeServer interface {
	Send(*ResourceUsageRecord) error
	grpc.ServerStream
}

type resourceMeteringPubSubSubscribeServer struct {
	grpc.ServerStream
}

func (x *resourceMeteringPubSubSubscribeServer) Send(m *ResourceUsageRecord) error {
	return x.ServerStream.SendMsg(m)
}

var _ResourceMeteringPubSub_serviceDesc = grpc.ServiceDesc{
	ServiceName: "resource_usage_agent.ResourceMeteringPubSub",
	HandlerType: (*ResourceMeteringPubSubServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Subscribe",
			Handler:       _ResourceMeteringPubSub_Subscribe_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "resource_usage_agent.proto",
}

func (m *ResourceMeteringRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResourceMeteringRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResourceMeteringRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *EmptyResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EmptyResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EmptyResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ResourceUsageRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResourceUsageRecord) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResourceUsageRecord) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RecordOneof != nil {
		{
			size := m.RecordOneof.Size()
			i -= size
			if _, err := m.RecordOneof.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *ResourceUsageRecord_Record) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResourceUsageRecord_Record) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Record != nil {
		{
			size, err := m.Record.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceUsageAgent(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *GroupTagRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupTagRecord) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GroupTagRecord) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintResourceUsageAgent(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.ResourceGroupTag) > 0 {
		i -= len(m.ResourceGroupTag)
		copy(dAtA[i:], m.ResourceGroupTag)
		i = encodeVarintResourceUsageAgent(dAtA, i, uint64(len(m.ResourceGroupTag)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GroupTagRecordItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupTagRecordItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GroupTagRecordItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.WriteKeys != 0 {
		i = encodeVarintResourceUsageAgent(dAtA, i, uint64(m.WriteKeys))
		i--
		dAtA[i] = 0x20
	}
	if m.ReadKeys != 0 {
		i = encodeVarintResourceUsageAgent(dAtA, i, uint64(m.ReadKeys))
		i--
		dAtA[i] = 0x18
	}
	if m.CpuTimeMs != 0 {
		i = encodeVarintResourceUsageAgent(dAtA, i, uint64(m.CpuTimeMs))
		i--
		dAtA[i] = 0x10
	}
	if m.TimestampSec != 0 {
		i = encodeVarintResourceUsageAgent(dAtA, i, uint64(m.TimestampSec))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintResourceUsageAgent(dAtA []byte, offset int, v uint64) int {
	offset -= sovResourceUsageAgent(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ResourceMeteringRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *EmptyResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ResourceUsageRecord) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RecordOneof != nil {
		n += m.RecordOneof.Size()
	}
	return n
}

func (m *ResourceUsageRecord_Record) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Record != nil {
		l = m.Record.Size()
		n += 1 + l + sovResourceUsageAgent(uint64(l))
	}
	return n
}
func (m *GroupTagRecord) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ResourceGroupTag)
	if l > 0 {
		n += 1 + l + sovResourceUsageAgent(uint64(l))
	}
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovResourceUsageAgent(uint64(l))
		}
	}
	return n
}

func (m *GroupTagRecordItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TimestampSec != 0 {
		n += 1 + sovResourceUsageAgent(uint64(m.TimestampSec))
	}
	if m.CpuTimeMs != 0 {
		n += 1 + sovResourceUsageAgent(uint64(m.CpuTimeMs))
	}
	if m.ReadKeys != 0 {
		n += 1 + sovResourceUsageAgent(uint64(m.ReadKeys))
	}
	if m.WriteKeys != 0 {
		n += 1 + sovResourceUsageAgent(uint64(m.WriteKeys))
	}
	return n
}

func sovResourceUsageAgent(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozResourceUsageAgent(x uint64) (n int) {
	return sovResourceUsageAgent(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ResourceMeteringRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceUsageAgent
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResourceMeteringRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResourceMeteringRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipResourceUsageAgent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EmptyResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceUsageAgent
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EmptyResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EmptyResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipResourceUsageAgent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResourceUsageRecord) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceUsageAgent
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResourceUsageRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResourceUsageRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Record", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GroupTagRecord{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.RecordOneof = &ResourceUsageRecord_Record{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceUsageAgent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupTagRecord) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceUsageAgent
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GroupTagRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GroupTagRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResourceGroupTag", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResourceGroupTag = append(m.ResourceGroupTag[:0], dAtA[iNdEx:postIndex]...)
			if m.ResourceGroupTag == nil {
				m.ResourceGroupTag = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, &GroupTagRecordItem{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceUsageAgent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupTagRecordItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceUsageAgent
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GroupTagRecordItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GroupTagRecordItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimestampSec", wireType)
			}
			m.TimestampSec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimestampSec |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CpuTimeMs", wireType)
			}
			m.CpuTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CpuTimeMs |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadKeys", wireType)
			}
			m.ReadKeys = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReadKeys |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WriteKeys", wireType)
			}
			m.WriteKeys = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WriteKeys |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResourceUsageAgent(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceUsageAgent
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipResourceUsageAgent(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowResourceUsageAgent
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowResourceUsageAgent
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthResourceUsageAgent
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupResourceUsageAgent
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthResourceUsageAgent
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthResourceUsageAgent        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowResourceUsageAgent          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupResourceUsageAgent = fmt.Errorf("proto: unexpected end of group")
)
