// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: cdcpb.proto

package cdcpb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	errorpb "github.com/pingcap/kvproto/pkg/errorpb"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
	raft_cmdpb "github.com/pingcap/kvproto/pkg/raft_cmdpb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type Event_LogType int32

const (
	Event_UNKNOWN     Event_LogType = 0
	Event_PREWRITE    Event_LogType = 1
	Event_COMMIT      Event_LogType = 2
	Event_ROLLBACK    Event_LogType = 3
	Event_COMMITTED   Event_LogType = 4
	Event_INITIALIZED Event_LogType = 5
)

var Event_LogType_name = map[int32]string{
	0: "UNKNOWN",
	1: "PREWRITE",
	2: "COMMIT",
	3: "ROLLBACK",
	4: "COMMITTED",
	5: "INITIALIZED",
}

var Event_LogType_value = map[string]int32{
	"UNKNOWN":     0,
	"PREWRITE":    1,
	"COMMIT":      2,
	"ROLLBACK":    3,
	"COMMITTED":   4,
	"INITIALIZED": 5,
}

func (x Event_LogType) String() string {
	return proto.EnumName(Event_LogType_name, int32(x))
}

func (Event_LogType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{8, 0}
}

type Event_Row_OpType int32

const (
	Event_Row_UNKNOWN Event_Row_OpType = 0
	Event_Row_PUT     Event_Row_OpType = 1
	Event_Row_DELETE  Event_Row_OpType = 2
)

var Event_Row_OpType_name = map[int32]string{
	0: "UNKNOWN",
	1: "PUT",
	2: "DELETE",
}

var Event_Row_OpType_value = map[string]int32{
	"UNKNOWN": 0,
	"PUT":     1,
	"DELETE":  2,
}

func (x Event_Row_OpType) String() string {
	return proto.EnumName(Event_Row_OpType_name, int32(x))
}

func (Event_Row_OpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{8, 0, 0}
}

// KvAPI specifies to capture data written by different KV API.
// See more details in https://github.com/tikv/rfcs/blob/master/text/0069-api-v2.md.
type ChangeDataRequest_KvAPI int32

const (
	ChangeDataRequest_TiDB  ChangeDataRequest_KvAPI = 0
	ChangeDataRequest_RawKV ChangeDataRequest_KvAPI = 1
	ChangeDataRequest_TxnKV ChangeDataRequest_KvAPI = 2
)

var ChangeDataRequest_KvAPI_name = map[int32]string{
	0: "TiDB",
	1: "RawKV",
	2: "TxnKV",
}

var ChangeDataRequest_KvAPI_value = map[string]int32{
	"TiDB":  0,
	"RawKV": 1,
	"TxnKV": 2,
}

func (x ChangeDataRequest_KvAPI) String() string {
	return proto.EnumName(ChangeDataRequest_KvAPI_name, int32(x))
}

func (ChangeDataRequest_KvAPI) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{11, 0}
}

type Header struct {
	ClusterId    uint64 `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	TicdcVersion string `protobuf:"bytes,2,opt,name=ticdc_version,json=ticdcVersion,proto3" json:"ticdc_version,omitempty"`
}

func (m *Header) Reset()         { *m = Header{} }
func (m *Header) String() string { return proto.CompactTextString(m) }
func (*Header) ProtoMessage()    {}
func (*Header) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{0}
}
func (m *Header) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Header) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Header.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Header) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Header.Merge(m, src)
}
func (m *Header) XXX_Size() int {
	return m.Size()
}
func (m *Header) XXX_DiscardUnknown() {
	xxx_messageInfo_Header.DiscardUnknown(m)
}

var xxx_messageInfo_Header proto.InternalMessageInfo

func (m *Header) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

func (m *Header) GetTicdcVersion() string {
	if m != nil {
		return m.TicdcVersion
	}
	return ""
}

type DuplicateRequest struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *DuplicateRequest) Reset()         { *m = DuplicateRequest{} }
func (m *DuplicateRequest) String() string { return proto.CompactTextString(m) }
func (*DuplicateRequest) ProtoMessage()    {}
func (*DuplicateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{1}
}
func (m *DuplicateRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DuplicateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DuplicateRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DuplicateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DuplicateRequest.Merge(m, src)
}
func (m *DuplicateRequest) XXX_Size() int {
	return m.Size()
}
func (m *DuplicateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DuplicateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DuplicateRequest proto.InternalMessageInfo

func (m *DuplicateRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type Compatibility struct {
	RequiredVersion string `protobuf:"bytes,1,opt,name=required_version,json=requiredVersion,proto3" json:"required_version,omitempty"`
}

func (m *Compatibility) Reset()         { *m = Compatibility{} }
func (m *Compatibility) String() string { return proto.CompactTextString(m) }
func (*Compatibility) ProtoMessage()    {}
func (*Compatibility) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{2}
}
func (m *Compatibility) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Compatibility) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Compatibility.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Compatibility) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Compatibility.Merge(m, src)
}
func (m *Compatibility) XXX_Size() int {
	return m.Size()
}
func (m *Compatibility) XXX_DiscardUnknown() {
	xxx_messageInfo_Compatibility.DiscardUnknown(m)
}

var xxx_messageInfo_Compatibility proto.InternalMessageInfo

func (m *Compatibility) GetRequiredVersion() string {
	if m != nil {
		return m.RequiredVersion
	}
	return ""
}

// ClusterIDMismatch is an error variable that
// tells people that the cluster ID of the request does not match the TiKV cluster ID.
type ClusterIDMismatch struct {
	// The current tikv cluster ID.
	Current uint64 `protobuf:"varint,1,opt,name=current,proto3" json:"current,omitempty"`
	// The cluster ID of the TiCDC request.
	Request uint64 `protobuf:"varint,2,opt,name=request,proto3" json:"request,omitempty"`
}

func (m *ClusterIDMismatch) Reset()         { *m = ClusterIDMismatch{} }
func (m *ClusterIDMismatch) String() string { return proto.CompactTextString(m) }
func (*ClusterIDMismatch) ProtoMessage()    {}
func (*ClusterIDMismatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{3}
}
func (m *ClusterIDMismatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ClusterIDMismatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ClusterIDMismatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ClusterIDMismatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClusterIDMismatch.Merge(m, src)
}
func (m *ClusterIDMismatch) XXX_Size() int {
	return m.Size()
}
func (m *ClusterIDMismatch) XXX_DiscardUnknown() {
	xxx_messageInfo_ClusterIDMismatch.DiscardUnknown(m)
}

var xxx_messageInfo_ClusterIDMismatch proto.InternalMessageInfo

func (m *ClusterIDMismatch) GetCurrent() uint64 {
	if m != nil {
		return m.Current
	}
	return 0
}

func (m *ClusterIDMismatch) GetRequest() uint64 {
	if m != nil {
		return m.Request
	}
	return 0
}

// Congested is an error variable that
// tells people that the TiKV-CDC is congested.
type Congested struct {
	// The region ID that triggers the congestion.
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *Congested) Reset()         { *m = Congested{} }
func (m *Congested) String() string { return proto.CompactTextString(m) }
func (*Congested) ProtoMessage()    {}
func (*Congested) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{4}
}
func (m *Congested) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Congested) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Congested.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Congested) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Congested.Merge(m, src)
}
func (m *Congested) XXX_Size() int {
	return m.Size()
}
func (m *Congested) XXX_DiscardUnknown() {
	xxx_messageInfo_Congested.DiscardUnknown(m)
}

var xxx_messageInfo_Congested proto.InternalMessageInfo

func (m *Congested) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type Error struct {
	NotLeader         *errorpb.NotLeader      `protobuf:"bytes,1,opt,name=not_leader,json=notLeader,proto3" json:"not_leader,omitempty"`
	RegionNotFound    *errorpb.RegionNotFound `protobuf:"bytes,2,opt,name=region_not_found,json=regionNotFound,proto3" json:"region_not_found,omitempty"`
	EpochNotMatch     *errorpb.EpochNotMatch  `protobuf:"bytes,3,opt,name=epoch_not_match,json=epochNotMatch,proto3" json:"epoch_not_match,omitempty"`
	DuplicateRequest  *DuplicateRequest       `protobuf:"bytes,4,opt,name=duplicate_request,json=duplicateRequest,proto3" json:"duplicate_request,omitempty"`
	Compatibility     *Compatibility          `protobuf:"bytes,5,opt,name=compatibility,proto3" json:"compatibility,omitempty"`
	ClusterIdMismatch *ClusterIDMismatch      `protobuf:"bytes,6,opt,name=cluster_id_mismatch,json=clusterIdMismatch,proto3" json:"cluster_id_mismatch,omitempty"`
	ServerIsBusy      *errorpb.ServerIsBusy   `protobuf:"bytes,7,opt,name=server_is_busy,json=serverIsBusy,proto3" json:"server_is_busy,omitempty"`
	Congested         *Congested              `protobuf:"bytes,8,opt,name=congested,proto3" json:"congested,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{5}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetNotLeader() *errorpb.NotLeader {
	if m != nil {
		return m.NotLeader
	}
	return nil
}

func (m *Error) GetRegionNotFound() *errorpb.RegionNotFound {
	if m != nil {
		return m.RegionNotFound
	}
	return nil
}

func (m *Error) GetEpochNotMatch() *errorpb.EpochNotMatch {
	if m != nil {
		return m.EpochNotMatch
	}
	return nil
}

func (m *Error) GetDuplicateRequest() *DuplicateRequest {
	if m != nil {
		return m.DuplicateRequest
	}
	return nil
}

func (m *Error) GetCompatibility() *Compatibility {
	if m != nil {
		return m.Compatibility
	}
	return nil
}

func (m *Error) GetClusterIdMismatch() *ClusterIDMismatch {
	if m != nil {
		return m.ClusterIdMismatch
	}
	return nil
}

func (m *Error) GetServerIsBusy() *errorpb.ServerIsBusy {
	if m != nil {
		return m.ServerIsBusy
	}
	return nil
}

func (m *Error) GetCongested() *Congested {
	if m != nil {
		return m.Congested
	}
	return nil
}

type TxnInfo struct {
	StartTs uint64 `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	Primary []byte `protobuf:"bytes,2,opt,name=primary,proto3" json:"primary,omitempty"`
}

func (m *TxnInfo) Reset()         { *m = TxnInfo{} }
func (m *TxnInfo) String() string { return proto.CompactTextString(m) }
func (*TxnInfo) ProtoMessage()    {}
func (*TxnInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{6}
}
func (m *TxnInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TxnInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TxnInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TxnInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TxnInfo.Merge(m, src)
}
func (m *TxnInfo) XXX_Size() int {
	return m.Size()
}
func (m *TxnInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TxnInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TxnInfo proto.InternalMessageInfo

func (m *TxnInfo) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *TxnInfo) GetPrimary() []byte {
	if m != nil {
		return m.Primary
	}
	return nil
}

type TxnStatus struct {
	StartTs      uint64 `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	MinCommitTs  uint64 `protobuf:"varint,2,opt,name=min_commit_ts,json=minCommitTs,proto3" json:"min_commit_ts,omitempty"`
	CommitTs     uint64 `protobuf:"varint,3,opt,name=commit_ts,json=commitTs,proto3" json:"commit_ts,omitempty"`
	IsRolledBack bool   `protobuf:"varint,4,opt,name=is_rolled_back,json=isRolledBack,proto3" json:"is_rolled_back,omitempty"`
}

func (m *TxnStatus) Reset()         { *m = TxnStatus{} }
func (m *TxnStatus) String() string { return proto.CompactTextString(m) }
func (*TxnStatus) ProtoMessage()    {}
func (*TxnStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{7}
}
func (m *TxnStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TxnStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TxnStatus.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TxnStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TxnStatus.Merge(m, src)
}
func (m *TxnStatus) XXX_Size() int {
	return m.Size()
}
func (m *TxnStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_TxnStatus.DiscardUnknown(m)
}

var xxx_messageInfo_TxnStatus proto.InternalMessageInfo

func (m *TxnStatus) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *TxnStatus) GetMinCommitTs() uint64 {
	if m != nil {
		return m.MinCommitTs
	}
	return 0
}

func (m *TxnStatus) GetCommitTs() uint64 {
	if m != nil {
		return m.CommitTs
	}
	return 0
}

func (m *TxnStatus) GetIsRolledBack() bool {
	if m != nil {
		return m.IsRolledBack
	}
	return false
}

type Event struct {
	RegionId  uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	Index     uint64 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	RequestId uint64 `protobuf:"varint,7,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Types that are valid to be assigned to Event:
	//	*Event_Entries_
	//	*Event_Admin_
	//	*Event_Error
	//	*Event_ResolvedTs
	//	*Event_LongTxn_
	Event isEvent_Event `protobuf_oneof:"event"`
}

func (m *Event) Reset()         { *m = Event{} }
func (m *Event) String() string { return proto.CompactTextString(m) }
func (*Event) ProtoMessage()    {}
func (*Event) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{8}
}
func (m *Event) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Event) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Event.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Event) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Event.Merge(m, src)
}
func (m *Event) XXX_Size() int {
	return m.Size()
}
func (m *Event) XXX_DiscardUnknown() {
	xxx_messageInfo_Event.DiscardUnknown(m)
}

var xxx_messageInfo_Event proto.InternalMessageInfo

type isEvent_Event interface {
	isEvent_Event()
	MarshalTo([]byte) (int, error)
	Size() int
}

type Event_Entries_ struct {
	Entries *Event_Entries `protobuf:"bytes,3,opt,name=entries,proto3,oneof" json:"entries,omitempty"`
}
type Event_Admin_ struct {
	Admin *Event_Admin `protobuf:"bytes,4,opt,name=admin,proto3,oneof" json:"admin,omitempty"`
}
type Event_Error struct {
	Error *Error `protobuf:"bytes,5,opt,name=error,proto3,oneof" json:"error,omitempty"`
}
type Event_ResolvedTs struct {
	ResolvedTs uint64 `protobuf:"varint,6,opt,name=resolved_ts,json=resolvedTs,proto3,oneof" json:"resolved_ts,omitempty"`
}
type Event_LongTxn_ struct {
	LongTxn *Event_LongTxn `protobuf:"bytes,8,opt,name=long_txn,json=longTxn,proto3,oneof" json:"long_txn,omitempty"`
}

func (*Event_Entries_) isEvent_Event()   {}
func (*Event_Admin_) isEvent_Event()     {}
func (*Event_Error) isEvent_Event()      {}
func (*Event_ResolvedTs) isEvent_Event() {}
func (*Event_LongTxn_) isEvent_Event()   {}

func (m *Event) GetEvent() isEvent_Event {
	if m != nil {
		return m.Event
	}
	return nil
}

func (m *Event) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *Event) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *Event) GetRequestId() uint64 {
	if m != nil {
		return m.RequestId
	}
	return 0
}

func (m *Event) GetEntries() *Event_Entries {
	if x, ok := m.GetEvent().(*Event_Entries_); ok {
		return x.Entries
	}
	return nil
}

func (m *Event) GetAdmin() *Event_Admin {
	if x, ok := m.GetEvent().(*Event_Admin_); ok {
		return x.Admin
	}
	return nil
}

func (m *Event) GetError() *Error {
	if x, ok := m.GetEvent().(*Event_Error); ok {
		return x.Error
	}
	return nil
}

// Deprecated: Do not use.
func (m *Event) GetResolvedTs() uint64 {
	if x, ok := m.GetEvent().(*Event_ResolvedTs); ok {
		return x.ResolvedTs
	}
	return 0
}

func (m *Event) GetLongTxn() *Event_LongTxn {
	if x, ok := m.GetEvent().(*Event_LongTxn_); ok {
		return x.LongTxn
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*Event) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*Event_Entries_)(nil),
		(*Event_Admin_)(nil),
		(*Event_Error)(nil),
		(*Event_ResolvedTs)(nil),
		(*Event_LongTxn_)(nil),
	}
}

type Event_Row struct {
	StartTs  uint64        `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	CommitTs uint64        `protobuf:"varint,2,opt,name=commit_ts,json=commitTs,proto3" json:"commit_ts,omitempty"`
	Type     Event_LogType `protobuf:"varint,3,opt,name=type,proto3,enum=cdcpb.Event_LogType" json:"type,omitempty"`
	// generation is for pipelined DML protocol. See kvrpcpb.FlushRequest.generation.
	Generation uint64           `protobuf:"varint,10,opt,name=generation,proto3" json:"generation,omitempty"`
	OpType     Event_Row_OpType `protobuf:"varint,4,opt,name=op_type,json=opType,proto3,enum=cdcpb.Event_Row_OpType" json:"op_type,omitempty"`
	Key        []byte           `protobuf:"bytes,5,opt,name=key,proto3" json:"key,omitempty"`
	Value      []byte           `protobuf:"bytes,6,opt,name=value,proto3" json:"value,omitempty"`
	OldValue   []byte           `protobuf:"bytes,7,opt,name=old_value,json=oldValue,proto3" json:"old_value,omitempty"`
	// expire_ts_unix_secs is used for RawKV (see `ChangeDataRequest.KvApi`),
	// and represents the expiration time of this row.
	// Absolute time, seconds since Unix epoch.
	ExpireTsUnixSecs uint64 `protobuf:"varint,8,opt,name=expire_ts_unix_secs,json=expireTsUnixSecs,proto3" json:"expire_ts_unix_secs,omitempty"`
	// The source of this row.
	TxnSource uint64 `protobuf:"varint,9,opt,name=txn_source,json=txnSource,proto3" json:"txn_source,omitempty"`
}

func (m *Event_Row) Reset()         { *m = Event_Row{} }
func (m *Event_Row) String() string { return proto.CompactTextString(m) }
func (*Event_Row) ProtoMessage()    {}
func (*Event_Row) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{8, 0}
}
func (m *Event_Row) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Event_Row) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Event_Row.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Event_Row) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Event_Row.Merge(m, src)
}
func (m *Event_Row) XXX_Size() int {
	return m.Size()
}
func (m *Event_Row) XXX_DiscardUnknown() {
	xxx_messageInfo_Event_Row.DiscardUnknown(m)
}

var xxx_messageInfo_Event_Row proto.InternalMessageInfo

func (m *Event_Row) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *Event_Row) GetCommitTs() uint64 {
	if m != nil {
		return m.CommitTs
	}
	return 0
}

func (m *Event_Row) GetType() Event_LogType {
	if m != nil {
		return m.Type
	}
	return Event_UNKNOWN
}

func (m *Event_Row) GetGeneration() uint64 {
	if m != nil {
		return m.Generation
	}
	return 0
}

func (m *Event_Row) GetOpType() Event_Row_OpType {
	if m != nil {
		return m.OpType
	}
	return Event_Row_UNKNOWN
}

func (m *Event_Row) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *Event_Row) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *Event_Row) GetOldValue() []byte {
	if m != nil {
		return m.OldValue
	}
	return nil
}

func (m *Event_Row) GetExpireTsUnixSecs() uint64 {
	if m != nil {
		return m.ExpireTsUnixSecs
	}
	return 0
}

func (m *Event_Row) GetTxnSource() uint64 {
	if m != nil {
		return m.TxnSource
	}
	return 0
}

type Event_Entries struct {
	Entries []*Event_Row `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (m *Event_Entries) Reset()         { *m = Event_Entries{} }
func (m *Event_Entries) String() string { return proto.CompactTextString(m) }
func (*Event_Entries) ProtoMessage()    {}
func (*Event_Entries) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{8, 1}
}
func (m *Event_Entries) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Event_Entries) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Event_Entries.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Event_Entries) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Event_Entries.Merge(m, src)
}
func (m *Event_Entries) XXX_Size() int {
	return m.Size()
}
func (m *Event_Entries) XXX_DiscardUnknown() {
	xxx_messageInfo_Event_Entries.DiscardUnknown(m)
}

var xxx_messageInfo_Event_Entries proto.InternalMessageInfo

func (m *Event_Entries) GetEntries() []*Event_Row {
	if m != nil {
		return m.Entries
	}
	return nil
}

type Event_Admin struct {
	AdminRequest  *raft_cmdpb.AdminRequest  `protobuf:"bytes,1,opt,name=admin_request,json=adminRequest,proto3" json:"admin_request,omitempty"`
	AdminResponse *raft_cmdpb.AdminResponse `protobuf:"bytes,2,opt,name=admin_response,json=adminResponse,proto3" json:"admin_response,omitempty"`
}

func (m *Event_Admin) Reset()         { *m = Event_Admin{} }
func (m *Event_Admin) String() string { return proto.CompactTextString(m) }
func (*Event_Admin) ProtoMessage()    {}
func (*Event_Admin) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{8, 2}
}
func (m *Event_Admin) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Event_Admin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Event_Admin.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Event_Admin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Event_Admin.Merge(m, src)
}
func (m *Event_Admin) XXX_Size() int {
	return m.Size()
}
func (m *Event_Admin) XXX_DiscardUnknown() {
	xxx_messageInfo_Event_Admin.DiscardUnknown(m)
}

var xxx_messageInfo_Event_Admin proto.InternalMessageInfo

func (m *Event_Admin) GetAdminRequest() *raft_cmdpb.AdminRequest {
	if m != nil {
		return m.AdminRequest
	}
	return nil
}

func (m *Event_Admin) GetAdminResponse() *raft_cmdpb.AdminResponse {
	if m != nil {
		return m.AdminResponse
	}
	return nil
}

type Event_LongTxn struct {
	TxnInfo []*TxnInfo `protobuf:"bytes,1,rep,name=txn_info,json=txnInfo,proto3" json:"txn_info,omitempty"`
}

func (m *Event_LongTxn) Reset()         { *m = Event_LongTxn{} }
func (m *Event_LongTxn) String() string { return proto.CompactTextString(m) }
func (*Event_LongTxn) ProtoMessage()    {}
func (*Event_LongTxn) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{8, 3}
}
func (m *Event_LongTxn) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Event_LongTxn) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Event_LongTxn.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Event_LongTxn) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Event_LongTxn.Merge(m, src)
}
func (m *Event_LongTxn) XXX_Size() int {
	return m.Size()
}
func (m *Event_LongTxn) XXX_DiscardUnknown() {
	xxx_messageInfo_Event_LongTxn.DiscardUnknown(m)
}

var xxx_messageInfo_Event_LongTxn proto.InternalMessageInfo

func (m *Event_LongTxn) GetTxnInfo() []*TxnInfo {
	if m != nil {
		return m.TxnInfo
	}
	return nil
}

// NOTE: events and resolved_ts won't appear simultaneously in one ChangeDataEvent.
type ChangeDataEvent struct {
	Events     []*Event    `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	ResolvedTs *ResolvedTs `protobuf:"bytes,2,opt,name=resolved_ts,json=resolvedTs,proto3" json:"resolved_ts,omitempty"`
}

func (m *ChangeDataEvent) Reset()         { *m = ChangeDataEvent{} }
func (m *ChangeDataEvent) String() string { return proto.CompactTextString(m) }
func (*ChangeDataEvent) ProtoMessage()    {}
func (*ChangeDataEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{9}
}
func (m *ChangeDataEvent) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangeDataEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangeDataEvent.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangeDataEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDataEvent.Merge(m, src)
}
func (m *ChangeDataEvent) XXX_Size() int {
	return m.Size()
}
func (m *ChangeDataEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDataEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDataEvent proto.InternalMessageInfo

func (m *ChangeDataEvent) GetEvents() []*Event {
	if m != nil {
		return m.Events
	}
	return nil
}

func (m *ChangeDataEvent) GetResolvedTs() *ResolvedTs {
	if m != nil {
		return m.ResolvedTs
	}
	return nil
}

type ResolvedTs struct {
	Regions   []uint64 `protobuf:"varint,1,rep,packed,name=regions,proto3" json:"regions,omitempty"`
	Ts        uint64   `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	RequestId uint64   `protobuf:"varint,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (m *ResolvedTs) Reset()         { *m = ResolvedTs{} }
func (m *ResolvedTs) String() string { return proto.CompactTextString(m) }
func (*ResolvedTs) ProtoMessage()    {}
func (*ResolvedTs) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{10}
}
func (m *ResolvedTs) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResolvedTs) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResolvedTs.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResolvedTs) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResolvedTs.Merge(m, src)
}
func (m *ResolvedTs) XXX_Size() int {
	return m.Size()
}
func (m *ResolvedTs) XXX_DiscardUnknown() {
	xxx_messageInfo_ResolvedTs.DiscardUnknown(m)
}

var xxx_messageInfo_ResolvedTs proto.InternalMessageInfo

func (m *ResolvedTs) GetRegions() []uint64 {
	if m != nil {
		return m.Regions
	}
	return nil
}

func (m *ResolvedTs) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *ResolvedTs) GetRequestId() uint64 {
	if m != nil {
		return m.RequestId
	}
	return 0
}

type ChangeDataRequest struct {
	Header       *Header             `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RegionId     uint64              `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	RegionEpoch  *metapb.RegionEpoch `protobuf:"bytes,3,opt,name=region_epoch,json=regionEpoch,proto3" json:"region_epoch,omitempty"`
	CheckpointTs uint64              `protobuf:"varint,4,opt,name=checkpoint_ts,json=checkpointTs,proto3" json:"checkpoint_ts,omitempty"`
	StartKey     []byte              `protobuf:"bytes,5,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey       []byte              `protobuf:"bytes,6,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	// Used for CDC to identify events corresponding to different requests.
	// Generally in one call stream, a region can be subscribed multiple
	// times with different `request_id`s.
	RequestId uint64          `protobuf:"varint,7,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ExtraOp   kvrpcpb.ExtraOp `protobuf:"varint,8,opt,name=extra_op,json=extraOp,proto3,enum=kvrpcpb.ExtraOp" json:"extra_op,omitempty"`
	// Types that are valid to be assigned to Request:
	//	*ChangeDataRequest_Register_
	//	*ChangeDataRequest_NotifyTxnStatus_
	//	*ChangeDataRequest_Deregister_
	Request isChangeDataRequest_Request `protobuf_oneof:"request"`
	KvApi   ChangeDataRequest_KvAPI     `protobuf:"varint,11,opt,name=kv_api,json=kvApi,proto3,enum=cdcpb.ChangeDataRequest_KvAPI" json:"kv_api,omitempty"`
	// Whether to filter out the value write by cdc itself.
	FilterLoop bool `protobuf:"varint,12,opt,name=filter_loop,json=filterLoop,proto3" json:"filter_loop,omitempty"`
}

func (m *ChangeDataRequest) Reset()         { *m = ChangeDataRequest{} }
func (m *ChangeDataRequest) String() string { return proto.CompactTextString(m) }
func (*ChangeDataRequest) ProtoMessage()    {}
func (*ChangeDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{11}
}
func (m *ChangeDataRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangeDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangeDataRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangeDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDataRequest.Merge(m, src)
}
func (m *ChangeDataRequest) XXX_Size() int {
	return m.Size()
}
func (m *ChangeDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDataRequest proto.InternalMessageInfo

type isChangeDataRequest_Request interface {
	isChangeDataRequest_Request()
	MarshalTo([]byte) (int, error)
	Size() int
}

type ChangeDataRequest_Register_ struct {
	Register *ChangeDataRequest_Register `protobuf:"bytes,9,opt,name=register,proto3,oneof" json:"register,omitempty"`
}
type ChangeDataRequest_NotifyTxnStatus_ struct {
	NotifyTxnStatus *ChangeDataRequest_NotifyTxnStatus `protobuf:"bytes,10,opt,name=notify_txn_status,json=notifyTxnStatus,proto3,oneof" json:"notify_txn_status,omitempty"`
}
type ChangeDataRequest_Deregister_ struct {
	Deregister *ChangeDataRequest_Deregister `protobuf:"bytes,13,opt,name=deregister,proto3,oneof" json:"deregister,omitempty"`
}

func (*ChangeDataRequest_Register_) isChangeDataRequest_Request()        {}
func (*ChangeDataRequest_NotifyTxnStatus_) isChangeDataRequest_Request() {}
func (*ChangeDataRequest_Deregister_) isChangeDataRequest_Request()      {}

func (m *ChangeDataRequest) GetRequest() isChangeDataRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (m *ChangeDataRequest) GetHeader() *Header {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ChangeDataRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *ChangeDataRequest) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *ChangeDataRequest) GetCheckpointTs() uint64 {
	if m != nil {
		return m.CheckpointTs
	}
	return 0
}

func (m *ChangeDataRequest) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *ChangeDataRequest) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (m *ChangeDataRequest) GetRequestId() uint64 {
	if m != nil {
		return m.RequestId
	}
	return 0
}

func (m *ChangeDataRequest) GetExtraOp() kvrpcpb.ExtraOp {
	if m != nil {
		return m.ExtraOp
	}
	return kvrpcpb.ExtraOp_Noop
}

func (m *ChangeDataRequest) GetRegister() *ChangeDataRequest_Register {
	if x, ok := m.GetRequest().(*ChangeDataRequest_Register_); ok {
		return x.Register
	}
	return nil
}

func (m *ChangeDataRequest) GetNotifyTxnStatus() *ChangeDataRequest_NotifyTxnStatus {
	if x, ok := m.GetRequest().(*ChangeDataRequest_NotifyTxnStatus_); ok {
		return x.NotifyTxnStatus
	}
	return nil
}

func (m *ChangeDataRequest) GetDeregister() *ChangeDataRequest_Deregister {
	if x, ok := m.GetRequest().(*ChangeDataRequest_Deregister_); ok {
		return x.Deregister
	}
	return nil
}

func (m *ChangeDataRequest) GetKvApi() ChangeDataRequest_KvAPI {
	if m != nil {
		return m.KvApi
	}
	return ChangeDataRequest_TiDB
}

func (m *ChangeDataRequest) GetFilterLoop() bool {
	if m != nil {
		return m.FilterLoop
	}
	return false
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*ChangeDataRequest) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*ChangeDataRequest_Register_)(nil),
		(*ChangeDataRequest_NotifyTxnStatus_)(nil),
		(*ChangeDataRequest_Deregister_)(nil),
	}
}

type ChangeDataRequest_Register struct {
}

func (m *ChangeDataRequest_Register) Reset()         { *m = ChangeDataRequest_Register{} }
func (m *ChangeDataRequest_Register) String() string { return proto.CompactTextString(m) }
func (*ChangeDataRequest_Register) ProtoMessage()    {}
func (*ChangeDataRequest_Register) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{11, 0}
}
func (m *ChangeDataRequest_Register) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangeDataRequest_Register) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangeDataRequest_Register.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangeDataRequest_Register) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDataRequest_Register.Merge(m, src)
}
func (m *ChangeDataRequest_Register) XXX_Size() int {
	return m.Size()
}
func (m *ChangeDataRequest_Register) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDataRequest_Register.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDataRequest_Register proto.InternalMessageInfo

type ChangeDataRequest_Deregister struct {
}

func (m *ChangeDataRequest_Deregister) Reset()         { *m = ChangeDataRequest_Deregister{} }
func (m *ChangeDataRequest_Deregister) String() string { return proto.CompactTextString(m) }
func (*ChangeDataRequest_Deregister) ProtoMessage()    {}
func (*ChangeDataRequest_Deregister) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{11, 1}
}
func (m *ChangeDataRequest_Deregister) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangeDataRequest_Deregister) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangeDataRequest_Deregister.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangeDataRequest_Deregister) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDataRequest_Deregister.Merge(m, src)
}
func (m *ChangeDataRequest_Deregister) XXX_Size() int {
	return m.Size()
}
func (m *ChangeDataRequest_Deregister) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDataRequest_Deregister.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDataRequest_Deregister proto.InternalMessageInfo

type ChangeDataRequest_NotifyTxnStatus struct {
	TxnStatus []*TxnStatus `protobuf:"bytes,1,rep,name=txn_status,json=txnStatus,proto3" json:"txn_status,omitempty"`
}

func (m *ChangeDataRequest_NotifyTxnStatus) Reset()         { *m = ChangeDataRequest_NotifyTxnStatus{} }
func (m *ChangeDataRequest_NotifyTxnStatus) String() string { return proto.CompactTextString(m) }
func (*ChangeDataRequest_NotifyTxnStatus) ProtoMessage()    {}
func (*ChangeDataRequest_NotifyTxnStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_911209a1d38ef245, []int{11, 2}
}
func (m *ChangeDataRequest_NotifyTxnStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChangeDataRequest_NotifyTxnStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChangeDataRequest_NotifyTxnStatus.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChangeDataRequest_NotifyTxnStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeDataRequest_NotifyTxnStatus.Merge(m, src)
}
func (m *ChangeDataRequest_NotifyTxnStatus) XXX_Size() int {
	return m.Size()
}
func (m *ChangeDataRequest_NotifyTxnStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeDataRequest_NotifyTxnStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeDataRequest_NotifyTxnStatus proto.InternalMessageInfo

func (m *ChangeDataRequest_NotifyTxnStatus) GetTxnStatus() []*TxnStatus {
	if m != nil {
		return m.TxnStatus
	}
	return nil
}

func init() {
	proto.RegisterEnum("cdcpb.Event_LogType", Event_LogType_name, Event_LogType_value)
	proto.RegisterEnum("cdcpb.Event_Row_OpType", Event_Row_OpType_name, Event_Row_OpType_value)
	proto.RegisterEnum("cdcpb.ChangeDataRequest_KvAPI", ChangeDataRequest_KvAPI_name, ChangeDataRequest_KvAPI_value)
	proto.RegisterType((*Header)(nil), "cdcpb.Header")
	proto.RegisterType((*DuplicateRequest)(nil), "cdcpb.DuplicateRequest")
	proto.RegisterType((*Compatibility)(nil), "cdcpb.Compatibility")
	proto.RegisterType((*ClusterIDMismatch)(nil), "cdcpb.ClusterIDMismatch")
	proto.RegisterType((*Congested)(nil), "cdcpb.Congested")
	proto.RegisterType((*Error)(nil), "cdcpb.Error")
	proto.RegisterType((*TxnInfo)(nil), "cdcpb.TxnInfo")
	proto.RegisterType((*TxnStatus)(nil), "cdcpb.TxnStatus")
	proto.RegisterType((*Event)(nil), "cdcpb.Event")
	proto.RegisterType((*Event_Row)(nil), "cdcpb.Event.Row")
	proto.RegisterType((*Event_Entries)(nil), "cdcpb.Event.Entries")
	proto.RegisterType((*Event_Admin)(nil), "cdcpb.Event.Admin")
	proto.RegisterType((*Event_LongTxn)(nil), "cdcpb.Event.LongTxn")
	proto.RegisterType((*ChangeDataEvent)(nil), "cdcpb.ChangeDataEvent")
	proto.RegisterType((*ResolvedTs)(nil), "cdcpb.ResolvedTs")
	proto.RegisterType((*ChangeDataRequest)(nil), "cdcpb.ChangeDataRequest")
	proto.RegisterType((*ChangeDataRequest_Register)(nil), "cdcpb.ChangeDataRequest.Register")
	proto.RegisterType((*ChangeDataRequest_Deregister)(nil), "cdcpb.ChangeDataRequest.Deregister")
	proto.RegisterType((*ChangeDataRequest_NotifyTxnStatus)(nil), "cdcpb.ChangeDataRequest.NotifyTxnStatus")
}

func init() { proto.RegisterFile("cdcpb.proto", fileDescriptor_911209a1d38ef245) }

var fileDescriptor_911209a1d38ef245 = []byte{
	// 1559 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x57, 0x4b, 0x8f, 0x1b, 0x4b,
	0x15, 0x76, 0xfb, 0xed, 0xe3, 0xc7, 0xf4, 0x54, 0xc2, 0x8d, 0xaf, 0x11, 0x26, 0xf8, 0xe6, 0x8a,
	0xb9, 0x17, 0xe1, 0xdc, 0x6b, 0xb8, 0x2c, 0x82, 0x08, 0x8c, 0x1f, 0x61, 0xac, 0xf1, 0x78, 0xa2,
	0x9a, 0x9e, 0x89, 0xc4, 0x82, 0x56, 0x4f, 0x77, 0x8d, 0xa7, 0x65, 0xbb, 0xab, 0xd3, 0x5d, 0x76,
	0xec, 0x1d, 0x4b, 0x36, 0x48, 0x48, 0xb0, 0xe0, 0x27, 0xf0, 0x53, 0x58, 0x66, 0x99, 0x25, 0xca,
	0xfc, 0x07, 0xc4, 0x12, 0xd5, 0xa9, 0xee, 0xf6, 0x23, 0xcc, 0x20, 0xb1, 0x72, 0x9d, 0xf3, 0x9d,
	0x3a, 0x75, 0xea, 0x3c, 0xbe, 0x6a, 0x43, 0xd9, 0x76, 0x6c, 0xff, 0xba, 0xed, 0x07, 0x5c, 0x70,
	0x92, 0x43, 0xa1, 0xa1, 0x07, 0xd6, 0x8d, 0x30, 0xed, 0xb9, 0x13, 0x03, 0x8d, 0xca, 0x9c, 0x09,
	0x2b, 0x91, 0xaa, 0x2c, 0x08, 0x78, 0xb0, 0x11, 0xa7, 0xcb, 0xc0, 0x4f, 0x9c, 0x34, 0x1e, 0x4f,
	0xf8, 0x84, 0xe3, 0xf2, 0xb9, 0x5c, 0x45, 0xda, 0x83, 0x60, 0x11, 0x0a, 0x5c, 0x2a, 0x45, 0x6b,
	0x04, 0xf9, 0x13, 0x66, 0x39, 0x2c, 0x20, 0x3f, 0x00, 0xb0, 0x67, 0x8b, 0x50, 0xb0, 0xc0, 0x74,
	0x9d, 0xba, 0xf6, 0x54, 0x3b, 0xca, 0xd2, 0x52, 0xa4, 0x19, 0x3a, 0xe4, 0x0b, 0xa8, 0x0a, 0xd7,
	0x76, 0x6c, 0x73, 0xc9, 0x82, 0xd0, 0xe5, 0x5e, 0x3d, 0xfd, 0x54, 0x3b, 0x2a, 0xd1, 0x0a, 0x2a,
	0xaf, 0x94, 0xae, 0xf5, 0x1c, 0xf4, 0xfe, 0xc2, 0x9f, 0xb9, 0xb6, 0x25, 0x18, 0x65, 0x6f, 0x17,
	0x2c, 0x14, 0xe4, 0xfb, 0x50, 0x0a, 0xd8, 0xc4, 0xe5, 0xde, 0xc6, 0x6d, 0x51, 0x29, 0x86, 0x4e,
	0xeb, 0x05, 0x54, 0x7b, 0x7c, 0xee, 0x5b, 0xc2, 0xbd, 0x76, 0x67, 0xae, 0x58, 0x93, 0xaf, 0x40,
	0x0f, 0xd8, 0xdb, 0x85, 0x1b, 0x30, 0x27, 0x39, 0x49, 0xc3, 0x93, 0x0e, 0x62, 0x7d, 0x7c, 0xd8,
	0x6f, 0xe1, 0xb0, 0x17, 0x85, 0xd7, 0x3f, 0x73, 0xc3, 0xb9, 0x25, 0xec, 0x5b, 0x52, 0x87, 0x82,
	0xbd, 0x08, 0x02, 0xe6, 0x89, 0xe8, 0xac, 0x58, 0x94, 0x48, 0xa0, 0x42, 0xc2, 0xd0, 0xb3, 0x34,
	0x16, 0x5b, 0x47, 0x50, 0xea, 0x71, 0x6f, 0xc2, 0x42, 0xc1, 0x9c, 0x87, 0xc3, 0xfd, 0x57, 0x06,
	0x72, 0x03, 0x99, 0x75, 0xf2, 0x2d, 0x80, 0xc7, 0x85, 0x39, 0xc3, 0xdc, 0xa1, 0x5d, 0xb9, 0x43,
	0xda, 0x71, 0x45, 0xc6, 0x5c, 0x8c, 0x10, 0xa1, 0x25, 0x2f, 0x5e, 0x92, 0x63, 0x79, 0x35, 0xf4,
	0x2c, 0x77, 0xde, 0xf0, 0x85, 0xe7, 0x60, 0x24, 0xe5, 0xce, 0x93, 0x64, 0x23, 0x45, 0x83, 0x31,
	0x17, 0xaf, 0x24, 0x4c, 0x6b, 0xc1, 0x8e, 0x4c, 0x5e, 0xc2, 0x01, 0xf3, 0xb9, 0x7d, 0x8b, 0x1e,
	0xf0, 0xc2, 0xf5, 0x0c, 0x7a, 0xf8, 0x2c, 0xf1, 0x30, 0x90, 0xf8, 0x98, 0x8b, 0x33, 0x89, 0xd2,
	0x2a, 0xdb, 0x16, 0x49, 0x1f, 0x0e, 0x9d, 0xb8, 0x3e, 0x66, 0x9c, 0x8d, 0x6c, 0x14, 0x83, 0x6a,
	0xc1, 0xfd, 0xfa, 0x51, 0xdd, 0xd9, 0xaf, 0xe8, 0x0b, 0xa8, 0xda, 0xdb, 0x45, 0xab, 0xe7, 0xd0,
	0xc3, 0xe3, 0xc8, 0xc3, 0x4e, 0x41, 0xe9, 0xae, 0x29, 0x39, 0x81, 0x47, 0x9b, 0x2e, 0x33, 0xe7,
	0x51, 0xd9, 0xea, 0x79, 0xf4, 0x50, 0x8f, 0x3d, 0xec, 0x97, 0x95, 0x1e, 0x26, 0x8d, 0x98, 0x54,
	0xfa, 0x97, 0x50, 0x0b, 0x59, 0xb0, 0x94, 0x8e, 0x42, 0xf3, 0x7a, 0x11, 0xae, 0xeb, 0x05, 0x74,
	0xf2, 0xbd, 0x24, 0x15, 0x17, 0x08, 0x0f, 0xc3, 0xee, 0x22, 0x5c, 0xd3, 0x4a, 0xb8, 0x25, 0x91,
	0x36, 0x94, 0xec, 0xb8, 0xe4, 0xf5, 0x22, 0xee, 0xd3, 0x93, 0xf0, 0x23, 0x3d, 0xdd, 0x98, 0xb4,
	0x5e, 0x42, 0xc1, 0x58, 0x79, 0x43, 0xef, 0x86, 0x93, 0xcf, 0xa1, 0x18, 0x0a, 0x2b, 0x10, 0xa6,
	0x08, 0xe3, 0x16, 0x43, 0xd9, 0x08, 0x65, 0x8b, 0xf9, 0x81, 0x3b, 0xb7, 0x82, 0x35, 0x16, 0xb6,
	0x42, 0x63, 0xb1, 0xf5, 0x27, 0x0d, 0x4a, 0xc6, 0xca, 0xbb, 0x10, 0x96, 0x58, 0x84, 0x0f, 0xb9,
	0x68, 0x41, 0x75, 0xee, 0x7a, 0xa6, 0xcd, 0xe7, 0x73, 0x17, 0x71, 0xd5, 0xab, 0xe5, 0xb9, 0xeb,
	0xf5, 0x50, 0x67, 0x84, 0xb2, 0x45, 0x37, 0x78, 0x46, 0xb5, 0xa8, 0x1d, 0x83, 0xcf, 0xa0, 0xe6,
	0x86, 0x66, 0xc0, 0x67, 0x33, 0xe6, 0x98, 0xd7, 0x96, 0x3d, 0xc5, 0xfa, 0x16, 0x69, 0xc5, 0x0d,
	0x29, 0x2a, 0xbb, 0x96, 0x3d, 0x6d, 0xfd, 0xa5, 0x08, 0xb9, 0xc1, 0x52, 0x8e, 0xc5, 0x43, 0xfd,
	0x4e, 0x1e, 0x43, 0xce, 0xf5, 0x1c, 0xb6, 0x8a, 0xa2, 0x50, 0x82, 0x64, 0x8a, 0xa8, 0x77, 0xe4,
	0x9e, 0x82, 0x62, 0x8a, 0x48, 0x33, 0x74, 0xc8, 0x37, 0x50, 0x60, 0x9e, 0x08, 0x5c, 0x16, 0x46,
	0xcd, 0x19, 0x37, 0x06, 0x1e, 0xd8, 0x1e, 0x28, 0xec, 0x24, 0x45, 0x63, 0x33, 0xf2, 0x35, 0xe4,
	0x2c, 0x67, 0xee, 0x7a, 0x51, 0x2b, 0x92, 0x1d, 0xfb, 0x63, 0x89, 0x9c, 0xa4, 0xa8, 0x32, 0x21,
	0xcf, 0x20, 0x87, 0xf5, 0x8d, 0x9a, 0xae, 0x12, 0xdb, 0x4a, 0x9d, 0xb4, 0x42, 0x90, 0x7c, 0x09,
	0xe5, 0x80, 0x85, 0x7c, 0xb6, 0x64, 0x8e, 0x4c, 0x92, 0x6c, 0xaf, 0x6c, 0x37, 0x5d, 0xd7, 0x4e,
	0x52, 0x14, 0x62, 0xc0, 0x08, 0xc9, 0xb7, 0x50, 0x9c, 0x71, 0x6f, 0x62, 0x8a, 0x95, 0x17, 0x75,
	0xc1, 0x6e, 0xac, 0x23, 0xee, 0x4d, 0x8c, 0x95, 0x3c, 0xbd, 0x30, 0x53, 0xcb, 0xc6, 0xbf, 0xd3,
	0x90, 0xa1, 0xfc, 0xdd, 0x43, 0x35, 0xdc, 0xa9, 0x4f, 0x7a, 0xaf, 0x3e, 0x47, 0x90, 0x15, 0x6b,
	0x9f, 0x61, 0x6a, 0x6a, 0x9f, 0x1c, 0x37, 0x31, 0xd6, 0x3e, 0xa3, 0x68, 0x41, 0x9a, 0x00, 0x13,
	0xe6, 0xb1, 0xc0, 0x12, 0x92, 0x04, 0x01, 0xfd, 0x6c, 0x69, 0x64, 0x9e, 0xb9, 0x6f, 0xa2, 0xb3,
	0x2c, 0x3a, 0x7b, 0xb2, 0xe3, 0x8c, 0xf2, 0x77, 0xed, 0x73, 0x1f, 0xfd, 0xe5, 0x39, 0xfe, 0x12,
	0x1d, 0x32, 0x53, 0xa6, 0xc6, 0xb5, 0x42, 0xe5, 0x52, 0x16, 0x78, 0x69, 0xcd, 0x16, 0x0c, 0x33,
	0x54, 0xa1, 0x4a, 0x90, 0x17, 0xe0, 0x33, 0xc7, 0x54, 0x48, 0x01, 0x91, 0x22, 0x9f, 0x39, 0x57,
	0x08, 0xfe, 0x14, 0x1e, 0xb1, 0x95, 0xef, 0x06, 0xcc, 0x14, 0xa1, 0xb9, 0xf0, 0xdc, 0x95, 0x19,
	0x32, 0x3b, 0xc4, 0xf4, 0x65, 0xa9, 0xae, 0x20, 0x23, 0xbc, 0xf4, 0xdc, 0xd5, 0x05, 0xb3, 0x43,
	0xd9, 0x2c, 0x62, 0xe5, 0x99, 0x21, 0x5f, 0x04, 0x36, 0xab, 0x97, 0x54, 0xb3, 0x88, 0x95, 0x77,
	0x81, 0x8a, 0xd6, 0xd7, 0x90, 0x57, 0x41, 0x92, 0x32, 0x14, 0x2e, 0xc7, 0xa7, 0xe3, 0xf3, 0x37,
	0x63, 0x3d, 0x45, 0x0a, 0x90, 0x79, 0x7d, 0x69, 0xe8, 0x1a, 0x01, 0xc8, 0xf7, 0x07, 0xa3, 0x81,
	0x31, 0xd0, 0xd3, 0x8d, 0xef, 0xa0, 0x30, 0x48, 0x3a, 0x26, 0xe9, 0x31, 0xed, 0x69, 0x66, 0x6b,
	0x7a, 0x93, 0xbb, 0x27, 0xdd, 0xd5, 0xf8, 0xa3, 0x06, 0x39, 0x6c, 0x22, 0xf2, 0x2b, 0xa8, 0x62,
	0x13, 0x25, 0xd4, 0xa7, 0x45, 0xb4, 0xb3, 0xf5, 0xd2, 0xa2, 0x65, 0xcc, 0x7d, 0x15, 0x6b, 0x4b,
	0x22, 0xbf, 0x81, 0x5a, 0xbc, 0x3d, 0xf4, 0xb9, 0x17, 0xb2, 0x88, 0xbe, 0x3f, 0xff, 0x2f, 0xfb,
	0x95, 0x01, 0xad, 0x5a, 0xdb, 0x62, 0xe3, 0xe7, 0x50, 0x88, 0x5a, 0x8a, 0x7c, 0x05, 0x45, 0x99,
	0x17, 0xd7, 0xbb, 0xe1, 0xd1, 0x15, 0x6a, 0xd1, 0x15, 0x22, 0xa2, 0xa1, 0x05, 0xa1, 0x16, 0xad,
	0xdf, 0xcb, 0x5d, 0x93, 0x4f, 0x93, 0x54, 0x81, 0xe2, 0x6b, 0x3a, 0x78, 0x43, 0x87, 0xc6, 0x40,
	0x65, 0xaa, 0x77, 0x7e, 0x76, 0x36, 0x34, 0xf4, 0xb4, 0x44, 0xe8, 0xf9, 0x68, 0xd4, 0x3d, 0xee,
	0x9d, 0xea, 0x19, 0x52, 0x85, 0x92, 0x42, 0x8c, 0x41, 0x5f, 0xcf, 0x92, 0x03, 0x28, 0x0f, 0xc7,
	0x43, 0x63, 0x78, 0x3c, 0x1a, 0xfe, 0x6e, 0xd0, 0xd7, 0x73, 0xdd, 0x02, 0xe4, 0x98, 0x4c, 0x5b,
	0x6b, 0x0a, 0x07, 0xbd, 0x5b, 0xcb, 0x9b, 0xb0, 0xbe, 0x25, 0x2c, 0x45, 0x0f, 0xcf, 0x20, 0x8f,
	0x58, 0x9c, 0xe7, 0xca, 0x76, 0x9e, 0x69, 0x84, 0x91, 0xce, 0xee, 0xb8, 0xa9, 0xb4, 0x1c, 0x46,
	0xa6, 0x34, 0x99, 0xb7, 0xed, 0xd9, 0x6b, 0x5d, 0x02, 0x6c, 0x10, 0xf5, 0x3a, 0x4b, 0xd6, 0x51,
	0x07, 0xe1, 0xeb, 0x8c, 0x22, 0xa9, 0x41, 0x3a, 0x19, 0xa3, 0xb4, 0x08, 0xf7, 0xd8, 0x27, 0xb3,
	0xc7, 0x3e, 0xad, 0x3f, 0xe4, 0xe1, 0x70, 0x73, 0x89, 0xb8, 0x74, 0x5f, 0x42, 0xfe, 0x76, 0xfb,
	0xa9, 0xae, 0x46, 0xb1, 0xa9, 0x6f, 0x1f, 0x1a, 0x81, 0xbb, 0x64, 0x98, 0xde, 0x23, 0xc3, 0x5f,
	0x40, 0x25, 0x02, 0xf1, 0x51, 0x8d, 0xc8, 0xed, 0x51, 0x3b, 0xfa, 0x28, 0x53, 0x4f, 0x37, 0x3e,
	0xbf, 0xb4, 0x1c, 0x6c, 0x04, 0xf9, 0xe5, 0x64, 0xdf, 0x32, 0x7b, 0xea, 0x73, 0xd7, 0x43, 0x4a,
	0xc8, 0xa2, 0xe3, 0xca, 0x46, 0xa9, 0x38, 0x43, 0xd1, 0xc9, 0x66, 0x40, 0x15, 0xbf, 0x9c, 0xb2,
	0x35, 0x79, 0x22, 0xbb, 0xdd, 0x41, 0x48, 0xcd, 0x69, 0x9e, 0x79, 0x8e, 0x04, 0xfe, 0x07, 0x13,
	0xff, 0x04, 0x8a, 0x6c, 0x25, 0x02, 0xcb, 0xe4, 0x3e, 0xce, 0x67, 0xad, 0xa3, 0xb7, 0xe3, 0xaf,
	0xc4, 0x81, 0x04, 0xce, 0x7d, 0x5a, 0x60, 0x6a, 0x41, 0x7e, 0x0d, 0x78, 0x55, 0xf9, 0xca, 0xe2,
	0x98, 0x96, 0x3b, 0x3f, 0x8a, 0x5f, 0xc4, 0xfd, 0x74, 0xe2, 0x5d, 0xa5, 0xe1, 0x49, 0x8a, 0x26,
	0x9b, 0xc8, 0x15, 0x1c, 0x7a, 0x5c, 0xb8, 0x37, 0x6b, 0x13, 0x07, 0x1e, 0x9f, 0x3a, 0xa4, 0xad,
	0x72, 0xe7, 0xe8, 0x5e, 0x4f, 0x63, 0xdc, 0x91, 0x3c, 0x8d, 0x27, 0x29, 0x7a, 0xe0, 0xed, 0xaa,
	0xc8, 0x00, 0xc0, 0x61, 0x49, 0x68, 0x55, 0x74, 0xf8, 0xc5, 0xbd, 0x0e, 0xfb, 0x89, 0xa9, 0xe4,
	0xfa, 0xcd, 0x46, 0xf2, 0x1d, 0xe4, 0xa7, 0x4b, 0xd3, 0xf2, 0xdd, 0x7a, 0x19, 0x53, 0xd1, 0xbc,
	0xd7, 0xc5, 0xe9, 0xf2, 0xf8, 0xf5, 0x90, 0xe6, 0xa6, 0xcb, 0x63, 0xdf, 0x25, 0x3f, 0x84, 0xf2,
	0x8d, 0x3b, 0x93, 0xdf, 0x2b, 0x33, 0xce, 0xfd, 0x7a, 0x05, 0x1f, 0x53, 0x50, 0xaa, 0x11, 0xe7,
	0x7e, 0x03, 0xa0, 0x18, 0xa7, 0xa3, 0x51, 0x01, 0xd8, 0x9c, 0xdf, 0xe8, 0xc2, 0xc1, 0xde, 0xf5,
	0xc8, 0xf3, 0x88, 0x0d, 0x55, 0x72, 0x76, 0xa9, 0x2b, 0xb1, 0x52, 0xfc, 0x88, 0xcb, 0xd6, 0x8f,
	0x21, 0x87, 0xe1, 0x90, 0x22, 0x64, 0x0d, 0xb7, 0xdf, 0xd5, 0x53, 0xa4, 0x04, 0x39, 0x6a, 0xbd,
	0x3b, 0xbd, 0xd2, 0x35, 0xb9, 0x34, 0x56, 0xde, 0xe9, 0x95, 0x9e, 0xee, 0x96, 0x92, 0xcf, 0xdb,
	0xce, 0x5f, 0x35, 0x80, 0xcd, 0xad, 0xc8, 0x31, 0x94, 0x70, 0x5a, 0x5f, 0x31, 0xe6, 0x90, 0xfa,
	0x7d, 0xb7, 0x6e, 0x7c, 0xf6, 0x09, 0x82, 0xbb, 0x8e, 0xb4, 0x6f, 0x34, 0xd2, 0x83, 0x72, 0xe2,
	0xe2, 0xaa, 0xf3, 0xff, 0x39, 0xe9, 0x76, 0x3e, 0xfc, 0xbd, 0xa8, 0xfd, 0xe3, 0x63, 0x53, 0x7b,
	0xff, 0xb1, 0xa9, 0xfd, 0xf3, 0x63, 0x53, 0xfb, 0xf3, 0x5d, 0x33, 0xf5, 0xb7, 0xbb, 0x66, 0xea,
	0xfd, 0x5d, 0x33, 0xf5, 0xe1, 0xae, 0x99, 0x02, 0x9d, 0x07, 0x93, 0xb6, 0x70, 0xa7, 0xcb, 0xf6,
	0x74, 0x89, 0x7f, 0x4f, 0xae, 0xf3, 0xf8, 0xf3, 0xb3, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0x29,
	0xf0, 0x56, 0x9e, 0x20, 0x0d, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChangeDataClient is the client API for ChangeData service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChangeDataClient interface {
	EventFeed(ctx context.Context, opts ...grpc.CallOption) (ChangeData_EventFeedClient, error)
	// EventFeedV2 is like EventFeed, with some new changes:
	//  * clients send requested features in HTTP/2 headers;
	//  * if servers meets unsupported feature request,
	//    it can fail the stream with an UNIMPLEMENTED error.
	EventFeedV2(ctx context.Context, opts ...grpc.CallOption) (ChangeData_EventFeedV2Client, error)
}

type changeDataClient struct {
	cc *grpc.ClientConn
}

func NewChangeDataClient(cc *grpc.ClientConn) ChangeDataClient {
	return &changeDataClient{cc}
}

func (c *changeDataClient) EventFeed(ctx context.Context, opts ...grpc.CallOption) (ChangeData_EventFeedClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ChangeData_serviceDesc.Streams[0], "/cdcpb.ChangeData/EventFeed", opts...)
	if err != nil {
		return nil, err
	}
	x := &changeDataEventFeedClient{stream}
	return x, nil
}

type ChangeData_EventFeedClient interface {
	Send(*ChangeDataRequest) error
	Recv() (*ChangeDataEvent, error)
	grpc.ClientStream
}

type changeDataEventFeedClient struct {
	grpc.ClientStream
}

func (x *changeDataEventFeedClient) Send(m *ChangeDataRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *changeDataEventFeedClient) Recv() (*ChangeDataEvent, error) {
	m := new(ChangeDataEvent)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *changeDataClient) EventFeedV2(ctx context.Context, opts ...grpc.CallOption) (ChangeData_EventFeedV2Client, error) {
	stream, err := c.cc.NewStream(ctx, &_ChangeData_serviceDesc.Streams[1], "/cdcpb.ChangeData/EventFeedV2", opts...)
	if err != nil {
		return nil, err
	}
	x := &changeDataEventFeedV2Client{stream}
	return x, nil
}

type ChangeData_EventFeedV2Client interface {
	Send(*ChangeDataRequest) error
	Recv() (*ChangeDataEvent, error)
	grpc.ClientStream
}

type changeDataEventFeedV2Client struct {
	grpc.ClientStream
}

func (x *changeDataEventFeedV2Client) Send(m *ChangeDataRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *changeDataEventFeedV2Client) Recv() (*ChangeDataEvent, error) {
	m := new(ChangeDataEvent)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ChangeDataServer is the server API for ChangeData service.
type ChangeDataServer interface {
	EventFeed(ChangeData_EventFeedServer) error
	// EventFeedV2 is like EventFeed, with some new changes:
	//  * clients send requested features in HTTP/2 headers;
	//  * if servers meets unsupported feature request,
	//    it can fail the stream with an UNIMPLEMENTED error.
	EventFeedV2(ChangeData_EventFeedV2Server) error
}

// UnimplementedChangeDataServer can be embedded to have forward compatible implementations.
type UnimplementedChangeDataServer struct {
}

func (*UnimplementedChangeDataServer) EventFeed(srv ChangeData_EventFeedServer) error {
	return status.Errorf(codes.Unimplemented, "method EventFeed not implemented")
}
func (*UnimplementedChangeDataServer) EventFeedV2(srv ChangeData_EventFeedV2Server) error {
	return status.Errorf(codes.Unimplemented, "method EventFeedV2 not implemented")
}

func RegisterChangeDataServer(s *grpc.Server, srv ChangeDataServer) {
	s.RegisterService(&_ChangeData_serviceDesc, srv)
}

func _ChangeData_EventFeed_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ChangeDataServer).EventFeed(&changeDataEventFeedServer{stream})
}

type ChangeData_EventFeedServer interface {
	Send(*ChangeDataEvent) error
	Recv() (*ChangeDataRequest, error)
	grpc.ServerStream
}

type changeDataEventFeedServer struct {
	grpc.ServerStream
}

func (x *changeDataEventFeedServer) Send(m *ChangeDataEvent) error {
	return x.ServerStream.SendMsg(m)
}

func (x *changeDataEventFeedServer) Recv() (*ChangeDataRequest, error) {
	m := new(ChangeDataRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ChangeData_EventFeedV2_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ChangeDataServer).EventFeedV2(&changeDataEventFeedV2Server{stream})
}

type ChangeData_EventFeedV2Server interface {
	Send(*ChangeDataEvent) error
	Recv() (*ChangeDataRequest, error)
	grpc.ServerStream
}

type changeDataEventFeedV2Server struct {
	grpc.ServerStream
}

func (x *changeDataEventFeedV2Server) Send(m *ChangeDataEvent) error {
	return x.ServerStream.SendMsg(m)
}

func (x *changeDataEventFeedV2Server) Recv() (*ChangeDataRequest, error) {
	m := new(ChangeDataRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _ChangeData_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cdcpb.ChangeData",
	HandlerType: (*ChangeDataServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "EventFeed",
			Handler:       _ChangeData_EventFeed_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "EventFeedV2",
			Handler:       _ChangeData_EventFeedV2_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "cdcpb.proto",
}

func (m *Header) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Header) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Header) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TicdcVersion) > 0 {
		i -= len(m.TicdcVersion)
		copy(dAtA[i:], m.TicdcVersion)
		i = encodeVarintCdcpb(dAtA, i, uint64(len(m.TicdcVersion)))
		i--
		dAtA[i] = 0x12
	}
	if m.ClusterId != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.ClusterId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DuplicateRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DuplicateRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DuplicateRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Compatibility) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Compatibility) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Compatibility) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RequiredVersion) > 0 {
		i -= len(m.RequiredVersion)
		copy(dAtA[i:], m.RequiredVersion)
		i = encodeVarintCdcpb(dAtA, i, uint64(len(m.RequiredVersion)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ClusterIDMismatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClusterIDMismatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ClusterIDMismatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Request != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.Request))
		i--
		dAtA[i] = 0x10
	}
	if m.Current != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.Current))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Congested) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Congested) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Congested) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Congested != nil {
		{
			size, err := m.Congested.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.ServerIsBusy != nil {
		{
			size, err := m.ServerIsBusy.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.ClusterIdMismatch != nil {
		{
			size, err := m.ClusterIdMismatch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Compatibility != nil {
		{
			size, err := m.Compatibility.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.DuplicateRequest != nil {
		{
			size, err := m.DuplicateRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.EpochNotMatch != nil {
		{
			size, err := m.EpochNotMatch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionNotFound != nil {
		{
			size, err := m.RegionNotFound.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.NotLeader != nil {
		{
			size, err := m.NotLeader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TxnInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TxnInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TxnInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Primary) > 0 {
		i -= len(m.Primary)
		copy(dAtA[i:], m.Primary)
		i = encodeVarintCdcpb(dAtA, i, uint64(len(m.Primary)))
		i--
		dAtA[i] = 0x12
	}
	if m.StartTs != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TxnStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TxnStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TxnStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsRolledBack {
		i--
		if m.IsRolledBack {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if m.CommitTs != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.CommitTs))
		i--
		dAtA[i] = 0x18
	}
	if m.MinCommitTs != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.MinCommitTs))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTs != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Event) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Event) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Event != nil {
		{
			size := m.Event.Size()
			i -= size
			if _, err := m.Event.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	if m.RequestId != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.RequestId))
		i--
		dAtA[i] = 0x38
	}
	if m.Index != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.Index))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Event_Entries_) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_Entries_) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Entries != nil {
		{
			size, err := m.Entries.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *Event_Admin_) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_Admin_) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Admin != nil {
		{
			size, err := m.Admin.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	return len(dAtA) - i, nil
}
func (m *Event_Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	return len(dAtA) - i, nil
}
func (m *Event_ResolvedTs) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_ResolvedTs) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i = encodeVarintCdcpb(dAtA, i, uint64(m.ResolvedTs))
	i--
	dAtA[i] = 0x30
	return len(dAtA) - i, nil
}
func (m *Event_LongTxn_) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_LongTxn_) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.LongTxn != nil {
		{
			size, err := m.LongTxn.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	return len(dAtA) - i, nil
}
func (m *Event_Row) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Event_Row) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_Row) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Generation != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.Generation))
		i--
		dAtA[i] = 0x50
	}
	if m.TxnSource != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.TxnSource))
		i--
		dAtA[i] = 0x48
	}
	if m.ExpireTsUnixSecs != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.ExpireTsUnixSecs))
		i--
		dAtA[i] = 0x40
	}
	if len(m.OldValue) > 0 {
		i -= len(m.OldValue)
		copy(dAtA[i:], m.OldValue)
		i = encodeVarintCdcpb(dAtA, i, uint64(len(m.OldValue)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintCdcpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintCdcpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x2a
	}
	if m.OpType != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.OpType))
		i--
		dAtA[i] = 0x20
	}
	if m.Type != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x18
	}
	if m.CommitTs != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.CommitTs))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTs != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Event_Entries) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Event_Entries) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_Entries) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Entries) > 0 {
		for iNdEx := len(m.Entries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Entries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCdcpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Event_Admin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Event_Admin) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_Admin) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AdminResponse != nil {
		{
			size, err := m.AdminResponse.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.AdminRequest != nil {
		{
			size, err := m.AdminRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Event_LongTxn) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Event_LongTxn) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Event_LongTxn) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TxnInfo) > 0 {
		for iNdEx := len(m.TxnInfo) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TxnInfo[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCdcpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ChangeDataEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeDataEvent) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangeDataEvent) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ResolvedTs != nil {
		{
			size, err := m.ResolvedTs.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Events) > 0 {
		for iNdEx := len(m.Events) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Events[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCdcpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ResolvedTs) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResolvedTs) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResolvedTs) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RequestId != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.RequestId))
		i--
		dAtA[i] = 0x18
	}
	if m.Ts != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.Ts))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Regions) > 0 {
		dAtA17 := make([]byte, len(m.Regions)*10)
		var j16 int
		for _, num := range m.Regions {
			for num >= 1<<7 {
				dAtA17[j16] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j16++
			}
			dAtA17[j16] = uint8(num)
			j16++
		}
		i -= j16
		copy(dAtA[i:], dAtA17[:j16])
		i = encodeVarintCdcpb(dAtA, i, uint64(j16))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ChangeDataRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeDataRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangeDataRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Request != nil {
		{
			size := m.Request.Size()
			i -= size
			if _, err := m.Request.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	if m.FilterLoop {
		i--
		if m.FilterLoop {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x60
	}
	if m.KvApi != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.KvApi))
		i--
		dAtA[i] = 0x58
	}
	if m.ExtraOp != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.ExtraOp))
		i--
		dAtA[i] = 0x40
	}
	if m.RequestId != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.RequestId))
		i--
		dAtA[i] = 0x38
	}
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintCdcpb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintCdcpb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x2a
	}
	if m.CheckpointTs != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.CheckpointTs))
		i--
		dAtA[i] = 0x20
	}
	if m.RegionEpoch != nil {
		{
			size, err := m.RegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionId != 0 {
		i = encodeVarintCdcpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ChangeDataRequest_Register_) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangeDataRequest_Register_) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Register != nil {
		{
			size, err := m.Register.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	return len(dAtA) - i, nil
}
func (m *ChangeDataRequest_NotifyTxnStatus_) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangeDataRequest_NotifyTxnStatus_) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.NotifyTxnStatus != nil {
		{
			size, err := m.NotifyTxnStatus.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	return len(dAtA) - i, nil
}
func (m *ChangeDataRequest_Deregister_) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangeDataRequest_Deregister_) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Deregister != nil {
		{
			size, err := m.Deregister.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCdcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	return len(dAtA) - i, nil
}
func (m *ChangeDataRequest_Register) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeDataRequest_Register) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangeDataRequest_Register) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ChangeDataRequest_Deregister) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeDataRequest_Deregister) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangeDataRequest_Deregister) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ChangeDataRequest_NotifyTxnStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeDataRequest_NotifyTxnStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChangeDataRequest_NotifyTxnStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TxnStatus) > 0 {
		for iNdEx := len(m.TxnStatus) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TxnStatus[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCdcpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintCdcpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovCdcpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Header) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovCdcpb(uint64(m.ClusterId))
	}
	l = len(m.TicdcVersion)
	if l > 0 {
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}

func (m *DuplicateRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovCdcpb(uint64(m.RegionId))
	}
	return n
}

func (m *Compatibility) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RequiredVersion)
	if l > 0 {
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}

func (m *ClusterIDMismatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Current != 0 {
		n += 1 + sovCdcpb(uint64(m.Current))
	}
	if m.Request != 0 {
		n += 1 + sovCdcpb(uint64(m.Request))
	}
	return n
}

func (m *Congested) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovCdcpb(uint64(m.RegionId))
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.NotLeader != nil {
		l = m.NotLeader.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.RegionNotFound != nil {
		l = m.RegionNotFound.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.EpochNotMatch != nil {
		l = m.EpochNotMatch.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.DuplicateRequest != nil {
		l = m.DuplicateRequest.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.Compatibility != nil {
		l = m.Compatibility.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.ClusterIdMismatch != nil {
		l = m.ClusterIdMismatch.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.ServerIsBusy != nil {
		l = m.ServerIsBusy.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.Congested != nil {
		l = m.Congested.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}

func (m *TxnInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTs != 0 {
		n += 1 + sovCdcpb(uint64(m.StartTs))
	}
	l = len(m.Primary)
	if l > 0 {
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}

func (m *TxnStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTs != 0 {
		n += 1 + sovCdcpb(uint64(m.StartTs))
	}
	if m.MinCommitTs != 0 {
		n += 1 + sovCdcpb(uint64(m.MinCommitTs))
	}
	if m.CommitTs != 0 {
		n += 1 + sovCdcpb(uint64(m.CommitTs))
	}
	if m.IsRolledBack {
		n += 2
	}
	return n
}

func (m *Event) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovCdcpb(uint64(m.RegionId))
	}
	if m.Index != 0 {
		n += 1 + sovCdcpb(uint64(m.Index))
	}
	if m.Event != nil {
		n += m.Event.Size()
	}
	if m.RequestId != 0 {
		n += 1 + sovCdcpb(uint64(m.RequestId))
	}
	return n
}

func (m *Event_Entries_) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Entries != nil {
		l = m.Entries.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}
func (m *Event_Admin_) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Admin != nil {
		l = m.Admin.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}
func (m *Event_Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}
func (m *Event_ResolvedTs) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovCdcpb(uint64(m.ResolvedTs))
	return n
}
func (m *Event_LongTxn_) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.LongTxn != nil {
		l = m.LongTxn.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}
func (m *Event_Row) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTs != 0 {
		n += 1 + sovCdcpb(uint64(m.StartTs))
	}
	if m.CommitTs != 0 {
		n += 1 + sovCdcpb(uint64(m.CommitTs))
	}
	if m.Type != 0 {
		n += 1 + sovCdcpb(uint64(m.Type))
	}
	if m.OpType != 0 {
		n += 1 + sovCdcpb(uint64(m.OpType))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovCdcpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovCdcpb(uint64(l))
	}
	l = len(m.OldValue)
	if l > 0 {
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.ExpireTsUnixSecs != 0 {
		n += 1 + sovCdcpb(uint64(m.ExpireTsUnixSecs))
	}
	if m.TxnSource != 0 {
		n += 1 + sovCdcpb(uint64(m.TxnSource))
	}
	if m.Generation != 0 {
		n += 1 + sovCdcpb(uint64(m.Generation))
	}
	return n
}

func (m *Event_Entries) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Entries) > 0 {
		for _, e := range m.Entries {
			l = e.Size()
			n += 1 + l + sovCdcpb(uint64(l))
		}
	}
	return n
}

func (m *Event_Admin) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.AdminRequest != nil {
		l = m.AdminRequest.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.AdminResponse != nil {
		l = m.AdminResponse.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}

func (m *Event_LongTxn) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TxnInfo) > 0 {
		for _, e := range m.TxnInfo {
			l = e.Size()
			n += 1 + l + sovCdcpb(uint64(l))
		}
	}
	return n
}

func (m *ChangeDataEvent) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Events) > 0 {
		for _, e := range m.Events {
			l = e.Size()
			n += 1 + l + sovCdcpb(uint64(l))
		}
	}
	if m.ResolvedTs != nil {
		l = m.ResolvedTs.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}

func (m *ResolvedTs) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Regions) > 0 {
		l = 0
		for _, e := range m.Regions {
			l += sovCdcpb(uint64(e))
		}
		n += 1 + sovCdcpb(uint64(l)) + l
	}
	if m.Ts != 0 {
		n += 1 + sovCdcpb(uint64(m.Ts))
	}
	if m.RequestId != 0 {
		n += 1 + sovCdcpb(uint64(m.RequestId))
	}
	return n
}

func (m *ChangeDataRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovCdcpb(uint64(m.RegionId))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.CheckpointTs != 0 {
		n += 1 + sovCdcpb(uint64(m.CheckpointTs))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovCdcpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovCdcpb(uint64(l))
	}
	if m.RequestId != 0 {
		n += 1 + sovCdcpb(uint64(m.RequestId))
	}
	if m.ExtraOp != 0 {
		n += 1 + sovCdcpb(uint64(m.ExtraOp))
	}
	if m.Request != nil {
		n += m.Request.Size()
	}
	if m.KvApi != 0 {
		n += 1 + sovCdcpb(uint64(m.KvApi))
	}
	if m.FilterLoop {
		n += 2
	}
	return n
}

func (m *ChangeDataRequest_Register_) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Register != nil {
		l = m.Register.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}
func (m *ChangeDataRequest_NotifyTxnStatus_) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.NotifyTxnStatus != nil {
		l = m.NotifyTxnStatus.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}
func (m *ChangeDataRequest_Deregister_) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Deregister != nil {
		l = m.Deregister.Size()
		n += 1 + l + sovCdcpb(uint64(l))
	}
	return n
}
func (m *ChangeDataRequest_Register) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ChangeDataRequest_Deregister) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ChangeDataRequest_NotifyTxnStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TxnStatus) > 0 {
		for _, e := range m.TxnStatus {
			l = e.Size()
			n += 1 + l + sovCdcpb(uint64(l))
		}
	}
	return n
}

func sovCdcpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozCdcpb(x uint64) (n int) {
	return sovCdcpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Header) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Header: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Header: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicdcVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TicdcVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DuplicateRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DuplicateRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DuplicateRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Compatibility) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Compatibility: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Compatibility: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequiredVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequiredVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClusterIDMismatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ClusterIDMismatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ClusterIDMismatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Current", wireType)
			}
			m.Current = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Current |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Request", wireType)
			}
			m.Request = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Request |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Congested) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Congested: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Congested: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NotLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NotLeader == nil {
				m.NotLeader = &errorpb.NotLeader{}
			}
			if err := m.NotLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionNotFound", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionNotFound == nil {
				m.RegionNotFound = &errorpb.RegionNotFound{}
			}
			if err := m.RegionNotFound.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EpochNotMatch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EpochNotMatch == nil {
				m.EpochNotMatch = &errorpb.EpochNotMatch{}
			}
			if err := m.EpochNotMatch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DuplicateRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DuplicateRequest == nil {
				m.DuplicateRequest = &DuplicateRequest{}
			}
			if err := m.DuplicateRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Compatibility", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Compatibility == nil {
				m.Compatibility = &Compatibility{}
			}
			if err := m.Compatibility.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterIdMismatch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ClusterIdMismatch == nil {
				m.ClusterIdMismatch = &ClusterIDMismatch{}
			}
			if err := m.ClusterIdMismatch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServerIsBusy", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ServerIsBusy == nil {
				m.ServerIsBusy = &errorpb.ServerIsBusy{}
			}
			if err := m.ServerIsBusy.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Congested", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Congested == nil {
				m.Congested = &Congested{}
			}
			if err := m.Congested.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TxnInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TxnInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TxnInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Primary", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Primary = append(m.Primary[:0], dAtA[iNdEx:postIndex]...)
			if m.Primary == nil {
				m.Primary = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TxnStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TxnStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TxnStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinCommitTs", wireType)
			}
			m.MinCommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinCommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitTs", wireType)
			}
			m.CommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsRolledBack", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRolledBack = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Event) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Event: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Event: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Event_Entries{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Event = &Event_Entries_{v}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Admin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Event_Admin{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Event = &Event_Admin_{v}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Error{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Event = &Event_Error{v}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolvedTs", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Event = &Event_ResolvedTs{v}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			m.RequestId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LongTxn", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Event_LongTxn{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Event = &Event_LongTxn_{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Event_Row) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Row: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Row: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitTs", wireType)
			}
			m.CommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= Event_LogType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= Event_Row_OpType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OldValue", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OldValue = append(m.OldValue[:0], dAtA[iNdEx:postIndex]...)
			if m.OldValue == nil {
				m.OldValue = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExpireTsUnixSecs", wireType)
			}
			m.ExpireTsUnixSecs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTsUnixSecs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxnSource", wireType)
			}
			m.TxnSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TxnSource |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Generation", wireType)
			}
			m.Generation = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Generation |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Event_Entries) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Entries: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Entries: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Entries = append(m.Entries, &Event_Row{})
			if err := m.Entries[len(m.Entries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Event_Admin) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Admin: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Admin: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdminRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdminRequest == nil {
				m.AdminRequest = &raft_cmdpb.AdminRequest{}
			}
			if err := m.AdminRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdminResponse", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdminResponse == nil {
				m.AdminResponse = &raft_cmdpb.AdminResponse{}
			}
			if err := m.AdminResponse.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Event_LongTxn) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LongTxn: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LongTxn: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxnInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TxnInfo = append(m.TxnInfo, &TxnInfo{})
			if err := m.TxnInfo[len(m.TxnInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeDataEvent) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangeDataEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangeDataEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Events", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Events = append(m.Events, &Event{})
			if err := m.Events[len(m.Events)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolvedTs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ResolvedTs == nil {
				m.ResolvedTs = &ResolvedTs{}
			}
			if err := m.ResolvedTs.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResolvedTs) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResolvedTs: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResolvedTs: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCdcpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Regions = append(m.Regions, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCdcpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCdcpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthCdcpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Regions) == 0 {
					m.Regions = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCdcpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Regions = append(m.Regions, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			m.RequestId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeDataRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChangeDataRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChangeDataRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &Header{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckpointTs", wireType)
			}
			m.CheckpointTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckpointTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestId", wireType)
			}
			m.RequestId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExtraOp", wireType)
			}
			m.ExtraOp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtraOp |= kvrpcpb.ExtraOp(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Register", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ChangeDataRequest_Register{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Request = &ChangeDataRequest_Register_{v}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NotifyTxnStatus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ChangeDataRequest_NotifyTxnStatus{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Request = &ChangeDataRequest_NotifyTxnStatus_{v}
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvApi", wireType)
			}
			m.KvApi = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KvApi |= ChangeDataRequest_KvAPI(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FilterLoop", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FilterLoop = bool(v != 0)
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Deregister", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ChangeDataRequest_Deregister{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Request = &ChangeDataRequest_Deregister_{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeDataRequest_Register) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Register: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Register: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeDataRequest_Deregister) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Deregister: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Deregister: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeDataRequest_NotifyTxnStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NotifyTxnStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NotifyTxnStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxnStatus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCdcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCdcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TxnStatus = append(m.TxnStatus, &TxnStatus{})
			if err := m.TxnStatus[len(m.TxnStatus)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCdcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCdcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipCdcpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCdcpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCdcpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthCdcpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupCdcpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthCdcpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthCdcpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCdcpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupCdcpb = fmt.Errorf("proto: unexpected end of group")
)
