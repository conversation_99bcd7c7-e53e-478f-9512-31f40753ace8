// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: keyspacepb.proto

package keyspacepb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	pdpb "github.com/pingcap/kvproto/pkg/pdpb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type KeyspaceState int32

const (
	KeyspaceState_ENABLED   KeyspaceState = 0
	KeyspaceState_DISABLED  KeyspaceState = 1
	KeyspaceState_ARCHIVED  KeyspaceState = 2
	KeyspaceState_TOMBSTONE KeyspaceState = 3
)

var KeyspaceState_name = map[int32]string{
	0: "ENABLED",
	1: "DISABLED",
	2: "ARCHIVED",
	3: "TOMBSTONE",
}

var KeyspaceState_value = map[string]int32{
	"ENABLED":   0,
	"DISABLED":  1,
	"ARCHIVED":  2,
	"TOMBSTONE": 3,
}

func (x KeyspaceState) String() string {
	return proto.EnumName(KeyspaceState_name, int32(x))
}

func (KeyspaceState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{0}
}

type KeyspaceMeta struct {
	Id             uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	State          KeyspaceState     `protobuf:"varint,3,opt,name=state,proto3,enum=keyspacepb.KeyspaceState" json:"state,omitempty"`
	CreatedAt      int64             `protobuf:"varint,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	StateChangedAt int64             `protobuf:"varint,5,opt,name=state_changed_at,json=stateChangedAt,proto3" json:"state_changed_at,omitempty"`
	Config         map[string]string `protobuf:"bytes,7,rep,name=config,proto3" json:"config,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (m *KeyspaceMeta) Reset()         { *m = KeyspaceMeta{} }
func (m *KeyspaceMeta) String() string { return proto.CompactTextString(m) }
func (*KeyspaceMeta) ProtoMessage()    {}
func (*KeyspaceMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{0}
}
func (m *KeyspaceMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeyspaceMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeyspaceMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KeyspaceMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyspaceMeta.Merge(m, src)
}
func (m *KeyspaceMeta) XXX_Size() int {
	return m.Size()
}
func (m *KeyspaceMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyspaceMeta.DiscardUnknown(m)
}

var xxx_messageInfo_KeyspaceMeta proto.InternalMessageInfo

func (m *KeyspaceMeta) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *KeyspaceMeta) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *KeyspaceMeta) GetState() KeyspaceState {
	if m != nil {
		return m.State
	}
	return KeyspaceState_ENABLED
}

func (m *KeyspaceMeta) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *KeyspaceMeta) GetStateChangedAt() int64 {
	if m != nil {
		return m.StateChangedAt
	}
	return 0
}

func (m *KeyspaceMeta) GetConfig() map[string]string {
	if m != nil {
		return m.Config
	}
	return nil
}

type LoadKeyspaceRequest struct {
	Header *pdpb.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Name   string              `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (m *LoadKeyspaceRequest) Reset()         { *m = LoadKeyspaceRequest{} }
func (m *LoadKeyspaceRequest) String() string { return proto.CompactTextString(m) }
func (*LoadKeyspaceRequest) ProtoMessage()    {}
func (*LoadKeyspaceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{1}
}
func (m *LoadKeyspaceRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LoadKeyspaceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LoadKeyspaceRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LoadKeyspaceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoadKeyspaceRequest.Merge(m, src)
}
func (m *LoadKeyspaceRequest) XXX_Size() int {
	return m.Size()
}
func (m *LoadKeyspaceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_LoadKeyspaceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_LoadKeyspaceRequest proto.InternalMessageInfo

func (m *LoadKeyspaceRequest) GetHeader() *pdpb.RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *LoadKeyspaceRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type LoadKeyspaceResponse struct {
	Header   *pdpb.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Keyspace *KeyspaceMeta        `protobuf:"bytes,2,opt,name=keyspace,proto3" json:"keyspace,omitempty"`
}

func (m *LoadKeyspaceResponse) Reset()         { *m = LoadKeyspaceResponse{} }
func (m *LoadKeyspaceResponse) String() string { return proto.CompactTextString(m) }
func (*LoadKeyspaceResponse) ProtoMessage()    {}
func (*LoadKeyspaceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{2}
}
func (m *LoadKeyspaceResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LoadKeyspaceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LoadKeyspaceResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LoadKeyspaceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoadKeyspaceResponse.Merge(m, src)
}
func (m *LoadKeyspaceResponse) XXX_Size() int {
	return m.Size()
}
func (m *LoadKeyspaceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LoadKeyspaceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LoadKeyspaceResponse proto.InternalMessageInfo

func (m *LoadKeyspaceResponse) GetHeader() *pdpb.ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *LoadKeyspaceResponse) GetKeyspace() *KeyspaceMeta {
	if m != nil {
		return m.Keyspace
	}
	return nil
}

type WatchKeyspacesRequest struct {
	Header *pdpb.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (m *WatchKeyspacesRequest) Reset()         { *m = WatchKeyspacesRequest{} }
func (m *WatchKeyspacesRequest) String() string { return proto.CompactTextString(m) }
func (*WatchKeyspacesRequest) ProtoMessage()    {}
func (*WatchKeyspacesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{3}
}
func (m *WatchKeyspacesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WatchKeyspacesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WatchKeyspacesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WatchKeyspacesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WatchKeyspacesRequest.Merge(m, src)
}
func (m *WatchKeyspacesRequest) XXX_Size() int {
	return m.Size()
}
func (m *WatchKeyspacesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WatchKeyspacesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WatchKeyspacesRequest proto.InternalMessageInfo

func (m *WatchKeyspacesRequest) GetHeader() *pdpb.RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

type WatchKeyspacesResponse struct {
	Header    *pdpb.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Keyspaces []*KeyspaceMeta      `protobuf:"bytes,2,rep,name=keyspaces,proto3" json:"keyspaces,omitempty"`
}

func (m *WatchKeyspacesResponse) Reset()         { *m = WatchKeyspacesResponse{} }
func (m *WatchKeyspacesResponse) String() string { return proto.CompactTextString(m) }
func (*WatchKeyspacesResponse) ProtoMessage()    {}
func (*WatchKeyspacesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{4}
}
func (m *WatchKeyspacesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WatchKeyspacesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WatchKeyspacesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WatchKeyspacesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WatchKeyspacesResponse.Merge(m, src)
}
func (m *WatchKeyspacesResponse) XXX_Size() int {
	return m.Size()
}
func (m *WatchKeyspacesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WatchKeyspacesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WatchKeyspacesResponse proto.InternalMessageInfo

func (m *WatchKeyspacesResponse) GetHeader() *pdpb.ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *WatchKeyspacesResponse) GetKeyspaces() []*KeyspaceMeta {
	if m != nil {
		return m.Keyspaces
	}
	return nil
}

type UpdateKeyspaceStateRequest struct {
	Header *pdpb.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Id     uint32              `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	State  KeyspaceState       `protobuf:"varint,3,opt,name=state,proto3,enum=keyspacepb.KeyspaceState" json:"state,omitempty"`
}

func (m *UpdateKeyspaceStateRequest) Reset()         { *m = UpdateKeyspaceStateRequest{} }
func (m *UpdateKeyspaceStateRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateKeyspaceStateRequest) ProtoMessage()    {}
func (*UpdateKeyspaceStateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{5}
}
func (m *UpdateKeyspaceStateRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateKeyspaceStateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateKeyspaceStateRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateKeyspaceStateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateKeyspaceStateRequest.Merge(m, src)
}
func (m *UpdateKeyspaceStateRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpdateKeyspaceStateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateKeyspaceStateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateKeyspaceStateRequest proto.InternalMessageInfo

func (m *UpdateKeyspaceStateRequest) GetHeader() *pdpb.RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *UpdateKeyspaceStateRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateKeyspaceStateRequest) GetState() KeyspaceState {
	if m != nil {
		return m.State
	}
	return KeyspaceState_ENABLED
}

type UpdateKeyspaceStateResponse struct {
	Header   *pdpb.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Keyspace *KeyspaceMeta        `protobuf:"bytes,2,opt,name=keyspace,proto3" json:"keyspace,omitempty"`
}

func (m *UpdateKeyspaceStateResponse) Reset()         { *m = UpdateKeyspaceStateResponse{} }
func (m *UpdateKeyspaceStateResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateKeyspaceStateResponse) ProtoMessage()    {}
func (*UpdateKeyspaceStateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{6}
}
func (m *UpdateKeyspaceStateResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateKeyspaceStateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateKeyspaceStateResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateKeyspaceStateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateKeyspaceStateResponse.Merge(m, src)
}
func (m *UpdateKeyspaceStateResponse) XXX_Size() int {
	return m.Size()
}
func (m *UpdateKeyspaceStateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateKeyspaceStateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateKeyspaceStateResponse proto.InternalMessageInfo

func (m *UpdateKeyspaceStateResponse) GetHeader() *pdpb.ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *UpdateKeyspaceStateResponse) GetKeyspace() *KeyspaceMeta {
	if m != nil {
		return m.Keyspace
	}
	return nil
}

type GetAllKeyspacesRequest struct {
	Header  *pdpb.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	StartId uint32              `protobuf:"varint,2,opt,name=start_id,json=startId,proto3" json:"start_id,omitempty"`
	Limit   uint32              `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (m *GetAllKeyspacesRequest) Reset()         { *m = GetAllKeyspacesRequest{} }
func (m *GetAllKeyspacesRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllKeyspacesRequest) ProtoMessage()    {}
func (*GetAllKeyspacesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{7}
}
func (m *GetAllKeyspacesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAllKeyspacesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAllKeyspacesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAllKeyspacesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllKeyspacesRequest.Merge(m, src)
}
func (m *GetAllKeyspacesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetAllKeyspacesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllKeyspacesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllKeyspacesRequest proto.InternalMessageInfo

func (m *GetAllKeyspacesRequest) GetHeader() *pdpb.RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetAllKeyspacesRequest) GetStartId() uint32 {
	if m != nil {
		return m.StartId
	}
	return 0
}

func (m *GetAllKeyspacesRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAllKeyspacesResponse struct {
	Header    *pdpb.ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Keyspaces []*KeyspaceMeta      `protobuf:"bytes,2,rep,name=keyspaces,proto3" json:"keyspaces,omitempty"`
}

func (m *GetAllKeyspacesResponse) Reset()         { *m = GetAllKeyspacesResponse{} }
func (m *GetAllKeyspacesResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllKeyspacesResponse) ProtoMessage()    {}
func (*GetAllKeyspacesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c5d91f3e5071166, []int{8}
}
func (m *GetAllKeyspacesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAllKeyspacesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAllKeyspacesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAllKeyspacesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllKeyspacesResponse.Merge(m, src)
}
func (m *GetAllKeyspacesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetAllKeyspacesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllKeyspacesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllKeyspacesResponse proto.InternalMessageInfo

func (m *GetAllKeyspacesResponse) GetHeader() *pdpb.ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetAllKeyspacesResponse) GetKeyspaces() []*KeyspaceMeta {
	if m != nil {
		return m.Keyspaces
	}
	return nil
}

func init() {
	proto.RegisterEnum("keyspacepb.KeyspaceState", KeyspaceState_name, KeyspaceState_value)
	proto.RegisterType((*KeyspaceMeta)(nil), "keyspacepb.KeyspaceMeta")
	proto.RegisterMapType((map[string]string)(nil), "keyspacepb.KeyspaceMeta.ConfigEntry")
	proto.RegisterType((*LoadKeyspaceRequest)(nil), "keyspacepb.LoadKeyspaceRequest")
	proto.RegisterType((*LoadKeyspaceResponse)(nil), "keyspacepb.LoadKeyspaceResponse")
	proto.RegisterType((*WatchKeyspacesRequest)(nil), "keyspacepb.WatchKeyspacesRequest")
	proto.RegisterType((*WatchKeyspacesResponse)(nil), "keyspacepb.WatchKeyspacesResponse")
	proto.RegisterType((*UpdateKeyspaceStateRequest)(nil), "keyspacepb.UpdateKeyspaceStateRequest")
	proto.RegisterType((*UpdateKeyspaceStateResponse)(nil), "keyspacepb.UpdateKeyspaceStateResponse")
	proto.RegisterType((*GetAllKeyspacesRequest)(nil), "keyspacepb.GetAllKeyspacesRequest")
	proto.RegisterType((*GetAllKeyspacesResponse)(nil), "keyspacepb.GetAllKeyspacesResponse")
}

func init() { proto.RegisterFile("keyspacepb.proto", fileDescriptor_5c5d91f3e5071166) }

var fileDescriptor_5c5d91f3e5071166 = []byte{
	// 656 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x55, 0x4d, 0x6f, 0xd3, 0x4c,
	0x10, 0xce, 0x3a, 0xfd, 0x48, 0x26, 0x4d, 0x6a, 0x6d, 0xf3, 0xf6, 0x75, 0x8d, 0x30, 0xc1, 0x20,
	0x88, 0x00, 0xb9, 0xc8, 0x20, 0x04, 0x88, 0x4b, 0xda, 0x44, 0x6d, 0x45, 0x3f, 0x24, 0xa7, 0x94,
	0x03, 0x48, 0xd5, 0x36, 0x5e, 0x12, 0x2b, 0x69, 0x6c, 0xec, 0x6d, 0xa4, 0x72, 0x00, 0x21, 0x71,
	0xe2, 0xc4, 0x91, 0x3f, 0x80, 0xc4, 0x4f, 0xe1, 0xd8, 0x63, 0x8f, 0xa8, 0xf9, 0x23, 0xc8, 0x6b,
	0x3b, 0x75, 0x82, 0x53, 0x44, 0x90, 0x7a, 0xca, 0xcc, 0xec, 0xb3, 0xcf, 0xce, 0x33, 0x1f, 0x31,
	0x88, 0x6d, 0x7a, 0xec, 0x39, 0xa4, 0x41, 0x9d, 0x03, 0xcd, 0x71, 0x6d, 0x66, 0x63, 0x38, 0x8f,
	0xc8, 0xe0, 0x98, 0x51, 0x5c, 0x2e, 0x36, 0xed, 0xa6, 0xcd, 0xcd, 0x65, 0xdf, 0x0a, 0xa3, 0xf3,
	0xee, 0x91, 0xc7, 0xb8, 0x19, 0x04, 0xd4, 0x6f, 0x02, 0xcc, 0x3d, 0x0f, 0x19, 0xb6, 0x28, 0x23,
	0xb8, 0x00, 0x82, 0x65, 0x4a, 0xa8, 0x84, 0xca, 0x79, 0x43, 0xb0, 0x4c, 0x8c, 0x61, 0xaa, 0x4b,
	0x0e, 0xa9, 0x24, 0x94, 0x50, 0x39, 0x6b, 0x70, 0x1b, 0x2f, 0xc3, 0xb4, 0xc7, 0x08, 0xa3, 0x52,
	0xba, 0x84, 0xca, 0x05, 0x7d, 0x49, 0x8b, 0x65, 0x15, 0x91, 0xd5, 0x7d, 0x80, 0x11, 0xe0, 0xf0,
	0x55, 0x80, 0x86, 0x4b, 0x09, 0xa3, 0xe6, 0x3e, 0x61, 0xd2, 0x54, 0x09, 0x95, 0xd3, 0x46, 0x36,
	0x8c, 0x54, 0x18, 0x2e, 0x83, 0xc8, 0x71, 0xfb, 0x8d, 0x16, 0xe9, 0x36, 0x03, 0xd0, 0x34, 0x07,
	0x15, 0x78, 0x7c, 0x35, 0x08, 0x57, 0x18, 0x7e, 0x06, 0x33, 0x0d, 0xbb, 0xfb, 0xc6, 0x6a, 0x4a,
	0xb3, 0xa5, 0x74, 0x39, 0xa7, 0xdf, 0x4c, 0x7a, 0xda, 0xd7, 0xa1, 0xad, 0x72, 0x58, 0xad, 0xcb,
	0xdc, 0x63, 0x23, 0xbc, 0x23, 0x3f, 0x81, 0x5c, 0x2c, 0x8c, 0x45, 0x48, 0xb7, 0xe9, 0x31, 0xd7,
	0x9a, 0x35, 0x7c, 0x13, 0x17, 0x61, 0xba, 0x47, 0x3a, 0x47, 0x91, 0xda, 0xc0, 0x79, 0x2a, 0x3c,
	0x46, 0xea, 0x1e, 0x2c, 0x6c, 0xda, 0xc4, 0x8c, 0x9e, 0x30, 0xe8, 0xdb, 0x23, 0xea, 0x31, 0x7c,
	0x17, 0x66, 0x5a, 0x94, 0x98, 0xd4, 0xe5, 0x2c, 0x39, 0x7d, 0x41, 0xe3, 0x2d, 0x08, 0x8f, 0xd7,
	0xf9, 0x91, 0x11, 0x42, 0x92, 0x4a, 0xa9, 0xbe, 0x83, 0xe2, 0x30, 0xaf, 0xe7, 0xd8, 0x5d, 0x8f,
	0xe2, 0x7b, 0x23, 0xc4, 0xc5, 0x88, 0x38, 0x38, 0x1f, 0x61, 0x7e, 0x08, 0x99, 0xa8, 0x0e, 0x9c,
	0x3d, 0xa7, 0x4b, 0xe3, 0x0a, 0x63, 0x0c, 0x90, 0x6a, 0x15, 0xfe, 0x7b, 0x49, 0x58, 0xa3, 0x15,
	0x1d, 0x7b, 0x93, 0xa8, 0x52, 0xdf, 0xc3, 0xe2, 0x28, 0xcb, 0x44, 0x1a, 0x1e, 0x41, 0x36, 0xca,
	0xcc, 0x93, 0x04, 0xde, 0xdd, 0xf1, 0x22, 0xce, 0xa1, 0xea, 0x67, 0x04, 0xf2, 0x0b, 0xc7, 0x24,
	0x8c, 0x0e, 0x8f, 0xde, 0x24, 0x1d, 0x0a, 0x86, 0x5f, 0x18, 0x0c, 0xff, 0xdf, 0x0e, 0xba, 0xfa,
	0x11, 0xc1, 0x95, 0xc4, 0x64, 0x2e, 0xb1, 0xad, 0x3d, 0x58, 0x5c, 0xa3, 0xac, 0xd2, 0xe9, 0xfc,
	0x53, 0x5f, 0xf1, 0x12, 0x64, 0x3c, 0x46, 0x5c, 0xb6, 0x3f, 0xa8, 0xc8, 0x2c, 0xf7, 0x37, 0x4c,
	0x7f, 0x4d, 0x3a, 0xd6, 0xa1, 0xc5, 0x78, 0x59, 0xf2, 0x46, 0xe0, 0xa8, 0x1f, 0xe0, 0xff, 0xdf,
	0xde, 0xbd, 0xcc, 0x49, 0xb8, 0xb3, 0x06, 0xf9, 0xa1, 0xaa, 0xe3, 0x1c, 0xcc, 0xd6, 0xb6, 0x2b,
	0x2b, 0x9b, 0xb5, 0xaa, 0x98, 0xc2, 0x73, 0x90, 0xa9, 0x6e, 0xd4, 0x03, 0x0f, 0xf9, 0x5e, 0xc5,
	0x58, 0x5d, 0xdf, 0xd8, 0xab, 0x55, 0x45, 0x01, 0xe7, 0x21, 0xbb, 0xbb, 0xb3, 0xb5, 0x52, 0xdf,
	0xdd, 0xd9, 0xae, 0x89, 0x69, 0xfd, 0x53, 0x1a, 0x32, 0x11, 0x13, 0xae, 0xc3, 0x5c, 0x7c, 0x43,
	0xf1, 0xb5, 0x78, 0x2a, 0x09, 0xff, 0x09, 0x72, 0x69, 0x3c, 0x20, 0x90, 0xab, 0xa6, 0xf0, 0x2b,
	0x28, 0x0c, 0x2f, 0x0d, 0xbe, 0x1e, 0xbf, 0x95, 0xb8, 0x96, 0xb2, 0x7a, 0x11, 0x24, 0xa2, 0xbe,
	0x8f, 0x70, 0x0b, 0x16, 0x12, 0x66, 0x10, 0xdf, 0x8a, 0x5f, 0x1f, 0xbf, 0x31, 0xf2, 0xed, 0x3f,
	0xe2, 0x06, 0x32, 0x5e, 0xc3, 0xfc, 0x48, 0xcb, 0xf1, 0x50, 0x92, 0xc9, 0x73, 0x28, 0xdf, 0xb8,
	0x10, 0x13, 0xb1, 0xaf, 0xe8, 0xa7, 0xdf, 0x33, 0xe8, 0xc7, 0x99, 0x82, 0x4e, 0xce, 0x14, 0xf4,
	0xf3, 0x4c, 0x41, 0x5f, 0xfa, 0x4a, 0xea, 0x6b, 0x5f, 0x49, 0x9d, 0xf4, 0x95, 0xd4, 0x69, 0x5f,
	0x49, 0x81, 0x68, 0xbb, 0x4d, 0x8d, 0x59, 0xed, 0x9e, 0xd6, 0xee, 0xf1, 0xef, 0xd9, 0xc1, 0x0c,
	0xff, 0x79, 0xf0, 0x2b, 0x00, 0x00, 0xff, 0xff, 0xc1, 0x09, 0x9d, 0xfd, 0x29, 0x07, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// KeyspaceClient is the client API for Keyspace service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type KeyspaceClient interface {
	LoadKeyspace(ctx context.Context, in *LoadKeyspaceRequest, opts ...grpc.CallOption) (*LoadKeyspaceResponse, error)
	// WatchKeyspaces first return all current keyspaces' metadata as its first response.
	// Then, it returns responses containing keyspaces that had their metadata changed.
	WatchKeyspaces(ctx context.Context, in *WatchKeyspacesRequest, opts ...grpc.CallOption) (Keyspace_WatchKeyspacesClient, error)
	UpdateKeyspaceState(ctx context.Context, in *UpdateKeyspaceStateRequest, opts ...grpc.CallOption) (*UpdateKeyspaceStateResponse, error)
	GetAllKeyspaces(ctx context.Context, in *GetAllKeyspacesRequest, opts ...grpc.CallOption) (*GetAllKeyspacesResponse, error)
}

type keyspaceClient struct {
	cc *grpc.ClientConn
}

func NewKeyspaceClient(cc *grpc.ClientConn) KeyspaceClient {
	return &keyspaceClient{cc}
}

func (c *keyspaceClient) LoadKeyspace(ctx context.Context, in *LoadKeyspaceRequest, opts ...grpc.CallOption) (*LoadKeyspaceResponse, error) {
	out := new(LoadKeyspaceResponse)
	err := c.cc.Invoke(ctx, "/keyspacepb.Keyspace/LoadKeyspace", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keyspaceClient) WatchKeyspaces(ctx context.Context, in *WatchKeyspacesRequest, opts ...grpc.CallOption) (Keyspace_WatchKeyspacesClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Keyspace_serviceDesc.Streams[0], "/keyspacepb.Keyspace/WatchKeyspaces", opts...)
	if err != nil {
		return nil, err
	}
	x := &keyspaceWatchKeyspacesClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Keyspace_WatchKeyspacesClient interface {
	Recv() (*WatchKeyspacesResponse, error)
	grpc.ClientStream
}

type keyspaceWatchKeyspacesClient struct {
	grpc.ClientStream
}

func (x *keyspaceWatchKeyspacesClient) Recv() (*WatchKeyspacesResponse, error) {
	m := new(WatchKeyspacesResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *keyspaceClient) UpdateKeyspaceState(ctx context.Context, in *UpdateKeyspaceStateRequest, opts ...grpc.CallOption) (*UpdateKeyspaceStateResponse, error) {
	out := new(UpdateKeyspaceStateResponse)
	err := c.cc.Invoke(ctx, "/keyspacepb.Keyspace/UpdateKeyspaceState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *keyspaceClient) GetAllKeyspaces(ctx context.Context, in *GetAllKeyspacesRequest, opts ...grpc.CallOption) (*GetAllKeyspacesResponse, error) {
	out := new(GetAllKeyspacesResponse)
	err := c.cc.Invoke(ctx, "/keyspacepb.Keyspace/GetAllKeyspaces", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KeyspaceServer is the server API for Keyspace service.
type KeyspaceServer interface {
	LoadKeyspace(context.Context, *LoadKeyspaceRequest) (*LoadKeyspaceResponse, error)
	// WatchKeyspaces first return all current keyspaces' metadata as its first response.
	// Then, it returns responses containing keyspaces that had their metadata changed.
	WatchKeyspaces(*WatchKeyspacesRequest, Keyspace_WatchKeyspacesServer) error
	UpdateKeyspaceState(context.Context, *UpdateKeyspaceStateRequest) (*UpdateKeyspaceStateResponse, error)
	GetAllKeyspaces(context.Context, *GetAllKeyspacesRequest) (*GetAllKeyspacesResponse, error)
}

// UnimplementedKeyspaceServer can be embedded to have forward compatible implementations.
type UnimplementedKeyspaceServer struct {
}

func (*UnimplementedKeyspaceServer) LoadKeyspace(ctx context.Context, req *LoadKeyspaceRequest) (*LoadKeyspaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoadKeyspace not implemented")
}
func (*UnimplementedKeyspaceServer) WatchKeyspaces(req *WatchKeyspacesRequest, srv Keyspace_WatchKeyspacesServer) error {
	return status.Errorf(codes.Unimplemented, "method WatchKeyspaces not implemented")
}
func (*UnimplementedKeyspaceServer) UpdateKeyspaceState(ctx context.Context, req *UpdateKeyspaceStateRequest) (*UpdateKeyspaceStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKeyspaceState not implemented")
}
func (*UnimplementedKeyspaceServer) GetAllKeyspaces(ctx context.Context, req *GetAllKeyspacesRequest) (*GetAllKeyspacesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllKeyspaces not implemented")
}

func RegisterKeyspaceServer(s *grpc.Server, srv KeyspaceServer) {
	s.RegisterService(&_Keyspace_serviceDesc, srv)
}

func _Keyspace_LoadKeyspace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoadKeyspaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeyspaceServer).LoadKeyspace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/keyspacepb.Keyspace/LoadKeyspace",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeyspaceServer).LoadKeyspace(ctx, req.(*LoadKeyspaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keyspace_WatchKeyspaces_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchKeyspacesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(KeyspaceServer).WatchKeyspaces(m, &keyspaceWatchKeyspacesServer{stream})
}

type Keyspace_WatchKeyspacesServer interface {
	Send(*WatchKeyspacesResponse) error
	grpc.ServerStream
}

type keyspaceWatchKeyspacesServer struct {
	grpc.ServerStream
}

func (x *keyspaceWatchKeyspacesServer) Send(m *WatchKeyspacesResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Keyspace_UpdateKeyspaceState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKeyspaceStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeyspaceServer).UpdateKeyspaceState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/keyspacepb.Keyspace/UpdateKeyspaceState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeyspaceServer).UpdateKeyspaceState(ctx, req.(*UpdateKeyspaceStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Keyspace_GetAllKeyspaces_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllKeyspacesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KeyspaceServer).GetAllKeyspaces(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/keyspacepb.Keyspace/GetAllKeyspaces",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KeyspaceServer).GetAllKeyspaces(ctx, req.(*GetAllKeyspacesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Keyspace_serviceDesc = grpc.ServiceDesc{
	ServiceName: "keyspacepb.Keyspace",
	HandlerType: (*KeyspaceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LoadKeyspace",
			Handler:    _Keyspace_LoadKeyspace_Handler,
		},
		{
			MethodName: "UpdateKeyspaceState",
			Handler:    _Keyspace_UpdateKeyspaceState_Handler,
		},
		{
			MethodName: "GetAllKeyspaces",
			Handler:    _Keyspace_GetAllKeyspaces_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "WatchKeyspaces",
			Handler:       _Keyspace_WatchKeyspaces_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "keyspacepb.proto",
}

func (m *KeyspaceMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeyspaceMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KeyspaceMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Config) > 0 {
		for k := range m.Config {
			v := m.Config[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintKeyspacepb(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintKeyspacepb(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintKeyspacepb(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x3a
		}
	}
	if m.StateChangedAt != 0 {
		i = encodeVarintKeyspacepb(dAtA, i, uint64(m.StateChangedAt))
		i--
		dAtA[i] = 0x28
	}
	if m.CreatedAt != 0 {
		i = encodeVarintKeyspacepb(dAtA, i, uint64(m.CreatedAt))
		i--
		dAtA[i] = 0x20
	}
	if m.State != 0 {
		i = encodeVarintKeyspacepb(dAtA, i, uint64(m.State))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintKeyspacepb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if m.Id != 0 {
		i = encodeVarintKeyspacepb(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *LoadKeyspaceRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoadKeyspaceRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LoadKeyspaceRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintKeyspacepb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *LoadKeyspaceResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoadKeyspaceResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LoadKeyspaceResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Keyspace != nil {
		{
			size, err := m.Keyspace.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WatchKeyspacesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WatchKeyspacesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WatchKeyspacesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WatchKeyspacesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WatchKeyspacesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WatchKeyspacesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Keyspaces) > 0 {
		for iNdEx := len(m.Keyspaces) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Keyspaces[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpdateKeyspaceStateRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateKeyspaceStateRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateKeyspaceStateRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.State != 0 {
		i = encodeVarintKeyspacepb(dAtA, i, uint64(m.State))
		i--
		dAtA[i] = 0x18
	}
	if m.Id != 0 {
		i = encodeVarintKeyspacepb(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpdateKeyspaceStateResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateKeyspaceStateResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateKeyspaceStateResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Keyspace != nil {
		{
			size, err := m.Keyspace.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetAllKeyspacesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllKeyspacesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAllKeyspacesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Limit != 0 {
		i = encodeVarintKeyspacepb(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x18
	}
	if m.StartId != 0 {
		i = encodeVarintKeyspacepb(dAtA, i, uint64(m.StartId))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetAllKeyspacesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllKeyspacesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAllKeyspacesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Keyspaces) > 0 {
		for iNdEx := len(m.Keyspaces) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Keyspaces[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintKeyspacepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintKeyspacepb(dAtA []byte, offset int, v uint64) int {
	offset -= sovKeyspacepb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *KeyspaceMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovKeyspacepb(uint64(m.Id))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	if m.State != 0 {
		n += 1 + sovKeyspacepb(uint64(m.State))
	}
	if m.CreatedAt != 0 {
		n += 1 + sovKeyspacepb(uint64(m.CreatedAt))
	}
	if m.StateChangedAt != 0 {
		n += 1 + sovKeyspacepb(uint64(m.StateChangedAt))
	}
	if len(m.Config) > 0 {
		for k, v := range m.Config {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovKeyspacepb(uint64(len(k))) + 1 + len(v) + sovKeyspacepb(uint64(len(v)))
			n += mapEntrySize + 1 + sovKeyspacepb(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *LoadKeyspaceRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	return n
}

func (m *LoadKeyspaceResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	if m.Keyspace != nil {
		l = m.Keyspace.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	return n
}

func (m *WatchKeyspacesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	return n
}

func (m *WatchKeyspacesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	if len(m.Keyspaces) > 0 {
		for _, e := range m.Keyspaces {
			l = e.Size()
			n += 1 + l + sovKeyspacepb(uint64(l))
		}
	}
	return n
}

func (m *UpdateKeyspaceStateRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	if m.Id != 0 {
		n += 1 + sovKeyspacepb(uint64(m.Id))
	}
	if m.State != 0 {
		n += 1 + sovKeyspacepb(uint64(m.State))
	}
	return n
}

func (m *UpdateKeyspaceStateResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	if m.Keyspace != nil {
		l = m.Keyspace.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	return n
}

func (m *GetAllKeyspacesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	if m.StartId != 0 {
		n += 1 + sovKeyspacepb(uint64(m.StartId))
	}
	if m.Limit != 0 {
		n += 1 + sovKeyspacepb(uint64(m.Limit))
	}
	return n
}

func (m *GetAllKeyspacesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovKeyspacepb(uint64(l))
	}
	if len(m.Keyspaces) > 0 {
		for _, e := range m.Keyspaces {
			l = e.Size()
			n += 1 + l + sovKeyspacepb(uint64(l))
		}
	}
	return n
}

func sovKeyspacepb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozKeyspacepb(x uint64) (n int) {
	return sovKeyspacepb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *KeyspaceMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeyspaceMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeyspaceMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= KeyspaceState(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreatedAt", wireType)
			}
			m.CreatedAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreatedAt |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StateChangedAt", wireType)
			}
			m.StateChangedAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StateChangedAt |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Config", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Config == nil {
				m.Config = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowKeyspacepb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowKeyspacepb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthKeyspacepb
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthKeyspacepb
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowKeyspacepb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthKeyspacepb
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthKeyspacepb
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipKeyspacepb(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthKeyspacepb
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Config[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoadKeyspaceRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LoadKeyspaceRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LoadKeyspaceRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &pdpb.RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoadKeyspaceResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LoadKeyspaceResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LoadKeyspaceResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &pdpb.ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keyspace", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Keyspace == nil {
				m.Keyspace = &KeyspaceMeta{}
			}
			if err := m.Keyspace.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WatchKeyspacesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WatchKeyspacesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WatchKeyspacesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &pdpb.RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WatchKeyspacesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WatchKeyspacesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WatchKeyspacesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &pdpb.ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keyspaces", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Keyspaces = append(m.Keyspaces, &KeyspaceMeta{})
			if err := m.Keyspaces[len(m.Keyspaces)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateKeyspaceStateRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateKeyspaceStateRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateKeyspaceStateRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &pdpb.RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= KeyspaceState(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateKeyspaceStateResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateKeyspaceStateResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateKeyspaceStateResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &pdpb.ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keyspace", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Keyspace == nil {
				m.Keyspace = &KeyspaceMeta{}
			}
			if err := m.Keyspace.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllKeyspacesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllKeyspacesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllKeyspacesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &pdpb.RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartId", wireType)
			}
			m.StartId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartId |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllKeyspacesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllKeyspacesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllKeyspacesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &pdpb.ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keyspaces", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Keyspaces = append(m.Keyspaces, &KeyspaceMeta{})
			if err := m.Keyspaces[len(m.Keyspaces)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipKeyspacepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthKeyspacepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipKeyspacepb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowKeyspacepb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKeyspacepb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthKeyspacepb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupKeyspacepb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthKeyspacepb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthKeyspacepb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowKeyspacepb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupKeyspacepb = fmt.Errorf("proto: unexpected end of group")
)
