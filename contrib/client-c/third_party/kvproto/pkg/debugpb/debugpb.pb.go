// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: debugpb.proto

package debugpb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	eraftpb "github.com/pingcap/kvproto/pkg/eraftpb"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
	raft_serverpb "github.com/pingcap/kvproto/pkg/raft_serverpb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DB int32

const (
	DB_INVALID DB = 0
	DB_KV      DB = 1
	DB_RAFT    DB = 2
)

var DB_name = map[int32]string{
	0: "INVALID",
	1: "KV",
	2: "RAFT",
}

var DB_value = map[string]int32{
	"INVALID": 0,
	"KV":      1,
	"RAFT":    2,
}

func (x DB) String() string {
	return proto.EnumName(DB_name, int32(x))
}

func (DB) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{0}
}

type MODULE int32

const (
	MODULE_UNUSED      MODULE = 0
	MODULE_KVDB        MODULE = 1
	MODULE_RAFTDB      MODULE = 2
	MODULE_READPOOL    MODULE = 3
	MODULE_SERVER      MODULE = 4
	MODULE_STORAGE     MODULE = 5
	MODULE_PD          MODULE = 6
	MODULE_METRIC      MODULE = 7
	MODULE_COPROCESSOR MODULE = 8
	MODULE_SECURITY    MODULE = 9
	MODULE_IMPORT      MODULE = 10
)

var MODULE_name = map[int32]string{
	0:  "UNUSED",
	1:  "KVDB",
	2:  "RAFTDB",
	3:  "READPOOL",
	4:  "SERVER",
	5:  "STORAGE",
	6:  "PD",
	7:  "METRIC",
	8:  "COPROCESSOR",
	9:  "SECURITY",
	10: "IMPORT",
}

var MODULE_value = map[string]int32{
	"UNUSED":      0,
	"KVDB":        1,
	"RAFTDB":      2,
	"READPOOL":    3,
	"SERVER":      4,
	"STORAGE":     5,
	"PD":          6,
	"METRIC":      7,
	"COPROCESSOR": 8,
	"SECURITY":    9,
	"IMPORT":      10,
}

func (x MODULE) String() string {
	return proto.EnumName(MODULE_name, int32(x))
}

func (MODULE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{1}
}

type BottommostLevelCompaction int32

const (
	// Skip bottommost level compaction
	BottommostLevelCompaction_Skip BottommostLevelCompaction = 0
	// Force bottommost level compaction
	BottommostLevelCompaction_Force BottommostLevelCompaction = 1
	// Compact bottommost level if there is a compaction filter.
	BottommostLevelCompaction_IfHaveCompactionFilter BottommostLevelCompaction = 2
)

var BottommostLevelCompaction_name = map[int32]string{
	0: "Skip",
	1: "Force",
	2: "IfHaveCompactionFilter",
}

var BottommostLevelCompaction_value = map[string]int32{
	"Skip":                   0,
	"Force":                  1,
	"IfHaveCompactionFilter": 2,
}

func (x BottommostLevelCompaction) String() string {
	return proto.EnumName(BottommostLevelCompaction_name, int32(x))
}

func (BottommostLevelCompaction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{2}
}

type GetRequest struct {
	Db  DB     `protobuf:"varint,1,opt,name=db,proto3,enum=debugpb.DB" json:"db,omitempty"`
	Cf  string `protobuf:"bytes,2,opt,name=cf,proto3" json:"cf,omitempty"`
	Key []byte `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
}

func (m *GetRequest) Reset()         { *m = GetRequest{} }
func (m *GetRequest) String() string { return proto.CompactTextString(m) }
func (*GetRequest) ProtoMessage()    {}
func (*GetRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{0}
}
func (m *GetRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRequest.Merge(m, src)
}
func (m *GetRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRequest proto.InternalMessageInfo

func (m *GetRequest) GetDb() DB {
	if m != nil {
		return m.Db
	}
	return DB_INVALID
}

func (m *GetRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *GetRequest) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

type GetResponse struct {
	Value []byte `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *GetResponse) Reset()         { *m = GetResponse{} }
func (m *GetResponse) String() string { return proto.CompactTextString(m) }
func (*GetResponse) ProtoMessage()    {}
func (*GetResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{1}
}
func (m *GetResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResponse.Merge(m, src)
}
func (m *GetResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetResponse proto.InternalMessageInfo

func (m *GetResponse) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type RaftLogRequest struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	LogIndex uint64 `protobuf:"varint,2,opt,name=log_index,json=logIndex,proto3" json:"log_index,omitempty"`
}

func (m *RaftLogRequest) Reset()         { *m = RaftLogRequest{} }
func (m *RaftLogRequest) String() string { return proto.CompactTextString(m) }
func (*RaftLogRequest) ProtoMessage()    {}
func (*RaftLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{2}
}
func (m *RaftLogRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftLogRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftLogRequest.Merge(m, src)
}
func (m *RaftLogRequest) XXX_Size() int {
	return m.Size()
}
func (m *RaftLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RaftLogRequest proto.InternalMessageInfo

func (m *RaftLogRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RaftLogRequest) GetLogIndex() uint64 {
	if m != nil {
		return m.LogIndex
	}
	return 0
}

type RaftLogResponse struct {
	Entry *eraftpb.Entry `protobuf:"bytes,1,opt,name=entry,proto3" json:"entry,omitempty"`
}

func (m *RaftLogResponse) Reset()         { *m = RaftLogResponse{} }
func (m *RaftLogResponse) String() string { return proto.CompactTextString(m) }
func (*RaftLogResponse) ProtoMessage()    {}
func (*RaftLogResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{3}
}
func (m *RaftLogResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftLogResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftLogResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftLogResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftLogResponse.Merge(m, src)
}
func (m *RaftLogResponse) XXX_Size() int {
	return m.Size()
}
func (m *RaftLogResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftLogResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RaftLogResponse proto.InternalMessageInfo

func (m *RaftLogResponse) GetEntry() *eraftpb.Entry {
	if m != nil {
		return m.Entry
	}
	return nil
}

type RegionInfoRequest struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *RegionInfoRequest) Reset()         { *m = RegionInfoRequest{} }
func (m *RegionInfoRequest) String() string { return proto.CompactTextString(m) }
func (*RegionInfoRequest) ProtoMessage()    {}
func (*RegionInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{4}
}
func (m *RegionInfoRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionInfoRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionInfoRequest.Merge(m, src)
}
func (m *RegionInfoRequest) XXX_Size() int {
	return m.Size()
}
func (m *RegionInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RegionInfoRequest proto.InternalMessageInfo

func (m *RegionInfoRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type RegionInfoResponse struct {
	RaftLocalState   *raft_serverpb.RaftLocalState   `protobuf:"bytes,1,opt,name=raft_local_state,json=raftLocalState,proto3" json:"raft_local_state,omitempty"`
	RaftApplyState   *raft_serverpb.RaftApplyState   `protobuf:"bytes,2,opt,name=raft_apply_state,json=raftApplyState,proto3" json:"raft_apply_state,omitempty"`
	RegionLocalState *raft_serverpb.RegionLocalState `protobuf:"bytes,3,opt,name=region_local_state,json=regionLocalState,proto3" json:"region_local_state,omitempty"`
}

func (m *RegionInfoResponse) Reset()         { *m = RegionInfoResponse{} }
func (m *RegionInfoResponse) String() string { return proto.CompactTextString(m) }
func (*RegionInfoResponse) ProtoMessage()    {}
func (*RegionInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{5}
}
func (m *RegionInfoResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionInfoResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionInfoResponse.Merge(m, src)
}
func (m *RegionInfoResponse) XXX_Size() int {
	return m.Size()
}
func (m *RegionInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RegionInfoResponse proto.InternalMessageInfo

func (m *RegionInfoResponse) GetRaftLocalState() *raft_serverpb.RaftLocalState {
	if m != nil {
		return m.RaftLocalState
	}
	return nil
}

func (m *RegionInfoResponse) GetRaftApplyState() *raft_serverpb.RaftApplyState {
	if m != nil {
		return m.RaftApplyState
	}
	return nil
}

func (m *RegionInfoResponse) GetRegionLocalState() *raft_serverpb.RegionLocalState {
	if m != nil {
		return m.RegionLocalState
	}
	return nil
}

type RegionSizeRequest struct {
	RegionId uint64   `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	Cfs      []string `protobuf:"bytes,2,rep,name=cfs,proto3" json:"cfs,omitempty"`
}

func (m *RegionSizeRequest) Reset()         { *m = RegionSizeRequest{} }
func (m *RegionSizeRequest) String() string { return proto.CompactTextString(m) }
func (*RegionSizeRequest) ProtoMessage()    {}
func (*RegionSizeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{6}
}
func (m *RegionSizeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionSizeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionSizeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionSizeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionSizeRequest.Merge(m, src)
}
func (m *RegionSizeRequest) XXX_Size() int {
	return m.Size()
}
func (m *RegionSizeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionSizeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RegionSizeRequest proto.InternalMessageInfo

func (m *RegionSizeRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RegionSizeRequest) GetCfs() []string {
	if m != nil {
		return m.Cfs
	}
	return nil
}

type RegionSizeResponse struct {
	Entries []*RegionSizeResponse_Entry `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (m *RegionSizeResponse) Reset()         { *m = RegionSizeResponse{} }
func (m *RegionSizeResponse) String() string { return proto.CompactTextString(m) }
func (*RegionSizeResponse) ProtoMessage()    {}
func (*RegionSizeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{7}
}
func (m *RegionSizeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionSizeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionSizeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionSizeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionSizeResponse.Merge(m, src)
}
func (m *RegionSizeResponse) XXX_Size() int {
	return m.Size()
}
func (m *RegionSizeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionSizeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RegionSizeResponse proto.InternalMessageInfo

func (m *RegionSizeResponse) GetEntries() []*RegionSizeResponse_Entry {
	if m != nil {
		return m.Entries
	}
	return nil
}

type RegionSizeResponse_Entry struct {
	Cf    string `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Size_ uint64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
}

func (m *RegionSizeResponse_Entry) Reset()         { *m = RegionSizeResponse_Entry{} }
func (m *RegionSizeResponse_Entry) String() string { return proto.CompactTextString(m) }
func (*RegionSizeResponse_Entry) ProtoMessage()    {}
func (*RegionSizeResponse_Entry) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{7, 0}
}
func (m *RegionSizeResponse_Entry) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionSizeResponse_Entry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionSizeResponse_Entry.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionSizeResponse_Entry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionSizeResponse_Entry.Merge(m, src)
}
func (m *RegionSizeResponse_Entry) XXX_Size() int {
	return m.Size()
}
func (m *RegionSizeResponse_Entry) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionSizeResponse_Entry.DiscardUnknown(m)
}

var xxx_messageInfo_RegionSizeResponse_Entry proto.InternalMessageInfo

func (m *RegionSizeResponse_Entry) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *RegionSizeResponse_Entry) GetSize_() uint64 {
	if m != nil {
		return m.Size_
	}
	return 0
}

type ScanMvccRequest struct {
	FromKey []byte `protobuf:"bytes,1,opt,name=from_key,json=fromKey,proto3" json:"from_key,omitempty"`
	ToKey   []byte `protobuf:"bytes,2,opt,name=to_key,json=toKey,proto3" json:"to_key,omitempty"`
	Limit   uint64 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (m *ScanMvccRequest) Reset()         { *m = ScanMvccRequest{} }
func (m *ScanMvccRequest) String() string { return proto.CompactTextString(m) }
func (*ScanMvccRequest) ProtoMessage()    {}
func (*ScanMvccRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{8}
}
func (m *ScanMvccRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScanMvccRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScanMvccRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ScanMvccRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanMvccRequest.Merge(m, src)
}
func (m *ScanMvccRequest) XXX_Size() int {
	return m.Size()
}
func (m *ScanMvccRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanMvccRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ScanMvccRequest proto.InternalMessageInfo

func (m *ScanMvccRequest) GetFromKey() []byte {
	if m != nil {
		return m.FromKey
	}
	return nil
}

func (m *ScanMvccRequest) GetToKey() []byte {
	if m != nil {
		return m.ToKey
	}
	return nil
}

func (m *ScanMvccRequest) GetLimit() uint64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ScanMvccResponse struct {
	Key  []byte            `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Info *kvrpcpb.MvccInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
}

func (m *ScanMvccResponse) Reset()         { *m = ScanMvccResponse{} }
func (m *ScanMvccResponse) String() string { return proto.CompactTextString(m) }
func (*ScanMvccResponse) ProtoMessage()    {}
func (*ScanMvccResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{9}
}
func (m *ScanMvccResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScanMvccResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScanMvccResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ScanMvccResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanMvccResponse.Merge(m, src)
}
func (m *ScanMvccResponse) XXX_Size() int {
	return m.Size()
}
func (m *ScanMvccResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanMvccResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ScanMvccResponse proto.InternalMessageInfo

func (m *ScanMvccResponse) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *ScanMvccResponse) GetInfo() *kvrpcpb.MvccInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type CompactRequest struct {
	Db                        DB                        `protobuf:"varint,1,opt,name=db,proto3,enum=debugpb.DB" json:"db,omitempty"`
	Cf                        string                    `protobuf:"bytes,2,opt,name=cf,proto3" json:"cf,omitempty"`
	FromKey                   []byte                    `protobuf:"bytes,3,opt,name=from_key,json=fromKey,proto3" json:"from_key,omitempty"`
	ToKey                     []byte                    `protobuf:"bytes,4,opt,name=to_key,json=toKey,proto3" json:"to_key,omitempty"`
	Threads                   uint32                    `protobuf:"varint,5,opt,name=threads,proto3" json:"threads,omitempty"`
	BottommostLevelCompaction BottommostLevelCompaction `protobuf:"varint,6,opt,name=bottommost_level_compaction,json=bottommostLevelCompaction,proto3,enum=debugpb.BottommostLevelCompaction" json:"bottommost_level_compaction,omitempty"`
}

func (m *CompactRequest) Reset()         { *m = CompactRequest{} }
func (m *CompactRequest) String() string { return proto.CompactTextString(m) }
func (*CompactRequest) ProtoMessage()    {}
func (*CompactRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{10}
}
func (m *CompactRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CompactRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactRequest.Merge(m, src)
}
func (m *CompactRequest) XXX_Size() int {
	return m.Size()
}
func (m *CompactRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CompactRequest proto.InternalMessageInfo

func (m *CompactRequest) GetDb() DB {
	if m != nil {
		return m.Db
	}
	return DB_INVALID
}

func (m *CompactRequest) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *CompactRequest) GetFromKey() []byte {
	if m != nil {
		return m.FromKey
	}
	return nil
}

func (m *CompactRequest) GetToKey() []byte {
	if m != nil {
		return m.ToKey
	}
	return nil
}

func (m *CompactRequest) GetThreads() uint32 {
	if m != nil {
		return m.Threads
	}
	return 0
}

func (m *CompactRequest) GetBottommostLevelCompaction() BottommostLevelCompaction {
	if m != nil {
		return m.BottommostLevelCompaction
	}
	return BottommostLevelCompaction_Skip
}

type CompactResponse struct {
}

func (m *CompactResponse) Reset()         { *m = CompactResponse{} }
func (m *CompactResponse) String() string { return proto.CompactTextString(m) }
func (*CompactResponse) ProtoMessage()    {}
func (*CompactResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{11}
}
func (m *CompactResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CompactResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactResponse.Merge(m, src)
}
func (m *CompactResponse) XXX_Size() int {
	return m.Size()
}
func (m *CompactResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CompactResponse proto.InternalMessageInfo

type InjectFailPointRequest struct {
	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Actions string `protobuf:"bytes,2,opt,name=actions,proto3" json:"actions,omitempty"`
}

func (m *InjectFailPointRequest) Reset()         { *m = InjectFailPointRequest{} }
func (m *InjectFailPointRequest) String() string { return proto.CompactTextString(m) }
func (*InjectFailPointRequest) ProtoMessage()    {}
func (*InjectFailPointRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{12}
}
func (m *InjectFailPointRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *InjectFailPointRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_InjectFailPointRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *InjectFailPointRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InjectFailPointRequest.Merge(m, src)
}
func (m *InjectFailPointRequest) XXX_Size() int {
	return m.Size()
}
func (m *InjectFailPointRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InjectFailPointRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InjectFailPointRequest proto.InternalMessageInfo

func (m *InjectFailPointRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *InjectFailPointRequest) GetActions() string {
	if m != nil {
		return m.Actions
	}
	return ""
}

type InjectFailPointResponse struct {
}

func (m *InjectFailPointResponse) Reset()         { *m = InjectFailPointResponse{} }
func (m *InjectFailPointResponse) String() string { return proto.CompactTextString(m) }
func (*InjectFailPointResponse) ProtoMessage()    {}
func (*InjectFailPointResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{13}
}
func (m *InjectFailPointResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *InjectFailPointResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_InjectFailPointResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *InjectFailPointResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InjectFailPointResponse.Merge(m, src)
}
func (m *InjectFailPointResponse) XXX_Size() int {
	return m.Size()
}
func (m *InjectFailPointResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InjectFailPointResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InjectFailPointResponse proto.InternalMessageInfo

type RecoverFailPointRequest struct {
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (m *RecoverFailPointRequest) Reset()         { *m = RecoverFailPointRequest{} }
func (m *RecoverFailPointRequest) String() string { return proto.CompactTextString(m) }
func (*RecoverFailPointRequest) ProtoMessage()    {}
func (*RecoverFailPointRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{14}
}
func (m *RecoverFailPointRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RecoverFailPointRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RecoverFailPointRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RecoverFailPointRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverFailPointRequest.Merge(m, src)
}
func (m *RecoverFailPointRequest) XXX_Size() int {
	return m.Size()
}
func (m *RecoverFailPointRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverFailPointRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverFailPointRequest proto.InternalMessageInfo

func (m *RecoverFailPointRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type RecoverFailPointResponse struct {
}

func (m *RecoverFailPointResponse) Reset()         { *m = RecoverFailPointResponse{} }
func (m *RecoverFailPointResponse) String() string { return proto.CompactTextString(m) }
func (*RecoverFailPointResponse) ProtoMessage()    {}
func (*RecoverFailPointResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{15}
}
func (m *RecoverFailPointResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RecoverFailPointResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RecoverFailPointResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RecoverFailPointResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverFailPointResponse.Merge(m, src)
}
func (m *RecoverFailPointResponse) XXX_Size() int {
	return m.Size()
}
func (m *RecoverFailPointResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverFailPointResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverFailPointResponse proto.InternalMessageInfo

type ListFailPointsRequest struct {
}

func (m *ListFailPointsRequest) Reset()         { *m = ListFailPointsRequest{} }
func (m *ListFailPointsRequest) String() string { return proto.CompactTextString(m) }
func (*ListFailPointsRequest) ProtoMessage()    {}
func (*ListFailPointsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{16}
}
func (m *ListFailPointsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListFailPointsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListFailPointsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListFailPointsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListFailPointsRequest.Merge(m, src)
}
func (m *ListFailPointsRequest) XXX_Size() int {
	return m.Size()
}
func (m *ListFailPointsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListFailPointsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListFailPointsRequest proto.InternalMessageInfo

type ListFailPointsResponse struct {
	Entries []*ListFailPointsResponse_Entry `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (m *ListFailPointsResponse) Reset()         { *m = ListFailPointsResponse{} }
func (m *ListFailPointsResponse) String() string { return proto.CompactTextString(m) }
func (*ListFailPointsResponse) ProtoMessage()    {}
func (*ListFailPointsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{17}
}
func (m *ListFailPointsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListFailPointsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListFailPointsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListFailPointsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListFailPointsResponse.Merge(m, src)
}
func (m *ListFailPointsResponse) XXX_Size() int {
	return m.Size()
}
func (m *ListFailPointsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListFailPointsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListFailPointsResponse proto.InternalMessageInfo

func (m *ListFailPointsResponse) GetEntries() []*ListFailPointsResponse_Entry {
	if m != nil {
		return m.Entries
	}
	return nil
}

type ListFailPointsResponse_Entry struct {
	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Actions string `protobuf:"bytes,2,opt,name=actions,proto3" json:"actions,omitempty"`
}

func (m *ListFailPointsResponse_Entry) Reset()         { *m = ListFailPointsResponse_Entry{} }
func (m *ListFailPointsResponse_Entry) String() string { return proto.CompactTextString(m) }
func (*ListFailPointsResponse_Entry) ProtoMessage()    {}
func (*ListFailPointsResponse_Entry) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{17, 0}
}
func (m *ListFailPointsResponse_Entry) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListFailPointsResponse_Entry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListFailPointsResponse_Entry.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListFailPointsResponse_Entry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListFailPointsResponse_Entry.Merge(m, src)
}
func (m *ListFailPointsResponse_Entry) XXX_Size() int {
	return m.Size()
}
func (m *ListFailPointsResponse_Entry) XXX_DiscardUnknown() {
	xxx_messageInfo_ListFailPointsResponse_Entry.DiscardUnknown(m)
}

var xxx_messageInfo_ListFailPointsResponse_Entry proto.InternalMessageInfo

func (m *ListFailPointsResponse_Entry) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListFailPointsResponse_Entry) GetActions() string {
	if m != nil {
		return m.Actions
	}
	return ""
}

type GetMetricsRequest struct {
	All bool `protobuf:"varint,1,opt,name=all,proto3" json:"all,omitempty"`
}

func (m *GetMetricsRequest) Reset()         { *m = GetMetricsRequest{} }
func (m *GetMetricsRequest) String() string { return proto.CompactTextString(m) }
func (*GetMetricsRequest) ProtoMessage()    {}
func (*GetMetricsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{18}
}
func (m *GetMetricsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMetricsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMetricsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetMetricsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMetricsRequest.Merge(m, src)
}
func (m *GetMetricsRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetMetricsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMetricsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMetricsRequest proto.InternalMessageInfo

func (m *GetMetricsRequest) GetAll() bool {
	if m != nil {
		return m.All
	}
	return false
}

type GetMetricsResponse struct {
	Prometheus  string `protobuf:"bytes,1,opt,name=prometheus,proto3" json:"prometheus,omitempty"`
	RocksdbKv   string `protobuf:"bytes,2,opt,name=rocksdb_kv,json=rocksdbKv,proto3" json:"rocksdb_kv,omitempty"`
	RocksdbRaft string `protobuf:"bytes,3,opt,name=rocksdb_raft,json=rocksdbRaft,proto3" json:"rocksdb_raft,omitempty"`
	Jemalloc    string `protobuf:"bytes,4,opt,name=jemalloc,proto3" json:"jemalloc,omitempty"`
	StoreId     uint64 `protobuf:"varint,5,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
}

func (m *GetMetricsResponse) Reset()         { *m = GetMetricsResponse{} }
func (m *GetMetricsResponse) String() string { return proto.CompactTextString(m) }
func (*GetMetricsResponse) ProtoMessage()    {}
func (*GetMetricsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{19}
}
func (m *GetMetricsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMetricsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMetricsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetMetricsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMetricsResponse.Merge(m, src)
}
func (m *GetMetricsResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetMetricsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMetricsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMetricsResponse proto.InternalMessageInfo

func (m *GetMetricsResponse) GetPrometheus() string {
	if m != nil {
		return m.Prometheus
	}
	return ""
}

func (m *GetMetricsResponse) GetRocksdbKv() string {
	if m != nil {
		return m.RocksdbKv
	}
	return ""
}

func (m *GetMetricsResponse) GetRocksdbRaft() string {
	if m != nil {
		return m.RocksdbRaft
	}
	return ""
}

func (m *GetMetricsResponse) GetJemalloc() string {
	if m != nil {
		return m.Jemalloc
	}
	return ""
}

func (m *GetMetricsResponse) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

type RegionConsistencyCheckRequest struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *RegionConsistencyCheckRequest) Reset()         { *m = RegionConsistencyCheckRequest{} }
func (m *RegionConsistencyCheckRequest) String() string { return proto.CompactTextString(m) }
func (*RegionConsistencyCheckRequest) ProtoMessage()    {}
func (*RegionConsistencyCheckRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{20}
}
func (m *RegionConsistencyCheckRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionConsistencyCheckRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionConsistencyCheckRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionConsistencyCheckRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionConsistencyCheckRequest.Merge(m, src)
}
func (m *RegionConsistencyCheckRequest) XXX_Size() int {
	return m.Size()
}
func (m *RegionConsistencyCheckRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionConsistencyCheckRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RegionConsistencyCheckRequest proto.InternalMessageInfo

func (m *RegionConsistencyCheckRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type RegionConsistencyCheckResponse struct {
}

func (m *RegionConsistencyCheckResponse) Reset()         { *m = RegionConsistencyCheckResponse{} }
func (m *RegionConsistencyCheckResponse) String() string { return proto.CompactTextString(m) }
func (*RegionConsistencyCheckResponse) ProtoMessage()    {}
func (*RegionConsistencyCheckResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{21}
}
func (m *RegionConsistencyCheckResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionConsistencyCheckResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionConsistencyCheckResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionConsistencyCheckResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionConsistencyCheckResponse.Merge(m, src)
}
func (m *RegionConsistencyCheckResponse) XXX_Size() int {
	return m.Size()
}
func (m *RegionConsistencyCheckResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionConsistencyCheckResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RegionConsistencyCheckResponse proto.InternalMessageInfo

type ModifyTikvConfigRequest struct {
	Module      MODULE `protobuf:"varint,1,opt,name=module,proto3,enum=debugpb.MODULE" json:"module,omitempty"`
	ConfigName  string `protobuf:"bytes,2,opt,name=config_name,json=configName,proto3" json:"config_name,omitempty"`
	ConfigValue string `protobuf:"bytes,3,opt,name=config_value,json=configValue,proto3" json:"config_value,omitempty"`
}

func (m *ModifyTikvConfigRequest) Reset()         { *m = ModifyTikvConfigRequest{} }
func (m *ModifyTikvConfigRequest) String() string { return proto.CompactTextString(m) }
func (*ModifyTikvConfigRequest) ProtoMessage()    {}
func (*ModifyTikvConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{22}
}
func (m *ModifyTikvConfigRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ModifyTikvConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ModifyTikvConfigRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ModifyTikvConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyTikvConfigRequest.Merge(m, src)
}
func (m *ModifyTikvConfigRequest) XXX_Size() int {
	return m.Size()
}
func (m *ModifyTikvConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyTikvConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyTikvConfigRequest proto.InternalMessageInfo

func (m *ModifyTikvConfigRequest) GetModule() MODULE {
	if m != nil {
		return m.Module
	}
	return MODULE_UNUSED
}

func (m *ModifyTikvConfigRequest) GetConfigName() string {
	if m != nil {
		return m.ConfigName
	}
	return ""
}

func (m *ModifyTikvConfigRequest) GetConfigValue() string {
	if m != nil {
		return m.ConfigValue
	}
	return ""
}

type ModifyTikvConfigResponse struct {
}

func (m *ModifyTikvConfigResponse) Reset()         { *m = ModifyTikvConfigResponse{} }
func (m *ModifyTikvConfigResponse) String() string { return proto.CompactTextString(m) }
func (*ModifyTikvConfigResponse) ProtoMessage()    {}
func (*ModifyTikvConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{23}
}
func (m *ModifyTikvConfigResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ModifyTikvConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ModifyTikvConfigResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ModifyTikvConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyTikvConfigResponse.Merge(m, src)
}
func (m *ModifyTikvConfigResponse) XXX_Size() int {
	return m.Size()
}
func (m *ModifyTikvConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyTikvConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyTikvConfigResponse proto.InternalMessageInfo

type Property struct {
	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *Property) Reset()         { *m = Property{} }
func (m *Property) String() string { return proto.CompactTextString(m) }
func (*Property) ProtoMessage()    {}
func (*Property) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{24}
}
func (m *Property) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Property) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Property.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Property) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Property.Merge(m, src)
}
func (m *Property) XXX_Size() int {
	return m.Size()
}
func (m *Property) XXX_DiscardUnknown() {
	xxx_messageInfo_Property.DiscardUnknown(m)
}

var xxx_messageInfo_Property proto.InternalMessageInfo

func (m *Property) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Property) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type GetRegionPropertiesRequest struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *GetRegionPropertiesRequest) Reset()         { *m = GetRegionPropertiesRequest{} }
func (m *GetRegionPropertiesRequest) String() string { return proto.CompactTextString(m) }
func (*GetRegionPropertiesRequest) ProtoMessage()    {}
func (*GetRegionPropertiesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{25}
}
func (m *GetRegionPropertiesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRegionPropertiesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRegionPropertiesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRegionPropertiesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegionPropertiesRequest.Merge(m, src)
}
func (m *GetRegionPropertiesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRegionPropertiesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegionPropertiesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegionPropertiesRequest proto.InternalMessageInfo

func (m *GetRegionPropertiesRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type GetRegionPropertiesResponse struct {
	Props []*Property `protobuf:"bytes,1,rep,name=props,proto3" json:"props,omitempty"`
}

func (m *GetRegionPropertiesResponse) Reset()         { *m = GetRegionPropertiesResponse{} }
func (m *GetRegionPropertiesResponse) String() string { return proto.CompactTextString(m) }
func (*GetRegionPropertiesResponse) ProtoMessage()    {}
func (*GetRegionPropertiesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{26}
}
func (m *GetRegionPropertiesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRegionPropertiesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRegionPropertiesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRegionPropertiesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegionPropertiesResponse.Merge(m, src)
}
func (m *GetRegionPropertiesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetRegionPropertiesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegionPropertiesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegionPropertiesResponse proto.InternalMessageInfo

func (m *GetRegionPropertiesResponse) GetProps() []*Property {
	if m != nil {
		return m.Props
	}
	return nil
}

type GetStoreInfoRequest struct {
}

func (m *GetStoreInfoRequest) Reset()         { *m = GetStoreInfoRequest{} }
func (m *GetStoreInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetStoreInfoRequest) ProtoMessage()    {}
func (*GetStoreInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{27}
}
func (m *GetStoreInfoRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetStoreInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetStoreInfoRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetStoreInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreInfoRequest.Merge(m, src)
}
func (m *GetStoreInfoRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetStoreInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreInfoRequest proto.InternalMessageInfo

type GetStoreInfoResponse struct {
	StoreId    uint64             `protobuf:"varint,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	ApiVersion kvrpcpb.APIVersion `protobuf:"varint,2,opt,name=api_version,json=apiVersion,proto3,enum=kvrpcpb.APIVersion" json:"api_version,omitempty"`
}

func (m *GetStoreInfoResponse) Reset()         { *m = GetStoreInfoResponse{} }
func (m *GetStoreInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetStoreInfoResponse) ProtoMessage()    {}
func (*GetStoreInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{28}
}
func (m *GetStoreInfoResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetStoreInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetStoreInfoResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetStoreInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreInfoResponse.Merge(m, src)
}
func (m *GetStoreInfoResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetStoreInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreInfoResponse proto.InternalMessageInfo

func (m *GetStoreInfoResponse) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *GetStoreInfoResponse) GetApiVersion() kvrpcpb.APIVersion {
	if m != nil {
		return m.ApiVersion
	}
	return kvrpcpb.APIVersion_V1
}

type GetClusterInfoRequest struct {
}

func (m *GetClusterInfoRequest) Reset()         { *m = GetClusterInfoRequest{} }
func (m *GetClusterInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetClusterInfoRequest) ProtoMessage()    {}
func (*GetClusterInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{29}
}
func (m *GetClusterInfoRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetClusterInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetClusterInfoRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetClusterInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClusterInfoRequest.Merge(m, src)
}
func (m *GetClusterInfoRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetClusterInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClusterInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetClusterInfoRequest proto.InternalMessageInfo

type GetClusterInfoResponse struct {
	ClusterId uint64 `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
}

func (m *GetClusterInfoResponse) Reset()         { *m = GetClusterInfoResponse{} }
func (m *GetClusterInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetClusterInfoResponse) ProtoMessage()    {}
func (*GetClusterInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{30}
}
func (m *GetClusterInfoResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetClusterInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetClusterInfoResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetClusterInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClusterInfoResponse.Merge(m, src)
}
func (m *GetClusterInfoResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetClusterInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClusterInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetClusterInfoResponse proto.InternalMessageInfo

func (m *GetClusterInfoResponse) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

type GetAllRegionsInStoreRequest struct {
}

func (m *GetAllRegionsInStoreRequest) Reset()         { *m = GetAllRegionsInStoreRequest{} }
func (m *GetAllRegionsInStoreRequest) String() string { return proto.CompactTextString(m) }
func (*GetAllRegionsInStoreRequest) ProtoMessage()    {}
func (*GetAllRegionsInStoreRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{31}
}
func (m *GetAllRegionsInStoreRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAllRegionsInStoreRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAllRegionsInStoreRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAllRegionsInStoreRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllRegionsInStoreRequest.Merge(m, src)
}
func (m *GetAllRegionsInStoreRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetAllRegionsInStoreRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllRegionsInStoreRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllRegionsInStoreRequest proto.InternalMessageInfo

type GetAllRegionsInStoreResponse struct {
	Regions []uint64 `protobuf:"varint,1,rep,packed,name=regions,proto3" json:"regions,omitempty"`
}

func (m *GetAllRegionsInStoreResponse) Reset()         { *m = GetAllRegionsInStoreResponse{} }
func (m *GetAllRegionsInStoreResponse) String() string { return proto.CompactTextString(m) }
func (*GetAllRegionsInStoreResponse) ProtoMessage()    {}
func (*GetAllRegionsInStoreResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{32}
}
func (m *GetAllRegionsInStoreResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAllRegionsInStoreResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAllRegionsInStoreResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAllRegionsInStoreResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllRegionsInStoreResponse.Merge(m, src)
}
func (m *GetAllRegionsInStoreResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetAllRegionsInStoreResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllRegionsInStoreResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllRegionsInStoreResponse proto.InternalMessageInfo

func (m *GetAllRegionsInStoreResponse) GetRegions() []uint64 {
	if m != nil {
		return m.Regions
	}
	return nil
}

type ResetToVersionRequest struct {
	Ts uint64 `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (m *ResetToVersionRequest) Reset()         { *m = ResetToVersionRequest{} }
func (m *ResetToVersionRequest) String() string { return proto.CompactTextString(m) }
func (*ResetToVersionRequest) ProtoMessage()    {}
func (*ResetToVersionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{33}
}
func (m *ResetToVersionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResetToVersionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResetToVersionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResetToVersionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetToVersionRequest.Merge(m, src)
}
func (m *ResetToVersionRequest) XXX_Size() int {
	return m.Size()
}
func (m *ResetToVersionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetToVersionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ResetToVersionRequest proto.InternalMessageInfo

func (m *ResetToVersionRequest) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type ResetToVersionResponse struct {
}

func (m *ResetToVersionResponse) Reset()         { *m = ResetToVersionResponse{} }
func (m *ResetToVersionResponse) String() string { return proto.CompactTextString(m) }
func (*ResetToVersionResponse) ProtoMessage()    {}
func (*ResetToVersionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{34}
}
func (m *ResetToVersionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResetToVersionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResetToVersionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResetToVersionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetToVersionResponse.Merge(m, src)
}
func (m *ResetToVersionResponse) XXX_Size() int {
	return m.Size()
}
func (m *ResetToVersionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetToVersionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ResetToVersionResponse proto.InternalMessageInfo

type GetRangePropertiesRequest struct {
	StartKey []byte `protobuf:"bytes,1,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey   []byte `protobuf:"bytes,2,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
}

func (m *GetRangePropertiesRequest) Reset()         { *m = GetRangePropertiesRequest{} }
func (m *GetRangePropertiesRequest) String() string { return proto.CompactTextString(m) }
func (*GetRangePropertiesRequest) ProtoMessage()    {}
func (*GetRangePropertiesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{35}
}
func (m *GetRangePropertiesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRangePropertiesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRangePropertiesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRangePropertiesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRangePropertiesRequest.Merge(m, src)
}
func (m *GetRangePropertiesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRangePropertiesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRangePropertiesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRangePropertiesRequest proto.InternalMessageInfo

func (m *GetRangePropertiesRequest) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *GetRangePropertiesRequest) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

type GetRangePropertiesResponse struct {
	Properties []*GetRangePropertiesResponse_RangeProperty `protobuf:"bytes,1,rep,name=properties,proto3" json:"properties,omitempty"`
}

func (m *GetRangePropertiesResponse) Reset()         { *m = GetRangePropertiesResponse{} }
func (m *GetRangePropertiesResponse) String() string { return proto.CompactTextString(m) }
func (*GetRangePropertiesResponse) ProtoMessage()    {}
func (*GetRangePropertiesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{36}
}
func (m *GetRangePropertiesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRangePropertiesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRangePropertiesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRangePropertiesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRangePropertiesResponse.Merge(m, src)
}
func (m *GetRangePropertiesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetRangePropertiesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRangePropertiesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRangePropertiesResponse proto.InternalMessageInfo

func (m *GetRangePropertiesResponse) GetProperties() []*GetRangePropertiesResponse_RangeProperty {
	if m != nil {
		return m.Properties
	}
	return nil
}

type GetRangePropertiesResponse_RangeProperty struct {
	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *GetRangePropertiesResponse_RangeProperty) Reset() {
	*m = GetRangePropertiesResponse_RangeProperty{}
}
func (m *GetRangePropertiesResponse_RangeProperty) String() string { return proto.CompactTextString(m) }
func (*GetRangePropertiesResponse_RangeProperty) ProtoMessage()    {}
func (*GetRangePropertiesResponse_RangeProperty) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{36, 0}
}
func (m *GetRangePropertiesResponse_RangeProperty) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRangePropertiesResponse_RangeProperty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRangePropertiesResponse_RangeProperty.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRangePropertiesResponse_RangeProperty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRangePropertiesResponse_RangeProperty.Merge(m, src)
}
func (m *GetRangePropertiesResponse_RangeProperty) XXX_Size() int {
	return m.Size()
}
func (m *GetRangePropertiesResponse_RangeProperty) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRangePropertiesResponse_RangeProperty.DiscardUnknown(m)
}

var xxx_messageInfo_GetRangePropertiesResponse_RangeProperty proto.InternalMessageInfo

func (m *GetRangePropertiesResponse_RangeProperty) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *GetRangePropertiesResponse_RangeProperty) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type FlashbackToVersionRequest struct {
	Context  *kvrpcpb.Context `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	Version  uint64           `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	RegionId uint64           `protobuf:"varint,3,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	StartKey []byte           `protobuf:"bytes,4,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey   []byte           `protobuf:"bytes,5,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	StartTs  uint64           `protobuf:"varint,6,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	CommitTs uint64           `protobuf:"varint,7,opt,name=commit_ts,json=commitTs,proto3" json:"commit_ts,omitempty"`
}

func (m *FlashbackToVersionRequest) Reset()         { *m = FlashbackToVersionRequest{} }
func (m *FlashbackToVersionRequest) String() string { return proto.CompactTextString(m) }
func (*FlashbackToVersionRequest) ProtoMessage()    {}
func (*FlashbackToVersionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{37}
}
func (m *FlashbackToVersionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlashbackToVersionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlashbackToVersionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlashbackToVersionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlashbackToVersionRequest.Merge(m, src)
}
func (m *FlashbackToVersionRequest) XXX_Size() int {
	return m.Size()
}
func (m *FlashbackToVersionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FlashbackToVersionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FlashbackToVersionRequest proto.InternalMessageInfo

func (m *FlashbackToVersionRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *FlashbackToVersionRequest) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *FlashbackToVersionRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *FlashbackToVersionRequest) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *FlashbackToVersionRequest) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (m *FlashbackToVersionRequest) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *FlashbackToVersionRequest) GetCommitTs() uint64 {
	if m != nil {
		return m.CommitTs
	}
	return 0
}

type FlashbackToVersionResponse struct {
	Error string `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *FlashbackToVersionResponse) Reset()         { *m = FlashbackToVersionResponse{} }
func (m *FlashbackToVersionResponse) String() string { return proto.CompactTextString(m) }
func (*FlashbackToVersionResponse) ProtoMessage()    {}
func (*FlashbackToVersionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{38}
}
func (m *FlashbackToVersionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlashbackToVersionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlashbackToVersionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlashbackToVersionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlashbackToVersionResponse.Merge(m, src)
}
func (m *FlashbackToVersionResponse) XXX_Size() int {
	return m.Size()
}
func (m *FlashbackToVersionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FlashbackToVersionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FlashbackToVersionResponse proto.InternalMessageInfo

func (m *FlashbackToVersionResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type GetRegionReadProgressRequest struct {
	RegionId   uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	LogLocks   bool   `protobuf:"varint,2,opt,name=log_locks,json=logLocks,proto3" json:"log_locks,omitempty"`
	MinStartTs uint64 `protobuf:"varint,3,opt,name=min_start_ts,json=minStartTs,proto3" json:"min_start_ts,omitempty"`
}

func (m *GetRegionReadProgressRequest) Reset()         { *m = GetRegionReadProgressRequest{} }
func (m *GetRegionReadProgressRequest) String() string { return proto.CompactTextString(m) }
func (*GetRegionReadProgressRequest) ProtoMessage()    {}
func (*GetRegionReadProgressRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{39}
}
func (m *GetRegionReadProgressRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRegionReadProgressRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRegionReadProgressRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRegionReadProgressRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegionReadProgressRequest.Merge(m, src)
}
func (m *GetRegionReadProgressRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRegionReadProgressRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegionReadProgressRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegionReadProgressRequest proto.InternalMessageInfo

func (m *GetRegionReadProgressRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *GetRegionReadProgressRequest) GetLogLocks() bool {
	if m != nil {
		return m.LogLocks
	}
	return false
}

func (m *GetRegionReadProgressRequest) GetMinStartTs() uint64 {
	if m != nil {
		return m.MinStartTs
	}
	return 0
}

type GetRegionReadProgressResponse struct {
	// below are from region_read_progress module
	SafeTs                        uint64 `protobuf:"varint,1,opt,name=safe_ts,json=safeTs,proto3" json:"safe_ts,omitempty"`
	AppliedIndex                  uint64 `protobuf:"varint,2,opt,name=applied_index,json=appliedIndex,proto3" json:"applied_index,omitempty"`
	PendingFrontAppliedIndex      uint64 `protobuf:"varint,3,opt,name=pending_front_applied_index,json=pendingFrontAppliedIndex,proto3" json:"pending_front_applied_index,omitempty"`
	PendingFrontTs                uint64 `protobuf:"varint,4,opt,name=pending_front_ts,json=pendingFrontTs,proto3" json:"pending_front_ts,omitempty"`
	PendingBackAppliedIndex       uint64 `protobuf:"varint,5,opt,name=pending_back_applied_index,json=pendingBackAppliedIndex,proto3" json:"pending_back_applied_index,omitempty"`
	PendingBackTs                 uint64 `protobuf:"varint,6,opt,name=pending_back_ts,json=pendingBackTs,proto3" json:"pending_back_ts,omitempty"`
	RegionReadProgressPaused      bool   `protobuf:"varint,7,opt,name=region_read_progress_paused,json=regionReadProgressPaused,proto3" json:"region_read_progress_paused,omitempty"`
	DurationToLastUpdateSafeTsMs  uint64 `protobuf:"varint,8,opt,name=duration_to_last_update_safe_ts_ms,json=durationToLastUpdateSafeTsMs,proto3" json:"duration_to_last_update_safe_ts_ms,omitempty"`
	DurationToLastConsumeLeaderMs uint64 `protobuf:"varint,9,opt,name=duration_to_last_consume_leader_ms,json=durationToLastConsumeLeaderMs,proto3" json:"duration_to_last_consume_leader_ms,omitempty"`
	RegionReadProgressExist       bool   `protobuf:"varint,10,opt,name=region_read_progress_exist,json=regionReadProgressExist,proto3" json:"region_read_progress_exist,omitempty"`
	ReadStateTs                   uint64 `protobuf:"varint,18,opt,name=read_state_ts,json=readStateTs,proto3" json:"read_state_ts,omitempty"`
	ReadStateApplyIndex           uint64 `protobuf:"varint,19,opt,name=read_state_apply_index,json=readStateApplyIndex,proto3" json:"read_state_apply_index,omitempty"`
	Discard                       bool   `protobuf:"varint,20,opt,name=discard,proto3" json:"discard,omitempty"`
	// below are from resolved-ts module
	ResolvedTs           uint64 `protobuf:"varint,11,opt,name=resolved_ts,json=resolvedTs,proto3" json:"resolved_ts,omitempty"`
	ResolverTrackedIndex uint64 `protobuf:"varint,12,opt,name=resolver_tracked_index,json=resolverTrackedIndex,proto3" json:"resolver_tracked_index,omitempty"`
	ResolverExist        bool   `protobuf:"varint,13,opt,name=resolver_exist,json=resolverExist,proto3" json:"resolver_exist,omitempty"`
	ResolverStopped      bool   `protobuf:"varint,14,opt,name=resolver_stopped,json=resolverStopped,proto3" json:"resolver_stopped,omitempty"`
	NumLocks             uint64 `protobuf:"varint,16,opt,name=num_locks,json=numLocks,proto3" json:"num_locks,omitempty"`
	NumTransactions      uint64 `protobuf:"varint,17,opt,name=num_transactions,json=numTransactions,proto3" json:"num_transactions,omitempty"`
	Error                string `protobuf:"bytes,15,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *GetRegionReadProgressResponse) Reset()         { *m = GetRegionReadProgressResponse{} }
func (m *GetRegionReadProgressResponse) String() string { return proto.CompactTextString(m) }
func (*GetRegionReadProgressResponse) ProtoMessage()    {}
func (*GetRegionReadProgressResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_823ddaea3037c5c3, []int{40}
}
func (m *GetRegionReadProgressResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRegionReadProgressResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRegionReadProgressResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRegionReadProgressResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegionReadProgressResponse.Merge(m, src)
}
func (m *GetRegionReadProgressResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetRegionReadProgressResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegionReadProgressResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegionReadProgressResponse proto.InternalMessageInfo

func (m *GetRegionReadProgressResponse) GetSafeTs() uint64 {
	if m != nil {
		return m.SafeTs
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetAppliedIndex() uint64 {
	if m != nil {
		return m.AppliedIndex
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetPendingFrontAppliedIndex() uint64 {
	if m != nil {
		return m.PendingFrontAppliedIndex
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetPendingFrontTs() uint64 {
	if m != nil {
		return m.PendingFrontTs
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetPendingBackAppliedIndex() uint64 {
	if m != nil {
		return m.PendingBackAppliedIndex
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetPendingBackTs() uint64 {
	if m != nil {
		return m.PendingBackTs
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetRegionReadProgressPaused() bool {
	if m != nil {
		return m.RegionReadProgressPaused
	}
	return false
}

func (m *GetRegionReadProgressResponse) GetDurationToLastUpdateSafeTsMs() uint64 {
	if m != nil {
		return m.DurationToLastUpdateSafeTsMs
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetDurationToLastConsumeLeaderMs() uint64 {
	if m != nil {
		return m.DurationToLastConsumeLeaderMs
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetRegionReadProgressExist() bool {
	if m != nil {
		return m.RegionReadProgressExist
	}
	return false
}

func (m *GetRegionReadProgressResponse) GetReadStateTs() uint64 {
	if m != nil {
		return m.ReadStateTs
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetReadStateApplyIndex() uint64 {
	if m != nil {
		return m.ReadStateApplyIndex
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetDiscard() bool {
	if m != nil {
		return m.Discard
	}
	return false
}

func (m *GetRegionReadProgressResponse) GetResolvedTs() uint64 {
	if m != nil {
		return m.ResolvedTs
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetResolverTrackedIndex() uint64 {
	if m != nil {
		return m.ResolverTrackedIndex
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetResolverExist() bool {
	if m != nil {
		return m.ResolverExist
	}
	return false
}

func (m *GetRegionReadProgressResponse) GetResolverStopped() bool {
	if m != nil {
		return m.ResolverStopped
	}
	return false
}

func (m *GetRegionReadProgressResponse) GetNumLocks() uint64 {
	if m != nil {
		return m.NumLocks
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetNumTransactions() uint64 {
	if m != nil {
		return m.NumTransactions
	}
	return 0
}

func (m *GetRegionReadProgressResponse) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func init() {
	proto.RegisterEnum("debugpb.DB", DB_name, DB_value)
	proto.RegisterEnum("debugpb.MODULE", MODULE_name, MODULE_value)
	proto.RegisterEnum("debugpb.BottommostLevelCompaction", BottommostLevelCompaction_name, BottommostLevelCompaction_value)
	proto.RegisterType((*GetRequest)(nil), "debugpb.GetRequest")
	proto.RegisterType((*GetResponse)(nil), "debugpb.GetResponse")
	proto.RegisterType((*RaftLogRequest)(nil), "debugpb.RaftLogRequest")
	proto.RegisterType((*RaftLogResponse)(nil), "debugpb.RaftLogResponse")
	proto.RegisterType((*RegionInfoRequest)(nil), "debugpb.RegionInfoRequest")
	proto.RegisterType((*RegionInfoResponse)(nil), "debugpb.RegionInfoResponse")
	proto.RegisterType((*RegionSizeRequest)(nil), "debugpb.RegionSizeRequest")
	proto.RegisterType((*RegionSizeResponse)(nil), "debugpb.RegionSizeResponse")
	proto.RegisterType((*RegionSizeResponse_Entry)(nil), "debugpb.RegionSizeResponse.Entry")
	proto.RegisterType((*ScanMvccRequest)(nil), "debugpb.ScanMvccRequest")
	proto.RegisterType((*ScanMvccResponse)(nil), "debugpb.ScanMvccResponse")
	proto.RegisterType((*CompactRequest)(nil), "debugpb.CompactRequest")
	proto.RegisterType((*CompactResponse)(nil), "debugpb.CompactResponse")
	proto.RegisterType((*InjectFailPointRequest)(nil), "debugpb.InjectFailPointRequest")
	proto.RegisterType((*InjectFailPointResponse)(nil), "debugpb.InjectFailPointResponse")
	proto.RegisterType((*RecoverFailPointRequest)(nil), "debugpb.RecoverFailPointRequest")
	proto.RegisterType((*RecoverFailPointResponse)(nil), "debugpb.RecoverFailPointResponse")
	proto.RegisterType((*ListFailPointsRequest)(nil), "debugpb.ListFailPointsRequest")
	proto.RegisterType((*ListFailPointsResponse)(nil), "debugpb.ListFailPointsResponse")
	proto.RegisterType((*ListFailPointsResponse_Entry)(nil), "debugpb.ListFailPointsResponse.Entry")
	proto.RegisterType((*GetMetricsRequest)(nil), "debugpb.GetMetricsRequest")
	proto.RegisterType((*GetMetricsResponse)(nil), "debugpb.GetMetricsResponse")
	proto.RegisterType((*RegionConsistencyCheckRequest)(nil), "debugpb.RegionConsistencyCheckRequest")
	proto.RegisterType((*RegionConsistencyCheckResponse)(nil), "debugpb.RegionConsistencyCheckResponse")
	proto.RegisterType((*ModifyTikvConfigRequest)(nil), "debugpb.ModifyTikvConfigRequest")
	proto.RegisterType((*ModifyTikvConfigResponse)(nil), "debugpb.ModifyTikvConfigResponse")
	proto.RegisterType((*Property)(nil), "debugpb.Property")
	proto.RegisterType((*GetRegionPropertiesRequest)(nil), "debugpb.GetRegionPropertiesRequest")
	proto.RegisterType((*GetRegionPropertiesResponse)(nil), "debugpb.GetRegionPropertiesResponse")
	proto.RegisterType((*GetStoreInfoRequest)(nil), "debugpb.GetStoreInfoRequest")
	proto.RegisterType((*GetStoreInfoResponse)(nil), "debugpb.GetStoreInfoResponse")
	proto.RegisterType((*GetClusterInfoRequest)(nil), "debugpb.GetClusterInfoRequest")
	proto.RegisterType((*GetClusterInfoResponse)(nil), "debugpb.GetClusterInfoResponse")
	proto.RegisterType((*GetAllRegionsInStoreRequest)(nil), "debugpb.GetAllRegionsInStoreRequest")
	proto.RegisterType((*GetAllRegionsInStoreResponse)(nil), "debugpb.GetAllRegionsInStoreResponse")
	proto.RegisterType((*ResetToVersionRequest)(nil), "debugpb.ResetToVersionRequest")
	proto.RegisterType((*ResetToVersionResponse)(nil), "debugpb.ResetToVersionResponse")
	proto.RegisterType((*GetRangePropertiesRequest)(nil), "debugpb.GetRangePropertiesRequest")
	proto.RegisterType((*GetRangePropertiesResponse)(nil), "debugpb.GetRangePropertiesResponse")
	proto.RegisterType((*GetRangePropertiesResponse_RangeProperty)(nil), "debugpb.GetRangePropertiesResponse.RangeProperty")
	proto.RegisterType((*FlashbackToVersionRequest)(nil), "debugpb.FlashbackToVersionRequest")
	proto.RegisterType((*FlashbackToVersionResponse)(nil), "debugpb.FlashbackToVersionResponse")
	proto.RegisterType((*GetRegionReadProgressRequest)(nil), "debugpb.GetRegionReadProgressRequest")
	proto.RegisterType((*GetRegionReadProgressResponse)(nil), "debugpb.GetRegionReadProgressResponse")
}

func init() { proto.RegisterFile("debugpb.proto", fileDescriptor_823ddaea3037c5c3) }

var fileDescriptor_823ddaea3037c5c3 = []byte{
	// 2305 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x59, 0x4b, 0x73, 0xdb, 0xc8,
	0x11, 0x26, 0x28, 0x4a, 0xa4, 0x5a, 0x2f, 0x68, 0x24, 0x4b, 0x14, 0xbc, 0x92, 0x65, 0x78, 0x65,
	0x6b, 0x9d, 0x8a, 0xb2, 0xd1, 0x3a, 0xe5, 0xa4, 0x36, 0x8f, 0x92, 0xa8, 0xc7, 0x32, 0x92, 0x2c,
	0x2d, 0x48, 0xab, 0xca, 0x27, 0x14, 0x04, 0x0c, 0x69, 0x2c, 0x41, 0x0c, 0x83, 0x19, 0xb2, 0x2c,
	0x1f, 0x72, 0xcc, 0xd9, 0xc7, 0x54, 0xe5, 0x0f, 0xec, 0x25, 0xd7, 0xfc, 0x86, 0x1c, 0xf7, 0xb8,
	0xc7, 0x94, 0x7d, 0x4d, 0x8e, 0xb9, 0xa7, 0xe6, 0x01, 0x70, 0xf8, 0xb2, 0xb5, 0x39, 0x09, 0xd3,
	0xdd, 0xd3, 0xdd, 0xd3, 0xfd, 0xcd, 0xe3, 0xa3, 0x60, 0x21, 0xc0, 0x37, 0xdd, 0x66, 0xe7, 0x66,
	0xaf, 0x93, 0x10, 0x46, 0x50, 0x51, 0x0d, 0xad, 0x05, 0x9c, 0x78, 0x0d, 0x96, 0xca, 0xad, 0x85,
	0x56, 0x2f, 0xe9, 0xf8, 0xd9, 0x70, 0x85, 0x2b, 0x5d, 0x8a, 0x93, 0x1e, 0x4e, 0x32, 0xe1, 0x6a,
	0x93, 0x34, 0x89, 0xf8, 0xfc, 0x05, 0xff, 0x52, 0xd2, 0xa5, 0xa4, 0x4b, 0x99, 0xf8, 0x94, 0x02,
	0xfb, 0x0c, 0xe0, 0x14, 0x33, 0x07, 0xff, 0xa9, 0x8b, 0x29, 0x43, 0xf7, 0x21, 0x1f, 0xdc, 0x94,
	0x8d, 0x6d, 0x63, 0x77, 0x71, 0x7f, 0x6e, 0x2f, 0x4d, 0xe6, 0xe8, 0xd0, 0xc9, 0x07, 0x37, 0x68,
	0x11, 0xf2, 0x7e, 0xa3, 0x9c, 0xdf, 0x36, 0x76, 0x67, 0x9d, 0xbc, 0xdf, 0x40, 0x26, 0x4c, 0xb5,
	0xf0, 0x6d, 0x79, 0x6a, 0xdb, 0xd8, 0x9d, 0x77, 0xf8, 0xa7, 0xfd, 0x08, 0xe6, 0x84, 0x33, 0xda,
	0x21, 0x31, 0xc5, 0x68, 0x15, 0xa6, 0x7b, 0x5e, 0xd4, 0xc5, 0xc2, 0xe1, 0xbc, 0x23, 0x07, 0xf6,
	0x1f, 0x61, 0xd1, 0xf1, 0x1a, 0xec, 0x9c, 0x34, 0xfb, 0x51, 0x67, 0x13, 0xdc, 0x0c, 0x49, 0xec,
	0x86, 0x81, 0xb0, 0x2d, 0x38, 0x25, 0x29, 0xa8, 0x06, 0x5c, 0x19, 0x91, 0xa6, 0x1b, 0xc6, 0x01,
	0x7e, 0x23, 0x82, 0x17, 0x9c, 0x52, 0x44, 0x9a, 0x55, 0x3e, 0xb6, 0x9f, 0xc3, 0x52, 0xe6, 0x4b,
	0x05, 0xfd, 0x1c, 0xa6, 0x71, 0xcc, 0x92, 0x5b, 0xe1, 0x68, 0x6e, 0x7f, 0x71, 0x2f, 0x2d, 0xdd,
	0x31, 0x97, 0x3a, 0x52, 0x69, 0x7f, 0x09, 0xcb, 0x8e, 0x8c, 0x10, 0x37, 0xc8, 0x5d, 0xf2, 0xb0,
	0xff, 0x6b, 0x00, 0xd2, 0xa7, 0xa8, 0x70, 0xa7, 0x60, 0x8a, 0xea, 0x47, 0xc4, 0xf7, 0x22, 0x97,
	0x32, 0x8f, 0x61, 0x15, 0x79, 0x73, 0x6f, 0xb0, 0x2d, 0x32, 0x51, 0xdf, 0x8b, 0x6a, 0xdc, 0xc8,
	0x59, 0x4c, 0x06, 0xc6, 0x99, 0x23, 0xaf, 0xd3, 0x89, 0x6e, 0x95, 0xa3, 0xfc, 0x44, 0x47, 0x07,
	0xdc, 0x4a, 0x73, 0xd4, 0x1f, 0xa3, 0x0b, 0x40, 0x6a, 0x15, 0x7a, 0x4e, 0x53, 0xc2, 0xd5, 0x83,
	0x61, 0x57, 0xc2, 0x50, 0xcb, 0xca, 0x4c, 0x86, 0x24, 0xf6, 0x61, 0x5a, 0xa9, 0x5a, 0xf8, 0x16,
	0xdf, 0xa9, 0x63, 0x26, 0x4c, 0xf9, 0x0d, 0x5a, 0xce, 0x6f, 0x4f, 0xed, 0xce, 0x3a, 0xfc, 0xd3,
	0xfe, 0x73, 0x5a, 0x3a, 0xe9, 0x43, 0x95, 0xee, 0x6b, 0x28, 0xf2, 0x66, 0x84, 0x98, 0x96, 0x8d,
	0xed, 0xa9, 0xdd, 0xb9, 0xfd, 0x87, 0x19, 0xe2, 0x46, 0xad, 0x55, 0xfb, 0xd2, 0x19, 0xd6, 0xcf,
	0x60, 0x5a, 0x48, 0x14, 0x2a, 0x8d, 0x0c, 0x95, 0x08, 0x0a, 0x34, 0x7c, 0x8b, 0x15, 0x54, 0xc4,
	0xb7, 0xfd, 0x0a, 0x96, 0x6a, 0xbe, 0x17, 0x5f, 0xf4, 0x7c, 0x3f, 0x5d, 0xc1, 0x06, 0x94, 0x1a,
	0x09, 0x69, 0xbb, 0x1c, 0xc1, 0x12, 0x9e, 0x45, 0x3e, 0x3e, 0xc3, 0xb7, 0xe8, 0x1e, 0xcc, 0x30,
	0x22, 0x14, 0x79, 0x89, 0x5b, 0x46, 0xb8, 0x78, 0x15, 0xa6, 0xa3, 0xb0, 0x1d, 0x32, 0x51, 0xca,
	0x82, 0x23, 0x07, 0xf6, 0x19, 0x98, 0x7d, 0xd7, 0x6a, 0x61, 0x6a, 0x63, 0x18, 0xd9, 0xc6, 0x40,
	0x3b, 0x50, 0x08, 0xe3, 0x06, 0x51, 0x0d, 0x5d, 0xde, 0x4b, 0xf7, 0x2f, 0x9f, 0x26, 0xe0, 0x24,
	0xd4, 0xf6, 0x7f, 0x0c, 0x58, 0xac, 0x90, 0x76, 0xc7, 0xf3, 0xff, 0xbf, 0x1d, 0xa9, 0x2f, 0x6a,
	0x6a, 0xd2, 0xa2, 0x0a, 0xfa, 0xa2, 0xca, 0x50, 0x64, 0xaf, 0x13, 0xec, 0x05, 0xb4, 0x3c, 0xbd,
	0x6d, 0xec, 0x2e, 0x38, 0xe9, 0x10, 0xdd, 0xc0, 0xfd, 0x1b, 0xc2, 0x18, 0x69, 0xb7, 0x09, 0x65,
	0x6e, 0x84, 0x7b, 0x38, 0x72, 0x7d, 0x99, 0x5b, 0x48, 0xe2, 0xf2, 0x8c, 0xc8, 0xc8, 0xce, 0x32,
	0x3a, 0xcc, 0x6c, 0xcf, 0xb9, 0x69, 0x25, 0xb3, 0x74, 0x36, 0x6e, 0x26, 0xa9, 0xec, 0x65, 0x58,
	0xca, 0x96, 0x2b, 0x6b, 0x67, 0x9f, 0xc0, 0x5a, 0x35, 0xfe, 0x0e, 0xfb, 0xec, 0xc4, 0x0b, 0xa3,
	0x2b, 0x12, 0xc6, 0x59, 0x25, 0x10, 0x14, 0x62, 0xaf, 0x8d, 0x55, 0xab, 0xc5, 0x37, 0x4f, 0x5f,
	0xba, 0xa2, 0xaa, 0x0a, 0xe9, 0xd0, 0xde, 0x80, 0xf5, 0x11, 0x3f, 0x2a, 0xc4, 0xcf, 0x61, 0xdd,
	0xc1, 0x3e, 0xe9, 0xe1, 0xe4, 0x2e, 0x31, 0x6c, 0x0b, 0xca, 0xa3, 0xe6, 0xca, 0xd5, 0x3a, 0xdc,
	0x3b, 0x0f, 0x69, 0x3f, 0x06, 0x55, 0x8e, 0xec, 0x77, 0x06, 0xac, 0x0d, 0x6b, 0x14, 0x3a, 0xfe,
	0x30, 0x0c, 0xfb, 0x9d, 0xac, 0x88, 0xe3, 0x67, 0x0c, 0x43, 0xff, 0x57, 0x29, 0xf4, 0x7f, 0x5a,
	0x45, 0x76, 0x60, 0xf9, 0x14, 0xb3, 0x0b, 0xcc, 0x92, 0xd0, 0x4f, 0xf3, 0xe4, 0x50, 0xf5, 0xa2,
	0x48, 0x78, 0x28, 0x39, 0xfc, 0xd3, 0xfe, 0xbb, 0x01, 0x48, 0xb7, 0x53, 0x59, 0x6f, 0x01, 0x74,
	0x12, 0xd2, 0xc6, 0xec, 0x35, 0xee, 0x52, 0x15, 0x51, 0x93, 0xa0, 0x4d, 0x80, 0x84, 0xf8, 0x2d,
	0x1a, 0xdc, 0xb8, 0xad, 0x9e, 0x0a, 0x3d, 0xab, 0x24, 0x67, 0x3d, 0xf4, 0x10, 0xe6, 0x53, 0x35,
	0x3f, 0x81, 0x04, 0x3a, 0x67, 0x9d, 0x39, 0x25, 0xe3, 0x27, 0x1a, 0xb2, 0xa0, 0xf4, 0x1d, 0x6e,
	0x7b, 0x51, 0x44, 0x7c, 0x81, 0xd1, 0x59, 0x27, 0x1b, 0x73, 0x60, 0x53, 0x46, 0x12, 0xcc, 0x8f,
	0x9b, 0x69, 0xb1, 0xfd, 0x8a, 0x62, 0x5c, 0x0d, 0xec, 0xdf, 0xc2, 0xa6, 0x3c, 0x2d, 0x2a, 0x24,
	0xa6, 0x21, 0x65, 0x38, 0xf6, 0x6f, 0x2b, 0xaf, 0xb1, 0xdf, 0xba, 0xd3, 0xa9, 0xbe, 0x0d, 0x5b,
	0x93, 0x66, 0xab, 0x16, 0xff, 0xc5, 0x80, 0xf5, 0x0b, 0x12, 0x84, 0x8d, 0xdb, 0x7a, 0xd8, 0xea,
	0x55, 0x48, 0xdc, 0x08, 0xb3, 0x8b, 0xeb, 0x09, 0xcc, 0xb4, 0x49, 0xd0, 0x8d, 0xb0, 0xda, 0xa0,
	0x4b, 0x59, 0x27, 0x2f, 0x2e, 0x8f, 0x5e, 0x9e, 0x1f, 0x3b, 0x4a, 0x8d, 0x1e, 0xc0, 0x9c, 0x2f,
	0x66, 0xba, 0xa2, 0x61, 0xb2, 0x3c, 0x20, 0x45, 0x2f, 0x78, 0xdb, 0x1e, 0xc2, 0xbc, 0x32, 0x90,
	0x37, 0xa6, 0xaa, 0x8f, 0x94, 0x5d, 0x8b, 0x7b, 0xd3, 0x82, 0xf2, 0x68, 0x1e, 0x2a, 0xc9, 0x67,
	0x50, 0xba, 0x4a, 0x48, 0x07, 0x27, 0x6c, 0x3c, 0x2a, 0xb2, 0x9b, 0x58, 0x46, 0x56, 0x37, 0xf1,
	0x6f, 0xc0, 0x12, 0xd7, 0x35, 0x5f, 0xbf, 0x9a, 0x1e, 0x62, 0x7a, 0xa7, 0xba, 0x9d, 0xc0, 0xfd,
	0xb1, 0x53, 0x15, 0x5a, 0x9e, 0xc0, 0x74, 0x27, 0x21, 0x9d, 0x14, 0xe1, 0xcb, 0x59, 0x5d, 0xd2,
	0x2c, 0x1d, 0xa9, 0xb7, 0xef, 0xc1, 0xca, 0x29, 0x66, 0x35, 0xd1, 0xcb, 0xfe, 0x4d, 0x6c, 0x37,
	0x61, 0x75, 0x50, 0xac, 0xfc, 0xea, 0x38, 0x30, 0x06, 0x70, 0x80, 0x9e, 0xc1, 0x9c, 0xd7, 0x09,
	0xdd, 0x1e, 0x4e, 0x28, 0x3f, 0x9f, 0xf2, 0xa2, 0x21, 0x2b, 0xd9, 0x49, 0x7b, 0x70, 0x55, 0xbd,
	0x96, 0x2a, 0x07, 0xbc, 0x4e, 0xa8, 0xbe, 0xf9, 0x06, 0x3e, 0xc5, 0xac, 0x12, 0x75, 0x29, 0xc3,
	0x89, 0x9e, 0xc1, 0x73, 0x58, 0x1b, 0x56, 0xa8, 0x1c, 0x36, 0x01, 0x7c, 0x29, 0xee, 0x67, 0x31,
	0xab, 0x24, 0xd5, 0xc0, 0xde, 0x14, 0x95, 0x39, 0x88, 0x22, 0x59, 0x1c, 0x5a, 0x8d, 0xc5, 0x32,
	0x52, 0xbf, 0xbf, 0x86, 0xcf, 0xc6, 0xab, 0x95, 0xf7, 0x32, 0x14, 0x65, 0x91, 0x65, 0xed, 0x0a,
	0x4e, 0x3a, 0xb4, 0x9f, 0xc0, 0x3d, 0x07, 0x53, 0xcc, 0xea, 0x24, 0x5d, 0x88, 0x6a, 0xd4, 0x22,
	0xe4, 0x19, 0x55, 0x89, 0xe4, 0x19, 0xb5, 0xcb, 0xb0, 0x36, 0x6c, 0xa8, 0x60, 0xf2, 0x2d, 0x6c,
	0xf0, 0xae, 0x79, 0x71, 0x13, 0x8f, 0xed, 0x37, 0x65, 0x5e, 0xc2, 0xb4, 0x2b, 0xb1, 0x24, 0x04,
	0xfc, 0x9e, 0x58, 0xe7, 0x87, 0x56, 0xa0, 0x5d, 0x8a, 0x33, 0x38, 0x0e, 0xce, 0xf0, 0xad, 0xfd,
	0xbd, 0x21, 0x41, 0x34, 0xec, 0x53, 0x2d, 0xe7, 0x5b, 0x71, 0x6c, 0x28, 0xa9, 0x42, 0xc3, 0x2f,
	0x33, 0x34, 0x4c, 0x9e, 0xb8, 0xa7, 0xcb, 0x6f, 0x1d, 0xcd, 0x89, 0xf5, 0x1c, 0x16, 0x06, 0x94,
	0xfa, 0x75, 0x3b, 0x2b, 0xaf, 0xdb, 0xf1, 0x70, 0xff, 0xb7, 0x01, 0x1b, 0x27, 0x91, 0x47, 0x5f,
	0xdf, 0x78, 0x7e, 0x6b, 0xa4, 0x8a, 0x4f, 0xa1, 0xe8, 0x93, 0x98, 0xe1, 0x37, 0x4c, 0xbd, 0xdf,
	0xcc, 0x0c, 0x3b, 0x15, 0x29, 0x77, 0x52, 0x03, 0xde, 0x24, 0x1d, 0x67, 0x05, 0x27, 0x1d, 0x0e,
	0x6e, 0x9a, 0xa9, 0xd1, 0xa7, 0x6c, 0xbf, 0xc2, 0x85, 0xc9, 0x15, 0x9e, 0xd6, 0x2b, 0x2c, 0x31,
	0xcf, 0x67, 0x31, 0x2a, 0x6e, 0x5d, 0x81, 0x79, 0x2f, 0x61, 0x75, 0xca, 0x1d, 0xfa, 0xa4, 0xdd,
	0x0e, 0x85, 0xae, 0x28, 0xa3, 0x49, 0x41, 0x9d, 0xda, 0xfb, 0x60, 0x8d, 0x5b, 0x6d, 0xff, 0x6d,
	0x8e, 0x93, 0x84, 0x24, 0xaa, 0x6c, 0x72, 0x60, 0xbf, 0x15, 0xe8, 0x94, 0xd0, 0x74, 0xb0, 0x17,
	0x5c, 0x25, 0xa4, 0x99, 0x60, 0x4a, 0x7f, 0xca, 0x4b, 0x3d, 0xe2, 0x67, 0xba, 0xa8, 0x4b, 0x49,
	0xbc, 0xd4, 0xcf, 0xf9, 0x18, 0x6d, 0xc3, 0x7c, 0x3b, 0x8c, 0xdd, 0x6c, 0x25, 0xb2, 0x36, 0xd0,
	0x0e, 0xe3, 0x9a, 0x5c, 0x8c, 0xfd, 0x8f, 0x22, 0x6c, 0x4e, 0x08, 0xae, 0x72, 0x5e, 0x87, 0x22,
	0xf5, 0x1a, 0xd8, 0xcd, 0xd0, 0x3e, 0xc3, 0x87, 0x75, 0x8a, 0x1e, 0xc1, 0x02, 0x7f, 0x36, 0x87,
	0x38, 0x18, 0xe0, 0x09, 0xf3, 0x4a, 0x28, 0xb8, 0x02, 0xfa, 0x1d, 0xdc, 0xef, 0xe0, 0x38, 0x08,
	0xe3, 0xa6, 0xdb, 0x48, 0x48, 0x2c, 0x5f, 0xda, 0xfd, 0x29, 0x32, 0xa1, 0xb2, 0x32, 0x39, 0xe1,
	0x16, 0x07, 0xfa, 0xf4, 0x5d, 0x30, 0x07, 0xa7, 0x33, 0x2a, 0x7a, 0x58, 0x70, 0x16, 0xf5, 0x39,
	0x75, 0x8a, 0xbe, 0x06, 0x2b, 0xb5, 0xe4, 0xb5, 0x1f, 0x8a, 0x23, 0xaf, 0xaf, 0x75, 0x65, 0x71,
	0xe8, 0xf9, 0xad, 0x81, 0x30, 0x8f, 0x61, 0x69, 0x60, 0x72, 0xd6, 0xf4, 0x05, 0x6d, 0x46, 0x9d,
	0xf2, 0xd5, 0xa8, 0x4e, 0xf0, 0xe7, 0x9a, 0xdb, 0x51, 0xb5, 0x72, 0x3b, 0x5e, 0x97, 0xe2, 0x40,
	0x80, 0xa1, 0xe4, 0x94, 0x93, 0x91, 0x62, 0x5e, 0x09, 0x3d, 0xfa, 0x06, 0xec, 0xa0, 0x9b, 0x78,
	0xfc, 0x65, 0xe0, 0x32, 0xe2, 0x46, 0x1e, 0x65, 0x6e, 0xb7, 0x13, 0x78, 0x0c, 0xbb, 0xaa, 0xc4,
	0x6e, 0x9b, 0x96, 0x4b, 0x22, 0xf2, 0x67, 0xa9, 0x65, 0x9d, 0x9c, 0x7b, 0x94, 0xbd, 0x14, 0x66,
	0x35, 0x51, 0xf9, 0x0b, 0x8a, 0xaa, 0x63, 0x3c, 0xf9, 0x24, 0xa6, 0xdd, 0x36, 0x76, 0x23, 0xec,
	0x05, 0x38, 0xe1, 0x9e, 0x66, 0x85, 0xa7, 0xcd, 0x41, 0x4f, 0x15, 0x69, 0x76, 0x2e, 0xac, 0x2e,
	0x44, 0xe1, 0xc6, 0xae, 0x09, 0xbf, 0x09, 0x29, 0x2b, 0x83, 0x58, 0xd2, 0xfa, 0xe8, 0x92, 0x8e,
	0xb9, 0x1a, 0xd9, 0xb0, 0x20, 0x66, 0x09, 0xba, 0xc3, 0xcb, 0x86, 0x44, 0xc8, 0x39, 0x2e, 0x14,
	0x4c, 0xa6, 0x4e, 0xd1, 0x57, 0xb0, 0xa6, 0xd9, 0x48, 0xa6, 0x25, 0xbb, 0xb2, 0x22, 0x8c, 0x57,
	0x32, 0x63, 0xc1, 0xa7, 0x64, 0x47, 0xca, 0x50, 0x0c, 0x42, 0xea, 0x7b, 0x49, 0x50, 0x5e, 0x15,
	0x29, 0xa4, 0x43, 0x7e, 0xab, 0x27, 0x98, 0x92, 0xa8, 0x87, 0x03, 0x1e, 0x70, 0x4e, 0x42, 0x3a,
	0x15, 0xd5, 0x29, 0x7a, 0xc6, 0xe3, 0x89, 0x51, 0xe2, 0xb2, 0xc4, 0xf3, 0x5b, 0x19, 0x0a, 0xe6,
	0x85, 0xed, 0x6a, 0xaa, 0xad, 0x4b, 0xa5, 0x0c, 0xb8, 0x03, 0x8b, 0xd9, 0x2c, 0xb9, 0xf4, 0x05,
	0x11, 0x77, 0x21, 0x95, 0xca, 0x05, 0x7f, 0x01, 0x66, 0x66, 0x46, 0x19, 0xe9, 0x74, 0x70, 0x50,
	0x5e, 0x14, 0x86, 0x4b, 0xa9, 0xbc, 0x26, 0xc5, 0x7c, 0x67, 0xc6, 0xdd, 0xb6, 0xda, 0x99, 0xa6,
	0xdc, 0xb6, 0x71, 0xb7, 0x2d, 0x77, 0xe6, 0x17, 0x60, 0x72, 0x25, 0x4b, 0xbc, 0x98, 0xa6, 0x4f,
	0xc7, 0x65, 0x61, 0xb3, 0x14, 0x77, 0xdb, 0x75, 0x4d, 0xdc, 0x3f, 0x34, 0x96, 0xb4, 0x43, 0xe3,
	0xe9, 0x23, 0xc8, 0x1f, 0x1d, 0xa2, 0x39, 0x28, 0x56, 0x5f, 0x5c, 0x1f, 0x9c, 0x57, 0x8f, 0xcc,
	0x1c, 0x9a, 0x81, 0xfc, 0xd9, 0xb5, 0x69, 0xa0, 0x12, 0x14, 0x9c, 0x83, 0x93, 0xba, 0x99, 0x7f,
	0xfa, 0xce, 0x80, 0x19, 0xf9, 0x28, 0x42, 0x00, 0x33, 0x2f, 0x5f, 0xbc, 0xac, 0x1d, 0x73, 0xc3,
	0x12, 0x14, 0xce, 0xae, 0x8f, 0x0e, 0x4d, 0x83, 0x4b, 0xb9, 0xe9, 0xd1, 0xa1, 0x99, 0x47, 0xf3,
	0x50, 0x72, 0x8e, 0x0f, 0x8e, 0xae, 0x2e, 0x2f, 0xcf, 0xcd, 0x29, 0xae, 0xa9, 0x1d, 0x3b, 0xd7,
	0xc7, 0x8e, 0x59, 0xe0, 0x51, 0x6a, 0xf5, 0x4b, 0xe7, 0xe0, 0xf4, 0xd8, 0x9c, 0xe6, 0x51, 0xae,
	0x8e, 0xcc, 0x19, 0x6e, 0x70, 0x71, 0x5c, 0x77, 0xaa, 0x15, 0xb3, 0x88, 0x96, 0x60, 0xae, 0x72,
	0x79, 0xe5, 0x5c, 0x56, 0x8e, 0x6b, 0xb5, 0x4b, 0xc7, 0x2c, 0x71, 0x5f, 0xb5, 0xe3, 0xca, 0x4b,
	0xa7, 0x5a, 0x7f, 0x65, 0xce, 0x72, 0xd3, 0xea, 0xc5, 0xd5, 0xa5, 0x53, 0x37, 0xe1, 0xe9, 0x39,
	0x6c, 0x4c, 0x64, 0x2d, 0x3c, 0xb1, 0x5a, 0x2b, 0xec, 0x98, 0x39, 0x34, 0x0b, 0xd3, 0x27, 0x24,
	0xf1, 0xb1, 0x69, 0x20, 0x0b, 0xd6, 0xaa, 0x8d, 0x6f, 0xbc, 0x1e, 0xee, 0x1b, 0x9e, 0x84, 0x11,
	0xc3, 0x89, 0x99, 0xdf, 0xff, 0xdb, 0x02, 0x4c, 0x1f, 0xf1, 0x7b, 0x0d, 0xed, 0xc3, 0xd4, 0x29,
	0x66, 0x68, 0x65, 0xe0, 0x9a, 0x93, 0x07, 0xa8, 0xb5, 0x3a, 0x28, 0x54, 0xf7, 0x72, 0x0e, 0xfd,
	0x1e, 0x8a, 0xea, 0x87, 0x0c, 0xb4, 0xde, 0x67, 0xc1, 0x03, 0x3f, 0x93, 0x58, 0xe5, 0x51, 0x45,
	0x36, 0xff, 0x14, 0xa0, 0xff, 0xe3, 0x04, 0xb2, 0x86, 0x88, 0xb4, 0xf6, 0xb0, 0xb1, 0xee, 0x8f,
	0xd5, 0x8d, 0x3a, 0xe2, 0xe4, 0x7b, 0xc4, 0x91, 0xf6, 0x1b, 0xc0, 0x88, 0x23, 0x9d, 0xad, 0xdb,
	0x39, 0x54, 0x81, 0x52, 0x4a, 0x8c, 0x51, 0x3f, 0xf3, 0x21, 0x1a, 0x6e, 0x6d, 0x8c, 0xd1, 0xa4,
	0x2e, 0xbe, 0x34, 0x78, 0x59, 0x54, 0xa9, 0xb5, 0xb2, 0x0c, 0x32, 0x64, 0xad, 0x2c, 0xc3, 0x5c,
	0x32, 0x87, 0xae, 0x61, 0x69, 0x88, 0x05, 0xa2, 0x07, 0x99, 0xf9, 0x78, 0x9e, 0x69, 0x6d, 0x4f,
	0x36, 0xc8, 0xfc, 0xbe, 0x02, 0x73, 0x98, 0x13, 0xa2, 0x6d, 0xad, 0x1e, 0x63, 0xd9, 0xa5, 0xf5,
	0xf0, 0x23, 0x16, 0x99, 0xeb, 0x1a, 0x2c, 0x0e, 0xd2, 0x40, 0xb4, 0x35, 0x91, 0x1f, 0x4a, 0xb7,
	0x0f, 0x3e, 0xc1, 0x1f, 0x65, 0x57, 0xfb, 0x9c, 0x4e, 0xeb, 0xea, 0x08, 0x21, 0xd4, 0xba, 0x3a,
	0x4a, 0x02, 0xed, 0x1c, 0x6a, 0xc1, 0x9a, 0xa2, 0x47, 0x43, 0xa4, 0x09, 0x3d, 0x1e, 0x82, 0xc3,
	0x04, 0x3a, 0x66, 0x3d, 0xf9, 0xa4, 0x9d, 0x5e, 0xe5, 0x61, 0xc6, 0xa3, 0x55, 0x79, 0x02, 0x29,
	0xd3, 0xaa, 0x3c, 0x91, 0x2e, 0xe5, 0xd0, 0x8d, 0xe0, 0x1d, 0xc3, 0xfc, 0x05, 0x3d, 0x1a, 0xdc,
	0x9e, 0x63, 0x89, 0x91, 0xf5, 0xf9, 0xc7, 0x8d, 0xb2, 0x18, 0x17, 0x30, 0xaf, 0x93, 0x18, 0xf4,
	0x99, 0x3e, 0x6f, 0x98, 0xf2, 0x58, 0x9b, 0x13, 0xb4, 0x3a, 0x30, 0x06, 0x19, 0x89, 0x06, 0x8c,
	0xb1, 0x1c, 0x46, 0x03, 0xc6, 0x78, 0x2a, 0x63, 0xe7, 0x10, 0x16, 0x44, 0x6b, 0x84, 0x8e, 0xa0,
	0x81, 0x35, 0x4e, 0x22, 0x33, 0xd6, 0xce, 0x27, 0xac, 0xf4, 0xdc, 0x07, 0x29, 0x89, 0x96, 0xfb,
	0x58, 0x52, 0xa3, 0xe5, 0x3e, 0x81, 0xcb, 0xe4, 0x90, 0x2b, 0x7e, 0xa8, 0x18, 0x22, 0x10, 0xc8,
	0xfe, 0x28, 0xbb, 0x90, 0xce, 0x1f, 0xdd, 0x81, 0x81, 0xc8, 0x00, 0xa3, 0x2f, 0x68, 0x2d, 0xc0,
	0x44, 0x32, 0xa1, 0x05, 0x98, 0xfc, 0x04, 0xb7, 0x73, 0xe8, 0xb5, 0x60, 0x9f, 0xa3, 0x2f, 0x5e,
	0xb4, 0x33, 0x0a, 0xb1, 0x31, 0xcf, 0x71, 0xeb, 0xf1, 0xa7, 0xcc, 0xd2, 0x48, 0x87, 0xfb, 0x3f,
	0x7e, 0x5f, 0x32, 0xfe, 0xf9, 0x7e, 0xcb, 0xf8, 0xe1, 0xfd, 0x96, 0xf1, 0xaf, 0xf7, 0x5b, 0xc6,
	0xbb, 0x0f, 0x5b, 0xb9, 0xbf, 0x7e, 0xd8, 0xca, 0xfd, 0xf0, 0x61, 0x2b, 0xf7, 0xe3, 0x87, 0xad,
	0x1c, 0x98, 0x24, 0x69, 0xee, 0xb1, 0xb0, 0xd5, 0xdb, 0x6b, 0xf5, 0xc4, 0xbf, 0x06, 0x6e, 0x66,
	0xc4, 0x9f, 0xaf, 0xfe, 0x17, 0x00, 0x00, 0xff, 0xff, 0xce, 0x55, 0xae, 0xc7, 0x95, 0x18, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DebugClient is the client API for Debug service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DebugClient interface {
	// Read a value arbitrarily for a key.
	// Note: Server uses key directly w/o any encoding.
	Get(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*GetResponse, error)
	// Read raft info.
	RaftLog(ctx context.Context, in *RaftLogRequest, opts ...grpc.CallOption) (*RaftLogResponse, error)
	RegionInfo(ctx context.Context, in *RegionInfoRequest, opts ...grpc.CallOption) (*RegionInfoResponse, error)
	// Calculate size of a region.
	// Note: DO NOT CALL IT IN PRODUCTION, it's really expensive.
	RegionSize(ctx context.Context, in *RegionSizeRequest, opts ...grpc.CallOption) (*RegionSizeResponse, error)
	// Scan a specific range.
	// Note: DO NOT CALL IT IN PRODUCTION, it's really expensive.
	//       Server uses keys directly w/o any encoding.
	ScanMvcc(ctx context.Context, in *ScanMvccRequest, opts ...grpc.CallOption) (Debug_ScanMvccClient, error)
	// Compact a column family in a specified range.
	// Note: Server uses keys directly w/o any encoding.
	Compact(ctx context.Context, in *CompactRequest, opts ...grpc.CallOption) (*CompactResponse, error)
	// Inject a fail point. Currently, it's only used in tests.
	// Note: DO NOT CALL IT IN PRODUCTION.
	InjectFailPoint(ctx context.Context, in *InjectFailPointRequest, opts ...grpc.CallOption) (*InjectFailPointResponse, error)
	// Recover from a fail point.
	RecoverFailPoint(ctx context.Context, in *RecoverFailPointRequest, opts ...grpc.CallOption) (*RecoverFailPointResponse, error)
	// List all fail points.
	ListFailPoints(ctx context.Context, in *ListFailPointsRequest, opts ...grpc.CallOption) (*ListFailPointsResponse, error)
	// Get Metrics
	GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error)
	// Do a consistent check for a region.
	CheckRegionConsistency(ctx context.Context, in *RegionConsistencyCheckRequest, opts ...grpc.CallOption) (*RegionConsistencyCheckResponse, error)
	// dynamically modify tikv's config
	ModifyTikvConfig(ctx context.Context, in *ModifyTikvConfigRequest, opts ...grpc.CallOption) (*ModifyTikvConfigResponse, error)
	// Get region properties
	GetRegionProperties(ctx context.Context, in *GetRegionPropertiesRequest, opts ...grpc.CallOption) (*GetRegionPropertiesResponse, error)
	// Get store ID
	GetStoreInfo(ctx context.Context, in *GetStoreInfoRequest, opts ...grpc.CallOption) (*GetStoreInfoResponse, error)
	// Get cluster ID
	GetClusterInfo(ctx context.Context, in *GetClusterInfoRequest, opts ...grpc.CallOption) (*GetClusterInfoResponse, error)
	// Get all region IDs in the store
	GetAllRegionsInStore(ctx context.Context, in *GetAllRegionsInStoreRequest, opts ...grpc.CallOption) (*GetAllRegionsInStoreResponse, error)
	// Make this TiKV node return to the status on this node to certain ts.
	ResetToVersion(ctx context.Context, in *ResetToVersionRequest, opts ...grpc.CallOption) (*ResetToVersionResponse, error)
	// Get range properties
	GetRangeProperties(ctx context.Context, in *GetRangePropertiesRequest, opts ...grpc.CallOption) (*GetRangePropertiesResponse, error)
	// Flashback given key range to a specified version.
	FlashbackToVersion(ctx context.Context, in *FlashbackToVersionRequest, opts ...grpc.CallOption) (*FlashbackToVersionResponse, error)
	// GetRegionReadProgress returns the some useful info in RegionReadProgress
	GetRegionReadProgress(ctx context.Context, in *GetRegionReadProgressRequest, opts ...grpc.CallOption) (*GetRegionReadProgressResponse, error)
}

type debugClient struct {
	cc *grpc.ClientConn
}

func NewDebugClient(cc *grpc.ClientConn) DebugClient {
	return &debugClient{cc}
}

func (c *debugClient) Get(ctx context.Context, in *GetRequest, opts ...grpc.CallOption) (*GetResponse, error) {
	out := new(GetResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/Get", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) RaftLog(ctx context.Context, in *RaftLogRequest, opts ...grpc.CallOption) (*RaftLogResponse, error) {
	out := new(RaftLogResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/RaftLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) RegionInfo(ctx context.Context, in *RegionInfoRequest, opts ...grpc.CallOption) (*RegionInfoResponse, error) {
	out := new(RegionInfoResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/RegionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) RegionSize(ctx context.Context, in *RegionSizeRequest, opts ...grpc.CallOption) (*RegionSizeResponse, error) {
	out := new(RegionSizeResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/RegionSize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) ScanMvcc(ctx context.Context, in *ScanMvccRequest, opts ...grpc.CallOption) (Debug_ScanMvccClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Debug_serviceDesc.Streams[0], "/debugpb.Debug/ScanMvcc", opts...)
	if err != nil {
		return nil, err
	}
	x := &debugScanMvccClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Debug_ScanMvccClient interface {
	Recv() (*ScanMvccResponse, error)
	grpc.ClientStream
}

type debugScanMvccClient struct {
	grpc.ClientStream
}

func (x *debugScanMvccClient) Recv() (*ScanMvccResponse, error) {
	m := new(ScanMvccResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *debugClient) Compact(ctx context.Context, in *CompactRequest, opts ...grpc.CallOption) (*CompactResponse, error) {
	out := new(CompactResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/Compact", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) InjectFailPoint(ctx context.Context, in *InjectFailPointRequest, opts ...grpc.CallOption) (*InjectFailPointResponse, error) {
	out := new(InjectFailPointResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/InjectFailPoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) RecoverFailPoint(ctx context.Context, in *RecoverFailPointRequest, opts ...grpc.CallOption) (*RecoverFailPointResponse, error) {
	out := new(RecoverFailPointResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/RecoverFailPoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) ListFailPoints(ctx context.Context, in *ListFailPointsRequest, opts ...grpc.CallOption) (*ListFailPointsResponse, error) {
	out := new(ListFailPointsResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/ListFailPoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error) {
	out := new(GetMetricsResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/GetMetrics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) CheckRegionConsistency(ctx context.Context, in *RegionConsistencyCheckRequest, opts ...grpc.CallOption) (*RegionConsistencyCheckResponse, error) {
	out := new(RegionConsistencyCheckResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/CheckRegionConsistency", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) ModifyTikvConfig(ctx context.Context, in *ModifyTikvConfigRequest, opts ...grpc.CallOption) (*ModifyTikvConfigResponse, error) {
	out := new(ModifyTikvConfigResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/ModifyTikvConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) GetRegionProperties(ctx context.Context, in *GetRegionPropertiesRequest, opts ...grpc.CallOption) (*GetRegionPropertiesResponse, error) {
	out := new(GetRegionPropertiesResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/GetRegionProperties", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) GetStoreInfo(ctx context.Context, in *GetStoreInfoRequest, opts ...grpc.CallOption) (*GetStoreInfoResponse, error) {
	out := new(GetStoreInfoResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/GetStoreInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) GetClusterInfo(ctx context.Context, in *GetClusterInfoRequest, opts ...grpc.CallOption) (*GetClusterInfoResponse, error) {
	out := new(GetClusterInfoResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/GetClusterInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) GetAllRegionsInStore(ctx context.Context, in *GetAllRegionsInStoreRequest, opts ...grpc.CallOption) (*GetAllRegionsInStoreResponse, error) {
	out := new(GetAllRegionsInStoreResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/GetAllRegionsInStore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) ResetToVersion(ctx context.Context, in *ResetToVersionRequest, opts ...grpc.CallOption) (*ResetToVersionResponse, error) {
	out := new(ResetToVersionResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/ResetToVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) GetRangeProperties(ctx context.Context, in *GetRangePropertiesRequest, opts ...grpc.CallOption) (*GetRangePropertiesResponse, error) {
	out := new(GetRangePropertiesResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/GetRangeProperties", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) FlashbackToVersion(ctx context.Context, in *FlashbackToVersionRequest, opts ...grpc.CallOption) (*FlashbackToVersionResponse, error) {
	out := new(FlashbackToVersionResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/FlashbackToVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *debugClient) GetRegionReadProgress(ctx context.Context, in *GetRegionReadProgressRequest, opts ...grpc.CallOption) (*GetRegionReadProgressResponse, error) {
	out := new(GetRegionReadProgressResponse)
	err := c.cc.Invoke(ctx, "/debugpb.Debug/GetRegionReadProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DebugServer is the server API for Debug service.
type DebugServer interface {
	// Read a value arbitrarily for a key.
	// Note: Server uses key directly w/o any encoding.
	Get(context.Context, *GetRequest) (*GetResponse, error)
	// Read raft info.
	RaftLog(context.Context, *RaftLogRequest) (*RaftLogResponse, error)
	RegionInfo(context.Context, *RegionInfoRequest) (*RegionInfoResponse, error)
	// Calculate size of a region.
	// Note: DO NOT CALL IT IN PRODUCTION, it's really expensive.
	RegionSize(context.Context, *RegionSizeRequest) (*RegionSizeResponse, error)
	// Scan a specific range.
	// Note: DO NOT CALL IT IN PRODUCTION, it's really expensive.
	//       Server uses keys directly w/o any encoding.
	ScanMvcc(*ScanMvccRequest, Debug_ScanMvccServer) error
	// Compact a column family in a specified range.
	// Note: Server uses keys directly w/o any encoding.
	Compact(context.Context, *CompactRequest) (*CompactResponse, error)
	// Inject a fail point. Currently, it's only used in tests.
	// Note: DO NOT CALL IT IN PRODUCTION.
	InjectFailPoint(context.Context, *InjectFailPointRequest) (*InjectFailPointResponse, error)
	// Recover from a fail point.
	RecoverFailPoint(context.Context, *RecoverFailPointRequest) (*RecoverFailPointResponse, error)
	// List all fail points.
	ListFailPoints(context.Context, *ListFailPointsRequest) (*ListFailPointsResponse, error)
	// Get Metrics
	GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error)
	// Do a consistent check for a region.
	CheckRegionConsistency(context.Context, *RegionConsistencyCheckRequest) (*RegionConsistencyCheckResponse, error)
	// dynamically modify tikv's config
	ModifyTikvConfig(context.Context, *ModifyTikvConfigRequest) (*ModifyTikvConfigResponse, error)
	// Get region properties
	GetRegionProperties(context.Context, *GetRegionPropertiesRequest) (*GetRegionPropertiesResponse, error)
	// Get store ID
	GetStoreInfo(context.Context, *GetStoreInfoRequest) (*GetStoreInfoResponse, error)
	// Get cluster ID
	GetClusterInfo(context.Context, *GetClusterInfoRequest) (*GetClusterInfoResponse, error)
	// Get all region IDs in the store
	GetAllRegionsInStore(context.Context, *GetAllRegionsInStoreRequest) (*GetAllRegionsInStoreResponse, error)
	// Make this TiKV node return to the status on this node to certain ts.
	ResetToVersion(context.Context, *ResetToVersionRequest) (*ResetToVersionResponse, error)
	// Get range properties
	GetRangeProperties(context.Context, *GetRangePropertiesRequest) (*GetRangePropertiesResponse, error)
	// Flashback given key range to a specified version.
	FlashbackToVersion(context.Context, *FlashbackToVersionRequest) (*FlashbackToVersionResponse, error)
	// GetRegionReadProgress returns the some useful info in RegionReadProgress
	GetRegionReadProgress(context.Context, *GetRegionReadProgressRequest) (*GetRegionReadProgressResponse, error)
}

// UnimplementedDebugServer can be embedded to have forward compatible implementations.
type UnimplementedDebugServer struct {
}

func (*UnimplementedDebugServer) Get(ctx context.Context, req *GetRequest) (*GetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (*UnimplementedDebugServer) RaftLog(ctx context.Context, req *RaftLogRequest) (*RaftLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RaftLog not implemented")
}
func (*UnimplementedDebugServer) RegionInfo(ctx context.Context, req *RegionInfoRequest) (*RegionInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegionInfo not implemented")
}
func (*UnimplementedDebugServer) RegionSize(ctx context.Context, req *RegionSizeRequest) (*RegionSizeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegionSize not implemented")
}
func (*UnimplementedDebugServer) ScanMvcc(req *ScanMvccRequest, srv Debug_ScanMvccServer) error {
	return status.Errorf(codes.Unimplemented, "method ScanMvcc not implemented")
}
func (*UnimplementedDebugServer) Compact(ctx context.Context, req *CompactRequest) (*CompactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Compact not implemented")
}
func (*UnimplementedDebugServer) InjectFailPoint(ctx context.Context, req *InjectFailPointRequest) (*InjectFailPointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InjectFailPoint not implemented")
}
func (*UnimplementedDebugServer) RecoverFailPoint(ctx context.Context, req *RecoverFailPointRequest) (*RecoverFailPointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverFailPoint not implemented")
}
func (*UnimplementedDebugServer) ListFailPoints(ctx context.Context, req *ListFailPointsRequest) (*ListFailPointsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFailPoints not implemented")
}
func (*UnimplementedDebugServer) GetMetrics(ctx context.Context, req *GetMetricsRequest) (*GetMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetrics not implemented")
}
func (*UnimplementedDebugServer) CheckRegionConsistency(ctx context.Context, req *RegionConsistencyCheckRequest) (*RegionConsistencyCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckRegionConsistency not implemented")
}
func (*UnimplementedDebugServer) ModifyTikvConfig(ctx context.Context, req *ModifyTikvConfigRequest) (*ModifyTikvConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyTikvConfig not implemented")
}
func (*UnimplementedDebugServer) GetRegionProperties(ctx context.Context, req *GetRegionPropertiesRequest) (*GetRegionPropertiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRegionProperties not implemented")
}
func (*UnimplementedDebugServer) GetStoreInfo(ctx context.Context, req *GetStoreInfoRequest) (*GetStoreInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStoreInfo not implemented")
}
func (*UnimplementedDebugServer) GetClusterInfo(ctx context.Context, req *GetClusterInfoRequest) (*GetClusterInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClusterInfo not implemented")
}
func (*UnimplementedDebugServer) GetAllRegionsInStore(ctx context.Context, req *GetAllRegionsInStoreRequest) (*GetAllRegionsInStoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllRegionsInStore not implemented")
}
func (*UnimplementedDebugServer) ResetToVersion(ctx context.Context, req *ResetToVersionRequest) (*ResetToVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetToVersion not implemented")
}
func (*UnimplementedDebugServer) GetRangeProperties(ctx context.Context, req *GetRangePropertiesRequest) (*GetRangePropertiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRangeProperties not implemented")
}
func (*UnimplementedDebugServer) FlashbackToVersion(ctx context.Context, req *FlashbackToVersionRequest) (*FlashbackToVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlashbackToVersion not implemented")
}
func (*UnimplementedDebugServer) GetRegionReadProgress(ctx context.Context, req *GetRegionReadProgressRequest) (*GetRegionReadProgressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRegionReadProgress not implemented")
}

func RegisterDebugServer(s *grpc.Server, srv DebugServer) {
	s.RegisterService(&_Debug_serviceDesc, srv)
}

func _Debug_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/Get",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).Get(ctx, req.(*GetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_RaftLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RaftLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).RaftLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/RaftLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).RaftLog(ctx, req.(*RaftLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_RegionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegionInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).RegionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/RegionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).RegionInfo(ctx, req.(*RegionInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_RegionSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegionSizeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).RegionSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/RegionSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).RegionSize(ctx, req.(*RegionSizeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_ScanMvcc_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ScanMvccRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(DebugServer).ScanMvcc(m, &debugScanMvccServer{stream})
}

type Debug_ScanMvccServer interface {
	Send(*ScanMvccResponse) error
	grpc.ServerStream
}

type debugScanMvccServer struct {
	grpc.ServerStream
}

func (x *debugScanMvccServer) Send(m *ScanMvccResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Debug_Compact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).Compact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/Compact",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).Compact(ctx, req.(*CompactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_InjectFailPoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InjectFailPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).InjectFailPoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/InjectFailPoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).InjectFailPoint(ctx, req.(*InjectFailPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_RecoverFailPoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverFailPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).RecoverFailPoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/RecoverFailPoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).RecoverFailPoint(ctx, req.(*RecoverFailPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_ListFailPoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFailPointsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).ListFailPoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/ListFailPoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).ListFailPoints(ctx, req.(*ListFailPointsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_GetMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).GetMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/GetMetrics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).GetMetrics(ctx, req.(*GetMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_CheckRegionConsistency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegionConsistencyCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).CheckRegionConsistency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/CheckRegionConsistency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).CheckRegionConsistency(ctx, req.(*RegionConsistencyCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_ModifyTikvConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyTikvConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).ModifyTikvConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/ModifyTikvConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).ModifyTikvConfig(ctx, req.(*ModifyTikvConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_GetRegionProperties_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRegionPropertiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).GetRegionProperties(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/GetRegionProperties",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).GetRegionProperties(ctx, req.(*GetRegionPropertiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_GetStoreInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStoreInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).GetStoreInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/GetStoreInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).GetStoreInfo(ctx, req.(*GetStoreInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_GetClusterInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClusterInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).GetClusterInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/GetClusterInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).GetClusterInfo(ctx, req.(*GetClusterInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_GetAllRegionsInStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllRegionsInStoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).GetAllRegionsInStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/GetAllRegionsInStore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).GetAllRegionsInStore(ctx, req.(*GetAllRegionsInStoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_ResetToVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetToVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).ResetToVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/ResetToVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).ResetToVersion(ctx, req.(*ResetToVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_GetRangeProperties_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRangePropertiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).GetRangeProperties(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/GetRangeProperties",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).GetRangeProperties(ctx, req.(*GetRangePropertiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_FlashbackToVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlashbackToVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).FlashbackToVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/FlashbackToVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).FlashbackToVersion(ctx, req.(*FlashbackToVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Debug_GetRegionReadProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRegionReadProgressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DebugServer).GetRegionReadProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/debugpb.Debug/GetRegionReadProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DebugServer).GetRegionReadProgress(ctx, req.(*GetRegionReadProgressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Debug_serviceDesc = grpc.ServiceDesc{
	ServiceName: "debugpb.Debug",
	HandlerType: (*DebugServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Get",
			Handler:    _Debug_Get_Handler,
		},
		{
			MethodName: "RaftLog",
			Handler:    _Debug_RaftLog_Handler,
		},
		{
			MethodName: "RegionInfo",
			Handler:    _Debug_RegionInfo_Handler,
		},
		{
			MethodName: "RegionSize",
			Handler:    _Debug_RegionSize_Handler,
		},
		{
			MethodName: "Compact",
			Handler:    _Debug_Compact_Handler,
		},
		{
			MethodName: "InjectFailPoint",
			Handler:    _Debug_InjectFailPoint_Handler,
		},
		{
			MethodName: "RecoverFailPoint",
			Handler:    _Debug_RecoverFailPoint_Handler,
		},
		{
			MethodName: "ListFailPoints",
			Handler:    _Debug_ListFailPoints_Handler,
		},
		{
			MethodName: "GetMetrics",
			Handler:    _Debug_GetMetrics_Handler,
		},
		{
			MethodName: "CheckRegionConsistency",
			Handler:    _Debug_CheckRegionConsistency_Handler,
		},
		{
			MethodName: "ModifyTikvConfig",
			Handler:    _Debug_ModifyTikvConfig_Handler,
		},
		{
			MethodName: "GetRegionProperties",
			Handler:    _Debug_GetRegionProperties_Handler,
		},
		{
			MethodName: "GetStoreInfo",
			Handler:    _Debug_GetStoreInfo_Handler,
		},
		{
			MethodName: "GetClusterInfo",
			Handler:    _Debug_GetClusterInfo_Handler,
		},
		{
			MethodName: "GetAllRegionsInStore",
			Handler:    _Debug_GetAllRegionsInStore_Handler,
		},
		{
			MethodName: "ResetToVersion",
			Handler:    _Debug_ResetToVersion_Handler,
		},
		{
			MethodName: "GetRangeProperties",
			Handler:    _Debug_GetRangeProperties_Handler,
		},
		{
			MethodName: "FlashbackToVersion",
			Handler:    _Debug_FlashbackToVersion_Handler,
		},
		{
			MethodName: "GetRegionReadProgress",
			Handler:    _Debug_GetRegionReadProgress_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ScanMvcc",
			Handler:       _Debug_ScanMvcc_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "debugpb.proto",
}

func (m *GetRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0x12
	}
	if m.Db != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.Db))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RaftLogRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftLogRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftLogRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LogIndex != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.LogIndex))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RaftLogResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftLogResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftLogResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Entry != nil {
		{
			size, err := m.Entry.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDebugpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RegionInfoRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionInfoRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionInfoRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionInfoResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionInfoResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionInfoResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionLocalState != nil {
		{
			size, err := m.RegionLocalState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDebugpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RaftApplyState != nil {
		{
			size, err := m.RaftApplyState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDebugpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.RaftLocalState != nil {
		{
			size, err := m.RaftLocalState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDebugpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RegionSizeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionSizeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionSizeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Cfs) > 0 {
		for iNdEx := len(m.Cfs) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Cfs[iNdEx])
			copy(dAtA[i:], m.Cfs[iNdEx])
			i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Cfs[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.RegionId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionSizeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionSizeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionSizeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Entries) > 0 {
		for iNdEx := len(m.Entries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Entries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDebugpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *RegionSizeResponse_Entry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionSizeResponse_Entry) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionSizeResponse_Entry) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Size_ != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.Size_))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ScanMvccRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanMvccRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ScanMvccRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Limit != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x18
	}
	if len(m.ToKey) > 0 {
		i -= len(m.ToKey)
		copy(dAtA[i:], m.ToKey)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.ToKey)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.FromKey) > 0 {
		i -= len(m.FromKey)
		copy(dAtA[i:], m.FromKey)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.FromKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ScanMvccResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanMvccResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ScanMvccResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Info != nil {
		{
			size, err := m.Info.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDebugpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CompactRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CompactRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.BottommostLevelCompaction != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.BottommostLevelCompaction))
		i--
		dAtA[i] = 0x30
	}
	if m.Threads != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.Threads))
		i--
		dAtA[i] = 0x28
	}
	if len(m.ToKey) > 0 {
		i -= len(m.ToKey)
		copy(dAtA[i:], m.ToKey)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.ToKey)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.FromKey) > 0 {
		i -= len(m.FromKey)
		copy(dAtA[i:], m.FromKey)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.FromKey)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0x12
	}
	if m.Db != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.Db))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CompactResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CompactResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *InjectFailPointRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InjectFailPointRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *InjectFailPointRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Actions) > 0 {
		i -= len(m.Actions)
		copy(dAtA[i:], m.Actions)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Actions)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *InjectFailPointResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InjectFailPointResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *InjectFailPointResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *RecoverFailPointRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecoverFailPointRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RecoverFailPointRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RecoverFailPointResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecoverFailPointResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RecoverFailPointResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ListFailPointsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListFailPointsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListFailPointsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ListFailPointsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListFailPointsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListFailPointsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Entries) > 0 {
		for iNdEx := len(m.Entries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Entries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDebugpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ListFailPointsResponse_Entry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListFailPointsResponse_Entry) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListFailPointsResponse_Entry) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Actions) > 0 {
		i -= len(m.Actions)
		copy(dAtA[i:], m.Actions)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Actions)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetMetricsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMetricsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetMetricsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.All {
		i--
		if m.All {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetMetricsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMetricsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetMetricsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StoreId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Jemalloc) > 0 {
		i -= len(m.Jemalloc)
		copy(dAtA[i:], m.Jemalloc)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Jemalloc)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.RocksdbRaft) > 0 {
		i -= len(m.RocksdbRaft)
		copy(dAtA[i:], m.RocksdbRaft)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.RocksdbRaft)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.RocksdbKv) > 0 {
		i -= len(m.RocksdbKv)
		copy(dAtA[i:], m.RocksdbKv)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.RocksdbKv)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Prometheus) > 0 {
		i -= len(m.Prometheus)
		copy(dAtA[i:], m.Prometheus)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Prometheus)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RegionConsistencyCheckRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionConsistencyCheckRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionConsistencyCheckRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionConsistencyCheckResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionConsistencyCheckResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionConsistencyCheckResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ModifyTikvConfigRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyTikvConfigRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ModifyTikvConfigRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ConfigValue) > 0 {
		i -= len(m.ConfigValue)
		copy(dAtA[i:], m.ConfigValue)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.ConfigValue)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ConfigName) > 0 {
		i -= len(m.ConfigName)
		copy(dAtA[i:], m.ConfigName)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.ConfigName)))
		i--
		dAtA[i] = 0x12
	}
	if m.Module != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.Module))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ModifyTikvConfigResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ModifyTikvConfigResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ModifyTikvConfigResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *Property) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Property) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Property) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetRegionPropertiesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRegionPropertiesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRegionPropertiesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetRegionPropertiesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRegionPropertiesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRegionPropertiesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Props) > 0 {
		for iNdEx := len(m.Props) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Props[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDebugpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetStoreInfoRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStoreInfoRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetStoreInfoRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetStoreInfoResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStoreInfoResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetStoreInfoResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ApiVersion != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.ApiVersion))
		i--
		dAtA[i] = 0x10
	}
	if m.StoreId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetClusterInfoRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetClusterInfoRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetClusterInfoRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetClusterInfoResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetClusterInfoResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetClusterInfoResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ClusterId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.ClusterId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetAllRegionsInStoreRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllRegionsInStoreRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAllRegionsInStoreRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetAllRegionsInStoreResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllRegionsInStoreResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAllRegionsInStoreResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Regions) > 0 {
		dAtA7 := make([]byte, len(m.Regions)*10)
		var j6 int
		for _, num := range m.Regions {
			for num >= 1<<7 {
				dAtA7[j6] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j6++
			}
			dAtA7[j6] = uint8(num)
			j6++
		}
		i -= j6
		copy(dAtA[i:], dAtA7[:j6])
		i = encodeVarintDebugpb(dAtA, i, uint64(j6))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ResetToVersionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResetToVersionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResetToVersionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Ts != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.Ts))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ResetToVersionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResetToVersionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResetToVersionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetRangePropertiesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRangePropertiesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRangePropertiesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetRangePropertiesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRangePropertiesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRangePropertiesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Properties) > 0 {
		for iNdEx := len(m.Properties) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Properties[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDebugpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetRangePropertiesResponse_RangeProperty) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRangePropertiesResponse_RangeProperty) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRangePropertiesResponse_RangeProperty) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FlashbackToVersionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlashbackToVersionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlashbackToVersionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CommitTs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.CommitTs))
		i--
		dAtA[i] = 0x38
	}
	if m.StartTs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x30
	}
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x22
	}
	if m.RegionId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x18
	}
	if m.Version != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x10
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDebugpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FlashbackToVersionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlashbackToVersionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlashbackToVersionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Error) > 0 {
		i -= len(m.Error)
		copy(dAtA[i:], m.Error)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Error)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetRegionReadProgressRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRegionReadProgressRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRegionReadProgressRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MinStartTs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.MinStartTs))
		i--
		dAtA[i] = 0x18
	}
	if m.LogLocks {
		i--
		if m.LogLocks {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetRegionReadProgressResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRegionReadProgressResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRegionReadProgressResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Discard {
		i--
		if m.Discard {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa0
	}
	if m.ReadStateApplyIndex != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.ReadStateApplyIndex))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x98
	}
	if m.ReadStateTs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.ReadStateTs))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x90
	}
	if m.NumTransactions != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.NumTransactions))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if m.NumLocks != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.NumLocks))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if len(m.Error) > 0 {
		i -= len(m.Error)
		copy(dAtA[i:], m.Error)
		i = encodeVarintDebugpb(dAtA, i, uint64(len(m.Error)))
		i--
		dAtA[i] = 0x7a
	}
	if m.ResolverStopped {
		i--
		if m.ResolverStopped {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x70
	}
	if m.ResolverExist {
		i--
		if m.ResolverExist {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x68
	}
	if m.ResolverTrackedIndex != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.ResolverTrackedIndex))
		i--
		dAtA[i] = 0x60
	}
	if m.ResolvedTs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.ResolvedTs))
		i--
		dAtA[i] = 0x58
	}
	if m.RegionReadProgressExist {
		i--
		if m.RegionReadProgressExist {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x50
	}
	if m.DurationToLastConsumeLeaderMs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.DurationToLastConsumeLeaderMs))
		i--
		dAtA[i] = 0x48
	}
	if m.DurationToLastUpdateSafeTsMs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.DurationToLastUpdateSafeTsMs))
		i--
		dAtA[i] = 0x40
	}
	if m.RegionReadProgressPaused {
		i--
		if m.RegionReadProgressPaused {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x38
	}
	if m.PendingBackTs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.PendingBackTs))
		i--
		dAtA[i] = 0x30
	}
	if m.PendingBackAppliedIndex != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.PendingBackAppliedIndex))
		i--
		dAtA[i] = 0x28
	}
	if m.PendingFrontTs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.PendingFrontTs))
		i--
		dAtA[i] = 0x20
	}
	if m.PendingFrontAppliedIndex != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.PendingFrontAppliedIndex))
		i--
		dAtA[i] = 0x18
	}
	if m.AppliedIndex != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.AppliedIndex))
		i--
		dAtA[i] = 0x10
	}
	if m.SafeTs != 0 {
		i = encodeVarintDebugpb(dAtA, i, uint64(m.SafeTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintDebugpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovDebugpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *GetRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Db != 0 {
		n += 1 + sovDebugpb(uint64(m.Db))
	}
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *GetResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *RaftLogRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovDebugpb(uint64(m.RegionId))
	}
	if m.LogIndex != 0 {
		n += 1 + sovDebugpb(uint64(m.LogIndex))
	}
	return n
}

func (m *RaftLogResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Entry != nil {
		l = m.Entry.Size()
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *RegionInfoRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovDebugpb(uint64(m.RegionId))
	}
	return n
}

func (m *RegionInfoResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RaftLocalState != nil {
		l = m.RaftLocalState.Size()
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.RaftApplyState != nil {
		l = m.RaftApplyState.Size()
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.RegionLocalState != nil {
		l = m.RegionLocalState.Size()
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *RegionSizeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovDebugpb(uint64(m.RegionId))
	}
	if len(m.Cfs) > 0 {
		for _, s := range m.Cfs {
			l = len(s)
			n += 1 + l + sovDebugpb(uint64(l))
		}
	}
	return n
}

func (m *RegionSizeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Entries) > 0 {
		for _, e := range m.Entries {
			l = e.Size()
			n += 1 + l + sovDebugpb(uint64(l))
		}
	}
	return n
}

func (m *RegionSizeResponse_Entry) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.Size_ != 0 {
		n += 1 + sovDebugpb(uint64(m.Size_))
	}
	return n
}

func (m *ScanMvccRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.FromKey)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.ToKey)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.Limit != 0 {
		n += 1 + sovDebugpb(uint64(m.Limit))
	}
	return n
}

func (m *ScanMvccResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *CompactRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Db != 0 {
		n += 1 + sovDebugpb(uint64(m.Db))
	}
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.FromKey)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.ToKey)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.Threads != 0 {
		n += 1 + sovDebugpb(uint64(m.Threads))
	}
	if m.BottommostLevelCompaction != 0 {
		n += 1 + sovDebugpb(uint64(m.BottommostLevelCompaction))
	}
	return n
}

func (m *CompactResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *InjectFailPointRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.Actions)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *InjectFailPointResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *RecoverFailPointRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *RecoverFailPointResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ListFailPointsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ListFailPointsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Entries) > 0 {
		for _, e := range m.Entries {
			l = e.Size()
			n += 1 + l + sovDebugpb(uint64(l))
		}
	}
	return n
}

func (m *ListFailPointsResponse_Entry) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.Actions)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *GetMetricsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.All {
		n += 2
	}
	return n
}

func (m *GetMetricsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Prometheus)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.RocksdbKv)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.RocksdbRaft)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.Jemalloc)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovDebugpb(uint64(m.StoreId))
	}
	return n
}

func (m *RegionConsistencyCheckRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovDebugpb(uint64(m.RegionId))
	}
	return n
}

func (m *RegionConsistencyCheckResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ModifyTikvConfigRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Module != 0 {
		n += 1 + sovDebugpb(uint64(m.Module))
	}
	l = len(m.ConfigName)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.ConfigValue)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *ModifyTikvConfigResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *Property) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *GetRegionPropertiesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovDebugpb(uint64(m.RegionId))
	}
	return n
}

func (m *GetRegionPropertiesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Props) > 0 {
		for _, e := range m.Props {
			l = e.Size()
			n += 1 + l + sovDebugpb(uint64(l))
		}
	}
	return n
}

func (m *GetStoreInfoRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetStoreInfoResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreId != 0 {
		n += 1 + sovDebugpb(uint64(m.StoreId))
	}
	if m.ApiVersion != 0 {
		n += 1 + sovDebugpb(uint64(m.ApiVersion))
	}
	return n
}

func (m *GetClusterInfoRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetClusterInfoResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovDebugpb(uint64(m.ClusterId))
	}
	return n
}

func (m *GetAllRegionsInStoreRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetAllRegionsInStoreResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Regions) > 0 {
		l = 0
		for _, e := range m.Regions {
			l += sovDebugpb(uint64(e))
		}
		n += 1 + sovDebugpb(uint64(l)) + l
	}
	return n
}

func (m *ResetToVersionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Ts != 0 {
		n += 1 + sovDebugpb(uint64(m.Ts))
	}
	return n
}

func (m *ResetToVersionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetRangePropertiesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *GetRangePropertiesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Properties) > 0 {
		for _, e := range m.Properties {
			l = e.Size()
			n += 1 + l + sovDebugpb(uint64(l))
		}
	}
	return n
}

func (m *GetRangePropertiesResponse_RangeProperty) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *FlashbackToVersionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.Version != 0 {
		n += 1 + sovDebugpb(uint64(m.Version))
	}
	if m.RegionId != 0 {
		n += 1 + sovDebugpb(uint64(m.RegionId))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.StartTs != 0 {
		n += 1 + sovDebugpb(uint64(m.StartTs))
	}
	if m.CommitTs != 0 {
		n += 1 + sovDebugpb(uint64(m.CommitTs))
	}
	return n
}

func (m *FlashbackToVersionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Error)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	return n
}

func (m *GetRegionReadProgressRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovDebugpb(uint64(m.RegionId))
	}
	if m.LogLocks {
		n += 2
	}
	if m.MinStartTs != 0 {
		n += 1 + sovDebugpb(uint64(m.MinStartTs))
	}
	return n
}

func (m *GetRegionReadProgressResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SafeTs != 0 {
		n += 1 + sovDebugpb(uint64(m.SafeTs))
	}
	if m.AppliedIndex != 0 {
		n += 1 + sovDebugpb(uint64(m.AppliedIndex))
	}
	if m.PendingFrontAppliedIndex != 0 {
		n += 1 + sovDebugpb(uint64(m.PendingFrontAppliedIndex))
	}
	if m.PendingFrontTs != 0 {
		n += 1 + sovDebugpb(uint64(m.PendingFrontTs))
	}
	if m.PendingBackAppliedIndex != 0 {
		n += 1 + sovDebugpb(uint64(m.PendingBackAppliedIndex))
	}
	if m.PendingBackTs != 0 {
		n += 1 + sovDebugpb(uint64(m.PendingBackTs))
	}
	if m.RegionReadProgressPaused {
		n += 2
	}
	if m.DurationToLastUpdateSafeTsMs != 0 {
		n += 1 + sovDebugpb(uint64(m.DurationToLastUpdateSafeTsMs))
	}
	if m.DurationToLastConsumeLeaderMs != 0 {
		n += 1 + sovDebugpb(uint64(m.DurationToLastConsumeLeaderMs))
	}
	if m.RegionReadProgressExist {
		n += 2
	}
	if m.ResolvedTs != 0 {
		n += 1 + sovDebugpb(uint64(m.ResolvedTs))
	}
	if m.ResolverTrackedIndex != 0 {
		n += 1 + sovDebugpb(uint64(m.ResolverTrackedIndex))
	}
	if m.ResolverExist {
		n += 2
	}
	if m.ResolverStopped {
		n += 2
	}
	l = len(m.Error)
	if l > 0 {
		n += 1 + l + sovDebugpb(uint64(l))
	}
	if m.NumLocks != 0 {
		n += 2 + sovDebugpb(uint64(m.NumLocks))
	}
	if m.NumTransactions != 0 {
		n += 2 + sovDebugpb(uint64(m.NumTransactions))
	}
	if m.ReadStateTs != 0 {
		n += 2 + sovDebugpb(uint64(m.ReadStateTs))
	}
	if m.ReadStateApplyIndex != 0 {
		n += 2 + sovDebugpb(uint64(m.ReadStateApplyIndex))
	}
	if m.Discard {
		n += 3
	}
	return n
}

func sovDebugpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDebugpb(x uint64) (n int) {
	return sovDebugpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Db", wireType)
			}
			m.Db = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Db |= DB(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftLogRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftLogRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftLogRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogIndex", wireType)
			}
			m.LogIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LogIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftLogResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftLogResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftLogResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entry", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Entry == nil {
				m.Entry = &eraftpb.Entry{}
			}
			if err := m.Entry.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionInfoRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionInfoRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionInfoRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionInfoResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionInfoResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionInfoResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RaftLocalState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RaftLocalState == nil {
				m.RaftLocalState = &raft_serverpb.RaftLocalState{}
			}
			if err := m.RaftLocalState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RaftApplyState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RaftApplyState == nil {
				m.RaftApplyState = &raft_serverpb.RaftApplyState{}
			}
			if err := m.RaftApplyState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionLocalState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionLocalState == nil {
				m.RegionLocalState = &raft_serverpb.RegionLocalState{}
			}
			if err := m.RegionLocalState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionSizeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionSizeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionSizeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cfs", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cfs = append(m.Cfs, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionSizeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionSizeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionSizeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Entries = append(m.Entries, &RegionSizeResponse_Entry{})
			if err := m.Entries[len(m.Entries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionSizeResponse_Entry) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Entry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Entry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanMvccRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScanMvccRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScanMvccRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromKey = append(m.FromKey[:0], dAtA[iNdEx:postIndex]...)
			if m.FromKey == nil {
				m.FromKey = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ToKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ToKey = append(m.ToKey[:0], dAtA[iNdEx:postIndex]...)
			if m.ToKey == nil {
				m.ToKey = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanMvccResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScanMvccResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScanMvccResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &kvrpcpb.MvccInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Db", wireType)
			}
			m.Db = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Db |= DB(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FromKey = append(m.FromKey[:0], dAtA[iNdEx:postIndex]...)
			if m.FromKey == nil {
				m.FromKey = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ToKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ToKey = append(m.ToKey[:0], dAtA[iNdEx:postIndex]...)
			if m.ToKey == nil {
				m.ToKey = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Threads", wireType)
			}
			m.Threads = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Threads |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BottommostLevelCompaction", wireType)
			}
			m.BottommostLevelCompaction = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BottommostLevelCompaction |= BottommostLevelCompaction(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *InjectFailPointRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: InjectFailPointRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: InjectFailPointRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Actions", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Actions = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *InjectFailPointResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: InjectFailPointResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: InjectFailPointResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecoverFailPointRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RecoverFailPointRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RecoverFailPointRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecoverFailPointResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RecoverFailPointResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RecoverFailPointResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListFailPointsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListFailPointsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListFailPointsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListFailPointsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListFailPointsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListFailPointsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Entries = append(m.Entries, &ListFailPointsResponse_Entry{})
			if err := m.Entries[len(m.Entries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListFailPointsResponse_Entry) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Entry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Entry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Actions", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Actions = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMetricsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMetricsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMetricsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field All", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.All = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMetricsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMetricsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMetricsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Prometheus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Prometheus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RocksdbKv", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RocksdbKv = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RocksdbRaft", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RocksdbRaft = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Jemalloc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Jemalloc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionConsistencyCheckRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionConsistencyCheckRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionConsistencyCheckRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionConsistencyCheckResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionConsistencyCheckResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionConsistencyCheckResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyTikvConfigRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ModifyTikvConfigRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ModifyTikvConfigRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Module", wireType)
			}
			m.Module = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Module |= MODULE(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConfigName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConfigName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConfigValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConfigValue = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ModifyTikvConfigResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ModifyTikvConfigResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ModifyTikvConfigResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Property) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Property: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Property: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRegionPropertiesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRegionPropertiesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRegionPropertiesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRegionPropertiesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRegionPropertiesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRegionPropertiesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Props", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Props = append(m.Props, &Property{})
			if err := m.Props[len(m.Props)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStoreInfoRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetStoreInfoRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetStoreInfoRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStoreInfoResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetStoreInfoResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetStoreInfoResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiVersion", wireType)
			}
			m.ApiVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApiVersion |= kvrpcpb.APIVersion(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetClusterInfoRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetClusterInfoRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetClusterInfoRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetClusterInfoResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetClusterInfoResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetClusterInfoResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllRegionsInStoreRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllRegionsInStoreRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllRegionsInStoreRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllRegionsInStoreResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAllRegionsInStoreResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAllRegionsInStoreResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowDebugpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Regions = append(m.Regions, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowDebugpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthDebugpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthDebugpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Regions) == 0 {
					m.Regions = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowDebugpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Regions = append(m.Regions, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResetToVersionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResetToVersionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResetToVersionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResetToVersionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResetToVersionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResetToVersionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRangePropertiesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRangePropertiesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRangePropertiesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRangePropertiesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRangePropertiesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRangePropertiesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Properties", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Properties = append(m.Properties, &GetRangePropertiesResponse_RangeProperty{})
			if err := m.Properties[len(m.Properties)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRangePropertiesResponse_RangeProperty) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RangeProperty: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RangeProperty: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlashbackToVersionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlashbackToVersionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlashbackToVersionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitTs", wireType)
			}
			m.CommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlashbackToVersionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlashbackToVersionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlashbackToVersionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Error = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRegionReadProgressRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRegionReadProgressRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRegionReadProgressRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogLocks", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.LogLocks = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinStartTs", wireType)
			}
			m.MinStartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinStartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRegionReadProgressResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRegionReadProgressResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRegionReadProgressResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SafeTs", wireType)
			}
			m.SafeTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SafeTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppliedIndex", wireType)
			}
			m.AppliedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppliedIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PendingFrontAppliedIndex", wireType)
			}
			m.PendingFrontAppliedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PendingFrontAppliedIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PendingFrontTs", wireType)
			}
			m.PendingFrontTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PendingFrontTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PendingBackAppliedIndex", wireType)
			}
			m.PendingBackAppliedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PendingBackAppliedIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PendingBackTs", wireType)
			}
			m.PendingBackTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PendingBackTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionReadProgressPaused", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RegionReadProgressPaused = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationToLastUpdateSafeTsMs", wireType)
			}
			m.DurationToLastUpdateSafeTsMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DurationToLastUpdateSafeTsMs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationToLastConsumeLeaderMs", wireType)
			}
			m.DurationToLastConsumeLeaderMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DurationToLastConsumeLeaderMs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionReadProgressExist", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RegionReadProgressExist = bool(v != 0)
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolvedTs", wireType)
			}
			m.ResolvedTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResolvedTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolverTrackedIndex", wireType)
			}
			m.ResolverTrackedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResolverTrackedIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolverExist", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ResolverExist = bool(v != 0)
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolverStopped", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ResolverStopped = bool(v != 0)
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDebugpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDebugpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Error = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NumLocks", wireType)
			}
			m.NumLocks = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NumLocks |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NumTransactions", wireType)
			}
			m.NumTransactions = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NumTransactions |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadStateTs", wireType)
			}
			m.ReadStateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReadStateTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 19:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadStateApplyIndex", wireType)
			}
			m.ReadStateApplyIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReadStateApplyIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 20:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Discard", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Discard = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipDebugpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDebugpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDebugpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDebugpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDebugpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDebugpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDebugpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDebugpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDebugpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDebugpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDebugpb = fmt.Errorf("proto: unexpected end of group")
)
