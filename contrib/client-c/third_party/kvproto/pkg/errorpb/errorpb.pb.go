// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: errorpb.proto

package errorpb

import (
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// NotLeader is the error variant that tells a request be handle by raft leader
// is sent to raft follower or learner.
type NotLeader struct {
	// The requested region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	// Region leader of the requested region
	Leader *metapb.Peer `protobuf:"bytes,2,opt,name=leader,proto3" json:"leader,omitempty"`
}

func (m *NotLeader) Reset()         { *m = NotLeader{} }
func (m *NotLeader) String() string { return proto.CompactTextString(m) }
func (*NotLeader) ProtoMessage()    {}
func (*NotLeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{0}
}
func (m *NotLeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NotLeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NotLeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *NotLeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotLeader.Merge(m, src)
}
func (m *NotLeader) XXX_Size() int {
	return m.Size()
}
func (m *NotLeader) XXX_DiscardUnknown() {
	xxx_messageInfo_NotLeader.DiscardUnknown(m)
}

var xxx_messageInfo_NotLeader proto.InternalMessageInfo

func (m *NotLeader) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *NotLeader) GetLeader() *metapb.Peer {
	if m != nil {
		return m.Leader
	}
	return nil
}

// IsWitness is the error variant that tells a request be handle by witness
// which should be forbidden and retry.
type IsWitness struct {
	// The requested region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *IsWitness) Reset()         { *m = IsWitness{} }
func (m *IsWitness) String() string { return proto.CompactTextString(m) }
func (*IsWitness) ProtoMessage()    {}
func (*IsWitness) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{1}
}
func (m *IsWitness) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IsWitness) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IsWitness.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IsWitness) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsWitness.Merge(m, src)
}
func (m *IsWitness) XXX_Size() int {
	return m.Size()
}
func (m *IsWitness) XXX_DiscardUnknown() {
	xxx_messageInfo_IsWitness.DiscardUnknown(m)
}

var xxx_messageInfo_IsWitness proto.InternalMessageInfo

func (m *IsWitness) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

// BucketVersionNotMatch is the error variant that tells the request buckets version is not match.
// client should update the buckets version and retry.
type BucketVersionNotMatch struct {
	Version uint64   `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Keys    [][]byte `protobuf:"bytes,2,rep,name=keys,proto3" json:"keys,omitempty"`
}

func (m *BucketVersionNotMatch) Reset()         { *m = BucketVersionNotMatch{} }
func (m *BucketVersionNotMatch) String() string { return proto.CompactTextString(m) }
func (*BucketVersionNotMatch) ProtoMessage()    {}
func (*BucketVersionNotMatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{2}
}
func (m *BucketVersionNotMatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BucketVersionNotMatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BucketVersionNotMatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BucketVersionNotMatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BucketVersionNotMatch.Merge(m, src)
}
func (m *BucketVersionNotMatch) XXX_Size() int {
	return m.Size()
}
func (m *BucketVersionNotMatch) XXX_DiscardUnknown() {
	xxx_messageInfo_BucketVersionNotMatch.DiscardUnknown(m)
}

var xxx_messageInfo_BucketVersionNotMatch proto.InternalMessageInfo

func (m *BucketVersionNotMatch) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *BucketVersionNotMatch) GetKeys() [][]byte {
	if m != nil {
		return m.Keys
	}
	return nil
}

type DiskFull struct {
	// The requested store ID
	StoreId []uint64 `protobuf:"varint,1,rep,packed,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// The detailed info
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (m *DiskFull) Reset()         { *m = DiskFull{} }
func (m *DiskFull) String() string { return proto.CompactTextString(m) }
func (*DiskFull) ProtoMessage()    {}
func (*DiskFull) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{3}
}
func (m *DiskFull) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DiskFull) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DiskFull.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DiskFull) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiskFull.Merge(m, src)
}
func (m *DiskFull) XXX_Size() int {
	return m.Size()
}
func (m *DiskFull) XXX_DiscardUnknown() {
	xxx_messageInfo_DiskFull.DiscardUnknown(m)
}

var xxx_messageInfo_DiskFull proto.InternalMessageInfo

func (m *DiskFull) GetStoreId() []uint64 {
	if m != nil {
		return m.StoreId
	}
	return nil
}

func (m *DiskFull) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

// StoreNotMatch is the error variant that tells the request is sent to wrong store.
// (i.e. inconsistency of the store ID that request shows and the real store ID of this server.)
type StoreNotMatch struct {
	// Store id in request
	RequestStoreId uint64 `protobuf:"varint,1,opt,name=request_store_id,json=requestStoreId,proto3" json:"request_store_id,omitempty"`
	// Actual store id
	ActualStoreId uint64 `protobuf:"varint,2,opt,name=actual_store_id,json=actualStoreId,proto3" json:"actual_store_id,omitempty"`
}

func (m *StoreNotMatch) Reset()         { *m = StoreNotMatch{} }
func (m *StoreNotMatch) String() string { return proto.CompactTextString(m) }
func (*StoreNotMatch) ProtoMessage()    {}
func (*StoreNotMatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{4}
}
func (m *StoreNotMatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreNotMatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreNotMatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreNotMatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreNotMatch.Merge(m, src)
}
func (m *StoreNotMatch) XXX_Size() int {
	return m.Size()
}
func (m *StoreNotMatch) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreNotMatch.DiscardUnknown(m)
}

var xxx_messageInfo_StoreNotMatch proto.InternalMessageInfo

func (m *StoreNotMatch) GetRequestStoreId() uint64 {
	if m != nil {
		return m.RequestStoreId
	}
	return 0
}

func (m *StoreNotMatch) GetActualStoreId() uint64 {
	if m != nil {
		return m.ActualStoreId
	}
	return 0
}

// RegionNotFound is the error variant that tells there isn't any region in this TiKV
// matches the requested region ID.
type RegionNotFound struct {
	// The requested region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *RegionNotFound) Reset()         { *m = RegionNotFound{} }
func (m *RegionNotFound) String() string { return proto.CompactTextString(m) }
func (*RegionNotFound) ProtoMessage()    {}
func (*RegionNotFound) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{5}
}
func (m *RegionNotFound) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionNotFound) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionNotFound.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionNotFound) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionNotFound.Merge(m, src)
}
func (m *RegionNotFound) XXX_Size() int {
	return m.Size()
}
func (m *RegionNotFound) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionNotFound.DiscardUnknown(m)
}

var xxx_messageInfo_RegionNotFound proto.InternalMessageInfo

func (m *RegionNotFound) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

// RegionNotInitialized is the error variant that tells there isn't any initialized peer
// matchesthe request region ID.
type RegionNotInitialized struct {
	// The request region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *RegionNotInitialized) Reset()         { *m = RegionNotInitialized{} }
func (m *RegionNotInitialized) String() string { return proto.CompactTextString(m) }
func (*RegionNotInitialized) ProtoMessage()    {}
func (*RegionNotInitialized) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{6}
}
func (m *RegionNotInitialized) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionNotInitialized) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionNotInitialized.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionNotInitialized) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionNotInitialized.Merge(m, src)
}
func (m *RegionNotInitialized) XXX_Size() int {
	return m.Size()
}
func (m *RegionNotInitialized) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionNotInitialized.DiscardUnknown(m)
}

var xxx_messageInfo_RegionNotInitialized proto.InternalMessageInfo

func (m *RegionNotInitialized) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

// KeyNotInRegion is the error variant that tells the key the request requires isn't present in
// this region.
type KeyNotInRegion struct {
	// The requested key
	Key []byte `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// The requested region ID
	RegionId uint64 `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	// Start key of the requested region
	StartKey []byte `protobuf:"bytes,3,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	// Snd key of the requested region
	EndKey []byte `protobuf:"bytes,4,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
}

func (m *KeyNotInRegion) Reset()         { *m = KeyNotInRegion{} }
func (m *KeyNotInRegion) String() string { return proto.CompactTextString(m) }
func (*KeyNotInRegion) ProtoMessage()    {}
func (*KeyNotInRegion) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{7}
}
func (m *KeyNotInRegion) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeyNotInRegion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeyNotInRegion.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KeyNotInRegion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyNotInRegion.Merge(m, src)
}
func (m *KeyNotInRegion) XXX_Size() int {
	return m.Size()
}
func (m *KeyNotInRegion) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyNotInRegion.DiscardUnknown(m)
}

var xxx_messageInfo_KeyNotInRegion proto.InternalMessageInfo

func (m *KeyNotInRegion) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *KeyNotInRegion) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *KeyNotInRegion) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *KeyNotInRegion) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

// EpochNotMatch is the error variant that tells a region has been updated.
// (e.g. by splitting / merging, or raft Confchange.)
// Hence, a command is based on a stale version of a region.
type EpochNotMatch struct {
	// Available regions that may be siblings of the requested one.
	CurrentRegions []*metapb.Region `protobuf:"bytes,1,rep,name=current_regions,json=currentRegions,proto3" json:"current_regions,omitempty"`
}

func (m *EpochNotMatch) Reset()         { *m = EpochNotMatch{} }
func (m *EpochNotMatch) String() string { return proto.CompactTextString(m) }
func (*EpochNotMatch) ProtoMessage()    {}
func (*EpochNotMatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{8}
}
func (m *EpochNotMatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EpochNotMatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EpochNotMatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EpochNotMatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EpochNotMatch.Merge(m, src)
}
func (m *EpochNotMatch) XXX_Size() int {
	return m.Size()
}
func (m *EpochNotMatch) XXX_DiscardUnknown() {
	xxx_messageInfo_EpochNotMatch.DiscardUnknown(m)
}

var xxx_messageInfo_EpochNotMatch proto.InternalMessageInfo

func (m *EpochNotMatch) GetCurrentRegions() []*metapb.Region {
	if m != nil {
		return m.CurrentRegions
	}
	return nil
}

// ServerIsBusy is the error variant that tells the server is too busy to response.
type ServerIsBusy struct {
	Reason string `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
	// The suggested backoff time
	BackoffMs       uint64 `protobuf:"varint,2,opt,name=backoff_ms,json=backoffMs,proto3" json:"backoff_ms,omitempty"`
	EstimatedWaitMs uint32 `protobuf:"varint,3,opt,name=estimated_wait_ms,json=estimatedWaitMs,proto3" json:"estimated_wait_ms,omitempty"`
	// Current applied_index at the leader, may be used in replica read.
	AppliedIndex uint64 `protobuf:"varint,4,opt,name=applied_index,json=appliedIndex,proto3" json:"applied_index,omitempty"`
}

func (m *ServerIsBusy) Reset()         { *m = ServerIsBusy{} }
func (m *ServerIsBusy) String() string { return proto.CompactTextString(m) }
func (*ServerIsBusy) ProtoMessage()    {}
func (*ServerIsBusy) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{9}
}
func (m *ServerIsBusy) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServerIsBusy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ServerIsBusy.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ServerIsBusy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerIsBusy.Merge(m, src)
}
func (m *ServerIsBusy) XXX_Size() int {
	return m.Size()
}
func (m *ServerIsBusy) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerIsBusy.DiscardUnknown(m)
}

var xxx_messageInfo_ServerIsBusy proto.InternalMessageInfo

func (m *ServerIsBusy) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *ServerIsBusy) GetBackoffMs() uint64 {
	if m != nil {
		return m.BackoffMs
	}
	return 0
}

func (m *ServerIsBusy) GetEstimatedWaitMs() uint32 {
	if m != nil {
		return m.EstimatedWaitMs
	}
	return 0
}

func (m *ServerIsBusy) GetAppliedIndex() uint64 {
	if m != nil {
		return m.AppliedIndex
	}
	return 0
}

// StaleCommand is the error variant that tells the command is stale, that is,
// the current request term is lower than current raft term.
// This can be retried at most time.
type StaleCommand struct {
}

func (m *StaleCommand) Reset()         { *m = StaleCommand{} }
func (m *StaleCommand) String() string { return proto.CompactTextString(m) }
func (*StaleCommand) ProtoMessage()    {}
func (*StaleCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{10}
}
func (m *StaleCommand) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StaleCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StaleCommand.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StaleCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StaleCommand.Merge(m, src)
}
func (m *StaleCommand) XXX_Size() int {
	return m.Size()
}
func (m *StaleCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_StaleCommand.DiscardUnknown(m)
}

var xxx_messageInfo_StaleCommand proto.InternalMessageInfo

// RaftEntryTooLarge is the error variant that tells the request is too large to be serialized to a
// reasonable small raft entry.
// (i.e. greater than the configured value `raft_entry_max_size` in `raftstore`)
type RaftEntryTooLarge struct {
	// The requested region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	// Size of the raft entry
	EntrySize uint64 `protobuf:"varint,2,opt,name=entry_size,json=entrySize,proto3" json:"entry_size,omitempty"`
}

func (m *RaftEntryTooLarge) Reset()         { *m = RaftEntryTooLarge{} }
func (m *RaftEntryTooLarge) String() string { return proto.CompactTextString(m) }
func (*RaftEntryTooLarge) ProtoMessage()    {}
func (*RaftEntryTooLarge) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{11}
}
func (m *RaftEntryTooLarge) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftEntryTooLarge) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftEntryTooLarge.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftEntryTooLarge) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftEntryTooLarge.Merge(m, src)
}
func (m *RaftEntryTooLarge) XXX_Size() int {
	return m.Size()
}
func (m *RaftEntryTooLarge) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftEntryTooLarge.DiscardUnknown(m)
}

var xxx_messageInfo_RaftEntryTooLarge proto.InternalMessageInfo

func (m *RaftEntryTooLarge) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RaftEntryTooLarge) GetEntrySize() uint64 {
	if m != nil {
		return m.EntrySize
	}
	return 0
}

// MaxTimestampNotSynced is the error variant that tells the peer has just become a leader and
// updating the max timestamp in the concurrency manager from PD TSO is ongoing. In this case,
// the prewrite of an async commit transaction cannot succeed. The client can backoff and
// resend the request.
type MaxTimestampNotSynced struct {
}

func (m *MaxTimestampNotSynced) Reset()         { *m = MaxTimestampNotSynced{} }
func (m *MaxTimestampNotSynced) String() string { return proto.CompactTextString(m) }
func (*MaxTimestampNotSynced) ProtoMessage()    {}
func (*MaxTimestampNotSynced) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{12}
}
func (m *MaxTimestampNotSynced) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MaxTimestampNotSynced) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MaxTimestampNotSynced.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MaxTimestampNotSynced) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaxTimestampNotSynced.Merge(m, src)
}
func (m *MaxTimestampNotSynced) XXX_Size() int {
	return m.Size()
}
func (m *MaxTimestampNotSynced) XXX_DiscardUnknown() {
	xxx_messageInfo_MaxTimestampNotSynced.DiscardUnknown(m)
}

var xxx_messageInfo_MaxTimestampNotSynced proto.InternalMessageInfo

// ReadIndexNotReady is the error variant that tells the read index request is not ready, that is,
// the current region is in a status that not ready to serve the read index request. For example,
// region is in splitting or merging status.
// This can be retried at most time.
type ReadIndexNotReady struct {
	// The reason why the region is not ready to serve read index request
	Reason string `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
	// The requested region ID
	RegionId uint64 `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *ReadIndexNotReady) Reset()         { *m = ReadIndexNotReady{} }
func (m *ReadIndexNotReady) String() string { return proto.CompactTextString(m) }
func (*ReadIndexNotReady) ProtoMessage()    {}
func (*ReadIndexNotReady) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{13}
}
func (m *ReadIndexNotReady) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadIndexNotReady) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadIndexNotReady.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadIndexNotReady) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadIndexNotReady.Merge(m, src)
}
func (m *ReadIndexNotReady) XXX_Size() int {
	return m.Size()
}
func (m *ReadIndexNotReady) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadIndexNotReady.DiscardUnknown(m)
}

var xxx_messageInfo_ReadIndexNotReady proto.InternalMessageInfo

func (m *ReadIndexNotReady) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *ReadIndexNotReady) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

// ProposalInMergingMode is the error variant that tells the proposal is rejected because raft is
// in the merging mode. This may happen when BR/Lightning try to ingest SST.
// This can be retried at most time.
type ProposalInMergingMode struct {
	// The requested region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *ProposalInMergingMode) Reset()         { *m = ProposalInMergingMode{} }
func (m *ProposalInMergingMode) String() string { return proto.CompactTextString(m) }
func (*ProposalInMergingMode) ProtoMessage()    {}
func (*ProposalInMergingMode) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{14}
}
func (m *ProposalInMergingMode) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ProposalInMergingMode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ProposalInMergingMode.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ProposalInMergingMode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProposalInMergingMode.Merge(m, src)
}
func (m *ProposalInMergingMode) XXX_Size() int {
	return m.Size()
}
func (m *ProposalInMergingMode) XXX_DiscardUnknown() {
	xxx_messageInfo_ProposalInMergingMode.DiscardUnknown(m)
}

var xxx_messageInfo_ProposalInMergingMode proto.InternalMessageInfo

func (m *ProposalInMergingMode) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type DataIsNotReady struct {
	// The requested region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	PeerId   uint64 `protobuf:"varint,2,opt,name=peer_id,json=peerId,proto3" json:"peer_id,omitempty"`
	SafeTs   uint64 `protobuf:"varint,3,opt,name=safe_ts,json=safeTs,proto3" json:"safe_ts,omitempty"`
}

func (m *DataIsNotReady) Reset()         { *m = DataIsNotReady{} }
func (m *DataIsNotReady) String() string { return proto.CompactTextString(m) }
func (*DataIsNotReady) ProtoMessage()    {}
func (*DataIsNotReady) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{15}
}
func (m *DataIsNotReady) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DataIsNotReady) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DataIsNotReady.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DataIsNotReady) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataIsNotReady.Merge(m, src)
}
func (m *DataIsNotReady) XXX_Size() int {
	return m.Size()
}
func (m *DataIsNotReady) XXX_DiscardUnknown() {
	xxx_messageInfo_DataIsNotReady.DiscardUnknown(m)
}

var xxx_messageInfo_DataIsNotReady proto.InternalMessageInfo

func (m *DataIsNotReady) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *DataIsNotReady) GetPeerId() uint64 {
	if m != nil {
		return m.PeerId
	}
	return 0
}

func (m *DataIsNotReady) GetSafeTs() uint64 {
	if m != nil {
		return m.SafeTs
	}
	return 0
}

type RecoveryInProgress struct {
	// The requested region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *RecoveryInProgress) Reset()         { *m = RecoveryInProgress{} }
func (m *RecoveryInProgress) String() string { return proto.CompactTextString(m) }
func (*RecoveryInProgress) ProtoMessage()    {}
func (*RecoveryInProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{16}
}
func (m *RecoveryInProgress) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RecoveryInProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RecoveryInProgress.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RecoveryInProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoveryInProgress.Merge(m, src)
}
func (m *RecoveryInProgress) XXX_Size() int {
	return m.Size()
}
func (m *RecoveryInProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoveryInProgress.DiscardUnknown(m)
}

var xxx_messageInfo_RecoveryInProgress proto.InternalMessageInfo

func (m *RecoveryInProgress) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type FlashbackInProgress struct {
	// The requested region ID
	RegionId         uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	FlashbackStartTs uint64 `protobuf:"varint,2,opt,name=flashback_start_ts,json=flashbackStartTs,proto3" json:"flashback_start_ts,omitempty"`
}

func (m *FlashbackInProgress) Reset()         { *m = FlashbackInProgress{} }
func (m *FlashbackInProgress) String() string { return proto.CompactTextString(m) }
func (*FlashbackInProgress) ProtoMessage()    {}
func (*FlashbackInProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{17}
}
func (m *FlashbackInProgress) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlashbackInProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlashbackInProgress.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlashbackInProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlashbackInProgress.Merge(m, src)
}
func (m *FlashbackInProgress) XXX_Size() int {
	return m.Size()
}
func (m *FlashbackInProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_FlashbackInProgress.DiscardUnknown(m)
}

var xxx_messageInfo_FlashbackInProgress proto.InternalMessageInfo

func (m *FlashbackInProgress) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *FlashbackInProgress) GetFlashbackStartTs() uint64 {
	if m != nil {
		return m.FlashbackStartTs
	}
	return 0
}

type FlashbackNotPrepared struct {
	// The requested region ID
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *FlashbackNotPrepared) Reset()         { *m = FlashbackNotPrepared{} }
func (m *FlashbackNotPrepared) String() string { return proto.CompactTextString(m) }
func (*FlashbackNotPrepared) ProtoMessage()    {}
func (*FlashbackNotPrepared) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{18}
}
func (m *FlashbackNotPrepared) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlashbackNotPrepared) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlashbackNotPrepared.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlashbackNotPrepared) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlashbackNotPrepared.Merge(m, src)
}
func (m *FlashbackNotPrepared) XXX_Size() int {
	return m.Size()
}
func (m *FlashbackNotPrepared) XXX_DiscardUnknown() {
	xxx_messageInfo_FlashbackNotPrepared.DiscardUnknown(m)
}

var xxx_messageInfo_FlashbackNotPrepared proto.InternalMessageInfo

func (m *FlashbackNotPrepared) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

// MismatchPeerId is the error variant that tells the request is sent to wrong peer.
// Client receives this error should reload the region info and retry.
type MismatchPeerId struct {
	RequestPeerId uint64 `protobuf:"varint,1,opt,name=request_peer_id,json=requestPeerId,proto3" json:"request_peer_id,omitempty"`
	StorePeerId   uint64 `protobuf:"varint,2,opt,name=store_peer_id,json=storePeerId,proto3" json:"store_peer_id,omitempty"`
}

func (m *MismatchPeerId) Reset()         { *m = MismatchPeerId{} }
func (m *MismatchPeerId) String() string { return proto.CompactTextString(m) }
func (*MismatchPeerId) ProtoMessage()    {}
func (*MismatchPeerId) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{19}
}
func (m *MismatchPeerId) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MismatchPeerId) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MismatchPeerId.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MismatchPeerId) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MismatchPeerId.Merge(m, src)
}
func (m *MismatchPeerId) XXX_Size() int {
	return m.Size()
}
func (m *MismatchPeerId) XXX_DiscardUnknown() {
	xxx_messageInfo_MismatchPeerId.DiscardUnknown(m)
}

var xxx_messageInfo_MismatchPeerId proto.InternalMessageInfo

func (m *MismatchPeerId) GetRequestPeerId() uint64 {
	if m != nil {
		return m.RequestPeerId
	}
	return 0
}

func (m *MismatchPeerId) GetStorePeerId() uint64 {
	if m != nil {
		return m.StorePeerId
	}
	return 0
}

// UndeterminedResult is the error variant that tells the result is not determined yet.
// For example, the raft protocol timed out and the apply result is unknown.
type UndeterminedResult struct {
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (m *UndeterminedResult) Reset()         { *m = UndeterminedResult{} }
func (m *UndeterminedResult) String() string { return proto.CompactTextString(m) }
func (*UndeterminedResult) ProtoMessage()    {}
func (*UndeterminedResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{20}
}
func (m *UndeterminedResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UndeterminedResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UndeterminedResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UndeterminedResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UndeterminedResult.Merge(m, src)
}
func (m *UndeterminedResult) XXX_Size() int {
	return m.Size()
}
func (m *UndeterminedResult) XXX_DiscardUnknown() {
	xxx_messageInfo_UndeterminedResult.DiscardUnknown(m)
}

var xxx_messageInfo_UndeterminedResult proto.InternalMessageInfo

func (m *UndeterminedResult) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

// Error wraps all region errors, indicates an error encountered by a request.
type Error struct {
	// The error message
	Message               string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	NotLeader             *NotLeader             `protobuf:"bytes,2,opt,name=not_leader,json=notLeader,proto3" json:"not_leader,omitempty"`
	RegionNotFound        *RegionNotFound        `protobuf:"bytes,3,opt,name=region_not_found,json=regionNotFound,proto3" json:"region_not_found,omitempty"`
	KeyNotInRegion        *KeyNotInRegion        `protobuf:"bytes,4,opt,name=key_not_in_region,json=keyNotInRegion,proto3" json:"key_not_in_region,omitempty"`
	EpochNotMatch         *EpochNotMatch         `protobuf:"bytes,5,opt,name=epoch_not_match,json=epochNotMatch,proto3" json:"epoch_not_match,omitempty"`
	ServerIsBusy          *ServerIsBusy          `protobuf:"bytes,6,opt,name=server_is_busy,json=serverIsBusy,proto3" json:"server_is_busy,omitempty"`
	StaleCommand          *StaleCommand          `protobuf:"bytes,7,opt,name=stale_command,json=staleCommand,proto3" json:"stale_command,omitempty"`
	StoreNotMatch         *StoreNotMatch         `protobuf:"bytes,8,opt,name=store_not_match,json=storeNotMatch,proto3" json:"store_not_match,omitempty"`
	RaftEntryTooLarge     *RaftEntryTooLarge     `protobuf:"bytes,9,opt,name=raft_entry_too_large,json=raftEntryTooLarge,proto3" json:"raft_entry_too_large,omitempty"`
	MaxTimestampNotSynced *MaxTimestampNotSynced `protobuf:"bytes,10,opt,name=max_timestamp_not_synced,json=maxTimestampNotSynced,proto3" json:"max_timestamp_not_synced,omitempty"`
	ReadIndexNotReady     *ReadIndexNotReady     `protobuf:"bytes,11,opt,name=read_index_not_ready,json=readIndexNotReady,proto3" json:"read_index_not_ready,omitempty"`
	ProposalInMergingMode *ProposalInMergingMode `protobuf:"bytes,12,opt,name=proposal_in_merging_mode,json=proposalInMergingMode,proto3" json:"proposal_in_merging_mode,omitempty"`
	DataIsNotReady        *DataIsNotReady        `protobuf:"bytes,13,opt,name=data_is_not_ready,json=dataIsNotReady,proto3" json:"data_is_not_ready,omitempty"`
	RegionNotInitialized  *RegionNotInitialized  `protobuf:"bytes,14,opt,name=region_not_initialized,json=regionNotInitialized,proto3" json:"region_not_initialized,omitempty"`
	DiskFull              *DiskFull              `protobuf:"bytes,15,opt,name=disk_full,json=diskFull,proto3" json:"disk_full,omitempty"`
	// Online recovery is still in performing, reject writes to avoid potential issues
	RecoveryInProgress *RecoveryInProgress `protobuf:"bytes,16,opt,name=RecoveryInProgress,proto3" json:"RecoveryInProgress,omitempty"`
	// Flashback is still in performing, reject any read or write to avoid potential issues.
	// NOTICE: this error is non-retryable, the request should fail ASAP when it meets this error.
	FlashbackInProgress *FlashbackInProgress `protobuf:"bytes,17,opt,name=FlashbackInProgress,proto3" json:"FlashbackInProgress,omitempty"`
	// If the second phase flashback request is sent to a region that is not prepared for the flashback,
	// this error will be returned.
	// NOTICE: this error is non-retryable, the client should retry the first phase flashback request when it meets this error.
	FlashbackNotPrepared *FlashbackNotPrepared `protobuf:"bytes,18,opt,name=FlashbackNotPrepared,proto3" json:"FlashbackNotPrepared,omitempty"`
	// IsWitness is the error variant that tells a request be handle by witness
	// which should be forbidden and retry.
	IsWitness      *IsWitness      `protobuf:"bytes,19,opt,name=is_witness,json=isWitness,proto3" json:"is_witness,omitempty"`
	MismatchPeerId *MismatchPeerId `protobuf:"bytes,20,opt,name=mismatch_peer_id,json=mismatchPeerId,proto3" json:"mismatch_peer_id,omitempty"`
	// BucketVersionNotMatch is the error variant that tells the request buckets version is not match.
	BucketVersionNotMatch *BucketVersionNotMatch `protobuf:"bytes,21,opt,name=bucket_version_not_match,json=bucketVersionNotMatch,proto3" json:"bucket_version_not_match,omitempty"`
	// UndeterminedResult is the error variant that tells the result is not determined yet.
	UndeterminedResult *UndeterminedResult `protobuf:"bytes,22,opt,name=undetermined_result,json=undeterminedResult,proto3" json:"undetermined_result,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_390aa86757fd1154, []int{21}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *Error) GetNotLeader() *NotLeader {
	if m != nil {
		return m.NotLeader
	}
	return nil
}

func (m *Error) GetRegionNotFound() *RegionNotFound {
	if m != nil {
		return m.RegionNotFound
	}
	return nil
}

func (m *Error) GetKeyNotInRegion() *KeyNotInRegion {
	if m != nil {
		return m.KeyNotInRegion
	}
	return nil
}

func (m *Error) GetEpochNotMatch() *EpochNotMatch {
	if m != nil {
		return m.EpochNotMatch
	}
	return nil
}

func (m *Error) GetServerIsBusy() *ServerIsBusy {
	if m != nil {
		return m.ServerIsBusy
	}
	return nil
}

func (m *Error) GetStaleCommand() *StaleCommand {
	if m != nil {
		return m.StaleCommand
	}
	return nil
}

func (m *Error) GetStoreNotMatch() *StoreNotMatch {
	if m != nil {
		return m.StoreNotMatch
	}
	return nil
}

func (m *Error) GetRaftEntryTooLarge() *RaftEntryTooLarge {
	if m != nil {
		return m.RaftEntryTooLarge
	}
	return nil
}

func (m *Error) GetMaxTimestampNotSynced() *MaxTimestampNotSynced {
	if m != nil {
		return m.MaxTimestampNotSynced
	}
	return nil
}

func (m *Error) GetReadIndexNotReady() *ReadIndexNotReady {
	if m != nil {
		return m.ReadIndexNotReady
	}
	return nil
}

func (m *Error) GetProposalInMergingMode() *ProposalInMergingMode {
	if m != nil {
		return m.ProposalInMergingMode
	}
	return nil
}

func (m *Error) GetDataIsNotReady() *DataIsNotReady {
	if m != nil {
		return m.DataIsNotReady
	}
	return nil
}

func (m *Error) GetRegionNotInitialized() *RegionNotInitialized {
	if m != nil {
		return m.RegionNotInitialized
	}
	return nil
}

func (m *Error) GetDiskFull() *DiskFull {
	if m != nil {
		return m.DiskFull
	}
	return nil
}

func (m *Error) GetRecoveryInProgress() *RecoveryInProgress {
	if m != nil {
		return m.RecoveryInProgress
	}
	return nil
}

func (m *Error) GetFlashbackInProgress() *FlashbackInProgress {
	if m != nil {
		return m.FlashbackInProgress
	}
	return nil
}

func (m *Error) GetFlashbackNotPrepared() *FlashbackNotPrepared {
	if m != nil {
		return m.FlashbackNotPrepared
	}
	return nil
}

func (m *Error) GetIsWitness() *IsWitness {
	if m != nil {
		return m.IsWitness
	}
	return nil
}

func (m *Error) GetMismatchPeerId() *MismatchPeerId {
	if m != nil {
		return m.MismatchPeerId
	}
	return nil
}

func (m *Error) GetBucketVersionNotMatch() *BucketVersionNotMatch {
	if m != nil {
		return m.BucketVersionNotMatch
	}
	return nil
}

func (m *Error) GetUndeterminedResult() *UndeterminedResult {
	if m != nil {
		return m.UndeterminedResult
	}
	return nil
}

func init() {
	proto.RegisterType((*NotLeader)(nil), "errorpb.NotLeader")
	proto.RegisterType((*IsWitness)(nil), "errorpb.IsWitness")
	proto.RegisterType((*BucketVersionNotMatch)(nil), "errorpb.BucketVersionNotMatch")
	proto.RegisterType((*DiskFull)(nil), "errorpb.DiskFull")
	proto.RegisterType((*StoreNotMatch)(nil), "errorpb.StoreNotMatch")
	proto.RegisterType((*RegionNotFound)(nil), "errorpb.RegionNotFound")
	proto.RegisterType((*RegionNotInitialized)(nil), "errorpb.RegionNotInitialized")
	proto.RegisterType((*KeyNotInRegion)(nil), "errorpb.KeyNotInRegion")
	proto.RegisterType((*EpochNotMatch)(nil), "errorpb.EpochNotMatch")
	proto.RegisterType((*ServerIsBusy)(nil), "errorpb.ServerIsBusy")
	proto.RegisterType((*StaleCommand)(nil), "errorpb.StaleCommand")
	proto.RegisterType((*RaftEntryTooLarge)(nil), "errorpb.RaftEntryTooLarge")
	proto.RegisterType((*MaxTimestampNotSynced)(nil), "errorpb.MaxTimestampNotSynced")
	proto.RegisterType((*ReadIndexNotReady)(nil), "errorpb.ReadIndexNotReady")
	proto.RegisterType((*ProposalInMergingMode)(nil), "errorpb.ProposalInMergingMode")
	proto.RegisterType((*DataIsNotReady)(nil), "errorpb.DataIsNotReady")
	proto.RegisterType((*RecoveryInProgress)(nil), "errorpb.RecoveryInProgress")
	proto.RegisterType((*FlashbackInProgress)(nil), "errorpb.FlashbackInProgress")
	proto.RegisterType((*FlashbackNotPrepared)(nil), "errorpb.FlashbackNotPrepared")
	proto.RegisterType((*MismatchPeerId)(nil), "errorpb.MismatchPeerId")
	proto.RegisterType((*UndeterminedResult)(nil), "errorpb.UndeterminedResult")
	proto.RegisterType((*Error)(nil), "errorpb.Error")
}

func init() { proto.RegisterFile("errorpb.proto", fileDescriptor_390aa86757fd1154) }

var fileDescriptor_390aa86757fd1154 = []byte{
	// 1284 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x97, 0xcf, 0x6e, 0xdb, 0x46,
	0x13, 0xc0, 0x2d, 0xdb, 0x91, 0xa5, 0xb1, 0xfe, 0x58, 0x8c, 0x65, 0xf3, 0x4b, 0xbe, 0x08, 0x01,
	0x5b, 0x04, 0x46, 0xd1, 0xaa, 0x88, 0x53, 0xa0, 0x40, 0x8b, 0x16, 0x88, 0x1b, 0x07, 0x11, 0x1c,
	0xa9, 0xee, 0xca, 0xad, 0x2f, 0x05, 0xd8, 0xb5, 0x38, 0x52, 0x16, 0x22, 0xb9, 0xea, 0xee, 0xd2,
	0x89, 0x72, 0xed, 0x0b, 0xf4, 0x54, 0xf4, 0x11, 0xfa, 0x28, 0x3d, 0xe6, 0x98, 0x63, 0x11, 0xbf,
	0x48, 0xb1, 0x4b, 0x8a, 0x12, 0x69, 0xc6, 0xe9, 0x49, 0x9c, 0xd9, 0x99, 0xd9, 0xd9, 0x9d, 0xdd,
	0xdf, 0x8e, 0xa0, 0x8e, 0x42, 0x70, 0x31, 0xbb, 0xe8, 0xce, 0x04, 0x57, 0xdc, 0xda, 0x4a, 0xc4,
	0x3b, 0xb5, 0x00, 0x15, 0x5d, 0xa8, 0xef, 0xec, 0x4e, 0xf8, 0x84, 0x9b, 0xcf, 0xcf, 0xf5, 0x57,
	0xa2, 0x6d, 0x8a, 0x48, 0x2a, 0xf3, 0x19, 0x2b, 0x9c, 0x01, 0x54, 0x07, 0x5c, 0x3d, 0x47, 0xea,
	0xa1, 0xb0, 0xee, 0x42, 0x55, 0xe0, 0x84, 0xf1, 0xd0, 0x65, 0x9e, 0x5d, 0xba, 0x5f, 0x3a, 0xd8,
	0x24, 0x95, 0x58, 0xd1, 0xf3, 0xac, 0x8f, 0xa1, 0xec, 0x1b, 0x33, 0x7b, 0xfd, 0x7e, 0xe9, 0x60,
	0xfb, 0xb0, 0xd6, 0x4d, 0xe6, 0x3b, 0x45, 0x14, 0x24, 0x19, 0x73, 0x0e, 0xa0, 0xda, 0x93, 0xe7,
	0x4c, 0x85, 0x28, 0xe5, 0x8d, 0xf1, 0x9c, 0x63, 0x68, 0x1f, 0x45, 0xa3, 0x29, 0xaa, 0x9f, 0x50,
	0x48, 0xc6, 0xc3, 0x01, 0x57, 0x7d, 0xaa, 0x46, 0x2f, 0x2c, 0x1b, 0xb6, 0x2e, 0x63, 0x55, 0xe2,
	0xb3, 0x10, 0x2d, 0x0b, 0x36, 0xa7, 0x38, 0x97, 0xf6, 0xfa, 0xfd, 0x8d, 0x83, 0x1a, 0x31, 0xdf,
	0xce, 0x37, 0x50, 0x79, 0xc2, 0xe4, 0xf4, 0x69, 0xe4, 0xfb, 0xd6, 0xff, 0xa0, 0x22, 0x15, 0x17,
	0x18, 0x4f, 0xb7, 0xa1, 0x5d, 0x8d, 0xdc, 0xf3, 0xac, 0x3d, 0x28, 0x0b, 0xa4, 0x92, 0x87, 0x26,
	0xfb, 0x2a, 0x49, 0x24, 0x87, 0x42, 0x7d, 0xa8, 0x4d, 0xd2, 0xd9, 0x0f, 0x60, 0x47, 0xe0, 0xaf,
	0x11, 0x4a, 0xe5, 0xae, 0xc4, 0xd2, 0x69, 0x34, 0x12, 0xfd, 0x30, 0x09, 0xf9, 0x00, 0x9a, 0x74,
	0xa4, 0x22, 0xea, 0x2f, 0x0d, 0xd7, 0x8d, 0x61, 0x3d, 0x56, 0x27, 0x76, 0xce, 0x67, 0xd0, 0x20,
	0x66, 0xd1, 0x03, 0xae, 0x9e, 0xf2, 0x28, 0xf4, 0x6e, 0xde, 0x97, 0x47, 0xb0, 0x9b, 0x9a, 0xf7,
	0x42, 0xa6, 0x18, 0xf5, 0xd9, 0x6b, 0xfc, 0x80, 0x53, 0x04, 0x8d, 0x13, 0x9c, 0x1b, 0x8f, 0xd8,
	0xd9, 0xda, 0x81, 0x8d, 0x29, 0xce, 0x8d, 0x61, 0x8d, 0xe8, 0xcf, 0x6c, 0x80, 0xf5, 0x5c, 0x75,
	0xef, 0x42, 0x55, 0x2a, 0x2a, 0x94, 0xab, 0x9d, 0x36, 0x8c, 0x53, 0xc5, 0x28, 0x4e, 0x70, 0x6e,
	0xed, 0xc3, 0x16, 0x86, 0x9e, 0x19, 0xda, 0x34, 0x43, 0x65, 0x0c, 0xbd, 0x13, 0x9c, 0x3b, 0xcf,
	0xa0, 0x7e, 0x3c, 0xe3, 0xa3, 0x17, 0xe9, 0xee, 0x7d, 0x09, 0xcd, 0x51, 0x24, 0x04, 0x86, 0xca,
	0x8d, 0x43, 0x4b, 0x53, 0x88, 0xed, 0xc3, 0xc6, 0xe2, 0xb4, 0xc4, 0xe9, 0x91, 0x46, 0x62, 0x16,
	0x8b, 0xd2, 0xf9, 0xa3, 0x04, 0xb5, 0x21, 0x8a, 0x4b, 0x14, 0x3d, 0x79, 0x14, 0xc9, 0xf9, 0x4a,
	0xc1, 0x4a, 0xab, 0x05, 0xb3, 0xee, 0x01, 0x5c, 0xd0, 0xd1, 0x94, 0x8f, 0xc7, 0x6e, 0x20, 0x93,
	0x65, 0x54, 0x13, 0x4d, 0x5f, 0x5a, 0x9f, 0x40, 0x0b, 0xa5, 0x62, 0x01, 0x55, 0xe8, 0xb9, 0x2f,
	0x29, 0x53, 0xda, 0x4a, 0xaf, 0xa7, 0x4e, 0x9a, 0xe9, 0xc0, 0x39, 0x65, 0xaa, 0x2f, 0xad, 0x8f,
	0xa0, 0x4e, 0x67, 0x33, 0x9f, 0xa1, 0xe7, 0xb2, 0xd0, 0xc3, 0x57, 0x66, 0x71, 0x9b, 0xa4, 0x96,
	0x28, 0x7b, 0x5a, 0xe7, 0x34, 0xa0, 0x36, 0x54, 0xd4, 0xc7, 0xef, 0x78, 0x10, 0xd0, 0xd0, 0x73,
	0xbe, 0x87, 0x16, 0xa1, 0x63, 0x75, 0x1c, 0x2a, 0x31, 0x3f, 0xe3, 0xfc, 0x39, 0x15, 0x13, 0xbc,
	0xf9, 0xe2, 0xdc, 0x03, 0x40, 0x6d, 0xed, 0x4a, 0xf6, 0x1a, 0x17, 0x19, 0x1b, 0xcd, 0x90, 0xbd,
	0x46, 0x67, 0x1f, 0xda, 0x7d, 0xfa, 0xea, 0x8c, 0x05, 0x28, 0x15, 0x0d, 0x66, 0x03, 0xae, 0x86,
	0xf3, 0x70, 0x84, 0x9e, 0xf3, 0x0c, 0x5a, 0x04, 0x69, 0x9c, 0xc6, 0x80, 0x2b, 0xfd, 0xfd, 0xfe,
	0x6d, 0xb9, 0xa9, 0xb8, 0xce, 0x17, 0xd0, 0x3e, 0x15, 0x7c, 0xc6, 0x25, 0xf5, 0x7b, 0x61, 0x1f,
	0xc5, 0x84, 0x85, 0x93, 0x3e, 0xf7, 0x6e, 0xce, 0xdb, 0x71, 0xa1, 0xf1, 0x84, 0x2a, 0xda, 0x93,
	0xe9, 0xe4, 0x37, 0x2e, 0x73, 0x1f, 0xb6, 0x66, 0x88, 0x62, 0x39, 0x7f, 0x59, 0x8b, 0xf1, 0x80,
	0xa4, 0x63, 0x74, 0x55, 0x5c, 0x88, 0x4d, 0x52, 0xd6, 0xe2, 0x99, 0x74, 0x1e, 0x82, 0x45, 0x70,
	0xc4, 0x2f, 0x51, 0xcc, 0x7b, 0xe1, 0xa9, 0xe0, 0x13, 0xf1, 0x41, 0x68, 0xfc, 0x02, 0xb7, 0x9f,
	0xfa, 0x54, 0xbe, 0xd0, 0x05, 0xff, 0x8f, 0x3e, 0xd6, 0xa7, 0x60, 0x8d, 0x17, 0x3e, 0x6e, 0x7c,
	0xc8, 0xd5, 0xe2, 0xe4, 0xec, 0xa4, 0x23, 0x43, 0x3d, 0x70, 0x26, 0xf5, 0xf5, 0x4b, 0x67, 0x18,
	0x70, 0x75, 0x2a, 0x70, 0x46, 0xc5, 0x87, 0xae, 0xdf, 0xcf, 0xd0, 0xe8, 0x33, 0x19, 0xe8, 0x2b,
	0x70, 0x1a, 0x2f, 0xfa, 0x01, 0x34, 0x17, 0x18, 0x59, 0xec, 0x4a, 0xec, 0x54, 0x4f, 0xd4, 0x89,
	0x9d, 0x03, 0xf5, 0x98, 0x1e, 0xd9, 0xbd, 0xdb, 0x36, 0xca, 0xd8, 0xc6, 0xe9, 0x82, 0xf5, 0x63,
	0xe8, 0xa1, 0x42, 0x11, 0xb0, 0x10, 0x3d, 0x82, 0x32, 0xf2, 0x95, 0xc6, 0x64, 0x80, 0x52, 0xd2,
	0x09, 0x26, 0x47, 0x61, 0x21, 0x3a, 0xbf, 0xd5, 0xe0, 0xd6, 0xb1, 0x7e, 0x14, 0xde, 0x6f, 0x63,
	0x3d, 0x04, 0x08, 0xb9, 0x72, 0x33, 0x44, 0xb7, 0xba, 0x8b, 0x97, 0x25, 0x7d, 0x12, 0x48, 0x35,
	0x4c, 0x5f, 0x87, 0xc7, 0x9a, 0x8c, 0x66, 0x07, 0xb4, 0xe7, 0x58, 0x93, 0xcc, 0x14, 0x74, 0xfb,
	0x70, 0x3f, 0x75, 0xcc, 0x82, 0x4e, 0x23, 0x33, 0x03, 0xbe, 0x23, 0x68, 0x4d, 0x71, 0x6e, 0xfc,
	0x59, 0x98, 0x10, 0xc2, 0xdc, 0xba, 0xd5, 0x18, 0x59, 0x90, 0x91, 0xc6, 0x34, 0x0b, 0xb6, 0x6f,
	0xa1, 0x89, 0x9a, 0x39, 0x26, 0x8a, 0xd9, 0x72, 0xfb, 0x96, 0x89, 0xb0, 0x97, 0x46, 0xc8, 0x30,
	0x89, 0xd4, 0x31, 0x83, 0xa8, 0xaf, 0xa1, 0x21, 0x0d, 0x68, 0x5c, 0x26, 0xdd, 0x8b, 0x48, 0xce,
	0xed, 0xb2, 0x71, 0x6f, 0xa7, 0xee, 0xab, 0x1c, 0x22, 0x35, 0xb9, 0x4a, 0xa5, 0xaf, 0x74, 0xb9,
	0xa8, 0x8f, 0xee, 0x28, 0xc6, 0x81, 0xbd, 0x95, 0xf7, 0x5d, 0x61, 0x05, 0xa9, 0xc9, 0x15, 0x49,
	0x27, 0x1e, 0x97, 0x7a, 0x99, 0x78, 0x25, 0x97, 0x78, 0xe6, 0x29, 0x22, 0xf1, 0xc9, 0x48, 0x13,
	0x3f, 0x81, 0x5d, 0x41, 0xc7, 0xca, 0x8d, 0x61, 0xa2, 0x38, 0x77, 0x7d, 0x0d, 0x1f, 0xbb, 0x6a,
	0x82, 0xdc, 0x59, 0xd6, 0x20, 0x8f, 0x27, 0xd2, 0x12, 0xd7, 0x88, 0x75, 0x0e, 0x76, 0x40, 0x5f,
	0xb9, 0x6a, 0x81, 0x1d, 0x93, 0x94, 0x34, 0xe0, 0xb1, 0xc1, 0x04, 0xec, 0xa4, 0x01, 0x0b, 0xf1,
	0x44, 0xda, 0x41, 0x91, 0xda, 0x64, 0x89, 0x34, 0x21, 0xaa, 0x89, 0xaa, 0xc5, 0xb9, 0xbd, 0x9d,
	0xcf, 0x32, 0x8f, 0x36, 0xd2, 0x12, 0xd7, 0x68, 0x77, 0x0e, 0xf6, 0x2c, 0x01, 0x97, 0x3e, 0x30,
	0x41, 0x8c, 0x2e, 0x37, 0xe0, 0x1e, 0xda, 0xb5, 0x5c, 0x96, 0x85, 0x84, 0x23, 0xed, 0x59, 0x21,
	0xf8, 0x8e, 0xa0, 0xe5, 0x51, 0x45, 0xf5, 0x11, 0x58, 0xa6, 0x58, 0xcf, 0x1d, 0xc4, 0x2c, 0xfd,
	0x48, 0xc3, 0xcb, 0xd2, 0x70, 0x08, 0x7b, 0x2b, 0xf7, 0x81, 0x2d, 0x9f, 0x6a, 0xbb, 0x61, 0x02,
	0xdd, 0xbb, 0x7e, 0x2b, 0x56, 0xde, 0x73, 0xb2, 0x2b, 0x8a, 0x5e, 0xf9, 0x2e, 0x54, 0x3d, 0x26,
	0xa7, 0xee, 0x38, 0xf2, 0x7d, 0xbb, 0x69, 0xe2, 0xb4, 0x96, 0x09, 0x25, 0x8d, 0x0e, 0xa9, 0x78,
	0x8b, 0x96, 0xe7, 0xa4, 0x88, 0xa1, 0xf6, 0x8e, 0x71, 0xbc, 0xbb, 0x92, 0x40, 0xde, 0x84, 0x14,
	0xa1, 0x77, 0x50, 0x48, 0x57, 0xbb, 0x65, 0xa2, 0xfd, 0x3f, 0x8d, 0x56, 0x60, 0x43, 0x0a, 0xb1,
	0xfc, 0x43, 0x31, 0x4b, 0x6d, 0x2b, 0xb7, 0x3f, 0x45, 0x46, 0xa4, 0x18, 0xc3, 0x0f, 0x01, 0x98,
	0x74, 0x5f, 0xc6, 0x0d, 0xa6, 0x7d, 0x3b, 0xc7, 0xad, 0xb4, 0xf5, 0x24, 0x55, 0x96, 0x76, 0xa1,
	0x8f, 0x61, 0x27, 0x48, 0xe0, 0x9c, 0x52, 0x76, 0x37, 0x57, 0xea, 0x2c, 0xbd, 0x49, 0x23, 0xc8,
	0xd2, 0xfc, 0x1c, 0xec, 0x0b, 0xd3, 0xab, 0xba, 0x49, 0x2b, 0xba, 0x72, 0x87, 0xdb, 0xb9, 0x73,
	0x58, 0xd8, 0xd4, 0x92, 0xf6, 0x45, 0x61, 0xaf, 0xfb, 0x1c, 0x6e, 0x47, 0x2b, 0x68, 0x77, 0x85,
	0x61, 0xbb, 0xbd, 0x97, 0xab, 0xdf, 0x75, 0xfc, 0x13, 0x2b, 0xba, 0xae, 0xdb, 0x8e, 0xd9, 0x64,
	0x78, 0x77, 0x74, 0xf8, 0xf6, 0xaf, 0x4a, 0xe9, 0xef, 0x77, 0x9d, 0xd2, 0x9b, 0x77, 0x9d, 0xd2,
	0x3f, 0xef, 0x3a, 0xa5, 0xdf, 0xaf, 0x3a, 0x6b, 0x7f, 0x5e, 0x75, 0xd6, 0xde, 0x5c, 0x75, 0xd6,
	0xde, 0x5e, 0x75, 0xd6, 0x60, 0x87, 0x8b, 0x49, 0x57, 0xb1, 0xe9, 0x65, 0x77, 0x7a, 0x69, 0xfe,
	0x0d, 0x5c, 0x94, 0xcd, 0xcf, 0xa3, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0xfd, 0x4e, 0xb6, 0x79,
	0x63, 0x0c, 0x00, 0x00,
}

func (m *NotLeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotLeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *NotLeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Leader != nil {
		{
			size, err := m.Leader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *IsWitness) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IsWitness) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IsWitness) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BucketVersionNotMatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BucketVersionNotMatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BucketVersionNotMatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Keys) > 0 {
		for iNdEx := len(m.Keys) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Keys[iNdEx])
			copy(dAtA[i:], m.Keys[iNdEx])
			i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Keys[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Version != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DiskFull) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DiskFull) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DiskFull) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Reason) > 0 {
		i -= len(m.Reason)
		copy(dAtA[i:], m.Reason)
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Reason)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.StoreId) > 0 {
		dAtA3 := make([]byte, len(m.StoreId)*10)
		var j2 int
		for _, num := range m.StoreId {
			for num >= 1<<7 {
				dAtA3[j2] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j2++
			}
			dAtA3[j2] = uint8(num)
			j2++
		}
		i -= j2
		copy(dAtA[i:], dAtA3[:j2])
		i = encodeVarintErrorpb(dAtA, i, uint64(j2))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StoreNotMatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreNotMatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreNotMatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ActualStoreId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.ActualStoreId))
		i--
		dAtA[i] = 0x10
	}
	if m.RequestStoreId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RequestStoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionNotFound) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionNotFound) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionNotFound) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionNotInitialized) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionNotInitialized) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionNotInitialized) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *KeyNotInRegion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeyNotInRegion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KeyNotInRegion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *EpochNotMatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EpochNotMatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EpochNotMatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CurrentRegions) > 0 {
		for iNdEx := len(m.CurrentRegions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.CurrentRegions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintErrorpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ServerIsBusy) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServerIsBusy) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServerIsBusy) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AppliedIndex != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.AppliedIndex))
		i--
		dAtA[i] = 0x20
	}
	if m.EstimatedWaitMs != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.EstimatedWaitMs))
		i--
		dAtA[i] = 0x18
	}
	if m.BackoffMs != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.BackoffMs))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Reason) > 0 {
		i -= len(m.Reason)
		copy(dAtA[i:], m.Reason)
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Reason)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StaleCommand) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StaleCommand) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StaleCommand) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *RaftEntryTooLarge) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftEntryTooLarge) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftEntryTooLarge) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.EntrySize != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.EntrySize))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MaxTimestampNotSynced) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MaxTimestampNotSynced) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MaxTimestampNotSynced) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *ReadIndexNotReady) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadIndexNotReady) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadIndexNotReady) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Reason) > 0 {
		i -= len(m.Reason)
		copy(dAtA[i:], m.Reason)
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Reason)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ProposalInMergingMode) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProposalInMergingMode) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ProposalInMergingMode) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DataIsNotReady) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DataIsNotReady) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataIsNotReady) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SafeTs != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.SafeTs))
		i--
		dAtA[i] = 0x18
	}
	if m.PeerId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.PeerId))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RecoveryInProgress) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecoveryInProgress) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RecoveryInProgress) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *FlashbackInProgress) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlashbackInProgress) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlashbackInProgress) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.FlashbackStartTs != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.FlashbackStartTs))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *FlashbackNotPrepared) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlashbackNotPrepared) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlashbackNotPrepared) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MismatchPeerId) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MismatchPeerId) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MismatchPeerId) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StorePeerId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.StorePeerId))
		i--
		dAtA[i] = 0x10
	}
	if m.RequestPeerId != 0 {
		i = encodeVarintErrorpb(dAtA, i, uint64(m.RequestPeerId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *UndeterminedResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UndeterminedResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UndeterminedResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.UndeterminedResult != nil {
		{
			size, err := m.UndeterminedResult.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	if m.BucketVersionNotMatch != nil {
		{
			size, err := m.BucketVersionNotMatch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	if m.MismatchPeerId != nil {
		{
			size, err := m.MismatchPeerId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	if m.IsWitness != nil {
		{
			size, err := m.IsWitness.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if m.FlashbackNotPrepared != nil {
		{
			size, err := m.FlashbackNotPrepared.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	if m.FlashbackInProgress != nil {
		{
			size, err := m.FlashbackInProgress.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if m.RecoveryInProgress != nil {
		{
			size, err := m.RecoveryInProgress.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if m.DiskFull != nil {
		{
			size, err := m.DiskFull.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x7a
	}
	if m.RegionNotInitialized != nil {
		{
			size, err := m.RegionNotInitialized.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	if m.DataIsNotReady != nil {
		{
			size, err := m.DataIsNotReady.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if m.ProposalInMergingMode != nil {
		{
			size, err := m.ProposalInMergingMode.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if m.ReadIndexNotReady != nil {
		{
			size, err := m.ReadIndexNotReady.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.MaxTimestampNotSynced != nil {
		{
			size, err := m.MaxTimestampNotSynced.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.RaftEntryTooLarge != nil {
		{
			size, err := m.RaftEntryTooLarge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.StoreNotMatch != nil {
		{
			size, err := m.StoreNotMatch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.StaleCommand != nil {
		{
			size, err := m.StaleCommand.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.ServerIsBusy != nil {
		{
			size, err := m.ServerIsBusy.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.EpochNotMatch != nil {
		{
			size, err := m.EpochNotMatch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.KeyNotInRegion != nil {
		{
			size, err := m.KeyNotInRegion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.RegionNotFound != nil {
		{
			size, err := m.RegionNotFound.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.NotLeader != nil {
		{
			size, err := m.NotLeader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintErrorpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintErrorpb(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintErrorpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovErrorpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *NotLeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	return n
}

func (m *IsWitness) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	return n
}

func (m *BucketVersionNotMatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Version != 0 {
		n += 1 + sovErrorpb(uint64(m.Version))
	}
	if len(m.Keys) > 0 {
		for _, b := range m.Keys {
			l = len(b)
			n += 1 + l + sovErrorpb(uint64(l))
		}
	}
	return n
}

func (m *DiskFull) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.StoreId) > 0 {
		l = 0
		for _, e := range m.StoreId {
			l += sovErrorpb(uint64(e))
		}
		n += 1 + sovErrorpb(uint64(l)) + l
	}
	l = len(m.Reason)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	return n
}

func (m *StoreNotMatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RequestStoreId != 0 {
		n += 1 + sovErrorpb(uint64(m.RequestStoreId))
	}
	if m.ActualStoreId != 0 {
		n += 1 + sovErrorpb(uint64(m.ActualStoreId))
	}
	return n
}

func (m *RegionNotFound) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	return n
}

func (m *RegionNotInitialized) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	return n
}

func (m *KeyNotInRegion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	return n
}

func (m *EpochNotMatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.CurrentRegions) > 0 {
		for _, e := range m.CurrentRegions {
			l = e.Size()
			n += 1 + l + sovErrorpb(uint64(l))
		}
	}
	return n
}

func (m *ServerIsBusy) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Reason)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.BackoffMs != 0 {
		n += 1 + sovErrorpb(uint64(m.BackoffMs))
	}
	if m.EstimatedWaitMs != 0 {
		n += 1 + sovErrorpb(uint64(m.EstimatedWaitMs))
	}
	if m.AppliedIndex != 0 {
		n += 1 + sovErrorpb(uint64(m.AppliedIndex))
	}
	return n
}

func (m *StaleCommand) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *RaftEntryTooLarge) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	if m.EntrySize != 0 {
		n += 1 + sovErrorpb(uint64(m.EntrySize))
	}
	return n
}

func (m *MaxTimestampNotSynced) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *ReadIndexNotReady) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Reason)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	return n
}

func (m *ProposalInMergingMode) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	return n
}

func (m *DataIsNotReady) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	if m.PeerId != 0 {
		n += 1 + sovErrorpb(uint64(m.PeerId))
	}
	if m.SafeTs != 0 {
		n += 1 + sovErrorpb(uint64(m.SafeTs))
	}
	return n
}

func (m *RecoveryInProgress) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	return n
}

func (m *FlashbackInProgress) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	if m.FlashbackStartTs != 0 {
		n += 1 + sovErrorpb(uint64(m.FlashbackStartTs))
	}
	return n
}

func (m *FlashbackNotPrepared) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovErrorpb(uint64(m.RegionId))
	}
	return n
}

func (m *MismatchPeerId) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RequestPeerId != 0 {
		n += 1 + sovErrorpb(uint64(m.RequestPeerId))
	}
	if m.StorePeerId != 0 {
		n += 1 + sovErrorpb(uint64(m.StorePeerId))
	}
	return n
}

func (m *UndeterminedResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.NotLeader != nil {
		l = m.NotLeader.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.RegionNotFound != nil {
		l = m.RegionNotFound.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.KeyNotInRegion != nil {
		l = m.KeyNotInRegion.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.EpochNotMatch != nil {
		l = m.EpochNotMatch.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.ServerIsBusy != nil {
		l = m.ServerIsBusy.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.StaleCommand != nil {
		l = m.StaleCommand.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.StoreNotMatch != nil {
		l = m.StoreNotMatch.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.RaftEntryTooLarge != nil {
		l = m.RaftEntryTooLarge.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.MaxTimestampNotSynced != nil {
		l = m.MaxTimestampNotSynced.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.ReadIndexNotReady != nil {
		l = m.ReadIndexNotReady.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.ProposalInMergingMode != nil {
		l = m.ProposalInMergingMode.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.DataIsNotReady != nil {
		l = m.DataIsNotReady.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.RegionNotInitialized != nil {
		l = m.RegionNotInitialized.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.DiskFull != nil {
		l = m.DiskFull.Size()
		n += 1 + l + sovErrorpb(uint64(l))
	}
	if m.RecoveryInProgress != nil {
		l = m.RecoveryInProgress.Size()
		n += 2 + l + sovErrorpb(uint64(l))
	}
	if m.FlashbackInProgress != nil {
		l = m.FlashbackInProgress.Size()
		n += 2 + l + sovErrorpb(uint64(l))
	}
	if m.FlashbackNotPrepared != nil {
		l = m.FlashbackNotPrepared.Size()
		n += 2 + l + sovErrorpb(uint64(l))
	}
	if m.IsWitness != nil {
		l = m.IsWitness.Size()
		n += 2 + l + sovErrorpb(uint64(l))
	}
	if m.MismatchPeerId != nil {
		l = m.MismatchPeerId.Size()
		n += 2 + l + sovErrorpb(uint64(l))
	}
	if m.BucketVersionNotMatch != nil {
		l = m.BucketVersionNotMatch.Size()
		n += 2 + l + sovErrorpb(uint64(l))
	}
	if m.UndeterminedResult != nil {
		l = m.UndeterminedResult.Size()
		n += 2 + l + sovErrorpb(uint64(l))
	}
	return n
}

func sovErrorpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozErrorpb(x uint64) (n int) {
	return sovErrorpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *NotLeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NotLeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NotLeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &metapb.Peer{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IsWitness) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IsWitness: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IsWitness: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BucketVersionNotMatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BucketVersionNotMatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BucketVersionNotMatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keys", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Keys = append(m.Keys, make([]byte, postIndex-iNdEx))
			copy(m.Keys[len(m.Keys)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DiskFull) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DiskFull: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DiskFull: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowErrorpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreId = append(m.StoreId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowErrorpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthErrorpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthErrorpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreId) == 0 {
					m.StoreId = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowErrorpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreId = append(m.StoreId, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreNotMatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreNotMatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreNotMatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestStoreId", wireType)
			}
			m.RequestStoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestStoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActualStoreId", wireType)
			}
			m.ActualStoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActualStoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionNotFound) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionNotFound: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionNotFound: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionNotInitialized) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionNotInitialized: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionNotInitialized: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KeyNotInRegion) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeyNotInRegion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeyNotInRegion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EpochNotMatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EpochNotMatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EpochNotMatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentRegions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CurrentRegions = append(m.CurrentRegions, &metapb.Region{})
			if err := m.CurrentRegions[len(m.CurrentRegions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServerIsBusy) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ServerIsBusy: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ServerIsBusy: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BackoffMs", wireType)
			}
			m.BackoffMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BackoffMs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EstimatedWaitMs", wireType)
			}
			m.EstimatedWaitMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EstimatedWaitMs |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppliedIndex", wireType)
			}
			m.AppliedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppliedIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StaleCommand) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StaleCommand: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StaleCommand: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftEntryTooLarge) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftEntryTooLarge: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftEntryTooLarge: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EntrySize", wireType)
			}
			m.EntrySize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EntrySize |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MaxTimestampNotSynced) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MaxTimestampNotSynced: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MaxTimestampNotSynced: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadIndexNotReady) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadIndexNotReady: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadIndexNotReady: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProposalInMergingMode) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ProposalInMergingMode: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ProposalInMergingMode: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DataIsNotReady) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DataIsNotReady: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DataIsNotReady: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeerId", wireType)
			}
			m.PeerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SafeTs", wireType)
			}
			m.SafeTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SafeTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecoveryInProgress) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RecoveryInProgress: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RecoveryInProgress: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlashbackInProgress) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlashbackInProgress: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlashbackInProgress: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlashbackStartTs", wireType)
			}
			m.FlashbackStartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FlashbackStartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlashbackNotPrepared) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlashbackNotPrepared: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlashbackNotPrepared: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MismatchPeerId) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MismatchPeerId: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MismatchPeerId: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestPeerId", wireType)
			}
			m.RequestPeerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestPeerId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorePeerId", wireType)
			}
			m.StorePeerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StorePeerId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UndeterminedResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UndeterminedResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UndeterminedResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NotLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.NotLeader == nil {
				m.NotLeader = &NotLeader{}
			}
			if err := m.NotLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionNotFound", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionNotFound == nil {
				m.RegionNotFound = &RegionNotFound{}
			}
			if err := m.RegionNotFound.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyNotInRegion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyNotInRegion == nil {
				m.KeyNotInRegion = &KeyNotInRegion{}
			}
			if err := m.KeyNotInRegion.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EpochNotMatch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EpochNotMatch == nil {
				m.EpochNotMatch = &EpochNotMatch{}
			}
			if err := m.EpochNotMatch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServerIsBusy", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ServerIsBusy == nil {
				m.ServerIsBusy = &ServerIsBusy{}
			}
			if err := m.ServerIsBusy.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StaleCommand", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StaleCommand == nil {
				m.StaleCommand = &StaleCommand{}
			}
			if err := m.StaleCommand.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreNotMatch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StoreNotMatch == nil {
				m.StoreNotMatch = &StoreNotMatch{}
			}
			if err := m.StoreNotMatch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RaftEntryTooLarge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RaftEntryTooLarge == nil {
				m.RaftEntryTooLarge = &RaftEntryTooLarge{}
			}
			if err := m.RaftEntryTooLarge.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxTimestampNotSynced", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MaxTimestampNotSynced == nil {
				m.MaxTimestampNotSynced = &MaxTimestampNotSynced{}
			}
			if err := m.MaxTimestampNotSynced.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadIndexNotReady", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReadIndexNotReady == nil {
				m.ReadIndexNotReady = &ReadIndexNotReady{}
			}
			if err := m.ReadIndexNotReady.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProposalInMergingMode", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ProposalInMergingMode == nil {
				m.ProposalInMergingMode = &ProposalInMergingMode{}
			}
			if err := m.ProposalInMergingMode.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataIsNotReady", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DataIsNotReady == nil {
				m.DataIsNotReady = &DataIsNotReady{}
			}
			if err := m.DataIsNotReady.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionNotInitialized", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionNotInitialized == nil {
				m.RegionNotInitialized = &RegionNotInitialized{}
			}
			if err := m.RegionNotInitialized.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DiskFull", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DiskFull == nil {
				m.DiskFull = &DiskFull{}
			}
			if err := m.DiskFull.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RecoveryInProgress", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RecoveryInProgress == nil {
				m.RecoveryInProgress = &RecoveryInProgress{}
			}
			if err := m.RecoveryInProgress.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlashbackInProgress", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FlashbackInProgress == nil {
				m.FlashbackInProgress = &FlashbackInProgress{}
			}
			if err := m.FlashbackInProgress.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlashbackNotPrepared", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FlashbackNotPrepared == nil {
				m.FlashbackNotPrepared = &FlashbackNotPrepared{}
			}
			if err := m.FlashbackNotPrepared.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsWitness", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.IsWitness == nil {
				m.IsWitness = &IsWitness{}
			}
			if err := m.IsWitness.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MismatchPeerId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MismatchPeerId == nil {
				m.MismatchPeerId = &MismatchPeerId{}
			}
			if err := m.MismatchPeerId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BucketVersionNotMatch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BucketVersionNotMatch == nil {
				m.BucketVersionNotMatch = &BucketVersionNotMatch{}
			}
			if err := m.BucketVersionNotMatch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UndeterminedResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthErrorpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthErrorpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.UndeterminedResult == nil {
				m.UndeterminedResult = &UndeterminedResult{}
			}
			if err := m.UndeterminedResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipErrorpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthErrorpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipErrorpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowErrorpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowErrorpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthErrorpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupErrorpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthErrorpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthErrorpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowErrorpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupErrorpb = fmt.Errorf("proto: unexpected end of group")
)
