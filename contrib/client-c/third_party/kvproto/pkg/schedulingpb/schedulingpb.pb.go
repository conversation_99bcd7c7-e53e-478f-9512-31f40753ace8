// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: schedulingpb.proto

package schedulingpb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
	pdpb "github.com/pingcap/kvproto/pkg/pdpb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ErrorType int32

const (
	ErrorType_OK                   ErrorType = 0
	ErrorType_UNKNOWN              ErrorType = 1
	ErrorType_NOT_BOOTSTRAPPED     ErrorType = 2
	ErrorType_ALREADY_BOOTSTRAPPED ErrorType = 3
	ErrorType_INVALID_VALUE        ErrorType = 4
	ErrorType_CLUSTER_MISMATCHED   ErrorType = 5
)

var ErrorType_name = map[int32]string{
	0: "OK",
	1: "UNKNOWN",
	2: "NOT_BOOTSTRAPPED",
	3: "ALREADY_BOOTSTRAPPED",
	4: "INVALID_VALUE",
	5: "CLUSTER_MISMATCHED",
}

var ErrorType_value = map[string]int32{
	"OK":                   0,
	"UNKNOWN":              1,
	"NOT_BOOTSTRAPPED":     2,
	"ALREADY_BOOTSTRAPPED": 3,
	"INVALID_VALUE":        4,
	"CLUSTER_MISMATCHED":   5,
}

func (x ErrorType) String() string {
	return proto.EnumName(ErrorType_name, int32(x))
}

func (ErrorType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{0}
}

type RequestHeader struct {
	// cluster_id is the ID of the cluster which be sent to.
	ClusterId uint64 `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// sender_id is the ID of the sender server.
	SenderId uint64 `protobuf:"varint,2,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
}

func (m *RequestHeader) Reset()         { *m = RequestHeader{} }
func (m *RequestHeader) String() string { return proto.CompactTextString(m) }
func (*RequestHeader) ProtoMessage()    {}
func (*RequestHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{0}
}
func (m *RequestHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RequestHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RequestHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RequestHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestHeader.Merge(m, src)
}
func (m *RequestHeader) XXX_Size() int {
	return m.Size()
}
func (m *RequestHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestHeader.DiscardUnknown(m)
}

var xxx_messageInfo_RequestHeader proto.InternalMessageInfo

func (m *RequestHeader) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

func (m *RequestHeader) GetSenderId() uint64 {
	if m != nil {
		return m.SenderId
	}
	return 0
}

type ResponseHeader struct {
	// cluster_id is the ID of the cluster which sent the response.
	ClusterId uint64 `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Error     *Error `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *ResponseHeader) Reset()         { *m = ResponseHeader{} }
func (m *ResponseHeader) String() string { return proto.CompactTextString(m) }
func (*ResponseHeader) ProtoMessage()    {}
func (*ResponseHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{1}
}
func (m *ResponseHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResponseHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResponseHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResponseHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResponseHeader.Merge(m, src)
}
func (m *ResponseHeader) XXX_Size() int {
	return m.Size()
}
func (m *ResponseHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_ResponseHeader.DiscardUnknown(m)
}

var xxx_messageInfo_ResponseHeader proto.InternalMessageInfo

func (m *ResponseHeader) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

func (m *ResponseHeader) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type Error struct {
	Type    ErrorType `protobuf:"varint,1,opt,name=type,proto3,enum=schedulingpb.ErrorType" json:"type,omitempty"`
	Message string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{2}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetType() ErrorType {
	if m != nil {
		return m.Type
	}
	return ErrorType_OK
}

func (m *Error) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type Participant struct {
	// name is the unique name of the scheduling participant.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// id is the unique id of the scheduling participant.
	Id uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// listen_urls is the serivce endpoint list in the url format.
	// listen_urls[0] is primary service endpoint.
	ListenUrls []string `protobuf:"bytes,3,rep,name=listen_urls,json=listenUrls,proto3" json:"listen_urls,omitempty"`
}

func (m *Participant) Reset()         { *m = Participant{} }
func (m *Participant) String() string { return proto.CompactTextString(m) }
func (*Participant) ProtoMessage()    {}
func (*Participant) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{3}
}
func (m *Participant) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Participant) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Participant.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Participant) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Participant.Merge(m, src)
}
func (m *Participant) XXX_Size() int {
	return m.Size()
}
func (m *Participant) XXX_DiscardUnknown() {
	xxx_messageInfo_Participant.DiscardUnknown(m)
}

var xxx_messageInfo_Participant proto.InternalMessageInfo

func (m *Participant) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Participant) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Participant) GetListenUrls() []string {
	if m != nil {
		return m.ListenUrls
	}
	return nil
}

type StoreHeartbeatRequest struct {
	Header *RequestHeader   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Stats  *pdpb.StoreStats `protobuf:"bytes,2,opt,name=stats,proto3" json:"stats,omitempty"`
}

func (m *StoreHeartbeatRequest) Reset()         { *m = StoreHeartbeatRequest{} }
func (m *StoreHeartbeatRequest) String() string { return proto.CompactTextString(m) }
func (*StoreHeartbeatRequest) ProtoMessage()    {}
func (*StoreHeartbeatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{4}
}
func (m *StoreHeartbeatRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreHeartbeatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreHeartbeatRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreHeartbeatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreHeartbeatRequest.Merge(m, src)
}
func (m *StoreHeartbeatRequest) XXX_Size() int {
	return m.Size()
}
func (m *StoreHeartbeatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreHeartbeatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StoreHeartbeatRequest proto.InternalMessageInfo

func (m *StoreHeartbeatRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *StoreHeartbeatRequest) GetStats() *pdpb.StoreStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

type StoreHeartbeatResponse struct {
	Header         *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ClusterVersion string          `protobuf:"bytes,2,opt,name=cluster_version,json=clusterVersion,proto3" json:"cluster_version,omitempty"`
}

func (m *StoreHeartbeatResponse) Reset()         { *m = StoreHeartbeatResponse{} }
func (m *StoreHeartbeatResponse) String() string { return proto.CompactTextString(m) }
func (*StoreHeartbeatResponse) ProtoMessage()    {}
func (*StoreHeartbeatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{5}
}
func (m *StoreHeartbeatResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreHeartbeatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreHeartbeatResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreHeartbeatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreHeartbeatResponse.Merge(m, src)
}
func (m *StoreHeartbeatResponse) XXX_Size() int {
	return m.Size()
}
func (m *StoreHeartbeatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreHeartbeatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StoreHeartbeatResponse proto.InternalMessageInfo

func (m *StoreHeartbeatResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *StoreHeartbeatResponse) GetClusterVersion() string {
	if m != nil {
		return m.ClusterVersion
	}
	return ""
}

type RegionHeartbeatRequest struct {
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Region *metapb.Region `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	// Leader Peer sending the heartbeat.
	Leader *metapb.Peer `protobuf:"bytes,3,opt,name=leader,proto3" json:"leader,omitempty"`
	// Term is the term of raft group.
	Term uint64 `protobuf:"varint,4,opt,name=term,proto3" json:"term,omitempty"`
	// Leader considers that these peers are down.
	DownPeers []*pdpb.PeerStats `protobuf:"bytes,5,rep,name=down_peers,json=downPeers,proto3" json:"down_peers,omitempty"`
	// Pending peers are the peers that the leader can't consider as
	// working followers.
	PendingPeers []*metapb.Peer `protobuf:"bytes,6,rep,name=pending_peers,json=pendingPeers,proto3" json:"pending_peers,omitempty"`
	// Bytes read/written during this period.
	BytesWritten uint64 `protobuf:"varint,7,opt,name=bytes_written,json=bytesWritten,proto3" json:"bytes_written,omitempty"`
	BytesRead    uint64 `protobuf:"varint,8,opt,name=bytes_read,json=bytesRead,proto3" json:"bytes_read,omitempty"`
	// Keys read/written during this period.
	KeysWritten uint64 `protobuf:"varint,9,opt,name=keys_written,json=keysWritten,proto3" json:"keys_written,omitempty"`
	KeysRead    uint64 `protobuf:"varint,10,opt,name=keys_read,json=keysRead,proto3" json:"keys_read,omitempty"`
	// Approximate region size.
	ApproximateSize uint64 `protobuf:"varint,11,opt,name=approximate_size,json=approximateSize,proto3" json:"approximate_size,omitempty"`
	// Approximate number of keys.
	ApproximateKeys uint64 `protobuf:"varint,12,opt,name=approximate_keys,json=approximateKeys,proto3" json:"approximate_keys,omitempty"`
	// QueryStats reported write query stats, and there are read query stats in store heartbeat
	QueryStats *pdpb.QueryStats `protobuf:"bytes,13,opt,name=query_stats,json=queryStats,proto3" json:"query_stats,omitempty"`
	// Actually reported time interval
	Interval *pdpb.TimeInterval `protobuf:"bytes,14,opt,name=interval,proto3" json:"interval,omitempty"`
}

func (m *RegionHeartbeatRequest) Reset()         { *m = RegionHeartbeatRequest{} }
func (m *RegionHeartbeatRequest) String() string { return proto.CompactTextString(m) }
func (*RegionHeartbeatRequest) ProtoMessage()    {}
func (*RegionHeartbeatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{6}
}
func (m *RegionHeartbeatRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionHeartbeatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionHeartbeatRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionHeartbeatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionHeartbeatRequest.Merge(m, src)
}
func (m *RegionHeartbeatRequest) XXX_Size() int {
	return m.Size()
}
func (m *RegionHeartbeatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionHeartbeatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RegionHeartbeatRequest proto.InternalMessageInfo

func (m *RegionHeartbeatRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetLeader() *metapb.Peer {
	if m != nil {
		return m.Leader
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

func (m *RegionHeartbeatRequest) GetDownPeers() []*pdpb.PeerStats {
	if m != nil {
		return m.DownPeers
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetPendingPeers() []*metapb.Peer {
	if m != nil {
		return m.PendingPeers
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetBytesWritten() uint64 {
	if m != nil {
		return m.BytesWritten
	}
	return 0
}

func (m *RegionHeartbeatRequest) GetBytesRead() uint64 {
	if m != nil {
		return m.BytesRead
	}
	return 0
}

func (m *RegionHeartbeatRequest) GetKeysWritten() uint64 {
	if m != nil {
		return m.KeysWritten
	}
	return 0
}

func (m *RegionHeartbeatRequest) GetKeysRead() uint64 {
	if m != nil {
		return m.KeysRead
	}
	return 0
}

func (m *RegionHeartbeatRequest) GetApproximateSize() uint64 {
	if m != nil {
		return m.ApproximateSize
	}
	return 0
}

func (m *RegionHeartbeatRequest) GetApproximateKeys() uint64 {
	if m != nil {
		return m.ApproximateKeys
	}
	return 0
}

func (m *RegionHeartbeatRequest) GetQueryStats() *pdpb.QueryStats {
	if m != nil {
		return m.QueryStats
	}
	return nil
}

func (m *RegionHeartbeatRequest) GetInterval() *pdpb.TimeInterval {
	if m != nil {
		return m.Interval
	}
	return nil
}

type RegionHeartbeatResponse struct {
	Header *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// ID of the region
	RegionId    uint64              `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	RegionEpoch *metapb.RegionEpoch `protobuf:"bytes,3,opt,name=region_epoch,json=regionEpoch,proto3" json:"region_epoch,omitempty"`
	// Leader of the region at the moment of the corresponding request was made.
	TargetPeer *metapb.Peer `protobuf:"bytes,4,opt,name=target_peer,json=targetPeer,proto3" json:"target_peer,omitempty"`
	// Notice, Pd only allows handling reported epoch >= current pd's.
	// Leader peer reports region status with RegionHeartbeatRequest
	// to pd regularly, pd will determine whether this region
	// should do ChangePeer or not.
	// E,g, max peer number is 3, region A, first only peer 1 in A.
	// 1. Pd region state -> Peers (1), ConfVer (1).
	// 2. Leader peer 1 reports region state to pd, pd finds the
	// peer number is < 3, so first changes its current region
	// state -> Peers (1, 2), ConfVer (1), and returns ChangePeer Adding 2.
	// 3. Leader does ChangePeer, then reports Peers (1, 2), ConfVer (2),
	// pd updates its state -> Peers (1, 2), ConfVer (2).
	// 4. Leader may report old Peers (1), ConfVer (1) to pd before ConfChange
	// finished, pd stills responses ChangePeer Adding 2, of course, we must
	// guarantee the second ChangePeer can't be applied in TiKV.
	ChangePeer *pdpb.ChangePeer `protobuf:"bytes,5,opt,name=change_peer,json=changePeer,proto3" json:"change_peer,omitempty"`
	// Pd can return transfer_leader to let TiKV does leader transfer itself.
	TransferLeader *pdpb.TransferLeader `protobuf:"bytes,6,opt,name=transfer_leader,json=transferLeader,proto3" json:"transfer_leader,omitempty"`
	Merge          *pdpb.Merge          `protobuf:"bytes,7,opt,name=merge,proto3" json:"merge,omitempty"`
	// PD sends split_region to let TiKV split a region into two regions.
	SplitRegion *pdpb.SplitRegion `protobuf:"bytes,8,opt,name=split_region,json=splitRegion,proto3" json:"split_region,omitempty"`
	// Multiple change peer operations atomically.
	// Note: PD can use both ChangePeer and ChangePeerV2 at the same time
	//       (not in the same RegionHeartbeatResponse).
	//       Now, PD use ChangePeerV2 in following scenarios:
	//       1. replacing peers
	//       2. demoting voter directly
	ChangePeerV2    *pdpb.ChangePeerV2       `protobuf:"bytes,9,opt,name=change_peer_v2,json=changePeerV2,proto3" json:"change_peer_v2,omitempty"`
	SwitchWitnesses *pdpb.BatchSwitchWitness `protobuf:"bytes,10,opt,name=switch_witnesses,json=switchWitnesses,proto3" json:"switch_witnesses,omitempty"`
}

func (m *RegionHeartbeatResponse) Reset()         { *m = RegionHeartbeatResponse{} }
func (m *RegionHeartbeatResponse) String() string { return proto.CompactTextString(m) }
func (*RegionHeartbeatResponse) ProtoMessage()    {}
func (*RegionHeartbeatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{7}
}
func (m *RegionHeartbeatResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionHeartbeatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionHeartbeatResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionHeartbeatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionHeartbeatResponse.Merge(m, src)
}
func (m *RegionHeartbeatResponse) XXX_Size() int {
	return m.Size()
}
func (m *RegionHeartbeatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionHeartbeatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RegionHeartbeatResponse proto.InternalMessageInfo

func (m *RegionHeartbeatResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RegionHeartbeatResponse) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetTargetPeer() *metapb.Peer {
	if m != nil {
		return m.TargetPeer
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetChangePeer() *pdpb.ChangePeer {
	if m != nil {
		return m.ChangePeer
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetTransferLeader() *pdpb.TransferLeader {
	if m != nil {
		return m.TransferLeader
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetMerge() *pdpb.Merge {
	if m != nil {
		return m.Merge
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetSplitRegion() *pdpb.SplitRegion {
	if m != nil {
		return m.SplitRegion
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetChangePeerV2() *pdpb.ChangePeerV2 {
	if m != nil {
		return m.ChangePeerV2
	}
	return nil
}

func (m *RegionHeartbeatResponse) GetSwitchWitnesses() *pdpb.BatchSwitchWitness {
	if m != nil {
		return m.SwitchWitnesses
	}
	return nil
}

type ScatterRegionsRequest struct {
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// If group is defined, the regions with the same group would be scattered as a whole group.
	// If not defined, the regions would be scattered in a cluster level.
	Group string `protobuf:"bytes,2,opt,name=group,proto3" json:"group,omitempty"`
	// If regions_id is defined, the region_id would be ignored.
	RegionsId      []uint64 `protobuf:"varint,3,rep,packed,name=regions_id,json=regionsId,proto3" json:"regions_id,omitempty"`
	RetryLimit     uint64   `protobuf:"varint,4,opt,name=retry_limit,json=retryLimit,proto3" json:"retry_limit,omitempty"`
	SkipStoreLimit bool     `protobuf:"varint,5,opt,name=skip_store_limit,json=skipStoreLimit,proto3" json:"skip_store_limit,omitempty"`
}

func (m *ScatterRegionsRequest) Reset()         { *m = ScatterRegionsRequest{} }
func (m *ScatterRegionsRequest) String() string { return proto.CompactTextString(m) }
func (*ScatterRegionsRequest) ProtoMessage()    {}
func (*ScatterRegionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{8}
}
func (m *ScatterRegionsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScatterRegionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScatterRegionsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ScatterRegionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScatterRegionsRequest.Merge(m, src)
}
func (m *ScatterRegionsRequest) XXX_Size() int {
	return m.Size()
}
func (m *ScatterRegionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ScatterRegionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ScatterRegionsRequest proto.InternalMessageInfo

func (m *ScatterRegionsRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ScatterRegionsRequest) GetGroup() string {
	if m != nil {
		return m.Group
	}
	return ""
}

func (m *ScatterRegionsRequest) GetRegionsId() []uint64 {
	if m != nil {
		return m.RegionsId
	}
	return nil
}

func (m *ScatterRegionsRequest) GetRetryLimit() uint64 {
	if m != nil {
		return m.RetryLimit
	}
	return 0
}

func (m *ScatterRegionsRequest) GetSkipStoreLimit() bool {
	if m != nil {
		return m.SkipStoreLimit
	}
	return false
}

type ScatterRegionsResponse struct {
	Header             *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FinishedPercentage uint64          `protobuf:"varint,2,opt,name=finished_percentage,json=finishedPercentage,proto3" json:"finished_percentage,omitempty"`
}

func (m *ScatterRegionsResponse) Reset()         { *m = ScatterRegionsResponse{} }
func (m *ScatterRegionsResponse) String() string { return proto.CompactTextString(m) }
func (*ScatterRegionsResponse) ProtoMessage()    {}
func (*ScatterRegionsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{9}
}
func (m *ScatterRegionsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ScatterRegionsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ScatterRegionsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ScatterRegionsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScatterRegionsResponse.Merge(m, src)
}
func (m *ScatterRegionsResponse) XXX_Size() int {
	return m.Size()
}
func (m *ScatterRegionsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ScatterRegionsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ScatterRegionsResponse proto.InternalMessageInfo

func (m *ScatterRegionsResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ScatterRegionsResponse) GetFinishedPercentage() uint64 {
	if m != nil {
		return m.FinishedPercentage
	}
	return 0
}

type SplitRegionsRequest struct {
	Header     *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SplitKeys  [][]byte       `protobuf:"bytes,2,rep,name=split_keys,json=splitKeys,proto3" json:"split_keys,omitempty"`
	RetryLimit uint64         `protobuf:"varint,3,opt,name=retry_limit,json=retryLimit,proto3" json:"retry_limit,omitempty"`
}

func (m *SplitRegionsRequest) Reset()         { *m = SplitRegionsRequest{} }
func (m *SplitRegionsRequest) String() string { return proto.CompactTextString(m) }
func (*SplitRegionsRequest) ProtoMessage()    {}
func (*SplitRegionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{10}
}
func (m *SplitRegionsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SplitRegionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SplitRegionsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SplitRegionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitRegionsRequest.Merge(m, src)
}
func (m *SplitRegionsRequest) XXX_Size() int {
	return m.Size()
}
func (m *SplitRegionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitRegionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SplitRegionsRequest proto.InternalMessageInfo

func (m *SplitRegionsRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *SplitRegionsRequest) GetSplitKeys() [][]byte {
	if m != nil {
		return m.SplitKeys
	}
	return nil
}

func (m *SplitRegionsRequest) GetRetryLimit() uint64 {
	if m != nil {
		return m.RetryLimit
	}
	return 0
}

type SplitRegionsResponse struct {
	Header             *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FinishedPercentage uint64          `protobuf:"varint,2,opt,name=finished_percentage,json=finishedPercentage,proto3" json:"finished_percentage,omitempty"`
	RegionsId          []uint64        `protobuf:"varint,3,rep,packed,name=regions_id,json=regionsId,proto3" json:"regions_id,omitempty"`
}

func (m *SplitRegionsResponse) Reset()         { *m = SplitRegionsResponse{} }
func (m *SplitRegionsResponse) String() string { return proto.CompactTextString(m) }
func (*SplitRegionsResponse) ProtoMessage()    {}
func (*SplitRegionsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{11}
}
func (m *SplitRegionsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SplitRegionsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SplitRegionsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SplitRegionsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SplitRegionsResponse.Merge(m, src)
}
func (m *SplitRegionsResponse) XXX_Size() int {
	return m.Size()
}
func (m *SplitRegionsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SplitRegionsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SplitRegionsResponse proto.InternalMessageInfo

func (m *SplitRegionsResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *SplitRegionsResponse) GetFinishedPercentage() uint64 {
	if m != nil {
		return m.FinishedPercentage
	}
	return 0
}

func (m *SplitRegionsResponse) GetRegionsId() []uint64 {
	if m != nil {
		return m.RegionsId
	}
	return nil
}

type GetOperatorRequest struct {
	Header   *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RegionId uint64         `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *GetOperatorRequest) Reset()         { *m = GetOperatorRequest{} }
func (m *GetOperatorRequest) String() string { return proto.CompactTextString(m) }
func (*GetOperatorRequest) ProtoMessage()    {}
func (*GetOperatorRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{12}
}
func (m *GetOperatorRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetOperatorRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetOperatorRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetOperatorRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperatorRequest.Merge(m, src)
}
func (m *GetOperatorRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetOperatorRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperatorRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperatorRequest proto.InternalMessageInfo

func (m *GetOperatorRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetOperatorRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type GetOperatorResponse struct {
	Header   *ResponseHeader     `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RegionId uint64              `protobuf:"varint,2,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	Desc     []byte              `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Status   pdpb.OperatorStatus `protobuf:"varint,4,opt,name=status,proto3,enum=pdpb.OperatorStatus" json:"status,omitempty"`
	Kind     []byte              `protobuf:"bytes,5,opt,name=kind,proto3" json:"kind,omitempty"`
}

func (m *GetOperatorResponse) Reset()         { *m = GetOperatorResponse{} }
func (m *GetOperatorResponse) String() string { return proto.CompactTextString(m) }
func (*GetOperatorResponse) ProtoMessage()    {}
func (*GetOperatorResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{13}
}
func (m *GetOperatorResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetOperatorResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetOperatorResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetOperatorResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperatorResponse.Merge(m, src)
}
func (m *GetOperatorResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetOperatorResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperatorResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperatorResponse proto.InternalMessageInfo

func (m *GetOperatorResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetOperatorResponse) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *GetOperatorResponse) GetDesc() []byte {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (m *GetOperatorResponse) GetStatus() pdpb.OperatorStatus {
	if m != nil {
		return m.Status
	}
	return pdpb.OperatorStatus_SUCCESS
}

func (m *GetOperatorResponse) GetKind() []byte {
	if m != nil {
		return m.Kind
	}
	return nil
}

type AskBatchSplitRequest struct {
	Header     *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Region     *metapb.Region `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	SplitCount uint32         `protobuf:"varint,3,opt,name=split_count,json=splitCount,proto3" json:"split_count,omitempty"`
}

func (m *AskBatchSplitRequest) Reset()         { *m = AskBatchSplitRequest{} }
func (m *AskBatchSplitRequest) String() string { return proto.CompactTextString(m) }
func (*AskBatchSplitRequest) ProtoMessage()    {}
func (*AskBatchSplitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{14}
}
func (m *AskBatchSplitRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AskBatchSplitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AskBatchSplitRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AskBatchSplitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AskBatchSplitRequest.Merge(m, src)
}
func (m *AskBatchSplitRequest) XXX_Size() int {
	return m.Size()
}
func (m *AskBatchSplitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AskBatchSplitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AskBatchSplitRequest proto.InternalMessageInfo

func (m *AskBatchSplitRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *AskBatchSplitRequest) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *AskBatchSplitRequest) GetSplitCount() uint32 {
	if m != nil {
		return m.SplitCount
	}
	return 0
}

type AskBatchSplitResponse struct {
	Header *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Ids    []*pdpb.SplitID `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
}

func (m *AskBatchSplitResponse) Reset()         { *m = AskBatchSplitResponse{} }
func (m *AskBatchSplitResponse) String() string { return proto.CompactTextString(m) }
func (*AskBatchSplitResponse) ProtoMessage()    {}
func (*AskBatchSplitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b4bfd49510230d67, []int{15}
}
func (m *AskBatchSplitResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AskBatchSplitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AskBatchSplitResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AskBatchSplitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AskBatchSplitResponse.Merge(m, src)
}
func (m *AskBatchSplitResponse) XXX_Size() int {
	return m.Size()
}
func (m *AskBatchSplitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AskBatchSplitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AskBatchSplitResponse proto.InternalMessageInfo

func (m *AskBatchSplitResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *AskBatchSplitResponse) GetIds() []*pdpb.SplitID {
	if m != nil {
		return m.Ids
	}
	return nil
}

func init() {
	proto.RegisterEnum("schedulingpb.ErrorType", ErrorType_name, ErrorType_value)
	proto.RegisterType((*RequestHeader)(nil), "schedulingpb.RequestHeader")
	proto.RegisterType((*ResponseHeader)(nil), "schedulingpb.ResponseHeader")
	proto.RegisterType((*Error)(nil), "schedulingpb.Error")
	proto.RegisterType((*Participant)(nil), "schedulingpb.Participant")
	proto.RegisterType((*StoreHeartbeatRequest)(nil), "schedulingpb.StoreHeartbeatRequest")
	proto.RegisterType((*StoreHeartbeatResponse)(nil), "schedulingpb.StoreHeartbeatResponse")
	proto.RegisterType((*RegionHeartbeatRequest)(nil), "schedulingpb.RegionHeartbeatRequest")
	proto.RegisterType((*RegionHeartbeatResponse)(nil), "schedulingpb.RegionHeartbeatResponse")
	proto.RegisterType((*ScatterRegionsRequest)(nil), "schedulingpb.ScatterRegionsRequest")
	proto.RegisterType((*ScatterRegionsResponse)(nil), "schedulingpb.ScatterRegionsResponse")
	proto.RegisterType((*SplitRegionsRequest)(nil), "schedulingpb.SplitRegionsRequest")
	proto.RegisterType((*SplitRegionsResponse)(nil), "schedulingpb.SplitRegionsResponse")
	proto.RegisterType((*GetOperatorRequest)(nil), "schedulingpb.GetOperatorRequest")
	proto.RegisterType((*GetOperatorResponse)(nil), "schedulingpb.GetOperatorResponse")
	proto.RegisterType((*AskBatchSplitRequest)(nil), "schedulingpb.AskBatchSplitRequest")
	proto.RegisterType((*AskBatchSplitResponse)(nil), "schedulingpb.AskBatchSplitResponse")
}

func init() { proto.RegisterFile("schedulingpb.proto", fileDescriptor_b4bfd49510230d67) }

var fileDescriptor_b4bfd49510230d67 = []byte{
	// 1382 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x57, 0xbb, 0x6f, 0x1b, 0xc7,
	0x13, 0xd6, 0x89, 0x0f, 0x8b, 0x73, 0xc7, 0x87, 0x57, 0xb4, 0x4c, 0x50, 0x3f, 0xd3, 0xd2, 0xd9,
	0xbf, 0x44, 0xce, 0x83, 0x4e, 0x68, 0x23, 0x48, 0x93, 0x82, 0x96, 0x88, 0x98, 0x90, 0x2c, 0x29,
	0x4b, 0x4a, 0x42, 0x0c, 0x04, 0x87, 0x13, 0x6f, 0x4d, 0x1e, 0x44, 0xde, 0x9d, 0x77, 0x97, 0x52,
	0xe8, 0x14, 0xe9, 0x53, 0xa5, 0x48, 0x91, 0x22, 0xe9, 0xf3, 0x3f, 0x04, 0x48, 0x9d, 0x22, 0x40,
	0x5c, 0xba, 0x0c, 0xac, 0x7f, 0x24, 0xd8, 0x07, 0x29, 0x1e, 0x25, 0xc8, 0x01, 0x94, 0xb8, 0xd2,
	0xee, 0xcc, 0xb7, 0x33, 0x3b, 0xdf, 0x7c, 0x3b, 0x3c, 0x01, 0x62, 0x9d, 0x1e, 0xf1, 0x86, 0x7d,
	0x3f, 0xe8, 0x46, 0x87, 0xd5, 0x88, 0x86, 0x3c, 0x44, 0xd6, 0xb4, 0xad, 0x0c, 0x91, 0x37, 0xf6,
	0x94, 0xad, 0x01, 0xe1, 0xee, 0x64, 0x57, 0xec, 0x86, 0xdd, 0x50, 0x2e, 0xef, 0x8b, 0x95, 0xb6,
	0xe6, 0xe9, 0x90, 0x71, 0xb9, 0x54, 0x06, 0x7b, 0x13, 0xb2, 0x98, 0x3c, 0x1f, 0x12, 0xc6, 0x1f,
	0x13, 0xd7, 0x23, 0x14, 0xdd, 0x02, 0xe8, 0xf4, 0x87, 0x8c, 0x13, 0xea, 0xf8, 0x5e, 0xc9, 0x58,
	0x31, 0xd6, 0x92, 0x38, 0xa3, 0x2d, 0x4d, 0x0f, 0x2d, 0x43, 0x86, 0x91, 0xc0, 0x53, 0xde, 0x79,
	0xe9, 0x5d, 0x50, 0x86, 0xa6, 0x67, 0x3f, 0x85, 0x1c, 0x26, 0x2c, 0x0a, 0x03, 0x46, 0xfe, 0x59,
	0xb4, 0x7b, 0x90, 0x22, 0x94, 0x86, 0x54, 0x46, 0x32, 0x6b, 0x8b, 0xd5, 0x58, 0xc1, 0x0d, 0xe1,
	0xc2, 0x0a, 0x61, 0x6f, 0x43, 0x4a, 0xee, 0xd1, 0xfb, 0x90, 0xe4, 0xa3, 0x88, 0xc8, 0x60, 0xb9,
	0xda, 0xcd, 0x0b, 0x8e, 0xb4, 0x47, 0x11, 0xc1, 0x12, 0x84, 0x4a, 0x70, 0x6d, 0x40, 0x18, 0x73,
	0xbb, 0x44, 0xa6, 0xc8, 0xe0, 0xf1, 0xd6, 0xc6, 0x60, 0xee, 0xba, 0x94, 0xfb, 0x1d, 0x3f, 0x72,
	0x03, 0x8e, 0x10, 0x24, 0x03, 0x77, 0xa0, 0xa2, 0x66, 0xb0, 0x5c, 0xa3, 0x1c, 0xcc, 0x4f, 0x8a,
	0x9c, 0xf7, 0x3d, 0x74, 0x1b, 0xcc, 0xbe, 0xcf, 0x38, 0x09, 0x9c, 0x21, 0xed, 0xb3, 0x52, 0x62,
	0x25, 0xb1, 0x96, 0xc1, 0xa0, 0x4c, 0x7b, 0xb4, 0xcf, 0x6c, 0x0e, 0x37, 0x5a, 0x3c, 0xa4, 0xa2,
	0x78, 0xca, 0x0f, 0x89, 0xcb, 0x35, 0xb5, 0xe8, 0x01, 0xa4, 0x7b, 0x92, 0x10, 0x19, 0xdf, 0xac,
	0x2d, 0xc7, 0x6f, 0x1d, 0xeb, 0x00, 0xd6, 0x50, 0xf4, 0x0e, 0xa4, 0x18, 0x77, 0x39, 0xd3, 0xe4,
	0x14, 0xaa, 0xb2, 0xd7, 0x32, 0x41, 0x4b, 0xd8, 0xb1, 0x72, 0xdb, 0x27, 0xb0, 0x34, 0x9b, 0x55,
	0xf5, 0x00, 0x3d, 0x9c, 0x49, 0xfb, 0xbf, 0xd9, 0xb4, 0xd3, 0xbd, 0x9a, 0xe4, 0x7d, 0x17, 0xf2,
	0xe3, 0x9e, 0x1d, 0x13, 0xca, 0xfc, 0x30, 0xd0, 0xdc, 0xe5, 0xb4, 0x79, 0x5f, 0x59, 0xed, 0x3f,
	0x93, 0xb0, 0x84, 0x49, 0xd7, 0x0f, 0x83, 0x7f, 0xab, 0xe0, 0x34, 0x95, 0xe1, 0x74, 0xc5, 0xb9,
	0xaa, 0x56, 0xb4, 0x4a, 0x82, 0xb5, 0x17, 0xdd, 0x85, 0x74, 0x5f, 0x05, 0x4f, 0x48, 0x9c, 0x35,
	0xc6, 0xed, 0x12, 0x11, 0x4d, 0xf9, 0x44, 0x47, 0x39, 0xa1, 0x83, 0x52, 0x52, 0xf6, 0x4f, 0xae,
	0x51, 0x15, 0xc0, 0x0b, 0x4f, 0x02, 0x27, 0x22, 0x84, 0xb2, 0x52, 0x6a, 0x25, 0xb1, 0x66, 0xd6,
	0xf2, 0x8a, 0x57, 0x71, 0x56, 0xd1, 0x9a, 0x11, 0x10, 0xb1, 0x65, 0xe8, 0x63, 0xc8, 0x46, 0x24,
	0xf0, 0xfc, 0xa0, 0xab, 0x8f, 0xa4, 0xe5, 0x91, 0x78, 0x42, 0x4b, 0x43, 0xd4, 0x91, 0x3b, 0x90,
	0x3d, 0x1c, 0x71, 0xc2, 0x9c, 0x13, 0xea, 0x73, 0x4e, 0x82, 0xd2, 0x35, 0x99, 0xdf, 0x92, 0xc6,
	0x03, 0x65, 0x13, 0xcf, 0x42, 0x81, 0x28, 0x71, 0xbd, 0xd2, 0x82, 0x7a, 0x16, 0xd2, 0x82, 0x89,
	0xeb, 0xa1, 0x55, 0xb0, 0x8e, 0xc8, 0xe8, 0x2c, 0x44, 0x46, 0x02, 0x4c, 0x61, 0x1b, 0x47, 0x58,
	0x86, 0x8c, 0x84, 0xc8, 0x00, 0xa0, 0xde, 0xa1, 0x30, 0xc8, 0xf3, 0xf7, 0xa0, 0xe0, 0x46, 0x11,
	0x0d, 0xbf, 0xf6, 0x07, 0x2e, 0x27, 0x0e, 0xf3, 0x5f, 0x90, 0x92, 0x29, 0x31, 0xf9, 0x29, 0x7b,
	0xcb, 0x7f, 0x41, 0x66, 0xa1, 0x22, 0x44, 0xc9, 0x3a, 0x07, 0xdd, 0x24, 0x23, 0x41, 0x86, 0xf9,
	0x7c, 0x48, 0xe8, 0xc8, 0x51, 0xaa, 0xcc, 0x4e, 0xab, 0xf2, 0x0b, 0xe1, 0x50, 0xf4, 0xc1, 0xf3,
	0xc9, 0x1a, 0x55, 0x61, 0xc1, 0x0f, 0x38, 0xa1, 0xc7, 0x6e, 0xbf, 0x94, 0x93, 0x78, 0xa4, 0xf0,
	0x6d, 0x7f, 0x40, 0x9a, 0xda, 0x83, 0x27, 0x18, 0xfb, 0xe7, 0x24, 0xdc, 0x3c, 0xa7, 0xa8, 0x2b,
	0x89, 0x79, 0x19, 0x32, 0x4a, 0x35, 0x53, 0xf3, 0x4a, 0x19, 0x9a, 0x1e, 0xfa, 0x04, 0x2c, 0xed,
	0x24, 0x51, 0xd8, 0xe9, 0x69, 0x39, 0x2d, 0xc6, 0x65, 0xd7, 0x10, 0x2e, 0x6c, 0xd2, 0xb3, 0x0d,
	0xfa, 0x10, 0x4c, 0xee, 0xd2, 0x2e, 0xe1, 0x52, 0x15, 0x52, 0x61, 0xb3, 0xa2, 0x00, 0x05, 0x10,
	0x6b, 0x41, 0x5c, 0xa7, 0xe7, 0x06, 0x5d, 0xa2, 0xe0, 0xa9, 0x69, 0xe2, 0xd6, 0xa5, 0x43, 0x1d,
	0xe9, 0x4c, 0xd6, 0xe8, 0x33, 0xc8, 0x73, 0xea, 0x06, 0xec, 0x19, 0xa1, 0x8e, 0xd6, 0x7a, 0x5a,
	0x1e, 0x2b, 0x6a, 0xfe, 0xb4, 0x73, 0x4b, 0x55, 0x9b, 0xe3, 0xb1, 0x3d, 0x5a, 0x85, 0xd4, 0x80,
	0xd0, 0x2e, 0x91, 0xe2, 0x33, 0x6b, 0xa6, 0x3a, 0xf4, 0x44, 0x98, 0xb0, 0xf2, 0xa0, 0x87, 0x60,
	0xb1, 0xa8, 0xef, 0x73, 0x47, 0x3f, 0xb9, 0x05, 0x89, 0xbc, 0xae, 0x87, 0x8c, 0xf0, 0xe8, 0x57,
	0x67, 0xb2, 0xb3, 0x0d, 0xfa, 0x14, 0x72, 0x53, 0xa5, 0x38, 0xc7, 0x35, 0xa9, 0xcd, 0x49, 0x5b,
	0xcf, 0xaa, 0xd9, 0xaf, 0x61, 0xab, 0x33, 0xb5, 0x43, 0xeb, 0x50, 0x60, 0x27, 0x3e, 0xef, 0xf4,
	0x9c, 0x13, 0x9f, 0x07, 0x84, 0x31, 0xc2, 0xa4, 0x6e, 0xcd, 0x5a, 0x49, 0x9d, 0x7d, 0xe4, 0xf2,
	0x4e, 0xaf, 0x25, 0x21, 0x07, 0x0a, 0x81, 0xf3, 0x6c, 0x7a, 0x4b, 0x98, 0xfd, 0x87, 0x01, 0x37,
	0x5a, 0x1d, 0x97, 0x73, 0x42, 0xd5, 0x85, 0xd8, 0x95, 0x06, 0x4e, 0x11, 0x52, 0x5d, 0x1a, 0x0e,
	0x23, 0x3d, 0xdf, 0xd4, 0x46, 0x3c, 0x4e, 0xc5, 0x09, 0x13, 0x9a, 0x11, 0x53, 0x3e, 0x89, 0xb5,
	0x88, 0x58, 0x53, 0xfe, 0x0a, 0x50, 0xc2, 0xe9, 0xc8, 0xe9, 0xfb, 0x03, 0x9f, 0xeb, 0xf1, 0x02,
	0xd2, 0xb4, 0x25, 0x2c, 0x68, 0x0d, 0x0a, 0xec, 0xc8, 0x8f, 0x1c, 0x26, 0x86, 0xb2, 0x46, 0x89,
	0x9e, 0x2f, 0xe0, 0x9c, 0xb0, 0xcb, 0x59, 0x2d, 0x91, 0xf6, 0xb7, 0xb0, 0x34, 0x5b, 0xcd, 0x95,
	0xc4, 0x7e, 0x1f, 0x16, 0x9f, 0xf9, 0x81, 0xcf, 0x7a, 0xc4, 0x73, 0x22, 0x42, 0x3b, 0x24, 0xe0,
	0xe3, 0x5f, 0xbe, 0x24, 0x46, 0x63, 0xd7, 0xee, 0xc4, 0x63, 0x7f, 0x67, 0xc0, 0xe2, 0x54, 0xaf,
	0xaf, 0xc6, 0xe6, 0x2d, 0x00, 0xa5, 0x28, 0x39, 0x44, 0xe6, 0x57, 0x12, 0x6b, 0x16, 0xce, 0x48,
	0x8b, 0x1c, 0x1f, 0x33, 0xbc, 0x25, 0x66, 0x79, 0xb3, 0x7f, 0x32, 0xa0, 0x18, 0xbf, 0xcc, 0x5b,
	0x25, 0xe3, 0x0d, 0x7d, 0xb7, 0x9f, 0x01, 0xfa, 0x9c, 0xf0, 0x9d, 0x88, 0x50, 0x97, 0x87, 0xf4,
	0x4a, 0x4c, 0x5d, 0x36, 0x94, 0xec, 0xdf, 0x0c, 0x58, 0x8c, 0x25, 0xfa, 0xef, 0xe6, 0x1f, 0x82,
	0xa4, 0x47, 0x58, 0x47, 0xf6, 0xc2, 0xc2, 0x72, 0x8d, 0x3e, 0x80, 0xb4, 0x98, 0xef, 0x43, 0x26,
	0x95, 0x9d, 0x1b, 0x0f, 0x9c, 0xf1, 0x75, 0x5a, 0xd2, 0x87, 0x35, 0x46, 0x44, 0x38, 0xf2, 0x03,
	0x4f, 0xea, 0xdb, 0xc2, 0x72, 0x6d, 0xff, 0x60, 0x40, 0xb1, 0xce, 0x8e, 0xd4, 0x7b, 0x56, 0xfd,
	0x7c, 0x0b, 0x1f, 0x05, 0xb7, 0x41, 0x0d, 0x2a, 0xa7, 0x13, 0x0e, 0x03, 0x25, 0xaf, 0x2c, 0x56,
	0x82, 0x5c, 0x17, 0x16, 0x3b, 0x80, 0x1b, 0x33, 0xb7, 0xba, 0x12, 0xb1, 0xb7, 0x21, 0xe1, 0x7b,
	0x4a, 0xe6, 0x66, 0x2d, 0x3b, 0x35, 0x36, 0x9b, 0x1b, 0x58, 0x78, 0xde, 0xfb, 0x06, 0x32, 0x93,
	0xaf, 0x51, 0x94, 0x86, 0xf9, 0x9d, 0xcd, 0xc2, 0x1c, 0x32, 0xe1, 0xda, 0xde, 0xf6, 0xe6, 0xf6,
	0xce, 0xc1, 0x76, 0xc1, 0x40, 0x45, 0x28, 0x6c, 0xef, 0xb4, 0x9d, 0x47, 0x3b, 0x3b, 0xed, 0x56,
	0x1b, 0xd7, 0x77, 0x77, 0x1b, 0x1b, 0x85, 0x79, 0x54, 0x82, 0x62, 0x7d, 0x0b, 0x37, 0xea, 0x1b,
	0x5f, 0xc6, 0x3d, 0x09, 0x74, 0x1d, 0xb2, 0xcd, 0xed, 0xfd, 0xfa, 0x56, 0x73, 0xc3, 0xd9, 0xaf,
	0x6f, 0xed, 0x35, 0x0a, 0x49, 0xb4, 0x04, 0x68, 0x7d, 0x6b, 0xaf, 0xd5, 0x6e, 0x60, 0xe7, 0x49,
	0xb3, 0xf5, 0xa4, 0xde, 0x5e, 0x7f, 0xdc, 0xd8, 0x28, 0xa4, 0x6a, 0xbf, 0x26, 0x01, 0x5a, 0x93,
	0x2a, 0xd0, 0x57, 0x90, 0x8b, 0x7f, 0x22, 0xa2, 0x3b, 0xf1, 0x22, 0x2f, 0xfc, 0x6c, 0x2d, 0xdf,
	0xbd, 0x1c, 0xa4, 0x78, 0xb1, 0xe7, 0x90, 0x07, 0xf9, 0x99, 0x5f, 0x6d, 0x74, 0x77, 0x96, 0xc4,
	0x8b, 0x3e, 0x13, 0xcb, 0xff, 0x7f, 0x03, 0x6a, 0x9c, 0x61, 0xcd, 0xf8, 0xc8, 0x40, 0x07, 0x60,
	0x4d, 0x8f, 0x07, 0xb4, 0x3a, 0x73, 0xbb, 0xf3, 0x73, 0xac, 0x6c, 0x5f, 0x06, 0x99, 0x5c, 0x5f,
	0xb0, 0x13, 0x1b, 0xc3, 0xe7, 0xd8, 0xb9, 0xe8, 0x27, 0xe7, 0x1c, 0x3b, 0x17, 0x4e, 0x72, 0x7b,
	0x0e, 0xb5, 0xc1, 0x9c, 0x7a, 0xcf, 0x68, 0x25, 0x7e, 0xec, 0xfc, 0x4c, 0x29, 0xaf, 0x5e, 0x82,
	0x98, 0x44, 0x7d, 0x0a, 0xd9, 0x98, 0x9c, 0xd1, 0x4c, 0xad, 0x17, 0xbd, 0xc0, 0xf2, 0x9d, 0x4b,
	0x31, 0xe3, 0xd8, 0x8f, 0x6a, 0xaf, 0x7e, 0x59, 0x30, 0x7e, 0x7f, 0x5d, 0x31, 0x5e, 0xbe, 0xae,
	0x18, 0x7f, 0xbd, 0xae, 0x18, 0xdf, 0x9f, 0x56, 0xe6, 0x7e, 0x3c, 0xad, 0xcc, 0xbd, 0x3c, 0xad,
	0xcc, 0xbd, 0x3a, 0xad, 0xcc, 0x41, 0x21, 0xa4, 0xdd, 0x2a, 0xf7, 0x8f, 0x8e, 0xab, 0x47, 0xc7,
	0xf2, 0x1f, 0xc9, 0xc3, 0xb4, 0xfc, 0xf3, 0xe0, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x33, 0x0d,
	0xc8, 0xea, 0xb4, 0x0e, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SchedulingClient is the client API for Scheduling service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SchedulingClient interface {
	StoreHeartbeat(ctx context.Context, in *StoreHeartbeatRequest, opts ...grpc.CallOption) (*StoreHeartbeatResponse, error)
	RegionHeartbeat(ctx context.Context, opts ...grpc.CallOption) (Scheduling_RegionHeartbeatClient, error)
	SplitRegions(ctx context.Context, in *SplitRegionsRequest, opts ...grpc.CallOption) (*SplitRegionsResponse, error)
	ScatterRegions(ctx context.Context, in *ScatterRegionsRequest, opts ...grpc.CallOption) (*ScatterRegionsResponse, error)
	GetOperator(ctx context.Context, in *GetOperatorRequest, opts ...grpc.CallOption) (*GetOperatorResponse, error)
	AskBatchSplit(ctx context.Context, in *AskBatchSplitRequest, opts ...grpc.CallOption) (*AskBatchSplitResponse, error)
}

type schedulingClient struct {
	cc *grpc.ClientConn
}

func NewSchedulingClient(cc *grpc.ClientConn) SchedulingClient {
	return &schedulingClient{cc}
}

func (c *schedulingClient) StoreHeartbeat(ctx context.Context, in *StoreHeartbeatRequest, opts ...grpc.CallOption) (*StoreHeartbeatResponse, error) {
	out := new(StoreHeartbeatResponse)
	err := c.cc.Invoke(ctx, "/schedulingpb.Scheduling/StoreHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingClient) RegionHeartbeat(ctx context.Context, opts ...grpc.CallOption) (Scheduling_RegionHeartbeatClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Scheduling_serviceDesc.Streams[0], "/schedulingpb.Scheduling/RegionHeartbeat", opts...)
	if err != nil {
		return nil, err
	}
	x := &schedulingRegionHeartbeatClient{stream}
	return x, nil
}

type Scheduling_RegionHeartbeatClient interface {
	Send(*RegionHeartbeatRequest) error
	Recv() (*RegionHeartbeatResponse, error)
	grpc.ClientStream
}

type schedulingRegionHeartbeatClient struct {
	grpc.ClientStream
}

func (x *schedulingRegionHeartbeatClient) Send(m *RegionHeartbeatRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *schedulingRegionHeartbeatClient) Recv() (*RegionHeartbeatResponse, error) {
	m := new(RegionHeartbeatResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *schedulingClient) SplitRegions(ctx context.Context, in *SplitRegionsRequest, opts ...grpc.CallOption) (*SplitRegionsResponse, error) {
	out := new(SplitRegionsResponse)
	err := c.cc.Invoke(ctx, "/schedulingpb.Scheduling/SplitRegions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingClient) ScatterRegions(ctx context.Context, in *ScatterRegionsRequest, opts ...grpc.CallOption) (*ScatterRegionsResponse, error) {
	out := new(ScatterRegionsResponse)
	err := c.cc.Invoke(ctx, "/schedulingpb.Scheduling/ScatterRegions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingClient) GetOperator(ctx context.Context, in *GetOperatorRequest, opts ...grpc.CallOption) (*GetOperatorResponse, error) {
	out := new(GetOperatorResponse)
	err := c.cc.Invoke(ctx, "/schedulingpb.Scheduling/GetOperator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *schedulingClient) AskBatchSplit(ctx context.Context, in *AskBatchSplitRequest, opts ...grpc.CallOption) (*AskBatchSplitResponse, error) {
	out := new(AskBatchSplitResponse)
	err := c.cc.Invoke(ctx, "/schedulingpb.Scheduling/AskBatchSplit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SchedulingServer is the server API for Scheduling service.
type SchedulingServer interface {
	StoreHeartbeat(context.Context, *StoreHeartbeatRequest) (*StoreHeartbeatResponse, error)
	RegionHeartbeat(Scheduling_RegionHeartbeatServer) error
	SplitRegions(context.Context, *SplitRegionsRequest) (*SplitRegionsResponse, error)
	ScatterRegions(context.Context, *ScatterRegionsRequest) (*ScatterRegionsResponse, error)
	GetOperator(context.Context, *GetOperatorRequest) (*GetOperatorResponse, error)
	AskBatchSplit(context.Context, *AskBatchSplitRequest) (*AskBatchSplitResponse, error)
}

// UnimplementedSchedulingServer can be embedded to have forward compatible implementations.
type UnimplementedSchedulingServer struct {
}

func (*UnimplementedSchedulingServer) StoreHeartbeat(ctx context.Context, req *StoreHeartbeatRequest) (*StoreHeartbeatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreHeartbeat not implemented")
}
func (*UnimplementedSchedulingServer) RegionHeartbeat(srv Scheduling_RegionHeartbeatServer) error {
	return status.Errorf(codes.Unimplemented, "method RegionHeartbeat not implemented")
}
func (*UnimplementedSchedulingServer) SplitRegions(ctx context.Context, req *SplitRegionsRequest) (*SplitRegionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SplitRegions not implemented")
}
func (*UnimplementedSchedulingServer) ScatterRegions(ctx context.Context, req *ScatterRegionsRequest) (*ScatterRegionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScatterRegions not implemented")
}
func (*UnimplementedSchedulingServer) GetOperator(ctx context.Context, req *GetOperatorRequest) (*GetOperatorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOperator not implemented")
}
func (*UnimplementedSchedulingServer) AskBatchSplit(ctx context.Context, req *AskBatchSplitRequest) (*AskBatchSplitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AskBatchSplit not implemented")
}

func RegisterSchedulingServer(s *grpc.Server, srv SchedulingServer) {
	s.RegisterService(&_Scheduling_serviceDesc, srv)
}

func _Scheduling_StoreHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreHeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingServer).StoreHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulingpb.Scheduling/StoreHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingServer).StoreHeartbeat(ctx, req.(*StoreHeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduling_RegionHeartbeat_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(SchedulingServer).RegionHeartbeat(&schedulingRegionHeartbeatServer{stream})
}

type Scheduling_RegionHeartbeatServer interface {
	Send(*RegionHeartbeatResponse) error
	Recv() (*RegionHeartbeatRequest, error)
	grpc.ServerStream
}

type schedulingRegionHeartbeatServer struct {
	grpc.ServerStream
}

func (x *schedulingRegionHeartbeatServer) Send(m *RegionHeartbeatResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *schedulingRegionHeartbeatServer) Recv() (*RegionHeartbeatRequest, error) {
	m := new(RegionHeartbeatRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Scheduling_SplitRegions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SplitRegionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingServer).SplitRegions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulingpb.Scheduling/SplitRegions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingServer).SplitRegions(ctx, req.(*SplitRegionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduling_ScatterRegions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScatterRegionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingServer).ScatterRegions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulingpb.Scheduling/ScatterRegions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingServer).ScatterRegions(ctx, req.(*ScatterRegionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduling_GetOperator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperatorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingServer).GetOperator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulingpb.Scheduling/GetOperator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingServer).GetOperator(ctx, req.(*GetOperatorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Scheduling_AskBatchSplit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AskBatchSplitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SchedulingServer).AskBatchSplit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/schedulingpb.Scheduling/AskBatchSplit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SchedulingServer).AskBatchSplit(ctx, req.(*AskBatchSplitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Scheduling_serviceDesc = grpc.ServiceDesc{
	ServiceName: "schedulingpb.Scheduling",
	HandlerType: (*SchedulingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StoreHeartbeat",
			Handler:    _Scheduling_StoreHeartbeat_Handler,
		},
		{
			MethodName: "SplitRegions",
			Handler:    _Scheduling_SplitRegions_Handler,
		},
		{
			MethodName: "ScatterRegions",
			Handler:    _Scheduling_ScatterRegions_Handler,
		},
		{
			MethodName: "GetOperator",
			Handler:    _Scheduling_GetOperator_Handler,
		},
		{
			MethodName: "AskBatchSplit",
			Handler:    _Scheduling_AskBatchSplit_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "RegionHeartbeat",
			Handler:       _Scheduling_RegionHeartbeat_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "schedulingpb.proto",
}

func (m *RequestHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RequestHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RequestHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SenderId != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.SenderId))
		i--
		dAtA[i] = 0x10
	}
	if m.ClusterId != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.ClusterId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ResponseHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResponseHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResponseHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.ClusterId != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.ClusterId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintSchedulingpb(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0x12
	}
	if m.Type != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Participant) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Participant) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Participant) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ListenUrls) > 0 {
		for iNdEx := len(m.ListenUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ListenUrls[iNdEx])
			copy(dAtA[i:], m.ListenUrls[iNdEx])
			i = encodeVarintSchedulingpb(dAtA, i, uint64(len(m.ListenUrls[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Id != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintSchedulingpb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StoreHeartbeatRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreHeartbeatRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreHeartbeatRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Stats != nil {
		{
			size, err := m.Stats.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StoreHeartbeatResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreHeartbeatResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreHeartbeatResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ClusterVersion) > 0 {
		i -= len(m.ClusterVersion)
		copy(dAtA[i:], m.ClusterVersion)
		i = encodeVarintSchedulingpb(dAtA, i, uint64(len(m.ClusterVersion)))
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RegionHeartbeatRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionHeartbeatRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionHeartbeatRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Interval != nil {
		{
			size, err := m.Interval.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	if m.QueryStats != nil {
		{
			size, err := m.QueryStats.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if m.ApproximateKeys != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.ApproximateKeys))
		i--
		dAtA[i] = 0x60
	}
	if m.ApproximateSize != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.ApproximateSize))
		i--
		dAtA[i] = 0x58
	}
	if m.KeysRead != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.KeysRead))
		i--
		dAtA[i] = 0x50
	}
	if m.KeysWritten != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.KeysWritten))
		i--
		dAtA[i] = 0x48
	}
	if m.BytesRead != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.BytesRead))
		i--
		dAtA[i] = 0x40
	}
	if m.BytesWritten != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.BytesWritten))
		i--
		dAtA[i] = 0x38
	}
	if len(m.PendingPeers) > 0 {
		for iNdEx := len(m.PendingPeers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.PendingPeers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.DownPeers) > 0 {
		for iNdEx := len(m.DownPeers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.DownPeers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.Term != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.Term))
		i--
		dAtA[i] = 0x20
	}
	if m.Leader != nil {
		{
			size, err := m.Leader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RegionHeartbeatResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionHeartbeatResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionHeartbeatResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SwitchWitnesses != nil {
		{
			size, err := m.SwitchWitnesses.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.ChangePeerV2 != nil {
		{
			size, err := m.ChangePeerV2.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.SplitRegion != nil {
		{
			size, err := m.SplitRegion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.Merge != nil {
		{
			size, err := m.Merge.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.TransferLeader != nil {
		{
			size, err := m.TransferLeader.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.ChangePeer != nil {
		{
			size, err := m.ChangePeer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.TargetPeer != nil {
		{
			size, err := m.TargetPeer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.RegionEpoch != nil {
		{
			size, err := m.RegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionId != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ScatterRegionsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScatterRegionsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ScatterRegionsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SkipStoreLimit {
		i--
		if m.SkipStoreLimit {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if m.RetryLimit != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.RetryLimit))
		i--
		dAtA[i] = 0x20
	}
	if len(m.RegionsId) > 0 {
		dAtA20 := make([]byte, len(m.RegionsId)*10)
		var j19 int
		for _, num := range m.RegionsId {
			for num >= 1<<7 {
				dAtA20[j19] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j19++
			}
			dAtA20[j19] = uint8(num)
			j19++
		}
		i -= j19
		copy(dAtA[i:], dAtA20[:j19])
		i = encodeVarintSchedulingpb(dAtA, i, uint64(j19))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Group) > 0 {
		i -= len(m.Group)
		copy(dAtA[i:], m.Group)
		i = encodeVarintSchedulingpb(dAtA, i, uint64(len(m.Group)))
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ScatterRegionsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScatterRegionsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ScatterRegionsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.FinishedPercentage != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.FinishedPercentage))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SplitRegionsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitRegionsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SplitRegionsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RetryLimit != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.RetryLimit))
		i--
		dAtA[i] = 0x18
	}
	if len(m.SplitKeys) > 0 {
		for iNdEx := len(m.SplitKeys) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.SplitKeys[iNdEx])
			copy(dAtA[i:], m.SplitKeys[iNdEx])
			i = encodeVarintSchedulingpb(dAtA, i, uint64(len(m.SplitKeys[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SplitRegionsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SplitRegionsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SplitRegionsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RegionsId) > 0 {
		dAtA25 := make([]byte, len(m.RegionsId)*10)
		var j24 int
		for _, num := range m.RegionsId {
			for num >= 1<<7 {
				dAtA25[j24] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j24++
			}
			dAtA25[j24] = uint8(num)
			j24++
		}
		i -= j24
		copy(dAtA[i:], dAtA25[:j24])
		i = encodeVarintSchedulingpb(dAtA, i, uint64(j24))
		i--
		dAtA[i] = 0x1a
	}
	if m.FinishedPercentage != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.FinishedPercentage))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetOperatorRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperatorRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetOperatorRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetOperatorResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperatorResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetOperatorResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Kind) > 0 {
		i -= len(m.Kind)
		copy(dAtA[i:], m.Kind)
		i = encodeVarintSchedulingpb(dAtA, i, uint64(len(m.Kind)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Status != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.Status))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Desc) > 0 {
		i -= len(m.Desc)
		copy(dAtA[i:], m.Desc)
		i = encodeVarintSchedulingpb(dAtA, i, uint64(len(m.Desc)))
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionId != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AskBatchSplitRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AskBatchSplitRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AskBatchSplitRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SplitCount != 0 {
		i = encodeVarintSchedulingpb(dAtA, i, uint64(m.SplitCount))
		i--
		dAtA[i] = 0x18
	}
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AskBatchSplitResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AskBatchSplitResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AskBatchSplitResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ids) > 0 {
		for iNdEx := len(m.Ids) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ids[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSchedulingpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintSchedulingpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovSchedulingpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *RequestHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovSchedulingpb(uint64(m.ClusterId))
	}
	if m.SenderId != 0 {
		n += 1 + sovSchedulingpb(uint64(m.SenderId))
	}
	return n
}

func (m *ResponseHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovSchedulingpb(uint64(m.ClusterId))
	}
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovSchedulingpb(uint64(m.Type))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	return n
}

func (m *Participant) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Id != 0 {
		n += 1 + sovSchedulingpb(uint64(m.Id))
	}
	if len(m.ListenUrls) > 0 {
		for _, s := range m.ListenUrls {
			l = len(s)
			n += 1 + l + sovSchedulingpb(uint64(l))
		}
	}
	return n
}

func (m *StoreHeartbeatRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Stats != nil {
		l = m.Stats.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	return n
}

func (m *StoreHeartbeatResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	l = len(m.ClusterVersion)
	if l > 0 {
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	return n
}

func (m *RegionHeartbeatRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Leader != nil {
		l = m.Leader.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Term != 0 {
		n += 1 + sovSchedulingpb(uint64(m.Term))
	}
	if len(m.DownPeers) > 0 {
		for _, e := range m.DownPeers {
			l = e.Size()
			n += 1 + l + sovSchedulingpb(uint64(l))
		}
	}
	if len(m.PendingPeers) > 0 {
		for _, e := range m.PendingPeers {
			l = e.Size()
			n += 1 + l + sovSchedulingpb(uint64(l))
		}
	}
	if m.BytesWritten != 0 {
		n += 1 + sovSchedulingpb(uint64(m.BytesWritten))
	}
	if m.BytesRead != 0 {
		n += 1 + sovSchedulingpb(uint64(m.BytesRead))
	}
	if m.KeysWritten != 0 {
		n += 1 + sovSchedulingpb(uint64(m.KeysWritten))
	}
	if m.KeysRead != 0 {
		n += 1 + sovSchedulingpb(uint64(m.KeysRead))
	}
	if m.ApproximateSize != 0 {
		n += 1 + sovSchedulingpb(uint64(m.ApproximateSize))
	}
	if m.ApproximateKeys != 0 {
		n += 1 + sovSchedulingpb(uint64(m.ApproximateKeys))
	}
	if m.QueryStats != nil {
		l = m.QueryStats.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Interval != nil {
		l = m.Interval.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	return n
}

func (m *RegionHeartbeatResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovSchedulingpb(uint64(m.RegionId))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.TargetPeer != nil {
		l = m.TargetPeer.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.ChangePeer != nil {
		l = m.ChangePeer.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.TransferLeader != nil {
		l = m.TransferLeader.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Merge != nil {
		l = m.Merge.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.SplitRegion != nil {
		l = m.SplitRegion.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.ChangePeerV2 != nil {
		l = m.ChangePeerV2.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.SwitchWitnesses != nil {
		l = m.SwitchWitnesses.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	return n
}

func (m *ScatterRegionsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	l = len(m.Group)
	if l > 0 {
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if len(m.RegionsId) > 0 {
		l = 0
		for _, e := range m.RegionsId {
			l += sovSchedulingpb(uint64(e))
		}
		n += 1 + sovSchedulingpb(uint64(l)) + l
	}
	if m.RetryLimit != 0 {
		n += 1 + sovSchedulingpb(uint64(m.RetryLimit))
	}
	if m.SkipStoreLimit {
		n += 2
	}
	return n
}

func (m *ScatterRegionsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.FinishedPercentage != 0 {
		n += 1 + sovSchedulingpb(uint64(m.FinishedPercentage))
	}
	return n
}

func (m *SplitRegionsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if len(m.SplitKeys) > 0 {
		for _, b := range m.SplitKeys {
			l = len(b)
			n += 1 + l + sovSchedulingpb(uint64(l))
		}
	}
	if m.RetryLimit != 0 {
		n += 1 + sovSchedulingpb(uint64(m.RetryLimit))
	}
	return n
}

func (m *SplitRegionsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.FinishedPercentage != 0 {
		n += 1 + sovSchedulingpb(uint64(m.FinishedPercentage))
	}
	if len(m.RegionsId) > 0 {
		l = 0
		for _, e := range m.RegionsId {
			l += sovSchedulingpb(uint64(e))
		}
		n += 1 + sovSchedulingpb(uint64(l)) + l
	}
	return n
}

func (m *GetOperatorRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovSchedulingpb(uint64(m.RegionId))
	}
	return n
}

func (m *GetOperatorResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovSchedulingpb(uint64(m.RegionId))
	}
	l = len(m.Desc)
	if l > 0 {
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Status != 0 {
		n += 1 + sovSchedulingpb(uint64(m.Status))
	}
	l = len(m.Kind)
	if l > 0 {
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	return n
}

func (m *AskBatchSplitRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if m.SplitCount != 0 {
		n += 1 + sovSchedulingpb(uint64(m.SplitCount))
	}
	return n
}

func (m *AskBatchSplitResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovSchedulingpb(uint64(l))
	}
	if len(m.Ids) > 0 {
		for _, e := range m.Ids {
			l = e.Size()
			n += 1 + l + sovSchedulingpb(uint64(l))
		}
	}
	return n
}

func sovSchedulingpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozSchedulingpb(x uint64) (n int) {
	return sovSchedulingpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RequestHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RequestHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RequestHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SenderId", wireType)
			}
			m.SenderId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SenderId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResponseHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResponseHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResponseHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= ErrorType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Participant) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Participant: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Participant: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListenUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ListenUrls = append(m.ListenUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreHeartbeatRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreHeartbeatRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreHeartbeatRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Stats == nil {
				m.Stats = &pdpb.StoreStats{}
			}
			if err := m.Stats.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreHeartbeatResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreHeartbeatResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreHeartbeatResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClusterVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionHeartbeatRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionHeartbeatRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionHeartbeatRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Leader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Leader == nil {
				m.Leader = &metapb.Peer{}
			}
			if err := m.Leader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DownPeers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DownPeers = append(m.DownPeers, &pdpb.PeerStats{})
			if err := m.DownPeers[len(m.DownPeers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PendingPeers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PendingPeers = append(m.PendingPeers, &metapb.Peer{})
			if err := m.PendingPeers[len(m.PendingPeers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesWritten", wireType)
			}
			m.BytesWritten = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BytesWritten |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BytesRead", wireType)
			}
			m.BytesRead = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BytesRead |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeysWritten", wireType)
			}
			m.KeysWritten = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeysWritten |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeysRead", wireType)
			}
			m.KeysRead = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeysRead |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApproximateSize", wireType)
			}
			m.ApproximateSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApproximateSize |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApproximateKeys", wireType)
			}
			m.ApproximateKeys = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApproximateKeys |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueryStats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.QueryStats == nil {
				m.QueryStats = &pdpb.QueryStats{}
			}
			if err := m.QueryStats.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Interval", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Interval == nil {
				m.Interval = &pdpb.TimeInterval{}
			}
			if err := m.Interval.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionHeartbeatResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionHeartbeatResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionHeartbeatResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetPeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TargetPeer == nil {
				m.TargetPeer = &metapb.Peer{}
			}
			if err := m.TargetPeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeer == nil {
				m.ChangePeer = &pdpb.ChangePeer{}
			}
			if err := m.ChangePeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferLeader", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TransferLeader == nil {
				m.TransferLeader = &pdpb.TransferLeader{}
			}
			if err := m.TransferLeader.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Merge", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Merge == nil {
				m.Merge = &pdpb.Merge{}
			}
			if err := m.Merge.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SplitRegion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SplitRegion == nil {
				m.SplitRegion = &pdpb.SplitRegion{}
			}
			if err := m.SplitRegion.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChangePeerV2", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ChangePeerV2 == nil {
				m.ChangePeerV2 = &pdpb.ChangePeerV2{}
			}
			if err := m.ChangePeerV2.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SwitchWitnesses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SwitchWitnesses == nil {
				m.SwitchWitnesses = &pdpb.BatchSwitchWitness{}
			}
			if err := m.SwitchWitnesses.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScatterRegionsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScatterRegionsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScatterRegionsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Group = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSchedulingpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RegionsId = append(m.RegionsId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSchedulingpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSchedulingpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthSchedulingpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.RegionsId) == 0 {
					m.RegionsId = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSchedulingpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RegionsId = append(m.RegionsId, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionsId", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RetryLimit", wireType)
			}
			m.RetryLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RetryLimit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SkipStoreLimit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SkipStoreLimit = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScatterRegionsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ScatterRegionsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ScatterRegionsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FinishedPercentage", wireType)
			}
			m.FinishedPercentage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishedPercentage |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitRegionsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SplitRegionsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SplitRegionsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SplitKeys", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SplitKeys = append(m.SplitKeys, make([]byte, postIndex-iNdEx))
			copy(m.SplitKeys[len(m.SplitKeys)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RetryLimit", wireType)
			}
			m.RetryLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RetryLimit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SplitRegionsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SplitRegionsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SplitRegionsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FinishedPercentage", wireType)
			}
			m.FinishedPercentage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishedPercentage |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSchedulingpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RegionsId = append(m.RegionsId, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSchedulingpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSchedulingpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthSchedulingpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.RegionsId) == 0 {
					m.RegionsId = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSchedulingpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RegionsId = append(m.RegionsId, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionsId", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperatorRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetOperatorRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetOperatorRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperatorResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetOperatorResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetOperatorResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Desc = append(m.Desc[:0], dAtA[iNdEx:postIndex]...)
			if m.Desc == nil {
				m.Desc = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= pdpb.OperatorStatus(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Kind", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Kind = append(m.Kind[:0], dAtA[iNdEx:postIndex]...)
			if m.Kind == nil {
				m.Kind = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AskBatchSplitRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AskBatchSplitRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AskBatchSplitRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SplitCount", wireType)
			}
			m.SplitCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SplitCount |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AskBatchSplitResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AskBatchSplitResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AskBatchSplitResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ids", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ids = append(m.Ids, &pdpb.SplitID{})
			if err := m.Ids[len(m.Ids)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSchedulingpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSchedulingpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipSchedulingpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSchedulingpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSchedulingpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthSchedulingpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupSchedulingpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthSchedulingpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthSchedulingpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSchedulingpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupSchedulingpb = fmt.Errorf("proto: unexpected end of group")
)
