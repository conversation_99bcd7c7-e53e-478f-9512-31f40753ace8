// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: encryptionpb.proto

package encryptionpb

import (
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type EncryptionMethod int32

const (
	EncryptionMethod_UNKNOWN    EncryptionMethod = 0
	EncryptionMethod_PLAINTEXT  EncryptionMethod = 1
	EncryptionMethod_AES128_CTR EncryptionMethod = 2
	EncryptionMethod_AES192_CTR EncryptionMethod = 3
	EncryptionMethod_AES256_CTR EncryptionMethod = 4
	EncryptionMethod_SM4_CTR    EncryptionMethod = 5
)

var EncryptionMethod_name = map[int32]string{
	0: "UNKNOWN",
	1: "PLAINTEXT",
	2: "AES128_CTR",
	3: "AES192_CTR",
	4: "AES256_CTR",
	5: "SM4_CTR",
}

var EncryptionMethod_value = map[string]int32{
	"UNKNOWN":    0,
	"PLAINTEXT":  1,
	"AES128_CTR": 2,
	"AES192_CTR": 3,
	"AES256_CTR": 4,
	"SM4_CTR":    5,
}

func (x EncryptionMethod) String() string {
	return proto.EnumName(EncryptionMethod_name, int32(x))
}

func (EncryptionMethod) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{0}
}

// General encryption metadata for any data type.
type EncryptionMeta struct {
	// ID of the key used to encrypt the data.
	KeyId uint64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// Initialization vector (IV) of the data.
	Iv []byte `protobuf:"bytes,2,opt,name=iv,proto3" json:"iv,omitempty"`
}

func (m *EncryptionMeta) Reset()         { *m = EncryptionMeta{} }
func (m *EncryptionMeta) String() string { return proto.CompactTextString(m) }
func (*EncryptionMeta) ProtoMessage()    {}
func (*EncryptionMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{0}
}
func (m *EncryptionMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EncryptionMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EncryptionMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EncryptionMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EncryptionMeta.Merge(m, src)
}
func (m *EncryptionMeta) XXX_Size() int {
	return m.Size()
}
func (m *EncryptionMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_EncryptionMeta.DiscardUnknown(m)
}

var xxx_messageInfo_EncryptionMeta proto.InternalMessageInfo

func (m *EncryptionMeta) GetKeyId() uint64 {
	if m != nil {
		return m.KeyId
	}
	return 0
}

func (m *EncryptionMeta) GetIv() []byte {
	if m != nil {
		return m.Iv
	}
	return nil
}

// Information about an encrypted file.
type FileInfo struct {
	// ID of the key used to encrypt the file.
	KeyId uint64 `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// Initialization vector (IV) of the file.
	Iv []byte `protobuf:"bytes,2,opt,name=iv,proto3" json:"iv,omitempty"`
	// Method of encryption algorithm used to encrypted the file.
	Method EncryptionMethod `protobuf:"varint,3,opt,name=method,proto3,enum=encryptionpb.EncryptionMethod" json:"method,omitempty"`
}

func (m *FileInfo) Reset()         { *m = FileInfo{} }
func (m *FileInfo) String() string { return proto.CompactTextString(m) }
func (*FileInfo) ProtoMessage()    {}
func (*FileInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{1}
}
func (m *FileInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FileInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FileInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FileInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FileInfo.Merge(m, src)
}
func (m *FileInfo) XXX_Size() int {
	return m.Size()
}
func (m *FileInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FileInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FileInfo proto.InternalMessageInfo

func (m *FileInfo) GetKeyId() uint64 {
	if m != nil {
		return m.KeyId
	}
	return 0
}

func (m *FileInfo) GetIv() []byte {
	if m != nil {
		return m.Iv
	}
	return nil
}

func (m *FileInfo) GetMethod() EncryptionMethod {
	if m != nil {
		return m.Method
	}
	return EncryptionMethod_UNKNOWN
}

type FileDictionary struct {
	// A map of file name to file info.
	Files map[string]*FileInfo `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (m *FileDictionary) Reset()         { *m = FileDictionary{} }
func (m *FileDictionary) String() string { return proto.CompactTextString(m) }
func (*FileDictionary) ProtoMessage()    {}
func (*FileDictionary) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{2}
}
func (m *FileDictionary) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FileDictionary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FileDictionary.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FileDictionary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FileDictionary.Merge(m, src)
}
func (m *FileDictionary) XXX_Size() int {
	return m.Size()
}
func (m *FileDictionary) XXX_DiscardUnknown() {
	xxx_messageInfo_FileDictionary.DiscardUnknown(m)
}

var xxx_messageInfo_FileDictionary proto.InternalMessageInfo

func (m *FileDictionary) GetFiles() map[string]*FileInfo {
	if m != nil {
		return m.Files
	}
	return nil
}

// The key used to encrypt the user data.
type DataKey struct {
	// A sequence of secret bytes used to encrypt data.
	Key []byte `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// Method of encryption algorithm used to encrypted data.
	Method EncryptionMethod `protobuf:"varint,2,opt,name=method,proto3,enum=encryptionpb.EncryptionMethod" json:"method,omitempty"`
	// Creation time of the key.
	CreationTime uint64 `protobuf:"varint,3,opt,name=creation_time,json=creationTime,proto3" json:"creation_time,omitempty"`
	// A flag for the key have ever been exposed.
	WasExposed bool `protobuf:"varint,4,opt,name=was_exposed,json=wasExposed,proto3" json:"was_exposed,omitempty"`
}

func (m *DataKey) Reset()         { *m = DataKey{} }
func (m *DataKey) String() string { return proto.CompactTextString(m) }
func (*DataKey) ProtoMessage()    {}
func (*DataKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{3}
}
func (m *DataKey) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DataKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DataKey.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DataKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataKey.Merge(m, src)
}
func (m *DataKey) XXX_Size() int {
	return m.Size()
}
func (m *DataKey) XXX_DiscardUnknown() {
	xxx_messageInfo_DataKey.DiscardUnknown(m)
}

var xxx_messageInfo_DataKey proto.InternalMessageInfo

func (m *DataKey) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *DataKey) GetMethod() EncryptionMethod {
	if m != nil {
		return m.Method
	}
	return EncryptionMethod_UNKNOWN
}

func (m *DataKey) GetCreationTime() uint64 {
	if m != nil {
		return m.CreationTime
	}
	return 0
}

func (m *DataKey) GetWasExposed() bool {
	if m != nil {
		return m.WasExposed
	}
	return false
}

type KeyDictionary struct {
	// A map of key ID to dat key.
	Keys map[uint64]*DataKey `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// ID of a key currently in use.
	CurrentKeyId uint64 `protobuf:"varint,2,opt,name=current_key_id,json=currentKeyId,proto3" json:"current_key_id,omitempty"`
}

func (m *KeyDictionary) Reset()         { *m = KeyDictionary{} }
func (m *KeyDictionary) String() string { return proto.CompactTextString(m) }
func (*KeyDictionary) ProtoMessage()    {}
func (*KeyDictionary) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{4}
}
func (m *KeyDictionary) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeyDictionary) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeyDictionary.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KeyDictionary) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyDictionary.Merge(m, src)
}
func (m *KeyDictionary) XXX_Size() int {
	return m.Size()
}
func (m *KeyDictionary) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyDictionary.DiscardUnknown(m)
}

var xxx_messageInfo_KeyDictionary proto.InternalMessageInfo

func (m *KeyDictionary) GetKeys() map[uint64]*DataKey {
	if m != nil {
		return m.Keys
	}
	return nil
}

func (m *KeyDictionary) GetCurrentKeyId() uint64 {
	if m != nil {
		return m.CurrentKeyId
	}
	return 0
}

// Master key config.
type MasterKey struct {
	// Types that are valid to be assigned to Backend:
	//	*MasterKey_Plaintext
	//	*MasterKey_File
	//	*MasterKey_Kms
	Backend isMasterKey_Backend `protobuf_oneof:"backend"`
}

func (m *MasterKey) Reset()         { *m = MasterKey{} }
func (m *MasterKey) String() string { return proto.CompactTextString(m) }
func (*MasterKey) ProtoMessage()    {}
func (*MasterKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{5}
}
func (m *MasterKey) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MasterKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MasterKey.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MasterKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterKey.Merge(m, src)
}
func (m *MasterKey) XXX_Size() int {
	return m.Size()
}
func (m *MasterKey) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterKey.DiscardUnknown(m)
}

var xxx_messageInfo_MasterKey proto.InternalMessageInfo

type isMasterKey_Backend interface {
	isMasterKey_Backend()
	MarshalTo([]byte) (int, error)
	Size() int
}

type MasterKey_Plaintext struct {
	Plaintext *MasterKeyPlaintext `protobuf:"bytes,1,opt,name=plaintext,proto3,oneof" json:"plaintext,omitempty"`
}
type MasterKey_File struct {
	File *MasterKeyFile `protobuf:"bytes,2,opt,name=file,proto3,oneof" json:"file,omitempty"`
}
type MasterKey_Kms struct {
	Kms *MasterKeyKms `protobuf:"bytes,3,opt,name=kms,proto3,oneof" json:"kms,omitempty"`
}

func (*MasterKey_Plaintext) isMasterKey_Backend() {}
func (*MasterKey_File) isMasterKey_Backend()      {}
func (*MasterKey_Kms) isMasterKey_Backend()       {}

func (m *MasterKey) GetBackend() isMasterKey_Backend {
	if m != nil {
		return m.Backend
	}
	return nil
}

func (m *MasterKey) GetPlaintext() *MasterKeyPlaintext {
	if x, ok := m.GetBackend().(*MasterKey_Plaintext); ok {
		return x.Plaintext
	}
	return nil
}

func (m *MasterKey) GetFile() *MasterKeyFile {
	if x, ok := m.GetBackend().(*MasterKey_File); ok {
		return x.File
	}
	return nil
}

func (m *MasterKey) GetKms() *MasterKeyKms {
	if x, ok := m.GetBackend().(*MasterKey_Kms); ok {
		return x.Kms
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*MasterKey) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*MasterKey_Plaintext)(nil),
		(*MasterKey_File)(nil),
		(*MasterKey_Kms)(nil),
	}
}

// MasterKeyPlaintext indicates content is stored as plaintext.
type MasterKeyPlaintext struct {
}

func (m *MasterKeyPlaintext) Reset()         { *m = MasterKeyPlaintext{} }
func (m *MasterKeyPlaintext) String() string { return proto.CompactTextString(m) }
func (*MasterKeyPlaintext) ProtoMessage()    {}
func (*MasterKeyPlaintext) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{6}
}
func (m *MasterKeyPlaintext) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MasterKeyPlaintext) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MasterKeyPlaintext.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MasterKeyPlaintext) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterKeyPlaintext.Merge(m, src)
}
func (m *MasterKeyPlaintext) XXX_Size() int {
	return m.Size()
}
func (m *MasterKeyPlaintext) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterKeyPlaintext.DiscardUnknown(m)
}

var xxx_messageInfo_MasterKeyPlaintext proto.InternalMessageInfo

// MasterKeyFile is a master key backed by a file containing encryption key in human-readable
// hex format.
type MasterKeyFile struct {
	// Local file path.
	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
}

func (m *MasterKeyFile) Reset()         { *m = MasterKeyFile{} }
func (m *MasterKeyFile) String() string { return proto.CompactTextString(m) }
func (*MasterKeyFile) ProtoMessage()    {}
func (*MasterKeyFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{7}
}
func (m *MasterKeyFile) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MasterKeyFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MasterKeyFile.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MasterKeyFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterKeyFile.Merge(m, src)
}
func (m *MasterKeyFile) XXX_Size() int {
	return m.Size()
}
func (m *MasterKeyFile) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterKeyFile.DiscardUnknown(m)
}

var xxx_messageInfo_MasterKeyFile proto.InternalMessageInfo

func (m *MasterKeyFile) GetPath() string {
	if m != nil {
		return m.Path
	}
	return ""
}

// MasterKeyKms is a master key backed by KMS service that manages the encryption key,
// and provide API to encrypt and decrypt a data key, which is used to encrypt the content.
type MasterKeyKms struct {
	// KMS vendor.
	Vendor string `protobuf:"bytes,1,opt,name=vendor,proto3" json:"vendor,omitempty"`
	// KMS key id.
	KeyId string `protobuf:"bytes,2,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	// KMS region.
	Region string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	// KMS endpoint. Normally not needed.
	Endpoint string `protobuf:"bytes,4,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	// optional, used to set up azure master key backend
	AzureKms *AzureKms `protobuf:"bytes,5,opt,name=azure_kms,json=azureKms,proto3" json:"azure_kms,omitempty"`
	// optional, used to set up gcp master key backend
	GcpKms *GcpKms `protobuf:"bytes,6,opt,name=gcp_kms,json=gcpKms,proto3" json:"gcp_kms,omitempty"`
	// optional, used to set up aws master key backend
	AwsKms *AwsKms `protobuf:"bytes,7,opt,name=aws_kms,json=awsKms,proto3" json:"aws_kms,omitempty"`
}

func (m *MasterKeyKms) Reset()         { *m = MasterKeyKms{} }
func (m *MasterKeyKms) String() string { return proto.CompactTextString(m) }
func (*MasterKeyKms) ProtoMessage()    {}
func (*MasterKeyKms) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{8}
}
func (m *MasterKeyKms) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MasterKeyKms) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MasterKeyKms.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MasterKeyKms) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterKeyKms.Merge(m, src)
}
func (m *MasterKeyKms) XXX_Size() int {
	return m.Size()
}
func (m *MasterKeyKms) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterKeyKms.DiscardUnknown(m)
}

var xxx_messageInfo_MasterKeyKms proto.InternalMessageInfo

func (m *MasterKeyKms) GetVendor() string {
	if m != nil {
		return m.Vendor
	}
	return ""
}

func (m *MasterKeyKms) GetKeyId() string {
	if m != nil {
		return m.KeyId
	}
	return ""
}

func (m *MasterKeyKms) GetRegion() string {
	if m != nil {
		return m.Region
	}
	return ""
}

func (m *MasterKeyKms) GetEndpoint() string {
	if m != nil {
		return m.Endpoint
	}
	return ""
}

func (m *MasterKeyKms) GetAzureKms() *AzureKms {
	if m != nil {
		return m.AzureKms
	}
	return nil
}

func (m *MasterKeyKms) GetGcpKms() *GcpKms {
	if m != nil {
		return m.GcpKms
	}
	return nil
}

func (m *MasterKeyKms) GetAwsKms() *AwsKms {
	if m != nil {
		return m.AwsKms
	}
	return nil
}

type AzureKms struct {
	TenantId     string `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	ClientId     string `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret string `protobuf:"bytes,3,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	// Key vault to encrypt/decrypt data key.
	KeyVaultUrl string `protobuf:"bytes,4,opt,name=key_vault_url,json=keyVaultUrl,proto3" json:"key_vault_url,omitempty"`
	// optional hsm used to generate data key
	HsmName                   string `protobuf:"bytes,5,opt,name=hsm_name,json=hsmName,proto3" json:"hsm_name,omitempty"`
	HsmUrl                    string `protobuf:"bytes,6,opt,name=hsm_url,json=hsmUrl,proto3" json:"hsm_url,omitempty"`
	ClientCertificate         string `protobuf:"bytes,7,opt,name=client_certificate,json=clientCertificate,proto3" json:"client_certificate,omitempty"`
	ClientCertificatePath     string `protobuf:"bytes,8,opt,name=client_certificate_path,json=clientCertificatePath,proto3" json:"client_certificate_path,omitempty"`
	ClientCertificatePassword string `protobuf:"bytes,9,opt,name=client_certificate_password,json=clientCertificatePassword,proto3" json:"client_certificate_password,omitempty"`
}

func (m *AzureKms) Reset()         { *m = AzureKms{} }
func (m *AzureKms) String() string { return proto.CompactTextString(m) }
func (*AzureKms) ProtoMessage()    {}
func (*AzureKms) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{9}
}
func (m *AzureKms) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AzureKms) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AzureKms.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AzureKms) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AzureKms.Merge(m, src)
}
func (m *AzureKms) XXX_Size() int {
	return m.Size()
}
func (m *AzureKms) XXX_DiscardUnknown() {
	xxx_messageInfo_AzureKms.DiscardUnknown(m)
}

var xxx_messageInfo_AzureKms proto.InternalMessageInfo

func (m *AzureKms) GetTenantId() string {
	if m != nil {
		return m.TenantId
	}
	return ""
}

func (m *AzureKms) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *AzureKms) GetClientSecret() string {
	if m != nil {
		return m.ClientSecret
	}
	return ""
}

func (m *AzureKms) GetKeyVaultUrl() string {
	if m != nil {
		return m.KeyVaultUrl
	}
	return ""
}

func (m *AzureKms) GetHsmName() string {
	if m != nil {
		return m.HsmName
	}
	return ""
}

func (m *AzureKms) GetHsmUrl() string {
	if m != nil {
		return m.HsmUrl
	}
	return ""
}

func (m *AzureKms) GetClientCertificate() string {
	if m != nil {
		return m.ClientCertificate
	}
	return ""
}

func (m *AzureKms) GetClientCertificatePath() string {
	if m != nil {
		return m.ClientCertificatePath
	}
	return ""
}

func (m *AzureKms) GetClientCertificatePassword() string {
	if m != nil {
		return m.ClientCertificatePassword
	}
	return ""
}

type GcpKms struct {
	Credential string `protobuf:"bytes,1,opt,name=credential,proto3" json:"credential,omitempty"`
}

func (m *GcpKms) Reset()         { *m = GcpKms{} }
func (m *GcpKms) String() string { return proto.CompactTextString(m) }
func (*GcpKms) ProtoMessage()    {}
func (*GcpKms) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{10}
}
func (m *GcpKms) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GcpKms) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GcpKms.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GcpKms) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GcpKms.Merge(m, src)
}
func (m *GcpKms) XXX_Size() int {
	return m.Size()
}
func (m *GcpKms) XXX_DiscardUnknown() {
	xxx_messageInfo_GcpKms.DiscardUnknown(m)
}

var xxx_messageInfo_GcpKms proto.InternalMessageInfo

func (m *GcpKms) GetCredential() string {
	if m != nil {
		return m.Credential
	}
	return ""
}

type AwsKms struct {
	AccessKey       string `protobuf:"bytes,1,opt,name=access_key,json=accessKey,proto3" json:"access_key,omitempty"`
	SecretAccessKey string `protobuf:"bytes,2,opt,name=secret_access_key,json=secretAccessKey,proto3" json:"secret_access_key,omitempty"`
}

func (m *AwsKms) Reset()         { *m = AwsKms{} }
func (m *AwsKms) String() string { return proto.CompactTextString(m) }
func (*AwsKms) ProtoMessage()    {}
func (*AwsKms) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{11}
}
func (m *AwsKms) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AwsKms) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AwsKms.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AwsKms) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwsKms.Merge(m, src)
}
func (m *AwsKms) XXX_Size() int {
	return m.Size()
}
func (m *AwsKms) XXX_DiscardUnknown() {
	xxx_messageInfo_AwsKms.DiscardUnknown(m)
}

var xxx_messageInfo_AwsKms proto.InternalMessageInfo

func (m *AwsKms) GetAccessKey() string {
	if m != nil {
		return m.AccessKey
	}
	return ""
}

func (m *AwsKms) GetSecretAccessKey() string {
	if m != nil {
		return m.SecretAccessKey
	}
	return ""
}

type EncryptedContent struct {
	// Metadata of the encrypted content.
	// Eg. IV, method and KMS key ID
	// It is preferred to define new fields for extra metadata than using this metadata map.
	Metadata map[string][]byte `protobuf:"bytes,1,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Encrypted content.
	Content []byte `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// Master key used to encrypt the content.
	MasterKey *MasterKey `protobuf:"bytes,3,opt,name=master_key,json=masterKey,proto3" json:"master_key,omitempty"`
	// Initilization vector (IV) used.
	Iv []byte `protobuf:"bytes,4,opt,name=iv,proto3" json:"iv,omitempty"`
	// Encrypted data key generated by KMS and used to actually encrypt data.
	// Valid only when KMS is used.
	CiphertextKey []byte `protobuf:"bytes,5,opt,name=ciphertext_key,json=ciphertextKey,proto3" json:"ciphertext_key,omitempty"`
}

func (m *EncryptedContent) Reset()         { *m = EncryptedContent{} }
func (m *EncryptedContent) String() string { return proto.CompactTextString(m) }
func (*EncryptedContent) ProtoMessage()    {}
func (*EncryptedContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{12}
}
func (m *EncryptedContent) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EncryptedContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EncryptedContent.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EncryptedContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EncryptedContent.Merge(m, src)
}
func (m *EncryptedContent) XXX_Size() int {
	return m.Size()
}
func (m *EncryptedContent) XXX_DiscardUnknown() {
	xxx_messageInfo_EncryptedContent.DiscardUnknown(m)
}

var xxx_messageInfo_EncryptedContent proto.InternalMessageInfo

func (m *EncryptedContent) GetMetadata() map[string][]byte {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (m *EncryptedContent) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *EncryptedContent) GetMasterKey() *MasterKey {
	if m != nil {
		return m.MasterKey
	}
	return nil
}

func (m *EncryptedContent) GetIv() []byte {
	if m != nil {
		return m.Iv
	}
	return nil
}

func (m *EncryptedContent) GetCiphertextKey() []byte {
	if m != nil {
		return m.CiphertextKey
	}
	return nil
}

type FileEncryptionInfo struct {
	// Types that are valid to be assigned to Mode:
	//	*FileEncryptionInfo_PlainTextDataKey
	//	*FileEncryptionInfo_MasterKeyBased
	Mode isFileEncryptionInfo_Mode `protobuf_oneof:"mode"`
	// file encryption method
	EncryptionMethod EncryptionMethod `protobuf:"varint,3,opt,name=encryption_method,json=encryptionMethod,proto3,enum=encryptionpb.EncryptionMethod" json:"encryption_method,omitempty"`
	// iv to encrypt the file by data key
	FileIv []byte `protobuf:"bytes,4,opt,name=file_iv,json=fileIv,proto3" json:"file_iv,omitempty"`
	// file checksum after encryption, optional if using GCM
	Checksum []byte `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (m *FileEncryptionInfo) Reset()         { *m = FileEncryptionInfo{} }
func (m *FileEncryptionInfo) String() string { return proto.CompactTextString(m) }
func (*FileEncryptionInfo) ProtoMessage()    {}
func (*FileEncryptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{13}
}
func (m *FileEncryptionInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FileEncryptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FileEncryptionInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FileEncryptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FileEncryptionInfo.Merge(m, src)
}
func (m *FileEncryptionInfo) XXX_Size() int {
	return m.Size()
}
func (m *FileEncryptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FileEncryptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FileEncryptionInfo proto.InternalMessageInfo

type isFileEncryptionInfo_Mode interface {
	isFileEncryptionInfo_Mode()
	MarshalTo([]byte) (int, error)
	Size() int
}

type FileEncryptionInfo_PlainTextDataKey struct {
	PlainTextDataKey *PlainTextDataKey `protobuf:"bytes,1,opt,name=plain_text_data_key,json=plainTextDataKey,proto3,oneof" json:"plain_text_data_key,omitempty"`
}
type FileEncryptionInfo_MasterKeyBased struct {
	MasterKeyBased *MasterKeyBased `protobuf:"bytes,2,opt,name=master_key_based,json=masterKeyBased,proto3,oneof" json:"master_key_based,omitempty"`
}

func (*FileEncryptionInfo_PlainTextDataKey) isFileEncryptionInfo_Mode() {}
func (*FileEncryptionInfo_MasterKeyBased) isFileEncryptionInfo_Mode()   {}

func (m *FileEncryptionInfo) GetMode() isFileEncryptionInfo_Mode {
	if m != nil {
		return m.Mode
	}
	return nil
}

func (m *FileEncryptionInfo) GetPlainTextDataKey() *PlainTextDataKey {
	if x, ok := m.GetMode().(*FileEncryptionInfo_PlainTextDataKey); ok {
		return x.PlainTextDataKey
	}
	return nil
}

func (m *FileEncryptionInfo) GetMasterKeyBased() *MasterKeyBased {
	if x, ok := m.GetMode().(*FileEncryptionInfo_MasterKeyBased); ok {
		return x.MasterKeyBased
	}
	return nil
}

func (m *FileEncryptionInfo) GetEncryptionMethod() EncryptionMethod {
	if m != nil {
		return m.EncryptionMethod
	}
	return EncryptionMethod_UNKNOWN
}

func (m *FileEncryptionInfo) GetFileIv() []byte {
	if m != nil {
		return m.FileIv
	}
	return nil
}

func (m *FileEncryptionInfo) GetChecksum() []byte {
	if m != nil {
		return m.Checksum
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*FileEncryptionInfo) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*FileEncryptionInfo_PlainTextDataKey)(nil),
		(*FileEncryptionInfo_MasterKeyBased)(nil),
	}
}

// not recommended in production.
// user needs to pass back the same data key for restore.
type PlainTextDataKey struct {
}

func (m *PlainTextDataKey) Reset()         { *m = PlainTextDataKey{} }
func (m *PlainTextDataKey) String() string { return proto.CompactTextString(m) }
func (*PlainTextDataKey) ProtoMessage()    {}
func (*PlainTextDataKey) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{14}
}
func (m *PlainTextDataKey) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PlainTextDataKey) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PlainTextDataKey.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PlainTextDataKey) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlainTextDataKey.Merge(m, src)
}
func (m *PlainTextDataKey) XXX_Size() int {
	return m.Size()
}
func (m *PlainTextDataKey) XXX_DiscardUnknown() {
	xxx_messageInfo_PlainTextDataKey.DiscardUnknown(m)
}

var xxx_messageInfo_PlainTextDataKey proto.InternalMessageInfo

type MasterKeyBased struct {
	// encrypted data key with metadata
	DataKeyEncryptedContent []*EncryptedContent `protobuf:"bytes,1,rep,name=data_key_encrypted_content,json=dataKeyEncryptedContent,proto3" json:"data_key_encrypted_content,omitempty"`
}

func (m *MasterKeyBased) Reset()         { *m = MasterKeyBased{} }
func (m *MasterKeyBased) String() string { return proto.CompactTextString(m) }
func (*MasterKeyBased) ProtoMessage()    {}
func (*MasterKeyBased) Descriptor() ([]byte, []int) {
	return fileDescriptor_a483860494a778a2, []int{15}
}
func (m *MasterKeyBased) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MasterKeyBased) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MasterKeyBased.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MasterKeyBased) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterKeyBased.Merge(m, src)
}
func (m *MasterKeyBased) XXX_Size() int {
	return m.Size()
}
func (m *MasterKeyBased) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterKeyBased.DiscardUnknown(m)
}

var xxx_messageInfo_MasterKeyBased proto.InternalMessageInfo

func (m *MasterKeyBased) GetDataKeyEncryptedContent() []*EncryptedContent {
	if m != nil {
		return m.DataKeyEncryptedContent
	}
	return nil
}

func init() {
	proto.RegisterEnum("encryptionpb.EncryptionMethod", EncryptionMethod_name, EncryptionMethod_value)
	proto.RegisterType((*EncryptionMeta)(nil), "encryptionpb.EncryptionMeta")
	proto.RegisterType((*FileInfo)(nil), "encryptionpb.FileInfo")
	proto.RegisterType((*FileDictionary)(nil), "encryptionpb.FileDictionary")
	proto.RegisterMapType((map[string]*FileInfo)(nil), "encryptionpb.FileDictionary.FilesEntry")
	proto.RegisterType((*DataKey)(nil), "encryptionpb.DataKey")
	proto.RegisterType((*KeyDictionary)(nil), "encryptionpb.KeyDictionary")
	proto.RegisterMapType((map[uint64]*DataKey)(nil), "encryptionpb.KeyDictionary.KeysEntry")
	proto.RegisterType((*MasterKey)(nil), "encryptionpb.MasterKey")
	proto.RegisterType((*MasterKeyPlaintext)(nil), "encryptionpb.MasterKeyPlaintext")
	proto.RegisterType((*MasterKeyFile)(nil), "encryptionpb.MasterKeyFile")
	proto.RegisterType((*MasterKeyKms)(nil), "encryptionpb.MasterKeyKms")
	proto.RegisterType((*AzureKms)(nil), "encryptionpb.AzureKms")
	proto.RegisterType((*GcpKms)(nil), "encryptionpb.GcpKms")
	proto.RegisterType((*AwsKms)(nil), "encryptionpb.AwsKms")
	proto.RegisterType((*EncryptedContent)(nil), "encryptionpb.EncryptedContent")
	proto.RegisterMapType((map[string][]byte)(nil), "encryptionpb.EncryptedContent.MetadataEntry")
	proto.RegisterType((*FileEncryptionInfo)(nil), "encryptionpb.FileEncryptionInfo")
	proto.RegisterType((*PlainTextDataKey)(nil), "encryptionpb.PlainTextDataKey")
	proto.RegisterType((*MasterKeyBased)(nil), "encryptionpb.MasterKeyBased")
}

func init() { proto.RegisterFile("encryptionpb.proto", fileDescriptor_a483860494a778a2) }

var fileDescriptor_a483860494a778a2 = []byte{
	// 1202 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0x4f, 0x73, 0xdb, 0x44,
	0x14, 0xb7, 0x14, 0x47, 0xb6, 0x9e, 0xff, 0xd4, 0x5d, 0xd2, 0xc6, 0x75, 0xc1, 0x64, 0x54, 0x3a,
	0x78, 0x4a, 0x6b, 0xa6, 0x2e, 0x84, 0x16, 0x06, 0x86, 0xa4, 0x0d, 0xd8, 0x63, 0xe2, 0x66, 0x94,
	0x14, 0x98, 0xe1, 0xa0, 0xd9, 0x48, 0xaf, 0xb6, 0xc6, 0x96, 0xe4, 0x91, 0xd6, 0x4e, 0xcd, 0x99,
	0x0f, 0xc0, 0x11, 0x8e, 0x70, 0xe2, 0x2b, 0x30, 0x5c, 0x38, 0x72, 0xec, 0xb1, 0x47, 0x26, 0xb9,
	0xf0, 0x31, 0x98, 0xdd, 0x95, 0x65, 0x2b, 0x71, 0x18, 0x7a, 0xd2, 0xbe, 0xf7, 0x7e, 0xbf, 0xb7,
	0xbb, 0xbf, 0xdd, 0xf7, 0x56, 0x40, 0xd0, 0xb7, 0xc3, 0xd9, 0x98, 0xb9, 0x81, 0x3f, 0x3e, 0x6e,
	0x8e, 0xc3, 0x80, 0x05, 0xa4, 0xb8, 0xec, 0xab, 0x6d, 0xf4, 0x83, 0x7e, 0x20, 0x02, 0xef, 0xf3,
	0x91, 0xc4, 0xd4, 0xae, 0x84, 0x93, 0x88, 0x89, 0xa1, 0x74, 0x18, 0x1f, 0x41, 0x79, 0x2f, 0xa1,
	0xed, 0x23, 0xa3, 0xe4, 0x1a, 0x68, 0x43, 0x9c, 0x59, 0xae, 0x53, 0x55, 0xb6, 0x94, 0x46, 0xd6,
	0x5c, 0x1f, 0xe2, 0xac, 0xe3, 0x90, 0x32, 0xa8, 0xee, 0xb4, 0xaa, 0x6e, 0x29, 0x8d, 0xa2, 0xa9,
	0xba, 0x53, 0xc3, 0x85, 0xfc, 0x17, 0xee, 0x08, 0x3b, 0xfe, 0xf3, 0xe0, 0x7f, 0x52, 0xc8, 0x36,
	0x68, 0x1e, 0xb2, 0x41, 0xe0, 0x54, 0xd7, 0xb6, 0x94, 0x46, 0xb9, 0x55, 0x6f, 0xa6, 0x76, 0x91,
	0x5a, 0xc7, 0x20, 0x70, 0xcc, 0x18, 0x6d, 0xfc, 0xa2, 0x40, 0x99, 0xcf, 0xf5, 0xc4, 0xb5, 0x79,
	0x94, 0x86, 0x33, 0xf2, 0x29, 0xac, 0x3f, 0x77, 0x47, 0x18, 0x55, 0x95, 0xad, 0xb5, 0x46, 0xa1,
	0xf5, 0x6e, 0x3a, 0x53, 0x1a, 0x2c, 0xcc, 0x68, 0xcf, 0x67, 0xe1, 0xcc, 0x94, 0xac, 0xda, 0x01,
	0xc0, 0xc2, 0x49, 0x2a, 0xb0, 0x36, 0xc4, 0x99, 0x58, 0xbb, 0x6e, 0xf2, 0x21, 0xb9, 0x0b, 0xeb,
	0x53, 0x3a, 0x9a, 0xa0, 0x58, 0x7c, 0xa1, 0x75, 0xfd, 0x62, 0x7a, 0xbe, 0x6f, 0x53, 0x82, 0x3e,
	0x56, 0x1f, 0x2a, 0xc6, 0xcf, 0x0a, 0xe4, 0x9e, 0x50, 0x46, 0xbb, 0x98, 0xca, 0x57, 0x94, 0xf9,
	0x16, 0x3b, 0x57, 0x5f, 0x67, 0xe7, 0xe4, 0x16, 0x94, 0xec, 0x10, 0x29, 0x8f, 0x58, 0xcc, 0xf5,
	0x50, 0x08, 0x97, 0x35, 0x8b, 0x73, 0xe7, 0x91, 0xeb, 0x21, 0x79, 0x1b, 0x0a, 0x27, 0x34, 0xb2,
	0xf0, 0xc5, 0x38, 0x88, 0xd0, 0xa9, 0x66, 0xb7, 0x94, 0x46, 0xde, 0x84, 0x13, 0x1a, 0xed, 0x49,
	0x8f, 0xf1, 0xa7, 0x02, 0xa5, 0x2e, 0xce, 0x96, 0xe4, 0x7b, 0x04, 0xd9, 0x21, 0xce, 0xe6, 0xea,
	0xdd, 0x4e, 0xaf, 0x26, 0x05, 0xe5, 0x56, 0xac, 0x9d, 0xa0, 0x90, 0x77, 0xa0, 0x6c, 0x4f, 0xc2,
	0x10, 0x7d, 0x66, 0xc5, 0x67, 0xae, 0xc6, 0x6b, 0x92, 0xde, 0x2e, 0x3f, 0xfa, 0x5a, 0x0f, 0xf4,
	0x84, 0xb8, 0xac, 0x47, 0x56, 0xea, 0xf1, 0x5e, 0x5a, 0xdf, 0x6b, 0xe9, 0x05, 0xc4, 0x3a, 0x2e,
	0xcb, 0xfb, 0x87, 0x02, 0xfa, 0x3e, 0x8d, 0x18, 0x86, 0x5c, 0xe0, 0xcf, 0x41, 0x1f, 0x8f, 0xa8,
	0xeb, 0x33, 0x7c, 0xc1, 0x44, 0xda, 0x42, 0x6b, 0x2b, 0x9d, 0x22, 0xc1, 0x1e, 0xcc, 0x71, 0xed,
	0x8c, 0xb9, 0x20, 0x91, 0xfb, 0x90, 0xe5, 0x37, 0x21, 0x9e, 0xff, 0xe6, 0x25, 0x64, 0x7e, 0xd0,
	0xed, 0x8c, 0x29, 0xa0, 0xa4, 0x09, 0x6b, 0x43, 0x2f, 0x12, 0x27, 0x50, 0x68, 0xd5, 0x2e, 0x61,
	0x74, 0xbd, 0xa8, 0x9d, 0x31, 0x39, 0x70, 0x57, 0x87, 0xdc, 0x31, 0xb5, 0x87, 0xe8, 0x3b, 0xc6,
	0x06, 0x90, 0x8b, 0x0b, 0x32, 0x6e, 0x41, 0x29, 0x35, 0x13, 0x21, 0x90, 0x1d, 0x53, 0x36, 0x88,
	0x2f, 0xa2, 0x18, 0x1b, 0x3f, 0xa8, 0x50, 0x5c, 0xce, 0x4e, 0xae, 0x83, 0x36, 0x45, 0xdf, 0x09,
	0xc2, 0x18, 0x16, 0x5b, 0x4b, 0x35, 0xa8, 0x0a, 0x7f, 0x5c, 0x83, 0xd7, 0x41, 0x0b, 0xb1, 0xef,
	0x06, 0xbe, 0x58, 0xb8, 0x6e, 0xc6, 0x16, 0xa9, 0x41, 0x1e, 0x7d, 0x67, 0x1c, 0xb8, 0x3e, 0x13,
	0x37, 0x46, 0x37, 0x13, 0x9b, 0x3c, 0x00, 0x9d, 0x7e, 0x3f, 0x09, 0xd1, 0xe2, 0xfb, 0x5d, 0x5f,
	0x55, 0x01, 0x3b, 0x3c, 0xdc, 0xf5, 0x22, 0x33, 0x4f, 0xe3, 0x11, 0xb9, 0x07, 0xb9, 0xbe, 0x3d,
	0x16, 0x14, 0x4d, 0x50, 0x36, 0xd2, 0x94, 0x2f, 0xed, 0x31, 0x27, 0x68, 0x7d, 0xf1, 0xe5, 0x70,
	0x7a, 0x12, 0x09, 0x78, 0x6e, 0x15, 0x7c, 0xe7, 0x24, 0x12, 0x70, 0x2a, 0xbe, 0xc6, 0x3f, 0x2a,
	0xe4, 0xe7, 0x93, 0x92, 0x9b, 0xa0, 0x33, 0xf4, 0xa9, 0xcf, 0xe6, 0x1d, 0x47, 0x37, 0xf3, 0xd2,
	0xd1, 0x71, 0x78, 0xd0, 0x1e, 0xb9, 0x28, 0x83, 0x52, 0x8a, 0xbc, 0x74, 0x74, 0x64, 0x3d, 0xc9,
	0x60, 0x84, 0x76, 0x88, 0x2c, 0x16, 0xa5, 0x28, 0x9d, 0x87, 0xc2, 0x47, 0x0c, 0x28, 0x71, 0x25,
	0xa7, 0x74, 0x32, 0x62, 0xd6, 0x24, 0x1c, 0xc5, 0xfa, 0x14, 0x86, 0x38, 0xfb, 0x9a, 0xfb, 0x9e,
	0x85, 0x23, 0x72, 0x03, 0xf2, 0x83, 0xc8, 0xb3, 0x7c, 0xea, 0xa1, 0x50, 0x48, 0x37, 0x73, 0x83,
	0xc8, 0xeb, 0x51, 0x0f, 0xc9, 0x26, 0xf0, 0xa1, 0x20, 0x6a, 0x52, 0xf2, 0x41, 0xe4, 0x71, 0xce,
	0x3d, 0x20, 0xf1, 0xe4, 0x36, 0x86, 0xcc, 0x7d, 0xee, 0xda, 0x94, 0xa1, 0xd8, 0xbd, 0x6e, 0x5e,
	0x95, 0x91, 0xc7, 0x8b, 0x00, 0xd9, 0x86, 0xcd, 0x8b, 0x70, 0x4b, 0x5c, 0x90, 0xbc, 0xe0, 0x5c,
	0xbb, 0xc0, 0x39, 0xa0, 0x6c, 0x40, 0x3e, 0x83, 0x9b, 0x2b, 0x79, 0x51, 0x74, 0x12, 0x84, 0x4e,
	0x55, 0x17, 0xdc, 0x1b, 0x2b, 0xb8, 0x12, 0x60, 0x34, 0x40, 0x93, 0x67, 0x45, 0xea, 0x00, 0x76,
	0x88, 0x0e, 0xfa, 0xcc, 0xa5, 0xa3, 0x58, 0xe8, 0x25, 0x8f, 0x71, 0x08, 0x9a, 0x3c, 0x26, 0xf2,
	0x16, 0x00, 0xb5, 0x6d, 0x8c, 0x22, 0x6b, 0xd1, 0x48, 0x75, 0xe9, 0xe1, 0xf5, 0x7a, 0x07, 0xae,
	0x4a, 0xbd, 0xad, 0x25, 0x94, 0x3c, 0x9b, 0x2b, 0x32, 0xb0, 0x33, 0xc7, 0x1a, 0xbf, 0xaa, 0x50,
	0x89, 0xfb, 0x21, 0x3a, 0x8f, 0x03, 0x9f, 0xa1, 0xcf, 0x48, 0x1b, 0xf2, 0x1e, 0x32, 0xea, 0x50,
	0x46, 0xe3, 0x9e, 0x75, 0x77, 0x65, 0x07, 0x4d, 0x18, 0xcd, 0xfd, 0x18, 0x2e, 0x5b, 0x57, 0xc2,
	0x26, 0x55, 0xc8, 0xd9, 0x12, 0x12, 0x3f, 0x4c, 0x73, 0x93, 0x6c, 0x03, 0x78, 0xa2, 0xd0, 0xc4,
	0xea, 0x64, 0x99, 0x6f, 0x5e, 0x52, 0xe6, 0xa6, 0xee, 0x25, 0xcd, 0x48, 0xbe, 0x72, 0xd9, 0xe4,
	0x95, 0xbb, 0x0d, 0x65, 0xdb, 0x1d, 0x0f, 0x30, 0xe4, 0x45, 0x2e, 0x72, 0xad, 0x8b, 0x58, 0x69,
	0xe1, 0xed, 0xe2, 0xac, 0xf6, 0x09, 0x94, 0x52, 0x6b, 0x5c, 0xf1, 0x0a, 0x6d, 0x2c, 0x77, 0xc9,
	0xe2, 0x72, 0x3b, 0xfc, 0x5d, 0x05, 0xc2, 0x5b, 0xc6, 0xe2, 0xe1, 0x10, 0xef, 0xf0, 0x53, 0x78,
	0x43, 0xb4, 0x38, 0x4b, 0x4c, 0xcd, 0x53, 0x27, 0xe7, 0x51, 0x38, 0xff, 0xe6, 0x88, 0x3e, 0x74,
	0x84, 0x2f, 0x58, 0xdc, 0x6d, 0xdb, 0x19, 0xb3, 0x32, 0x3e, 0xe7, 0x23, 0x6d, 0xa8, 0x2c, 0x34,
	0xb1, 0x8e, 0x29, 0x7f, 0x5f, 0x64, 0xcb, 0x7c, 0xf3, 0x12, 0x65, 0x76, 0x39, 0xa6, 0x9d, 0x31,
	0xcb, 0x5e, 0xca, 0x43, 0xba, 0x70, 0x75, 0x41, 0xb0, 0x5e, 0xeb, 0x37, 0xa0, 0x82, 0xe7, 0x3c,
	0xbc, 0xc4, 0x78, 0x4b, 0xb6, 0x12, 0xdd, 0x35, 0x6e, 0x76, 0xa6, 0xbc, 0xab, 0xd9, 0x03, 0xb4,
	0x87, 0xd1, 0xc4, 0x8b, 0x55, 0x4f, 0xec, 0x5d, 0x0d, 0xb2, 0x5e, 0xe0, 0xa0, 0x41, 0xa0, 0x72,
	0x7e, 0xef, 0x86, 0x07, 0xe5, 0xf4, 0x0e, 0xc8, 0x77, 0x50, 0x9b, 0xeb, 0x67, 0xe1, 0xfc, 0x72,
	0x59, 0xf3, 0xab, 0x23, 0xef, 0x60, 0xfd, 0xbf, 0xef, 0xa0, 0xb9, 0xe9, 0xc8, 0xf4, 0xe7, 0x03,
	0x77, 0x86, 0xc9, 0x15, 0x5f, 0xec, 0xa9, 0x00, 0xb9, 0x67, 0xbd, 0x6e, 0xef, 0xe9, 0x37, 0xbd,
	0x4a, 0x86, 0x94, 0x40, 0x3f, 0xf8, 0x6a, 0xa7, 0xd3, 0x3b, 0xda, 0xfb, 0xf6, 0xa8, 0xa2, 0x90,
	0x32, 0xc0, 0xce, 0xde, 0xe1, 0xfd, 0xd6, 0x43, 0xeb, 0xf1, 0x91, 0x59, 0x51, 0xe7, 0xf6, 0xa3,
	0x96, 0xb0, 0xd7, 0x62, 0xbb, 0xf5, 0xe1, 0xb6, 0xb0, 0xb3, 0x3c, 0xd7, 0xe1, 0xfe, 0x07, 0xc2,
	0x58, 0xdf, 0x6d, 0xbd, 0xfa, 0x2d, 0xaf, 0xfc, 0x75, 0x5a, 0x57, 0x5e, 0x9e, 0xd6, 0x95, 0xbf,
	0x4f, 0xeb, 0xca, 0x8f, 0x67, 0xf5, 0xcc, 0x4f, 0x67, 0xf5, 0xcc, 0xcb, 0xb3, 0x7a, 0xe6, 0xd5,
	0x59, 0x3d, 0x03, 0x95, 0x20, 0xec, 0x37, 0x99, 0x3b, 0x9c, 0x36, 0x87, 0x53, 0xf1, 0x57, 0x78,
	0xac, 0x89, 0xcf, 0x83, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x97, 0x9d, 0x07, 0xbe, 0x67, 0x0a,
	0x00, 0x00,
}

func (m *EncryptionMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EncryptionMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EncryptionMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Iv) > 0 {
		i -= len(m.Iv)
		copy(dAtA[i:], m.Iv)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Iv)))
		i--
		dAtA[i] = 0x12
	}
	if m.KeyId != 0 {
		i = encodeVarintEncryptionpb(dAtA, i, uint64(m.KeyId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *FileInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FileInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FileInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Method != 0 {
		i = encodeVarintEncryptionpb(dAtA, i, uint64(m.Method))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Iv) > 0 {
		i -= len(m.Iv)
		copy(dAtA[i:], m.Iv)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Iv)))
		i--
		dAtA[i] = 0x12
	}
	if m.KeyId != 0 {
		i = encodeVarintEncryptionpb(dAtA, i, uint64(m.KeyId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *FileDictionary) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FileDictionary) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FileDictionary) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Files) > 0 {
		for k := range m.Files {
			v := m.Files[k]
			baseI := i
			if v != nil {
				{
					size, err := v.MarshalToSizedBuffer(dAtA[:i])
					if err != nil {
						return 0, err
					}
					i -= size
					i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
				}
				i--
				dAtA[i] = 0x12
			}
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintEncryptionpb(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintEncryptionpb(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DataKey) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DataKey) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DataKey) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.WasExposed {
		i--
		if m.WasExposed {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if m.CreationTime != 0 {
		i = encodeVarintEncryptionpb(dAtA, i, uint64(m.CreationTime))
		i--
		dAtA[i] = 0x18
	}
	if m.Method != 0 {
		i = encodeVarintEncryptionpb(dAtA, i, uint64(m.Method))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *KeyDictionary) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeyDictionary) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KeyDictionary) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CurrentKeyId != 0 {
		i = encodeVarintEncryptionpb(dAtA, i, uint64(m.CurrentKeyId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Keys) > 0 {
		for k := range m.Keys {
			v := m.Keys[k]
			baseI := i
			if v != nil {
				{
					size, err := v.MarshalToSizedBuffer(dAtA[:i])
					if err != nil {
						return 0, err
					}
					i -= size
					i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
				}
				i--
				dAtA[i] = 0x12
			}
			i = encodeVarintEncryptionpb(dAtA, i, uint64(k))
			i--
			dAtA[i] = 0x8
			i = encodeVarintEncryptionpb(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MasterKey) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MasterKey) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MasterKey) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Backend != nil {
		{
			size := m.Backend.Size()
			i -= size
			if _, err := m.Backend.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *MasterKey_Plaintext) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MasterKey_Plaintext) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Plaintext != nil {
		{
			size, err := m.Plaintext.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *MasterKey_File) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MasterKey_File) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.File != nil {
		{
			size, err := m.File.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *MasterKey_Kms) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MasterKey_Kms) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Kms != nil {
		{
			size, err := m.Kms.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *MasterKeyPlaintext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MasterKeyPlaintext) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MasterKeyPlaintext) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *MasterKeyFile) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MasterKeyFile) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MasterKeyFile) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Path) > 0 {
		i -= len(m.Path)
		copy(dAtA[i:], m.Path)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Path)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MasterKeyKms) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MasterKeyKms) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MasterKeyKms) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AwsKms != nil {
		{
			size, err := m.AwsKms.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.GcpKms != nil {
		{
			size, err := m.GcpKms.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.AzureKms != nil {
		{
			size, err := m.AzureKms.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Endpoint) > 0 {
		i -= len(m.Endpoint)
		copy(dAtA[i:], m.Endpoint)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Endpoint)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Region) > 0 {
		i -= len(m.Region)
		copy(dAtA[i:], m.Region)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Region)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.KeyId) > 0 {
		i -= len(m.KeyId)
		copy(dAtA[i:], m.KeyId)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.KeyId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Vendor) > 0 {
		i -= len(m.Vendor)
		copy(dAtA[i:], m.Vendor)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Vendor)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AzureKms) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AzureKms) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AzureKms) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ClientCertificatePassword) > 0 {
		i -= len(m.ClientCertificatePassword)
		copy(dAtA[i:], m.ClientCertificatePassword)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.ClientCertificatePassword)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.ClientCertificatePath) > 0 {
		i -= len(m.ClientCertificatePath)
		copy(dAtA[i:], m.ClientCertificatePath)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.ClientCertificatePath)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.ClientCertificate) > 0 {
		i -= len(m.ClientCertificate)
		copy(dAtA[i:], m.ClientCertificate)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.ClientCertificate)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.HsmUrl) > 0 {
		i -= len(m.HsmUrl)
		copy(dAtA[i:], m.HsmUrl)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.HsmUrl)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.HsmName) > 0 {
		i -= len(m.HsmName)
		copy(dAtA[i:], m.HsmName)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.HsmName)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.KeyVaultUrl) > 0 {
		i -= len(m.KeyVaultUrl)
		copy(dAtA[i:], m.KeyVaultUrl)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.KeyVaultUrl)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ClientSecret) > 0 {
		i -= len(m.ClientSecret)
		copy(dAtA[i:], m.ClientSecret)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.ClientSecret)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ClientId) > 0 {
		i -= len(m.ClientId)
		copy(dAtA[i:], m.ClientId)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.ClientId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TenantId) > 0 {
		i -= len(m.TenantId)
		copy(dAtA[i:], m.TenantId)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.TenantId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GcpKms) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GcpKms) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GcpKms) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Credential) > 0 {
		i -= len(m.Credential)
		copy(dAtA[i:], m.Credential)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Credential)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *AwsKms) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwsKms) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AwsKms) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SecretAccessKey) > 0 {
		i -= len(m.SecretAccessKey)
		copy(dAtA[i:], m.SecretAccessKey)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.SecretAccessKey)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.AccessKey) > 0 {
		i -= len(m.AccessKey)
		copy(dAtA[i:], m.AccessKey)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.AccessKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *EncryptedContent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EncryptedContent) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EncryptedContent) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CiphertextKey) > 0 {
		i -= len(m.CiphertextKey)
		copy(dAtA[i:], m.CiphertextKey)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.CiphertextKey)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Iv) > 0 {
		i -= len(m.Iv)
		copy(dAtA[i:], m.Iv)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Iv)))
		i--
		dAtA[i] = 0x22
	}
	if m.MasterKey != nil {
		{
			size, err := m.MasterKey.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Content) > 0 {
		i -= len(m.Content)
		copy(dAtA[i:], m.Content)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Content)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Metadata) > 0 {
		for k := range m.Metadata {
			v := m.Metadata[k]
			baseI := i
			if len(v) > 0 {
				i -= len(v)
				copy(dAtA[i:], v)
				i = encodeVarintEncryptionpb(dAtA, i, uint64(len(v)))
				i--
				dAtA[i] = 0x12
			}
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintEncryptionpb(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintEncryptionpb(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *FileEncryptionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FileEncryptionInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FileEncryptionInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Checksum) > 0 {
		i -= len(m.Checksum)
		copy(dAtA[i:], m.Checksum)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.Checksum)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.FileIv) > 0 {
		i -= len(m.FileIv)
		copy(dAtA[i:], m.FileIv)
		i = encodeVarintEncryptionpb(dAtA, i, uint64(len(m.FileIv)))
		i--
		dAtA[i] = 0x22
	}
	if m.EncryptionMethod != 0 {
		i = encodeVarintEncryptionpb(dAtA, i, uint64(m.EncryptionMethod))
		i--
		dAtA[i] = 0x18
	}
	if m.Mode != nil {
		{
			size := m.Mode.Size()
			i -= size
			if _, err := m.Mode.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *FileEncryptionInfo_PlainTextDataKey) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FileEncryptionInfo_PlainTextDataKey) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.PlainTextDataKey != nil {
		{
			size, err := m.PlainTextDataKey.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *FileEncryptionInfo_MasterKeyBased) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FileEncryptionInfo_MasterKeyBased) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.MasterKeyBased != nil {
		{
			size, err := m.MasterKeyBased.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *PlainTextDataKey) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PlainTextDataKey) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PlainTextDataKey) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *MasterKeyBased) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MasterKeyBased) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MasterKeyBased) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DataKeyEncryptedContent) > 0 {
		for iNdEx := len(m.DataKeyEncryptedContent) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.DataKeyEncryptedContent[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintEncryptionpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintEncryptionpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovEncryptionpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *EncryptionMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.KeyId != 0 {
		n += 1 + sovEncryptionpb(uint64(m.KeyId))
	}
	l = len(m.Iv)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}

func (m *FileInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.KeyId != 0 {
		n += 1 + sovEncryptionpb(uint64(m.KeyId))
	}
	l = len(m.Iv)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	if m.Method != 0 {
		n += 1 + sovEncryptionpb(uint64(m.Method))
	}
	return n
}

func (m *FileDictionary) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Files) > 0 {
		for k, v := range m.Files {
			_ = k
			_ = v
			l = 0
			if v != nil {
				l = v.Size()
				l += 1 + sovEncryptionpb(uint64(l))
			}
			mapEntrySize := 1 + len(k) + sovEncryptionpb(uint64(len(k))) + l
			n += mapEntrySize + 1 + sovEncryptionpb(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *DataKey) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	if m.Method != 0 {
		n += 1 + sovEncryptionpb(uint64(m.Method))
	}
	if m.CreationTime != 0 {
		n += 1 + sovEncryptionpb(uint64(m.CreationTime))
	}
	if m.WasExposed {
		n += 2
	}
	return n
}

func (m *KeyDictionary) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Keys) > 0 {
		for k, v := range m.Keys {
			_ = k
			_ = v
			l = 0
			if v != nil {
				l = v.Size()
				l += 1 + sovEncryptionpb(uint64(l))
			}
			mapEntrySize := 1 + sovEncryptionpb(uint64(k)) + l
			n += mapEntrySize + 1 + sovEncryptionpb(uint64(mapEntrySize))
		}
	}
	if m.CurrentKeyId != 0 {
		n += 1 + sovEncryptionpb(uint64(m.CurrentKeyId))
	}
	return n
}

func (m *MasterKey) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Backend != nil {
		n += m.Backend.Size()
	}
	return n
}

func (m *MasterKey_Plaintext) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Plaintext != nil {
		l = m.Plaintext.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}
func (m *MasterKey_File) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.File != nil {
		l = m.File.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}
func (m *MasterKey_Kms) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Kms != nil {
		l = m.Kms.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}
func (m *MasterKeyPlaintext) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *MasterKeyFile) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Path)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}

func (m *MasterKeyKms) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Vendor)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.KeyId)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.Region)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.Endpoint)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	if m.AzureKms != nil {
		l = m.AzureKms.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	if m.GcpKms != nil {
		l = m.GcpKms.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	if m.AwsKms != nil {
		l = m.AwsKms.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}

func (m *AzureKms) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TenantId)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.ClientId)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.ClientSecret)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.KeyVaultUrl)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.HsmName)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.HsmUrl)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.ClientCertificate)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.ClientCertificatePath)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.ClientCertificatePassword)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}

func (m *GcpKms) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Credential)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}

func (m *AwsKms) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.AccessKey)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.SecretAccessKey)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}

func (m *EncryptedContent) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Metadata) > 0 {
		for k, v := range m.Metadata {
			_ = k
			_ = v
			l = 0
			if len(v) > 0 {
				l = 1 + len(v) + sovEncryptionpb(uint64(len(v)))
			}
			mapEntrySize := 1 + len(k) + sovEncryptionpb(uint64(len(k))) + l
			n += mapEntrySize + 1 + sovEncryptionpb(uint64(mapEntrySize))
		}
	}
	l = len(m.Content)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	if m.MasterKey != nil {
		l = m.MasterKey.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.Iv)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.CiphertextKey)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}

func (m *FileEncryptionInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Mode != nil {
		n += m.Mode.Size()
	}
	if m.EncryptionMethod != 0 {
		n += 1 + sovEncryptionpb(uint64(m.EncryptionMethod))
	}
	l = len(m.FileIv)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	l = len(m.Checksum)
	if l > 0 {
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}

func (m *FileEncryptionInfo_PlainTextDataKey) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PlainTextDataKey != nil {
		l = m.PlainTextDataKey.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}
func (m *FileEncryptionInfo_MasterKeyBased) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MasterKeyBased != nil {
		l = m.MasterKeyBased.Size()
		n += 1 + l + sovEncryptionpb(uint64(l))
	}
	return n
}
func (m *PlainTextDataKey) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *MasterKeyBased) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.DataKeyEncryptedContent) > 0 {
		for _, e := range m.DataKeyEncryptedContent {
			l = e.Size()
			n += 1 + l + sovEncryptionpb(uint64(l))
		}
	}
	return n
}

func sovEncryptionpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozEncryptionpb(x uint64) (n int) {
	return sovEncryptionpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *EncryptionMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EncryptionMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EncryptionMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyId", wireType)
			}
			m.KeyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeyId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Iv", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Iv = append(m.Iv[:0], dAtA[iNdEx:postIndex]...)
			if m.Iv == nil {
				m.Iv = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FileInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FileInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FileInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyId", wireType)
			}
			m.KeyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeyId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Iv", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Iv = append(m.Iv[:0], dAtA[iNdEx:postIndex]...)
			if m.Iv == nil {
				m.Iv = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Method", wireType)
			}
			m.Method = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Method |= EncryptionMethod(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FileDictionary) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FileDictionary: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FileDictionary: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Files", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Files == nil {
				m.Files = make(map[string]*FileInfo)
			}
			var mapkey string
			var mapvalue *FileInfo
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEncryptionpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEncryptionpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEncryptionpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &FileInfo{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipEncryptionpb(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Files[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DataKey) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DataKey: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DataKey: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Method", wireType)
			}
			m.Method = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Method |= EncryptionMethod(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreationTime", wireType)
			}
			m.CreationTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreationTime |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WasExposed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WasExposed = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KeyDictionary) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeyDictionary: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeyDictionary: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keys", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Keys == nil {
				m.Keys = make(map[uint64]*DataKey)
			}
			var mapkey uint64
			var mapvalue *DataKey
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEncryptionpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEncryptionpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEncryptionpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &DataKey{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipEncryptionpb(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Keys[mapkey] = mapvalue
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentKeyId", wireType)
			}
			m.CurrentKeyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentKeyId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MasterKey) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MasterKey: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MasterKey: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Plaintext", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MasterKeyPlaintext{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Backend = &MasterKey_Plaintext{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field File", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MasterKeyFile{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Backend = &MasterKey_File{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Kms", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MasterKeyKms{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Backend = &MasterKey_Kms{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MasterKeyPlaintext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MasterKeyPlaintext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MasterKeyPlaintext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MasterKeyFile) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MasterKeyFile: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MasterKeyFile: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Path", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Path = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MasterKeyKms) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MasterKeyKms: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MasterKeyKms: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Vendor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Vendor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeyId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Region = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Endpoint", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Endpoint = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AzureKms", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AzureKms == nil {
				m.AzureKms = &AzureKms{}
			}
			if err := m.AzureKms.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GcpKms", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GcpKms == nil {
				m.GcpKms = &GcpKms{}
			}
			if err := m.GcpKms.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AwsKms", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AwsKms == nil {
				m.AwsKms = &AwsKms{}
			}
			if err := m.AwsKms.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AzureKms) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AzureKms: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AzureKms: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientSecret", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientSecret = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyVaultUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeyVaultUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HsmName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HsmName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HsmUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HsmUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientCertificate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientCertificate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientCertificatePath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientCertificatePath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientCertificatePassword", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ClientCertificatePassword = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GcpKms) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GcpKms: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GcpKms: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Credential", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Credential = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwsKms) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AwsKms: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AwsKms: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AccessKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AccessKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SecretAccessKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SecretAccessKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EncryptedContent) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EncryptedContent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EncryptedContent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metadata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Metadata == nil {
				m.Metadata = make(map[string][]byte)
			}
			var mapkey string
			mapvalue := []byte{}
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowEncryptionpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEncryptionpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapbyteLen uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowEncryptionpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapbyteLen |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intMapbyteLen := int(mapbyteLen)
					if intMapbyteLen < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					postbytesIndex := iNdEx + intMapbyteLen
					if postbytesIndex < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					if postbytesIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = make([]byte, mapbyteLen)
					copy(mapvalue, dAtA[iNdEx:postbytesIndex])
					iNdEx = postbytesIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipEncryptionpb(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthEncryptionpb
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Metadata[mapkey] = mapvalue
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Content = append(m.Content[:0], dAtA[iNdEx:postIndex]...)
			if m.Content == nil {
				m.Content = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MasterKey", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MasterKey == nil {
				m.MasterKey = &MasterKey{}
			}
			if err := m.MasterKey.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Iv", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Iv = append(m.Iv[:0], dAtA[iNdEx:postIndex]...)
			if m.Iv == nil {
				m.Iv = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CiphertextKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CiphertextKey = append(m.CiphertextKey[:0], dAtA[iNdEx:postIndex]...)
			if m.CiphertextKey == nil {
				m.CiphertextKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FileEncryptionInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FileEncryptionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FileEncryptionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PlainTextDataKey", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &PlainTextDataKey{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Mode = &FileEncryptionInfo_PlainTextDataKey{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MasterKeyBased", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &MasterKeyBased{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Mode = &FileEncryptionInfo_MasterKeyBased{v}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EncryptionMethod", wireType)
			}
			m.EncryptionMethod = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EncryptionMethod |= EncryptionMethod(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileIv", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileIv = append(m.FileIv[:0], dAtA[iNdEx:postIndex]...)
			if m.FileIv == nil {
				m.FileIv = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checksum", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Checksum = append(m.Checksum[:0], dAtA[iNdEx:postIndex]...)
			if m.Checksum == nil {
				m.Checksum = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PlainTextDataKey) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PlainTextDataKey: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PlainTextDataKey: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MasterKeyBased) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MasterKeyBased: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MasterKeyBased: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataKeyEncryptedContent", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DataKeyEncryptedContent = append(m.DataKeyEncryptedContent, &EncryptedContent{})
			if err := m.DataKeyEncryptedContent[len(m.DataKeyEncryptedContent)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEncryptionpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEncryptionpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipEncryptionpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowEncryptionpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEncryptionpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthEncryptionpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupEncryptionpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthEncryptionpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthEncryptionpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowEncryptionpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupEncryptionpb = fmt.Errorf("proto: unexpected end of group")
)
