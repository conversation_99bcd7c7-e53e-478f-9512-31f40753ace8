// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: recoverdatapb.proto

package recover_data

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// request to read region meata from a store
type ReadRegionMetaRequest struct {
	StoreId uint64 `protobuf:"varint,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
}

func (m *ReadRegionMetaRequest) Reset()         { *m = ReadRegionMetaRequest{} }
func (m *ReadRegionMetaRequest) String() string { return proto.CompactTextString(m) }
func (*ReadRegionMetaRequest) ProtoMessage()    {}
func (*ReadRegionMetaRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{0}
}
func (m *ReadRegionMetaRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadRegionMetaRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadRegionMetaRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadRegionMetaRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadRegionMetaRequest.Merge(m, src)
}
func (m *ReadRegionMetaRequest) XXX_Size() int {
	return m.Size()
}
func (m *ReadRegionMetaRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadRegionMetaRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReadRegionMetaRequest proto.InternalMessageInfo

func (m *ReadRegionMetaRequest) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

type Error struct {
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{1}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type RegionMeta struct {
	RegionId    uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	PeerId      uint64 `protobuf:"varint,2,opt,name=peer_id,json=peerId,proto3" json:"peer_id,omitempty"`
	LastLogTerm uint64 `protobuf:"varint,3,opt,name=last_log_term,json=lastLogTerm,proto3" json:"last_log_term,omitempty"`
	LastIndex   uint64 `protobuf:"varint,4,opt,name=last_index,json=lastIndex,proto3" json:"last_index,omitempty"`
	CommitIndex uint64 `protobuf:"varint,5,opt,name=commit_index,json=commitIndex,proto3" json:"commit_index,omitempty"`
	Version     uint64 `protobuf:"varint,6,opt,name=version,proto3" json:"version,omitempty"`
	Tombstone   bool   `protobuf:"varint,7,opt,name=tombstone,proto3" json:"tombstone,omitempty"`
	StartKey    []byte `protobuf:"bytes,8,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey      []byte `protobuf:"bytes,9,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
}

func (m *RegionMeta) Reset()         { *m = RegionMeta{} }
func (m *RegionMeta) String() string { return proto.CompactTextString(m) }
func (*RegionMeta) ProtoMessage()    {}
func (*RegionMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{2}
}
func (m *RegionMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionMeta.Merge(m, src)
}
func (m *RegionMeta) XXX_Size() int {
	return m.Size()
}
func (m *RegionMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionMeta.DiscardUnknown(m)
}

var xxx_messageInfo_RegionMeta proto.InternalMessageInfo

func (m *RegionMeta) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RegionMeta) GetPeerId() uint64 {
	if m != nil {
		return m.PeerId
	}
	return 0
}

func (m *RegionMeta) GetLastLogTerm() uint64 {
	if m != nil {
		return m.LastLogTerm
	}
	return 0
}

func (m *RegionMeta) GetLastIndex() uint64 {
	if m != nil {
		return m.LastIndex
	}
	return 0
}

func (m *RegionMeta) GetCommitIndex() uint64 {
	if m != nil {
		return m.CommitIndex
	}
	return 0
}

func (m *RegionMeta) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *RegionMeta) GetTombstone() bool {
	if m != nil {
		return m.Tombstone
	}
	return false
}

func (m *RegionMeta) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *RegionMeta) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

// command to store for recover region
type RecoverRegionRequest struct {
	RegionId  uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	AsLeader  bool   `protobuf:"varint,2,opt,name=as_leader,json=asLeader,proto3" json:"as_leader,omitempty"`
	Tombstone bool   `protobuf:"varint,3,opt,name=tombstone,proto3" json:"tombstone,omitempty"`
}

func (m *RecoverRegionRequest) Reset()         { *m = RecoverRegionRequest{} }
func (m *RecoverRegionRequest) String() string { return proto.CompactTextString(m) }
func (*RecoverRegionRequest) ProtoMessage()    {}
func (*RecoverRegionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{3}
}
func (m *RecoverRegionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RecoverRegionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RecoverRegionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RecoverRegionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverRegionRequest.Merge(m, src)
}
func (m *RecoverRegionRequest) XXX_Size() int {
	return m.Size()
}
func (m *RecoverRegionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverRegionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverRegionRequest proto.InternalMessageInfo

func (m *RecoverRegionRequest) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RecoverRegionRequest) GetAsLeader() bool {
	if m != nil {
		return m.AsLeader
	}
	return false
}

func (m *RecoverRegionRequest) GetTombstone() bool {
	if m != nil {
		return m.Tombstone
	}
	return false
}

type RecoverRegionResponse struct {
	Error   *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	StoreId uint64 `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
}

func (m *RecoverRegionResponse) Reset()         { *m = RecoverRegionResponse{} }
func (m *RecoverRegionResponse) String() string { return proto.CompactTextString(m) }
func (*RecoverRegionResponse) ProtoMessage()    {}
func (*RecoverRegionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{4}
}
func (m *RecoverRegionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RecoverRegionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RecoverRegionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RecoverRegionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecoverRegionResponse.Merge(m, src)
}
func (m *RecoverRegionResponse) XXX_Size() int {
	return m.Size()
}
func (m *RecoverRegionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecoverRegionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecoverRegionResponse proto.InternalMessageInfo

func (m *RecoverRegionResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *RecoverRegionResponse) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

// wait apply to last index
type WaitApplyRequest struct {
	StoreId uint64 `protobuf:"varint,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
}

func (m *WaitApplyRequest) Reset()         { *m = WaitApplyRequest{} }
func (m *WaitApplyRequest) String() string { return proto.CompactTextString(m) }
func (*WaitApplyRequest) ProtoMessage()    {}
func (*WaitApplyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{5}
}
func (m *WaitApplyRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WaitApplyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WaitApplyRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WaitApplyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WaitApplyRequest.Merge(m, src)
}
func (m *WaitApplyRequest) XXX_Size() int {
	return m.Size()
}
func (m *WaitApplyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WaitApplyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WaitApplyRequest proto.InternalMessageInfo

func (m *WaitApplyRequest) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

type WaitApplyResponse struct {
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *WaitApplyResponse) Reset()         { *m = WaitApplyResponse{} }
func (m *WaitApplyResponse) String() string { return proto.CompactTextString(m) }
func (*WaitApplyResponse) ProtoMessage()    {}
func (*WaitApplyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{6}
}
func (m *WaitApplyResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WaitApplyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WaitApplyResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WaitApplyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WaitApplyResponse.Merge(m, src)
}
func (m *WaitApplyResponse) XXX_Size() int {
	return m.Size()
}
func (m *WaitApplyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WaitApplyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WaitApplyResponse proto.InternalMessageInfo

func (m *WaitApplyResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

// resolve data by resolved_ts
type ResolveKvDataRequest struct {
	ResolvedTs uint64 `protobuf:"varint,1,opt,name=resolved_ts,json=resolvedTs,proto3" json:"resolved_ts,omitempty"`
}

func (m *ResolveKvDataRequest) Reset()         { *m = ResolveKvDataRequest{} }
func (m *ResolveKvDataRequest) String() string { return proto.CompactTextString(m) }
func (*ResolveKvDataRequest) ProtoMessage()    {}
func (*ResolveKvDataRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{7}
}
func (m *ResolveKvDataRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResolveKvDataRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResolveKvDataRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResolveKvDataRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResolveKvDataRequest.Merge(m, src)
}
func (m *ResolveKvDataRequest) XXX_Size() int {
	return m.Size()
}
func (m *ResolveKvDataRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ResolveKvDataRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ResolveKvDataRequest proto.InternalMessageInfo

func (m *ResolveKvDataRequest) GetResolvedTs() uint64 {
	if m != nil {
		return m.ResolvedTs
	}
	return 0
}

type ResolveKvDataResponse struct {
	Error            *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	StoreId          uint64 `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	ResolvedKeyCount uint64 `protobuf:"varint,3,opt,name=resolved_key_count,json=resolvedKeyCount,proto3" json:"resolved_key_count,omitempty"`
	// cursor of delete key.commit_ts, reserved for progress of restore
	// progress is (current_commit_ts - resolved_ts) / (backup_ts - resolved_ts) x 100%
	CurrentCommitTs uint64 `protobuf:"varint,4,opt,name=current_commit_ts,json=currentCommitTs,proto3" json:"current_commit_ts,omitempty"`
}

func (m *ResolveKvDataResponse) Reset()         { *m = ResolveKvDataResponse{} }
func (m *ResolveKvDataResponse) String() string { return proto.CompactTextString(m) }
func (*ResolveKvDataResponse) ProtoMessage()    {}
func (*ResolveKvDataResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_6853a92ee81110ca, []int{8}
}
func (m *ResolveKvDataResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResolveKvDataResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResolveKvDataResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResolveKvDataResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResolveKvDataResponse.Merge(m, src)
}
func (m *ResolveKvDataResponse) XXX_Size() int {
	return m.Size()
}
func (m *ResolveKvDataResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ResolveKvDataResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ResolveKvDataResponse proto.InternalMessageInfo

func (m *ResolveKvDataResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *ResolveKvDataResponse) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *ResolveKvDataResponse) GetResolvedKeyCount() uint64 {
	if m != nil {
		return m.ResolvedKeyCount
	}
	return 0
}

func (m *ResolveKvDataResponse) GetCurrentCommitTs() uint64 {
	if m != nil {
		return m.CurrentCommitTs
	}
	return 0
}

func init() {
	proto.RegisterType((*ReadRegionMetaRequest)(nil), "recover_data.ReadRegionMetaRequest")
	proto.RegisterType((*Error)(nil), "recover_data.Error")
	proto.RegisterType((*RegionMeta)(nil), "recover_data.RegionMeta")
	proto.RegisterType((*RecoverRegionRequest)(nil), "recover_data.RecoverRegionRequest")
	proto.RegisterType((*RecoverRegionResponse)(nil), "recover_data.RecoverRegionResponse")
	proto.RegisterType((*WaitApplyRequest)(nil), "recover_data.WaitApplyRequest")
	proto.RegisterType((*WaitApplyResponse)(nil), "recover_data.WaitApplyResponse")
	proto.RegisterType((*ResolveKvDataRequest)(nil), "recover_data.ResolveKvDataRequest")
	proto.RegisterType((*ResolveKvDataResponse)(nil), "recover_data.ResolveKvDataResponse")
}

func init() { proto.RegisterFile("recoverdatapb.proto", fileDescriptor_6853a92ee81110ca) }

var fileDescriptor_6853a92ee81110ca = []byte{
	// 621 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x54, 0x41, 0x6e, 0xd3, 0x40,
	0x14, 0x8d, 0xd3, 0x36, 0xb1, 0x7f, 0x5a, 0xda, 0x4e, 0x8b, 0x70, 0x53, 0x70, 0x83, 0xbb, 0x09,
	0x08, 0x0a, 0x2a, 0x0b, 0x76, 0x48, 0x50, 0x58, 0x44, 0x29, 0x2c, 0x4c, 0x25, 0x36, 0x20, 0xcb,
	0x8d, 0xbf, 0x2c, 0xab, 0xb1, 0xc7, 0xcc, 0x4c, 0x22, 0x7c, 0x0b, 0x56, 0x88, 0x23, 0x70, 0x03,
	0xae, 0xc0, 0xb2, 0xcb, 0x2e, 0x51, 0xb3, 0xe7, 0x0c, 0x68, 0x66, 0xec, 0xa6, 0x4e, 0x4a, 0x05,
	0x12, 0xbb, 0xf9, 0xef, 0x3d, 0xcf, 0xfc, 0x79, 0xff, 0x8d, 0x61, 0x83, 0xe1, 0x80, 0x8e, 0x91,
	0x85, 0x81, 0x08, 0xb2, 0xe3, 0xbd, 0x8c, 0x51, 0x41, 0xc9, 0x72, 0x01, 0xfa, 0x12, 0x6d, 0x6f,
	0x46, 0x34, 0xa2, 0x8a, 0x78, 0x24, 0x57, 0x5a, 0xd3, 0x5e, 0x65, 0x23, 0x2e, 0xd4, 0x52, 0x03,
	0xee, 0x3e, 0xdc, 0xf4, 0x30, 0x08, 0x3d, 0x8c, 0x62, 0x9a, 0xbe, 0x46, 0x11, 0x78, 0xf8, 0x71,
	0x84, 0x5c, 0x90, 0x2d, 0x30, 0xb9, 0xa0, 0x0c, 0xfd, 0x38, 0xb4, 0x8d, 0x8e, 0xd1, 0x5d, 0xf4,
	0x9a, 0xaa, 0xee, 0x85, 0xee, 0x16, 0x2c, 0xbd, 0x62, 0x8c, 0x32, 0xb2, 0x06, 0x0b, 0x09, 0x8f,
	0x14, 0x6d, 0x79, 0x72, 0xe9, 0x7e, 0xa9, 0x03, 0x4c, 0xf7, 0x22, 0xdb, 0x60, 0x31, 0x55, 0x4d,
	0x77, 0x31, 0x35, 0xd0, 0x0b, 0xc9, 0x2d, 0x68, 0x66, 0x88, 0x4c, 0x52, 0x75, 0x45, 0x35, 0x64,
	0xd9, 0x0b, 0x89, 0x0b, 0x2b, 0xc3, 0x80, 0x0b, 0x7f, 0x48, 0x23, 0x5f, 0x20, 0x4b, 0xec, 0x05,
	0x45, 0xb7, 0x24, 0x78, 0x48, 0xa3, 0x23, 0x64, 0x09, 0xb9, 0x03, 0xa0, 0x34, 0x71, 0x1a, 0xe2,
	0x27, 0x7b, 0x51, 0x09, 0x2c, 0x89, 0xf4, 0x24, 0x40, 0xee, 0xc2, 0xf2, 0x80, 0x26, 0x49, 0x5c,
	0x0a, 0x96, 0xf4, 0x0e, 0x1a, 0xd3, 0x12, 0x1b, 0x9a, 0x63, 0x64, 0x3c, 0xa6, 0xa9, 0xdd, 0xd0,
	0xf7, 0x2b, 0x4a, 0x72, 0x1b, 0x2c, 0x41, 0x93, 0x63, 0x2e, 0x68, 0x8a, 0x76, 0xb3, 0x63, 0x74,
	0x4d, 0x6f, 0x0a, 0xc8, 0x3b, 0x71, 0x11, 0x30, 0xe1, 0x9f, 0x60, 0x6e, 0x9b, 0x1d, 0xa3, 0xbb,
	0xec, 0x99, 0x0a, 0xe8, 0x63, 0x2e, 0xef, 0x84, 0x69, 0xa8, 0x28, 0x4b, 0x51, 0x0d, 0x4c, 0xc3,
	0x3e, 0xe6, 0x6e, 0x0a, 0x9b, 0x9e, 0x1e, 0x8f, 0xb6, 0xa7, 0xb4, 0xf9, 0x5a, 0x87, 0xb6, 0xc1,
	0x0a, 0xb8, 0x3f, 0xc4, 0x20, 0x44, 0xa6, 0x3c, 0x32, 0x3d, 0x33, 0xe0, 0x87, 0xaa, 0xae, 0x76,
	0xb9, 0x30, 0xd3, 0xa5, 0xfb, 0x41, 0xce, 0xb5, 0x72, 0x1e, 0xcf, 0x68, 0xca, 0x91, 0xdc, 0x83,
	0x25, 0x94, 0xc3, 0x53, 0x87, 0xb5, 0xf6, 0x37, 0xf6, 0x2e, 0xa7, 0x66, 0x4f, 0xcd, 0xd5, 0xd3,
	0x8a, 0x4a, 0x04, 0xea, 0xd5, 0x08, 0x3c, 0x84, 0xb5, 0x77, 0x41, 0x2c, 0x9e, 0x67, 0xd9, 0x30,
	0xff, 0x8b, 0xc4, 0x3c, 0x83, 0xf5, 0x4b, 0xf2, 0x7f, 0xee, 0xc4, 0x7d, 0x2a, 0xdd, 0xe3, 0x74,
	0x38, 0xc6, 0xfe, 0xf8, 0x65, 0x30, 0x0d, 0xe9, 0x0e, 0xb4, 0x98, 0xc6, 0x43, 0x5f, 0xf0, 0xe2,
	0x54, 0x28, 0xa1, 0x23, 0xee, 0x7e, 0x37, 0xa4, 0x0f, 0x95, 0x2f, 0xff, 0xa7, 0x0f, 0xe4, 0x01,
	0x90, 0x8b, 0x06, 0x4e, 0x30, 0xf7, 0x07, 0x74, 0x94, 0x8a, 0x22, 0xaf, 0x6b, 0x25, 0xd3, 0xc7,
	0xfc, 0x40, 0xe2, 0xe4, 0x3e, 0xac, 0x0f, 0x46, 0x8c, 0x61, 0x2a, 0xfc, 0x22, 0x9d, 0x82, 0x17,
	0xd9, 0x5d, 0x2d, 0x88, 0x03, 0x85, 0x1f, 0xf1, 0xfd, 0x5f, 0x75, 0x68, 0x15, 0x13, 0x94, 0x7d,
	0x93, 0xb7, 0x70, 0xa3, 0xfa, 0x50, 0xc9, 0x6e, 0xb5, 0xe5, 0x2b, 0x9f, 0x71, 0xdb, 0x9e, 0x15,
	0x95, 0x02, 0xb7, 0xf6, 0xd8, 0x20, 0xef, 0x61, 0xa5, 0x92, 0x12, 0xe2, 0xce, 0xca, 0xe7, 0x23,
	0xdb, 0xde, 0xbd, 0x56, 0xa3, 0xed, 0x75, 0x6b, 0x5d, 0x83, 0xbc, 0x01, 0xeb, 0x62, 0xea, 0xc4,
	0xa9, 0x7e, 0x35, 0x9b, 0x9e, 0xf6, 0xce, 0x1f, 0xf9, 0x72, 0x47, 0xdd, 0xed, 0xa5, 0x59, 0xce,
	0x77, 0x3b, 0x1f, 0x91, 0xf9, 0x6e, 0xaf, 0x08, 0x83, 0xf4, 0xe2, 0x45, 0xe7, 0xec, 0x9b, 0x69,
	0xfc, 0x38, 0x77, 0x8c, 0xd3, 0x73, 0xc7, 0xf8, 0x79, 0xee, 0x18, 0x9f, 0x27, 0x4e, 0xed, 0xeb,
	0xc4, 0xa9, 0x9d, 0x4e, 0x9c, 0xda, 0xd9, 0xc4, 0xa9, 0x1d, 0x37, 0xd4, 0x2f, 0xf3, 0xc9, 0xef,
	0x00, 0x00, 0x00, 0xff, 0xff, 0x2e, 0x55, 0x2b, 0x8c, 0x7e, 0x05, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RecoverDataClient is the client API for RecoverData service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RecoverDataClient interface {
	// read region meta to ready region meta
	ReadRegionMeta(ctx context.Context, in *ReadRegionMetaRequest, opts ...grpc.CallOption) (RecoverData_ReadRegionMetaClient, error)
	// execute the recovery command
	RecoverRegion(ctx context.Context, opts ...grpc.CallOption) (RecoverData_RecoverRegionClient, error)
	// wait all region apply to last index
	WaitApply(ctx context.Context, in *WaitApplyRequest, opts ...grpc.CallOption) (*WaitApplyResponse, error)
	// execute delete data from kv db
	ResolveKvData(ctx context.Context, in *ResolveKvDataRequest, opts ...grpc.CallOption) (RecoverData_ResolveKvDataClient, error)
}

type recoverDataClient struct {
	cc *grpc.ClientConn
}

func NewRecoverDataClient(cc *grpc.ClientConn) RecoverDataClient {
	return &recoverDataClient{cc}
}

func (c *recoverDataClient) ReadRegionMeta(ctx context.Context, in *ReadRegionMetaRequest, opts ...grpc.CallOption) (RecoverData_ReadRegionMetaClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RecoverData_serviceDesc.Streams[0], "/recover_data.RecoverData/ReadRegionMeta", opts...)
	if err != nil {
		return nil, err
	}
	x := &recoverDataReadRegionMetaClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RecoverData_ReadRegionMetaClient interface {
	Recv() (*RegionMeta, error)
	grpc.ClientStream
}

type recoverDataReadRegionMetaClient struct {
	grpc.ClientStream
}

func (x *recoverDataReadRegionMetaClient) Recv() (*RegionMeta, error) {
	m := new(RegionMeta)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *recoverDataClient) RecoverRegion(ctx context.Context, opts ...grpc.CallOption) (RecoverData_RecoverRegionClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RecoverData_serviceDesc.Streams[1], "/recover_data.RecoverData/RecoverRegion", opts...)
	if err != nil {
		return nil, err
	}
	x := &recoverDataRecoverRegionClient{stream}
	return x, nil
}

type RecoverData_RecoverRegionClient interface {
	Send(*RecoverRegionRequest) error
	CloseAndRecv() (*RecoverRegionResponse, error)
	grpc.ClientStream
}

type recoverDataRecoverRegionClient struct {
	grpc.ClientStream
}

func (x *recoverDataRecoverRegionClient) Send(m *RecoverRegionRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *recoverDataRecoverRegionClient) CloseAndRecv() (*RecoverRegionResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(RecoverRegionResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *recoverDataClient) WaitApply(ctx context.Context, in *WaitApplyRequest, opts ...grpc.CallOption) (*WaitApplyResponse, error) {
	out := new(WaitApplyResponse)
	err := c.cc.Invoke(ctx, "/recover_data.RecoverData/WaitApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recoverDataClient) ResolveKvData(ctx context.Context, in *ResolveKvDataRequest, opts ...grpc.CallOption) (RecoverData_ResolveKvDataClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RecoverData_serviceDesc.Streams[2], "/recover_data.RecoverData/ResolveKvData", opts...)
	if err != nil {
		return nil, err
	}
	x := &recoverDataResolveKvDataClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RecoverData_ResolveKvDataClient interface {
	Recv() (*ResolveKvDataResponse, error)
	grpc.ClientStream
}

type recoverDataResolveKvDataClient struct {
	grpc.ClientStream
}

func (x *recoverDataResolveKvDataClient) Recv() (*ResolveKvDataResponse, error) {
	m := new(ResolveKvDataResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RecoverDataServer is the server API for RecoverData service.
type RecoverDataServer interface {
	// read region meta to ready region meta
	ReadRegionMeta(*ReadRegionMetaRequest, RecoverData_ReadRegionMetaServer) error
	// execute the recovery command
	RecoverRegion(RecoverData_RecoverRegionServer) error
	// wait all region apply to last index
	WaitApply(context.Context, *WaitApplyRequest) (*WaitApplyResponse, error)
	// execute delete data from kv db
	ResolveKvData(*ResolveKvDataRequest, RecoverData_ResolveKvDataServer) error
}

// UnimplementedRecoverDataServer can be embedded to have forward compatible implementations.
type UnimplementedRecoverDataServer struct {
}

func (*UnimplementedRecoverDataServer) ReadRegionMeta(req *ReadRegionMetaRequest, srv RecoverData_ReadRegionMetaServer) error {
	return status.Errorf(codes.Unimplemented, "method ReadRegionMeta not implemented")
}
func (*UnimplementedRecoverDataServer) RecoverRegion(srv RecoverData_RecoverRegionServer) error {
	return status.Errorf(codes.Unimplemented, "method RecoverRegion not implemented")
}
func (*UnimplementedRecoverDataServer) WaitApply(ctx context.Context, req *WaitApplyRequest) (*WaitApplyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WaitApply not implemented")
}
func (*UnimplementedRecoverDataServer) ResolveKvData(req *ResolveKvDataRequest, srv RecoverData_ResolveKvDataServer) error {
	return status.Errorf(codes.Unimplemented, "method ResolveKvData not implemented")
}

func RegisterRecoverDataServer(s *grpc.Server, srv RecoverDataServer) {
	s.RegisterService(&_RecoverData_serviceDesc, srv)
}

func _RecoverData_ReadRegionMeta_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ReadRegionMetaRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RecoverDataServer).ReadRegionMeta(m, &recoverDataReadRegionMetaServer{stream})
}

type RecoverData_ReadRegionMetaServer interface {
	Send(*RegionMeta) error
	grpc.ServerStream
}

type recoverDataReadRegionMetaServer struct {
	grpc.ServerStream
}

func (x *recoverDataReadRegionMetaServer) Send(m *RegionMeta) error {
	return x.ServerStream.SendMsg(m)
}

func _RecoverData_RecoverRegion_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(RecoverDataServer).RecoverRegion(&recoverDataRecoverRegionServer{stream})
}

type RecoverData_RecoverRegionServer interface {
	SendAndClose(*RecoverRegionResponse) error
	Recv() (*RecoverRegionRequest, error)
	grpc.ServerStream
}

type recoverDataRecoverRegionServer struct {
	grpc.ServerStream
}

func (x *recoverDataRecoverRegionServer) SendAndClose(m *RecoverRegionResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *recoverDataRecoverRegionServer) Recv() (*RecoverRegionRequest, error) {
	m := new(RecoverRegionRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _RecoverData_WaitApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WaitApplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecoverDataServer).WaitApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/recover_data.RecoverData/WaitApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecoverDataServer).WaitApply(ctx, req.(*WaitApplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecoverData_ResolveKvData_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ResolveKvDataRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RecoverDataServer).ResolveKvData(m, &recoverDataResolveKvDataServer{stream})
}

type RecoverData_ResolveKvDataServer interface {
	Send(*ResolveKvDataResponse) error
	grpc.ServerStream
}

type recoverDataResolveKvDataServer struct {
	grpc.ServerStream
}

func (x *recoverDataResolveKvDataServer) Send(m *ResolveKvDataResponse) error {
	return x.ServerStream.SendMsg(m)
}

var _RecoverData_serviceDesc = grpc.ServiceDesc{
	ServiceName: "recover_data.RecoverData",
	HandlerType: (*RecoverDataServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WaitApply",
			Handler:    _RecoverData_WaitApply_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ReadRegionMeta",
			Handler:       _RecoverData_ReadRegionMeta_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "RecoverRegion",
			Handler:       _RecoverData_RecoverRegion_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "ResolveKvData",
			Handler:       _RecoverData_ResolveKvData_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "recoverdatapb.proto",
}

func (m *ReadRegionMetaRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadRegionMetaRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadRegionMetaRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StoreId != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RegionMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x42
	}
	if m.Tombstone {
		i--
		if m.Tombstone {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x38
	}
	if m.Version != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x30
	}
	if m.CommitIndex != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.CommitIndex))
		i--
		dAtA[i] = 0x28
	}
	if m.LastIndex != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.LastIndex))
		i--
		dAtA[i] = 0x20
	}
	if m.LastLogTerm != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.LastLogTerm))
		i--
		dAtA[i] = 0x18
	}
	if m.PeerId != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.PeerId))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RecoverRegionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecoverRegionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RecoverRegionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Tombstone {
		i--
		if m.Tombstone {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.AsLeader {
		i--
		if m.AsLeader {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RecoverRegionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecoverRegionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RecoverRegionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StoreId != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x10
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRecoverdatapb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WaitApplyRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WaitApplyRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WaitApplyRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StoreId != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *WaitApplyResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WaitApplyResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WaitApplyResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRecoverdatapb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ResolveKvDataRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResolveKvDataRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResolveKvDataRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ResolvedTs != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.ResolvedTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ResolveKvDataResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResolveKvDataResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResolveKvDataResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CurrentCommitTs != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.CurrentCommitTs))
		i--
		dAtA[i] = 0x20
	}
	if m.ResolvedKeyCount != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.ResolvedKeyCount))
		i--
		dAtA[i] = 0x18
	}
	if m.StoreId != 0 {
		i = encodeVarintRecoverdatapb(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x10
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRecoverdatapb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintRecoverdatapb(dAtA []byte, offset int, v uint64) int {
	offset -= sovRecoverdatapb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ReadRegionMetaRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreId != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.StoreId))
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovRecoverdatapb(uint64(l))
	}
	return n
}

func (m *RegionMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.RegionId))
	}
	if m.PeerId != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.PeerId))
	}
	if m.LastLogTerm != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.LastLogTerm))
	}
	if m.LastIndex != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.LastIndex))
	}
	if m.CommitIndex != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.CommitIndex))
	}
	if m.Version != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.Version))
	}
	if m.Tombstone {
		n += 2
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovRecoverdatapb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovRecoverdatapb(uint64(l))
	}
	return n
}

func (m *RecoverRegionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.RegionId))
	}
	if m.AsLeader {
		n += 2
	}
	if m.Tombstone {
		n += 2
	}
	return n
}

func (m *RecoverRegionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovRecoverdatapb(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.StoreId))
	}
	return n
}

func (m *WaitApplyRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreId != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.StoreId))
	}
	return n
}

func (m *WaitApplyResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovRecoverdatapb(uint64(l))
	}
	return n
}

func (m *ResolveKvDataRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ResolvedTs != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.ResolvedTs))
	}
	return n
}

func (m *ResolveKvDataResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovRecoverdatapb(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.StoreId))
	}
	if m.ResolvedKeyCount != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.ResolvedKeyCount))
	}
	if m.CurrentCommitTs != 0 {
		n += 1 + sovRecoverdatapb(uint64(m.CurrentCommitTs))
	}
	return n
}

func sovRecoverdatapb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozRecoverdatapb(x uint64) (n int) {
	return sovRecoverdatapb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ReadRegionMetaRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadRegionMetaRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadRegionMetaRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeerId", wireType)
			}
			m.PeerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastLogTerm", wireType)
			}
			m.LastLogTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastLogTerm |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastIndex", wireType)
			}
			m.LastIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitIndex", wireType)
			}
			m.CommitIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tombstone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Tombstone = bool(v != 0)
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecoverRegionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RecoverRegionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RecoverRegionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AsLeader", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AsLeader = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tombstone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Tombstone = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecoverRegionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RecoverRegionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RecoverRegionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WaitApplyRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WaitApplyRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WaitApplyRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WaitApplyResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WaitApplyResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WaitApplyResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResolveKvDataRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResolveKvDataRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResolveKvDataRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolvedTs", wireType)
			}
			m.ResolvedTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResolvedTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResolveKvDataResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResolveKvDataResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResolveKvDataResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolvedKeyCount", wireType)
			}
			m.ResolvedKeyCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResolvedKeyCount |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentCommitTs", wireType)
			}
			m.CurrentCommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentCommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRecoverdatapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRecoverdatapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipRecoverdatapb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRecoverdatapb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRecoverdatapb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthRecoverdatapb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupRecoverdatapb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthRecoverdatapb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthRecoverdatapb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRecoverdatapb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupRecoverdatapb = fmt.Errorf("proto: unexpected end of group")
)
