// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: enginepb.proto

package enginepb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
	raft_cmdpb "github.com/pingcap/kvproto/pkg/raft_cmdpb"
	raft_serverpb "github.com/pingcap/kvproto/pkg/raft_serverpb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CommandRequestHeader struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	Index    uint64 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Term     uint64 `protobuf:"varint,3,opt,name=term,proto3" json:"term,omitempty"`
	// Flush in-memory data to disk.
	SyncLog bool `protobuf:"varint,4,opt,name=sync_log,json=syncLog,proto3" json:"sync_log,omitempty"`
	// Destroy the region.
	Destroy bool `protobuf:"varint,5,opt,name=destroy,proto3" json:"destroy,omitempty"`
	// Additional information for the request.
	Context []byte `protobuf:"bytes,6,opt,name=context,proto3" json:"context,omitempty"`
}

func (m *CommandRequestHeader) Reset()         { *m = CommandRequestHeader{} }
func (m *CommandRequestHeader) String() string { return proto.CompactTextString(m) }
func (*CommandRequestHeader) ProtoMessage()    {}
func (*CommandRequestHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{0}
}
func (m *CommandRequestHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandRequestHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandRequestHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandRequestHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandRequestHeader.Merge(m, src)
}
func (m *CommandRequestHeader) XXX_Size() int {
	return m.Size()
}
func (m *CommandRequestHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandRequestHeader.DiscardUnknown(m)
}

var xxx_messageInfo_CommandRequestHeader proto.InternalMessageInfo

func (m *CommandRequestHeader) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *CommandRequestHeader) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *CommandRequestHeader) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

func (m *CommandRequestHeader) GetSyncLog() bool {
	if m != nil {
		return m.SyncLog
	}
	return false
}

func (m *CommandRequestHeader) GetDestroy() bool {
	if m != nil {
		return m.Destroy
	}
	return false
}

func (m *CommandRequestHeader) GetContext() []byte {
	if m != nil {
		return m.Context
	}
	return nil
}

type CommandRequest struct {
	Header *CommandRequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// kv put / delete
	Requests []*raft_cmdpb.Request `protobuf:"bytes,2,rep,name=requests,proto3" json:"requests,omitempty"`
	// region metadata manipulation command.
	AdminRequest *raft_cmdpb.AdminRequest `protobuf:"bytes,3,opt,name=admin_request,json=adminRequest,proto3" json:"admin_request,omitempty"`
	// region metadata manipulation result.
	AdminResponse *raft_cmdpb.AdminResponse `protobuf:"bytes,4,opt,name=admin_response,json=adminResponse,proto3" json:"admin_response,omitempty"`
}

func (m *CommandRequest) Reset()         { *m = CommandRequest{} }
func (m *CommandRequest) String() string { return proto.CompactTextString(m) }
func (*CommandRequest) ProtoMessage()    {}
func (*CommandRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{1}
}
func (m *CommandRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandRequest.Merge(m, src)
}
func (m *CommandRequest) XXX_Size() int {
	return m.Size()
}
func (m *CommandRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommandRequest proto.InternalMessageInfo

func (m *CommandRequest) GetHeader() *CommandRequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *CommandRequest) GetRequests() []*raft_cmdpb.Request {
	if m != nil {
		return m.Requests
	}
	return nil
}

func (m *CommandRequest) GetAdminRequest() *raft_cmdpb.AdminRequest {
	if m != nil {
		return m.AdminRequest
	}
	return nil
}

func (m *CommandRequest) GetAdminResponse() *raft_cmdpb.AdminResponse {
	if m != nil {
		return m.AdminResponse
	}
	return nil
}

type CommandRequestBatch struct {
	Requests []*CommandRequest `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
}

func (m *CommandRequestBatch) Reset()         { *m = CommandRequestBatch{} }
func (m *CommandRequestBatch) String() string { return proto.CompactTextString(m) }
func (*CommandRequestBatch) ProtoMessage()    {}
func (*CommandRequestBatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{2}
}
func (m *CommandRequestBatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandRequestBatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandRequestBatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandRequestBatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandRequestBatch.Merge(m, src)
}
func (m *CommandRequestBatch) XXX_Size() int {
	return m.Size()
}
func (m *CommandRequestBatch) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandRequestBatch.DiscardUnknown(m)
}

var xxx_messageInfo_CommandRequestBatch proto.InternalMessageInfo

func (m *CommandRequestBatch) GetRequests() []*CommandRequest {
	if m != nil {
		return m.Requests
	}
	return nil
}

type CommandResponseHeader struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	// Region is destroyed.
	Destroyed bool `protobuf:"varint,2,opt,name=destroyed,proto3" json:"destroyed,omitempty"`
}

func (m *CommandResponseHeader) Reset()         { *m = CommandResponseHeader{} }
func (m *CommandResponseHeader) String() string { return proto.CompactTextString(m) }
func (*CommandResponseHeader) ProtoMessage()    {}
func (*CommandResponseHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{3}
}
func (m *CommandResponseHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandResponseHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandResponseHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandResponseHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandResponseHeader.Merge(m, src)
}
func (m *CommandResponseHeader) XXX_Size() int {
	return m.Size()
}
func (m *CommandResponseHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandResponseHeader.DiscardUnknown(m)
}

var xxx_messageInfo_CommandResponseHeader proto.InternalMessageInfo

func (m *CommandResponseHeader) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *CommandResponseHeader) GetDestroyed() bool {
	if m != nil {
		return m.Destroyed
	}
	return false
}

type CommandResponse struct {
	Header      *CommandResponseHeader        `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ApplyState  *raft_serverpb.RaftApplyState `protobuf:"bytes,2,opt,name=apply_state,json=applyState,proto3" json:"apply_state,omitempty"`
	AppliedTerm uint64                        `protobuf:"varint,3,opt,name=applied_term,json=appliedTerm,proto3" json:"applied_term,omitempty"`
}

func (m *CommandResponse) Reset()         { *m = CommandResponse{} }
func (m *CommandResponse) String() string { return proto.CompactTextString(m) }
func (*CommandResponse) ProtoMessage()    {}
func (*CommandResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{4}
}
func (m *CommandResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandResponse.Merge(m, src)
}
func (m *CommandResponse) XXX_Size() int {
	return m.Size()
}
func (m *CommandResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommandResponse proto.InternalMessageInfo

func (m *CommandResponse) GetHeader() *CommandResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *CommandResponse) GetApplyState() *raft_serverpb.RaftApplyState {
	if m != nil {
		return m.ApplyState
	}
	return nil
}

func (m *CommandResponse) GetAppliedTerm() uint64 {
	if m != nil {
		return m.AppliedTerm
	}
	return 0
}

type CommandResponseBatch struct {
	Responses []*CommandResponse `protobuf:"bytes,1,rep,name=responses,proto3" json:"responses,omitempty"`
}

func (m *CommandResponseBatch) Reset()         { *m = CommandResponseBatch{} }
func (m *CommandResponseBatch) String() string { return proto.CompactTextString(m) }
func (*CommandResponseBatch) ProtoMessage()    {}
func (*CommandResponseBatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{5}
}
func (m *CommandResponseBatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommandResponseBatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommandResponseBatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommandResponseBatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommandResponseBatch.Merge(m, src)
}
func (m *CommandResponseBatch) XXX_Size() int {
	return m.Size()
}
func (m *CommandResponseBatch) XXX_DiscardUnknown() {
	xxx_messageInfo_CommandResponseBatch.DiscardUnknown(m)
}

var xxx_messageInfo_CommandResponseBatch proto.InternalMessageInfo

func (m *CommandResponseBatch) GetResponses() []*CommandResponse {
	if m != nil {
		return m.Responses
	}
	return nil
}

type SnapshotState struct {
	Region     *metapb.Region                `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Peer       *metapb.Peer                  `protobuf:"bytes,2,opt,name=peer,proto3" json:"peer,omitempty"`
	ApplyState *raft_serverpb.RaftApplyState `protobuf:"bytes,3,opt,name=apply_state,json=applyState,proto3" json:"apply_state,omitempty"`
}

func (m *SnapshotState) Reset()         { *m = SnapshotState{} }
func (m *SnapshotState) String() string { return proto.CompactTextString(m) }
func (*SnapshotState) ProtoMessage()    {}
func (*SnapshotState) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{6}
}
func (m *SnapshotState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapshotState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotState.Merge(m, src)
}
func (m *SnapshotState) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotState) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotState.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotState proto.InternalMessageInfo

func (m *SnapshotState) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *SnapshotState) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

func (m *SnapshotState) GetApplyState() *raft_serverpb.RaftApplyState {
	if m != nil {
		return m.ApplyState
	}
	return nil
}

type SnapshotData struct {
	Cf       string                    `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Checksum uint32                    `protobuf:"varint,2,opt,name=checksum,proto3" json:"checksum,omitempty"`
	Data     []*raft_serverpb.KeyValue `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
}

func (m *SnapshotData) Reset()         { *m = SnapshotData{} }
func (m *SnapshotData) String() string { return proto.CompactTextString(m) }
func (*SnapshotData) ProtoMessage()    {}
func (*SnapshotData) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{7}
}
func (m *SnapshotData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapshotData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotData.Merge(m, src)
}
func (m *SnapshotData) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotData) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotData.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotData proto.InternalMessageInfo

func (m *SnapshotData) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *SnapshotData) GetChecksum() uint32 {
	if m != nil {
		return m.Checksum
	}
	return 0
}

func (m *SnapshotData) GetData() []*raft_serverpb.KeyValue {
	if m != nil {
		return m.Data
	}
	return nil
}

type SnapshotRequest struct {
	// Types that are valid to be assigned to Chunk:
	//	*SnapshotRequest_State
	//	*SnapshotRequest_Data
	Chunk isSnapshotRequest_Chunk `protobuf_oneof:"chunk"`
}

func (m *SnapshotRequest) Reset()         { *m = SnapshotRequest{} }
func (m *SnapshotRequest) String() string { return proto.CompactTextString(m) }
func (*SnapshotRequest) ProtoMessage()    {}
func (*SnapshotRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{8}
}
func (m *SnapshotRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapshotRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotRequest.Merge(m, src)
}
func (m *SnapshotRequest) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotRequest proto.InternalMessageInfo

type isSnapshotRequest_Chunk interface {
	isSnapshotRequest_Chunk()
	MarshalTo([]byte) (int, error)
	Size() int
}

type SnapshotRequest_State struct {
	State *SnapshotState `protobuf:"bytes,1,opt,name=state,proto3,oneof" json:"state,omitempty"`
}
type SnapshotRequest_Data struct {
	Data *SnapshotData `protobuf:"bytes,2,opt,name=data,proto3,oneof" json:"data,omitempty"`
}

func (*SnapshotRequest_State) isSnapshotRequest_Chunk() {}
func (*SnapshotRequest_Data) isSnapshotRequest_Chunk()  {}

func (m *SnapshotRequest) GetChunk() isSnapshotRequest_Chunk {
	if m != nil {
		return m.Chunk
	}
	return nil
}

func (m *SnapshotRequest) GetState() *SnapshotState {
	if x, ok := m.GetChunk().(*SnapshotRequest_State); ok {
		return x.State
	}
	return nil
}

func (m *SnapshotRequest) GetData() *SnapshotData {
	if x, ok := m.GetChunk().(*SnapshotRequest_Data); ok {
		return x.Data
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*SnapshotRequest) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*SnapshotRequest_State)(nil),
		(*SnapshotRequest_Data)(nil),
	}
}

type SnapshotDone struct {
}

func (m *SnapshotDone) Reset()         { *m = SnapshotDone{} }
func (m *SnapshotDone) String() string { return proto.CompactTextString(m) }
func (*SnapshotDone) ProtoMessage()    {}
func (*SnapshotDone) Descriptor() ([]byte, []int) {
	return fileDescriptor_33e98a233b10c1b5, []int{9}
}
func (m *SnapshotDone) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotDone) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotDone.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapshotDone) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotDone.Merge(m, src)
}
func (m *SnapshotDone) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotDone) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotDone.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotDone proto.InternalMessageInfo

func init() {
	proto.RegisterType((*CommandRequestHeader)(nil), "enginepb.CommandRequestHeader")
	proto.RegisterType((*CommandRequest)(nil), "enginepb.CommandRequest")
	proto.RegisterType((*CommandRequestBatch)(nil), "enginepb.CommandRequestBatch")
	proto.RegisterType((*CommandResponseHeader)(nil), "enginepb.CommandResponseHeader")
	proto.RegisterType((*CommandResponse)(nil), "enginepb.CommandResponse")
	proto.RegisterType((*CommandResponseBatch)(nil), "enginepb.CommandResponseBatch")
	proto.RegisterType((*SnapshotState)(nil), "enginepb.SnapshotState")
	proto.RegisterType((*SnapshotData)(nil), "enginepb.SnapshotData")
	proto.RegisterType((*SnapshotRequest)(nil), "enginepb.SnapshotRequest")
	proto.RegisterType((*SnapshotDone)(nil), "enginepb.SnapshotDone")
}

func init() { proto.RegisterFile("enginepb.proto", fileDescriptor_33e98a233b10c1b5) }

var fileDescriptor_33e98a233b10c1b5 = []byte{
	// 714 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0xcb, 0x6e, 0xd3, 0x40,
	0x14, 0xcd, 0xe4, 0x55, 0xf7, 0xe6, 0x51, 0x98, 0x16, 0xea, 0x04, 0x6a, 0x82, 0x17, 0x28, 0x12,
	0x28, 0x45, 0x01, 0xd1, 0x15, 0x88, 0x96, 0x87, 0x8a, 0x8a, 0x04, 0x9a, 0x22, 0xc4, 0x2e, 0x9a,
	0xda, 0x93, 0x87, 0x5a, 0x7b, 0x82, 0x3d, 0x41, 0xcd, 0x5f, 0xb0, 0x84, 0x3f, 0x40, 0x08, 0xfe,
	0x83, 0x65, 0x97, 0x5d, 0xa2, 0xe6, 0x2f, 0x58, 0x21, 0xcf, 0x8c, 0x1d, 0x27, 0x4d, 0x24, 0xd8,
	0xcd, 0x3d, 0xf7, 0xde, 0xb9, 0xe7, 0x9c, 0x3b, 0x36, 0x54, 0x99, 0xdf, 0x1b, 0xf8, 0x6c, 0x78,
	0xd4, 0x1a, 0x06, 0x5c, 0x70, 0x6c, 0xc4, 0x71, 0xbd, 0xec, 0x31, 0x41, 0x63, 0xbc, 0x7e, 0x25,
	0xa0, 0x5d, 0xd1, 0x71, 0x3c, 0x37, 0x41, 0xd6, 0x25, 0x12, 0xb2, 0xe0, 0x13, 0x0b, 0x12, 0x70,
	0xa3, 0xc7, 0x7b, 0x5c, 0x1e, 0xb7, 0xa3, 0x93, 0x46, 0xd7, 0x82, 0x51, 0x28, 0xe4, 0x51, 0x01,
	0xf6, 0x0f, 0x04, 0x1b, 0xcf, 0xb8, 0xe7, 0x51, 0xdf, 0x25, 0xec, 0xe3, 0x88, 0x85, 0x62, 0x9f,
	0x51, 0x97, 0x05, 0xf8, 0x06, 0xac, 0x06, 0xac, 0x37, 0xe0, 0x7e, 0x67, 0xe0, 0x9a, 0xa8, 0x81,
	0x9a, 0x79, 0x62, 0x28, 0xe0, 0x95, 0x8b, 0x37, 0xa0, 0x30, 0xf0, 0x5d, 0x76, 0x6a, 0x66, 0x65,
	0x42, 0x05, 0x18, 0x43, 0x5e, 0xb0, 0xc0, 0x33, 0x73, 0x12, 0x94, 0x67, 0x5c, 0x03, 0x23, 0x1c,
	0xfb, 0x4e, 0xe7, 0x84, 0xf7, 0xcc, 0x7c, 0x03, 0x35, 0x0d, 0xb2, 0x12, 0xc5, 0xaf, 0x79, 0x0f,
	0x9b, 0xb0, 0xe2, 0xb2, 0x50, 0x04, 0x7c, 0x6c, 0x16, 0x54, 0x46, 0x87, 0x51, 0xc6, 0xe1, 0xbe,
	0x60, 0xa7, 0xc2, 0x2c, 0x36, 0x50, 0xb3, 0x4c, 0xe2, 0xd0, 0xfe, 0x83, 0xa0, 0x3a, 0x4b, 0x17,
	0x3f, 0x82, 0x62, 0x5f, 0x52, 0x96, 0x2c, 0x4b, 0x6d, 0xab, 0x95, 0x18, 0xb9, 0x48, 0x18, 0xd1,
	0xd5, 0x78, 0x1b, 0x8c, 0x40, 0x25, 0x42, 0x33, 0xdb, 0xc8, 0x35, 0x4b, 0xed, 0xf5, 0x56, 0xca,
	0x5a, 0xdd, 0x44, 0x92, 0x22, 0xfc, 0x18, 0x2a, 0xd4, 0xf5, 0x06, 0x7e, 0x47, 0x23, 0x52, 0x67,
	0xa9, 0x6d, 0xa6, 0xbb, 0x76, 0xa3, 0x82, 0xb8, 0xb5, 0x4c, 0x53, 0x11, 0x7e, 0x0a, 0xd5, 0xb8,
	0x3d, 0x1c, 0x72, 0x3f, 0x64, 0xd2, 0x8f, 0x52, 0xbb, 0xb6, 0xa0, 0x5f, 0x15, 0x90, 0x0a, 0x4d,
	0x87, 0xf6, 0x01, 0xac, 0xcf, 0x2a, 0xda, 0xa3, 0xc2, 0xe9, 0xe3, 0x87, 0x29, 0x21, 0x48, 0x0a,
	0x31, 0x97, 0x59, 0x30, 0x55, 0x63, 0x13, 0xb8, 0x96, 0xe4, 0xd4, 0xfd, 0xff, 0xb2, 0xf8, 0x9b,
	0xb0, 0xaa, 0x97, 0xc4, 0x5c, 0xb9, 0x7c, 0x83, 0x4c, 0x01, 0xfb, 0x27, 0x82, 0xb5, 0xb9, 0x4b,
	0xf1, 0xce, 0xdc, 0x7a, 0x6e, 0x2d, 0xe0, 0x96, 0x9e, 0x9f, 0xec, 0xe7, 0x09, 0x94, 0xe8, 0x70,
	0x78, 0x32, 0xee, 0x84, 0x82, 0x0a, 0x26, 0x87, 0x95, 0xda, 0x5b, 0xad, 0xd9, 0xb7, 0x4e, 0x68,
	0x57, 0xec, 0x46, 0x55, 0x87, 0x51, 0x11, 0x01, 0x9a, 0x9c, 0xf1, 0x6d, 0x28, 0x47, 0xd1, 0x80,
	0xb9, 0x9d, 0xd4, 0xab, 0x2c, 0x69, 0xec, 0x1d, 0x0b, 0x3c, 0xfb, 0x4d, 0xea, 0xed, 0x2b, 0x0e,
	0xca, 0xd1, 0x9d, 0xc8, 0x02, 0x05, 0xc4, 0x96, 0xd6, 0x96, 0xd2, 0x26, 0xd3, 0x5a, 0xfb, 0x2b,
	0x82, 0xca, 0xa1, 0x4f, 0x87, 0x61, 0x9f, 0x0b, 0xc5, 0xe2, 0x0e, 0x14, 0x95, 0x79, 0x5a, 0x7e,
	0xb5, 0xa5, 0x3f, 0x66, 0x22, 0x51, 0xa2, 0xb3, 0xb8, 0x01, 0xf9, 0x21, 0x63, 0x81, 0x96, 0x59,
	0x8e, 0xab, 0xde, 0x32, 0x16, 0x10, 0x99, 0x99, 0xf7, 0x23, 0xf7, 0x9f, 0x7e, 0xd8, 0x3d, 0x28,
	0xc7, 0xd4, 0x9e, 0x53, 0x41, 0x71, 0x15, 0xb2, 0x4e, 0x57, 0xb2, 0x5a, 0x25, 0x59, 0xa7, 0x8b,
	0xeb, 0x60, 0x38, 0x7d, 0xe6, 0x1c, 0x87, 0x23, 0x4f, 0xb2, 0xa8, 0x90, 0x24, 0xc6, 0x77, 0x21,
	0xef, 0x52, 0x41, 0xcd, 0x9c, 0xf4, 0x62, 0x73, 0x6e, 0xe8, 0x01, 0x1b, 0xbf, 0xa7, 0x27, 0x23,
	0x46, 0x64, 0x91, 0x3d, 0x86, 0xb5, 0x78, 0x50, 0xfc, 0xf6, 0xb7, 0xa1, 0xa0, 0x58, 0x2b, 0x13,
	0x36, 0xa7, 0x66, 0xce, 0xb8, 0xb5, 0x9f, 0x21, 0xaa, 0x0e, 0xdf, 0xd3, 0x03, 0x95, 0x1d, 0xd7,
	0x2f, 0xd7, 0x47, 0x12, 0xf6, 0x33, 0x6a, 0xe2, 0xde, 0x0a, 0x14, 0x9c, 0xfe, 0xc8, 0x3f, 0xb6,
	0xab, 0x29, 0x8d, 0xdc, 0x67, 0xed, 0xef, 0x08, 0x8a, 0x2f, 0x64, 0x2b, 0xfe, 0x00, 0x57, 0xa5,
	0x31, 0x7a, 0x7b, 0x6a, 0xd1, 0x5b, 0xcb, 0x3e, 0x14, 0x99, 0xae, 0x5b, 0x4b, 0x97, 0x2e, 0xf3,
	0x76, 0xa6, 0x89, 0xee, 0x23, 0xfc, 0x12, 0x2a, 0xca, 0x72, 0x3d, 0x19, 0xd7, 0x2e, 0xd3, 0xd5,
	0xd7, 0xd6, 0x17, 0x29, 0xe1, 0x3e, 0x8b, 0x6e, 0xda, 0x6b, 0x9c, 0x7f, 0x33, 0xd0, 0xaf, 0x0b,
	0x0b, 0x9d, 0x5d, 0x58, 0xe8, 0xf7, 0x85, 0x85, 0x3e, 0x4f, 0xac, 0xcc, 0x97, 0x89, 0x95, 0x39,
	0x9b, 0x58, 0x99, 0xf3, 0x89, 0x95, 0x39, 0x2a, 0xca, 0x7f, 0xf6, 0x83, 0xbf, 0x01, 0x00, 0x00,
	0xff, 0xff, 0xb2, 0x10, 0x55, 0xc5, 0x2b, 0x06, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EngineClient is the client API for Engine service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EngineClient interface {
	ApplyCommandBatch(ctx context.Context, opts ...grpc.CallOption) (Engine_ApplyCommandBatchClient, error)
	ApplySnapshot(ctx context.Context, opts ...grpc.CallOption) (Engine_ApplySnapshotClient, error)
}

type engineClient struct {
	cc *grpc.ClientConn
}

func NewEngineClient(cc *grpc.ClientConn) EngineClient {
	return &engineClient{cc}
}

func (c *engineClient) ApplyCommandBatch(ctx context.Context, opts ...grpc.CallOption) (Engine_ApplyCommandBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Engine_serviceDesc.Streams[0], "/enginepb.Engine/ApplyCommandBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &engineApplyCommandBatchClient{stream}
	return x, nil
}

type Engine_ApplyCommandBatchClient interface {
	Send(*CommandRequestBatch) error
	Recv() (*CommandResponseBatch, error)
	grpc.ClientStream
}

type engineApplyCommandBatchClient struct {
	grpc.ClientStream
}

func (x *engineApplyCommandBatchClient) Send(m *CommandRequestBatch) error {
	return x.ClientStream.SendMsg(m)
}

func (x *engineApplyCommandBatchClient) Recv() (*CommandResponseBatch, error) {
	m := new(CommandResponseBatch)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *engineClient) ApplySnapshot(ctx context.Context, opts ...grpc.CallOption) (Engine_ApplySnapshotClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Engine_serviceDesc.Streams[1], "/enginepb.Engine/ApplySnapshot", opts...)
	if err != nil {
		return nil, err
	}
	x := &engineApplySnapshotClient{stream}
	return x, nil
}

type Engine_ApplySnapshotClient interface {
	Send(*SnapshotRequest) error
	CloseAndRecv() (*SnapshotDone, error)
	grpc.ClientStream
}

type engineApplySnapshotClient struct {
	grpc.ClientStream
}

func (x *engineApplySnapshotClient) Send(m *SnapshotRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *engineApplySnapshotClient) CloseAndRecv() (*SnapshotDone, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(SnapshotDone)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// EngineServer is the server API for Engine service.
type EngineServer interface {
	ApplyCommandBatch(Engine_ApplyCommandBatchServer) error
	ApplySnapshot(Engine_ApplySnapshotServer) error
}

// UnimplementedEngineServer can be embedded to have forward compatible implementations.
type UnimplementedEngineServer struct {
}

func (*UnimplementedEngineServer) ApplyCommandBatch(srv Engine_ApplyCommandBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method ApplyCommandBatch not implemented")
}
func (*UnimplementedEngineServer) ApplySnapshot(srv Engine_ApplySnapshotServer) error {
	return status.Errorf(codes.Unimplemented, "method ApplySnapshot not implemented")
}

func RegisterEngineServer(s *grpc.Server, srv EngineServer) {
	s.RegisterService(&_Engine_serviceDesc, srv)
}

func _Engine_ApplyCommandBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(EngineServer).ApplyCommandBatch(&engineApplyCommandBatchServer{stream})
}

type Engine_ApplyCommandBatchServer interface {
	Send(*CommandResponseBatch) error
	Recv() (*CommandRequestBatch, error)
	grpc.ServerStream
}

type engineApplyCommandBatchServer struct {
	grpc.ServerStream
}

func (x *engineApplyCommandBatchServer) Send(m *CommandResponseBatch) error {
	return x.ServerStream.SendMsg(m)
}

func (x *engineApplyCommandBatchServer) Recv() (*CommandRequestBatch, error) {
	m := new(CommandRequestBatch)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Engine_ApplySnapshot_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(EngineServer).ApplySnapshot(&engineApplySnapshotServer{stream})
}

type Engine_ApplySnapshotServer interface {
	SendAndClose(*SnapshotDone) error
	Recv() (*SnapshotRequest, error)
	grpc.ServerStream
}

type engineApplySnapshotServer struct {
	grpc.ServerStream
}

func (x *engineApplySnapshotServer) SendAndClose(m *SnapshotDone) error {
	return x.ServerStream.SendMsg(m)
}

func (x *engineApplySnapshotServer) Recv() (*SnapshotRequest, error) {
	m := new(SnapshotRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _Engine_serviceDesc = grpc.ServiceDesc{
	ServiceName: "enginepb.Engine",
	HandlerType: (*EngineServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ApplyCommandBatch",
			Handler:       _Engine_ApplyCommandBatch_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "ApplySnapshot",
			Handler:       _Engine_ApplySnapshot_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "enginepb.proto",
}

func (m *CommandRequestHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandRequestHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandRequestHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Context) > 0 {
		i -= len(m.Context)
		copy(dAtA[i:], m.Context)
		i = encodeVarintEnginepb(dAtA, i, uint64(len(m.Context)))
		i--
		dAtA[i] = 0x32
	}
	if m.Destroy {
		i--
		if m.Destroy {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if m.SyncLog {
		i--
		if m.SyncLog {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if m.Term != 0 {
		i = encodeVarintEnginepb(dAtA, i, uint64(m.Term))
		i--
		dAtA[i] = 0x18
	}
	if m.Index != 0 {
		i = encodeVarintEnginepb(dAtA, i, uint64(m.Index))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintEnginepb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CommandRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AdminResponse != nil {
		{
			size, err := m.AdminResponse.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.AdminRequest != nil {
		{
			size, err := m.AdminRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Requests) > 0 {
		for iNdEx := len(m.Requests) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Requests[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintEnginepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CommandRequestBatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandRequestBatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandRequestBatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Requests) > 0 {
		for iNdEx := len(m.Requests) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Requests[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintEnginepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CommandResponseHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandResponseHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandResponseHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Destroyed {
		i--
		if m.Destroyed {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintEnginepb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CommandResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AppliedTerm != 0 {
		i = encodeVarintEnginepb(dAtA, i, uint64(m.AppliedTerm))
		i--
		dAtA[i] = 0x18
	}
	if m.ApplyState != nil {
		{
			size, err := m.ApplyState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CommandResponseBatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommandResponseBatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommandResponseBatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Responses) > 0 {
		for iNdEx := len(m.Responses) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Responses[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintEnginepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SnapshotState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ApplyState != nil {
		{
			size, err := m.ApplyState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Peer != nil {
		{
			size, err := m.Peer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SnapshotData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		for iNdEx := len(m.Data) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Data[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintEnginepb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Checksum != 0 {
		i = encodeVarintEnginepb(dAtA, i, uint64(m.Checksum))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintEnginepb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SnapshotRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Chunk != nil {
		{
			size := m.Chunk.Size()
			i -= size
			if _, err := m.Chunk.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *SnapshotRequest_State) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotRequest_State) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.State != nil {
		{
			size, err := m.State.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *SnapshotRequest_Data) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotRequest_Data) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Data != nil {
		{
			size, err := m.Data.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintEnginepb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *SnapshotDone) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotDone) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotDone) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func encodeVarintEnginepb(dAtA []byte, offset int, v uint64) int {
	offset -= sovEnginepb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *CommandRequestHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovEnginepb(uint64(m.RegionId))
	}
	if m.Index != 0 {
		n += 1 + sovEnginepb(uint64(m.Index))
	}
	if m.Term != 0 {
		n += 1 + sovEnginepb(uint64(m.Term))
	}
	if m.SyncLog {
		n += 2
	}
	if m.Destroy {
		n += 2
	}
	l = len(m.Context)
	if l > 0 {
		n += 1 + l + sovEnginepb(uint64(l))
	}
	return n
}

func (m *CommandRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	if len(m.Requests) > 0 {
		for _, e := range m.Requests {
			l = e.Size()
			n += 1 + l + sovEnginepb(uint64(l))
		}
	}
	if m.AdminRequest != nil {
		l = m.AdminRequest.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	if m.AdminResponse != nil {
		l = m.AdminResponse.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	return n
}

func (m *CommandRequestBatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Requests) > 0 {
		for _, e := range m.Requests {
			l = e.Size()
			n += 1 + l + sovEnginepb(uint64(l))
		}
	}
	return n
}

func (m *CommandResponseHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovEnginepb(uint64(m.RegionId))
	}
	if m.Destroyed {
		n += 2
	}
	return n
}

func (m *CommandResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	if m.ApplyState != nil {
		l = m.ApplyState.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	if m.AppliedTerm != 0 {
		n += 1 + sovEnginepb(uint64(m.AppliedTerm))
	}
	return n
}

func (m *CommandResponseBatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Responses) > 0 {
		for _, e := range m.Responses {
			l = e.Size()
			n += 1 + l + sovEnginepb(uint64(l))
		}
	}
	return n
}

func (m *SnapshotState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	if m.ApplyState != nil {
		l = m.ApplyState.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	return n
}

func (m *SnapshotData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovEnginepb(uint64(l))
	}
	if m.Checksum != 0 {
		n += 1 + sovEnginepb(uint64(m.Checksum))
	}
	if len(m.Data) > 0 {
		for _, e := range m.Data {
			l = e.Size()
			n += 1 + l + sovEnginepb(uint64(l))
		}
	}
	return n
}

func (m *SnapshotRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Chunk != nil {
		n += m.Chunk.Size()
	}
	return n
}

func (m *SnapshotRequest_State) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.State != nil {
		l = m.State.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	return n
}
func (m *SnapshotRequest_Data) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovEnginepb(uint64(l))
	}
	return n
}
func (m *SnapshotDone) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func sovEnginepb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozEnginepb(x uint64) (n int) {
	return sovEnginepb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *CommandRequestHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandRequestHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandRequestHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SyncLog", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SyncLog = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Destroy", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Destroy = bool(v != 0)
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Context = append(m.Context[:0], dAtA[iNdEx:postIndex]...)
			if m.Context == nil {
				m.Context = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &CommandRequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Requests", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Requests = append(m.Requests, &raft_cmdpb.Request{})
			if err := m.Requests[len(m.Requests)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdminRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdminRequest == nil {
				m.AdminRequest = &raft_cmdpb.AdminRequest{}
			}
			if err := m.AdminRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AdminResponse", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AdminResponse == nil {
				m.AdminResponse = &raft_cmdpb.AdminResponse{}
			}
			if err := m.AdminResponse.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandRequestBatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandRequestBatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandRequestBatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Requests", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Requests = append(m.Requests, &CommandRequest{})
			if err := m.Requests[len(m.Requests)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandResponseHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandResponseHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandResponseHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Destroyed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Destroyed = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &CommandResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApplyState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ApplyState == nil {
				m.ApplyState = &raft_serverpb.RaftApplyState{}
			}
			if err := m.ApplyState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppliedTerm", wireType)
			}
			m.AppliedTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppliedTerm |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommandResponseBatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommandResponseBatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommandResponseBatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Responses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Responses = append(m.Responses, &CommandResponse{})
			if err := m.Responses[len(m.Responses)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApplyState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ApplyState == nil {
				m.ApplyState = &raft_serverpb.RaftApplyState{}
			}
			if err := m.ApplyState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checksum", wireType)
			}
			m.Checksum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Checksum |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data, &raft_serverpb.KeyValue{})
			if err := m.Data[len(m.Data)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &SnapshotState{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &SnapshotRequest_State{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthEnginepb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthEnginepb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &SnapshotData{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &SnapshotRequest_Data{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotDone) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotDone: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotDone: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipEnginepb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthEnginepb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipEnginepb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowEnginepb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowEnginepb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthEnginepb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupEnginepb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthEnginepb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthEnginepb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowEnginepb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupEnginepb = fmt.Errorf("proto: unexpected end of group")
)
