// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: disk_usage.proto

package disk_usage

import (
	"fmt"
	"math"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DiskUsage int32

const (
	DiskUsage_Normal      DiskUsage = 0
	DiskUsage_AlmostFull  DiskUsage = 1
	DiskUsage_AlreadyFull DiskUsage = 2
)

var DiskUsage_name = map[int32]string{
	0: "Normal",
	1: "AlmostFull",
	2: "AlreadyFull",
}

var DiskUsage_value = map[string]int32{
	"Normal":      0,
	"AlmostFull":  1,
	"AlreadyFull": 2,
}

func (x DiskUsage) String() string {
	return proto.EnumName(DiskUsage_name, int32(x))
}

func (DiskUsage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_82acb53abfd87223, []int{0}
}

func init() {
	proto.RegisterEnum("disk_usage.DiskUsage", DiskUsage_name, DiskUsage_value)
}

func init() { proto.RegisterFile("disk_usage.proto", fileDescriptor_82acb53abfd87223) }

var fileDescriptor_82acb53abfd87223 = []byte{
	// 177 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x48, 0xc9, 0x2c, 0xce,
	0x8e, 0x2f, 0x2d, 0x4e, 0x4c, 0x4f, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0xe2, 0x42, 0x88,
	0x48, 0xf1, 0x17, 0x95, 0x16, 0x97, 0x80, 0x85, 0x21, 0x92, 0x52, 0x22, 0xe9, 0xf9, 0xe9, 0xf9,
	0x60, 0xa6, 0x3e, 0x88, 0x05, 0x11, 0xd5, 0xb2, 0xe0, 0xe2, 0x74, 0xc9, 0x2c, 0xce, 0x0e, 0x05,
	0xe9, 0x11, 0xe2, 0xe2, 0x62, 0xf3, 0xcb, 0x2f, 0xca, 0x4d, 0xcc, 0x11, 0x60, 0x10, 0xe2, 0xe3,
	0xe2, 0x72, 0xcc, 0xc9, 0xcd, 0x2f, 0x2e, 0x71, 0x2b, 0xcd, 0xc9, 0x11, 0x60, 0x14, 0xe2, 0xe7,
	0xe2, 0x76, 0xcc, 0x29, 0x4a, 0x4d, 0x4c, 0xa9, 0x04, 0x0b, 0x30, 0x39, 0x19, 0xdd, 0x58, 0xc1,
	0xc1, 0x78, 0xe2, 0x91, 0x1c, 0xe3, 0x85, 0x47, 0x72, 0x8c, 0x0f, 0x1e, 0xc9, 0x31, 0x4e, 0x78,
	0x2c, 0xc7, 0x30, 0xe3, 0xb1, 0x1c, 0xc3, 0x85, 0xc7, 0x72, 0x0c, 0x37, 0x1e, 0xcb, 0x31, 0x70,
	0x09, 0xe4, 0x17, 0xa5, 0xeb, 0x95, 0x64, 0x66, 0x97, 0xe9, 0x65, 0x97, 0x81, 0x6d, 0x4b, 0x62,
	0x03, 0x53, 0xc6, 0x80, 0x00, 0x00, 0x00, 0xff, 0xff, 0x44, 0x7d, 0x25, 0x74, 0xbb, 0x00, 0x00,
	0x00,
}
