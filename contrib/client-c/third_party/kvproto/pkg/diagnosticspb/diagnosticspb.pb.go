// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: diagnosticspb.proto

package diagnosticspb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type LogLevel int32

const (
	LogLevel_UNKNOWN  LogLevel = 0
	LogLevel_Debug    LogLevel = 1
	LogLevel_Info     LogLevel = 2
	LogLevel_Warn     LogLevel = 3
	LogLevel_Trace    LogLevel = 4
	LogLevel_Critical LogLevel = 5
	LogLevel_Error    LogLevel = 6
)

var LogLevel_name = map[int32]string{
	0: "UNKNOWN",
	1: "Debug",
	2: "Info",
	3: "Warn",
	4: "Trace",
	5: "Critical",
	6: "Error",
}

var LogLevel_value = map[string]int32{
	"UNKNOWN":  0,
	"Debug":    1,
	"Info":     2,
	"Warn":     3,
	"Trace":    4,
	"Critical": 5,
	"Error":    6,
}

func (x LogLevel) String() string {
	return proto.EnumName(LogLevel_name, int32(x))
}

func (LogLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{0}
}

type ServerInfoType int32

const (
	ServerInfoType_All          ServerInfoType = 0
	ServerInfoType_HardwareInfo ServerInfoType = 1
	ServerInfoType_SystemInfo   ServerInfoType = 2
	ServerInfoType_LoadInfo     ServerInfoType = 3
)

var ServerInfoType_name = map[int32]string{
	0: "All",
	1: "HardwareInfo",
	2: "SystemInfo",
	3: "LoadInfo",
}

var ServerInfoType_value = map[string]int32{
	"All":          0,
	"HardwareInfo": 1,
	"SystemInfo":   2,
	"LoadInfo":     3,
}

func (x ServerInfoType) String() string {
	return proto.EnumName(ServerInfoType_name, int32(x))
}

func (ServerInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{1}
}

type SearchLogRequest_Target int32

const (
	SearchLogRequest_Normal SearchLogRequest_Target = 0
	SearchLogRequest_Slow   SearchLogRequest_Target = 1
)

var SearchLogRequest_Target_name = map[int32]string{
	0: "Normal",
	1: "Slow",
}

var SearchLogRequest_Target_value = map[string]int32{
	"Normal": 0,
	"Slow":   1,
}

func (x SearchLogRequest_Target) String() string {
	return proto.EnumName(SearchLogRequest_Target_name, int32(x))
}

func (SearchLogRequest_Target) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{0, 0}
}

type SearchLogRequest struct {
	StartTime int64      `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   int64      `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Levels    []LogLevel `protobuf:"varint,3,rep,packed,name=levels,proto3,enum=diagnosticspb.LogLevel" json:"levels,omitempty"`
	// We use a string array to represent multiple CNF pattern sceniaor like:
	// SELECT * FROM t WHERE c LIKE '%s%' and c REGEXP '.*a.*' because
	// Golang and Rust don't support perl-like (?=re1)(?=re2)
	Patterns []string                `protobuf:"bytes,4,rep,name=patterns,proto3" json:"patterns,omitempty"`
	Target   SearchLogRequest_Target `protobuf:"varint,5,opt,name=target,proto3,enum=diagnosticspb.SearchLogRequest_Target" json:"target,omitempty"`
}

func (m *SearchLogRequest) Reset()         { *m = SearchLogRequest{} }
func (m *SearchLogRequest) String() string { return proto.CompactTextString(m) }
func (*SearchLogRequest) ProtoMessage()    {}
func (*SearchLogRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{0}
}
func (m *SearchLogRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SearchLogRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SearchLogRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SearchLogRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchLogRequest.Merge(m, src)
}
func (m *SearchLogRequest) XXX_Size() int {
	return m.Size()
}
func (m *SearchLogRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchLogRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchLogRequest proto.InternalMessageInfo

func (m *SearchLogRequest) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SearchLogRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SearchLogRequest) GetLevels() []LogLevel {
	if m != nil {
		return m.Levels
	}
	return nil
}

func (m *SearchLogRequest) GetPatterns() []string {
	if m != nil {
		return m.Patterns
	}
	return nil
}

func (m *SearchLogRequest) GetTarget() SearchLogRequest_Target {
	if m != nil {
		return m.Target
	}
	return SearchLogRequest_Normal
}

type SearchLogResponse struct {
	Messages []*LogMessage `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
}

func (m *SearchLogResponse) Reset()         { *m = SearchLogResponse{} }
func (m *SearchLogResponse) String() string { return proto.CompactTextString(m) }
func (*SearchLogResponse) ProtoMessage()    {}
func (*SearchLogResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{1}
}
func (m *SearchLogResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SearchLogResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SearchLogResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SearchLogResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchLogResponse.Merge(m, src)
}
func (m *SearchLogResponse) XXX_Size() int {
	return m.Size()
}
func (m *SearchLogResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchLogResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchLogResponse proto.InternalMessageInfo

func (m *SearchLogResponse) GetMessages() []*LogMessage {
	if m != nil {
		return m.Messages
	}
	return nil
}

type LogMessage struct {
	Time    int64    `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`
	Level   LogLevel `protobuf:"varint,2,opt,name=level,proto3,enum=diagnosticspb.LogLevel" json:"level,omitempty"`
	Message string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (m *LogMessage) Reset()         { *m = LogMessage{} }
func (m *LogMessage) String() string { return proto.CompactTextString(m) }
func (*LogMessage) ProtoMessage()    {}
func (*LogMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{2}
}
func (m *LogMessage) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *LogMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_LogMessage.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *LogMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LogMessage.Merge(m, src)
}
func (m *LogMessage) XXX_Size() int {
	return m.Size()
}
func (m *LogMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_LogMessage.DiscardUnknown(m)
}

var xxx_messageInfo_LogMessage proto.InternalMessageInfo

func (m *LogMessage) GetTime() int64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *LogMessage) GetLevel() LogLevel {
	if m != nil {
		return m.Level
	}
	return LogLevel_UNKNOWN
}

func (m *LogMessage) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type ServerInfoRequest struct {
	Tp ServerInfoType `protobuf:"varint,1,opt,name=tp,proto3,enum=diagnosticspb.ServerInfoType" json:"tp,omitempty"`
}

func (m *ServerInfoRequest) Reset()         { *m = ServerInfoRequest{} }
func (m *ServerInfoRequest) String() string { return proto.CompactTextString(m) }
func (*ServerInfoRequest) ProtoMessage()    {}
func (*ServerInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{3}
}
func (m *ServerInfoRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServerInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ServerInfoRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ServerInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerInfoRequest.Merge(m, src)
}
func (m *ServerInfoRequest) XXX_Size() int {
	return m.Size()
}
func (m *ServerInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ServerInfoRequest proto.InternalMessageInfo

func (m *ServerInfoRequest) GetTp() ServerInfoType {
	if m != nil {
		return m.Tp
	}
	return ServerInfoType_All
}

type ServerInfoPair struct {
	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *ServerInfoPair) Reset()         { *m = ServerInfoPair{} }
func (m *ServerInfoPair) String() string { return proto.CompactTextString(m) }
func (*ServerInfoPair) ProtoMessage()    {}
func (*ServerInfoPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{4}
}
func (m *ServerInfoPair) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServerInfoPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ServerInfoPair.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ServerInfoPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerInfoPair.Merge(m, src)
}
func (m *ServerInfoPair) XXX_Size() int {
	return m.Size()
}
func (m *ServerInfoPair) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerInfoPair.DiscardUnknown(m)
}

var xxx_messageInfo_ServerInfoPair proto.InternalMessageInfo

func (m *ServerInfoPair) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ServerInfoPair) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type ServerInfoItem struct {
	// cpu, memory, disk, network ...
	Tp string `protobuf:"bytes,1,opt,name=tp,proto3" json:"tp,omitempty"`
	// eg. network: lo1/eth0, cpu: core1/core2, disk: sda1/sda2
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// all key-value pairs for specified item, e.g:
	// ServerInfoItem {
	//     tp = "network"
	//     name = "eth0"
	//     paris = [
	//         ServerInfoPair { key = "readbytes", value = "4k"},
	//         ServerInfoPair { key = "writebytes", value = "1k"},
	//     ]
	// }
	Pairs []*ServerInfoPair `protobuf:"bytes,3,rep,name=pairs,proto3" json:"pairs,omitempty"`
}

func (m *ServerInfoItem) Reset()         { *m = ServerInfoItem{} }
func (m *ServerInfoItem) String() string { return proto.CompactTextString(m) }
func (*ServerInfoItem) ProtoMessage()    {}
func (*ServerInfoItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{5}
}
func (m *ServerInfoItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServerInfoItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ServerInfoItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ServerInfoItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerInfoItem.Merge(m, src)
}
func (m *ServerInfoItem) XXX_Size() int {
	return m.Size()
}
func (m *ServerInfoItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerInfoItem.DiscardUnknown(m)
}

var xxx_messageInfo_ServerInfoItem proto.InternalMessageInfo

func (m *ServerInfoItem) GetTp() string {
	if m != nil {
		return m.Tp
	}
	return ""
}

func (m *ServerInfoItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ServerInfoItem) GetPairs() []*ServerInfoPair {
	if m != nil {
		return m.Pairs
	}
	return nil
}

type ServerInfoResponse struct {
	Items []*ServerInfoItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (m *ServerInfoResponse) Reset()         { *m = ServerInfoResponse{} }
func (m *ServerInfoResponse) String() string { return proto.CompactTextString(m) }
func (*ServerInfoResponse) ProtoMessage()    {}
func (*ServerInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b3c45b810c39272a, []int{6}
}
func (m *ServerInfoResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ServerInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ServerInfoResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ServerInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerInfoResponse.Merge(m, src)
}
func (m *ServerInfoResponse) XXX_Size() int {
	return m.Size()
}
func (m *ServerInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ServerInfoResponse proto.InternalMessageInfo

func (m *ServerInfoResponse) GetItems() []*ServerInfoItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func init() {
	proto.RegisterEnum("diagnosticspb.LogLevel", LogLevel_name, LogLevel_value)
	proto.RegisterEnum("diagnosticspb.ServerInfoType", ServerInfoType_name, ServerInfoType_value)
	proto.RegisterEnum("diagnosticspb.SearchLogRequest_Target", SearchLogRequest_Target_name, SearchLogRequest_Target_value)
	proto.RegisterType((*SearchLogRequest)(nil), "diagnosticspb.SearchLogRequest")
	proto.RegisterType((*SearchLogResponse)(nil), "diagnosticspb.SearchLogResponse")
	proto.RegisterType((*LogMessage)(nil), "diagnosticspb.LogMessage")
	proto.RegisterType((*ServerInfoRequest)(nil), "diagnosticspb.ServerInfoRequest")
	proto.RegisterType((*ServerInfoPair)(nil), "diagnosticspb.ServerInfoPair")
	proto.RegisterType((*ServerInfoItem)(nil), "diagnosticspb.ServerInfoItem")
	proto.RegisterType((*ServerInfoResponse)(nil), "diagnosticspb.ServerInfoResponse")
}

func init() { proto.RegisterFile("diagnosticspb.proto", fileDescriptor_b3c45b810c39272a) }

var fileDescriptor_b3c45b810c39272a = []byte{
	// 653 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x54, 0xc1, 0x6e, 0xd3, 0x4a,
	0x14, 0xcd, 0xc4, 0x71, 0x62, 0xdf, 0xf4, 0xe5, 0xf9, 0xcd, 0xab, 0x84, 0x1b, 0xa9, 0xc6, 0x78,
	0x81, 0xa2, 0x4a, 0x4d, 0x51, 0x2a, 0x24, 0x56, 0x48, 0x94, 0x22, 0x11, 0x28, 0x01, 0x39, 0x41,
	0x95, 0xd8, 0x54, 0xd3, 0x64, 0x6a, 0xac, 0xda, 0x1e, 0x33, 0x33, 0x49, 0xd5, 0xbf, 0x60, 0xc9,
	0x27, 0xf0, 0x09, 0x7c, 0x02, 0xcb, 0x2e, 0xbb, 0x44, 0xcd, 0x57, 0xb0, 0x43, 0x33, 0x8e, 0xdb,
	0xb4, 0x55, 0xcb, 0xca, 0x77, 0xee, 0x3d, 0x73, 0xe6, 0xdc, 0x73, 0xaf, 0x0c, 0xff, 0x4f, 0x62,
	0x12, 0x65, 0x4c, 0xc8, 0x78, 0x2c, 0xf2, 0xc3, 0x6e, 0xce, 0x99, 0x64, 0xf8, 0x9f, 0x6b, 0xc9,
	0xf6, 0x6a, 0xc4, 0x22, 0xa6, 0x2b, 0x5b, 0x2a, 0x2a, 0x40, 0xed, 0x7f, 0xf9, 0x54, 0x48, 0x1d,
	0x16, 0x89, 0xe0, 0x37, 0x02, 0x67, 0x48, 0x09, 0x1f, 0x7f, 0xde, 0x63, 0x51, 0x48, 0xbf, 0x4c,
	0xa9, 0x90, 0x78, 0x1d, 0x40, 0x48, 0xc2, 0xe5, 0x81, 0x8c, 0x53, 0xea, 0x22, 0x1f, 0x75, 0x8c,
	0xd0, 0xd6, 0x99, 0x51, 0x9c, 0x52, 0xbc, 0x06, 0x16, 0xcd, 0x26, 0x45, 0xb1, 0xaa, 0x8b, 0x0d,
	0x9a, 0x4d, 0x74, 0x69, 0x0b, 0xea, 0x09, 0x9d, 0xd1, 0x44, 0xb8, 0x86, 0x6f, 0x74, 0x5a, 0xbd,
	0x07, 0xdd, 0xeb, 0x52, 0xf7, 0x58, 0xb4, 0xa7, 0xea, 0xe1, 0x02, 0x86, 0xdb, 0x60, 0xe5, 0x44,
	0x4a, 0xca, 0x33, 0xe1, 0xd6, 0x7c, 0xa3, 0x63, 0x87, 0x97, 0x67, 0xfc, 0x1c, 0xea, 0x92, 0xf0,
	0x88, 0x4a, 0xd7, 0xf4, 0x51, 0xa7, 0xd5, 0x7b, 0x7c, 0x83, 0xec, 0xa6, 0xee, 0xee, 0x48, 0xa3,
	0xc3, 0xc5, 0xad, 0xc0, 0x83, 0x7a, 0x91, 0xc1, 0x00, 0xf5, 0x01, 0xe3, 0x29, 0x49, 0x9c, 0x0a,
	0xb6, 0xa0, 0x36, 0x4c, 0xd8, 0x89, 0x83, 0x82, 0x37, 0xf0, 0xdf, 0x12, 0x85, 0xc8, 0x59, 0x26,
	0x28, 0x7e, 0x0a, 0x56, 0x4a, 0x85, 0x20, 0x11, 0x15, 0x2e, 0xf2, 0x8d, 0x4e, 0xb3, 0xb7, 0x76,
	0xbb, 0x87, 0x77, 0x05, 0x22, 0xbc, 0x84, 0x06, 0x31, 0xc0, 0x55, 0x1e, 0x63, 0xa8, 0x2d, 0x59,
	0xa7, 0x63, 0xbc, 0x09, 0xa6, 0xee, 0x59, 0x5b, 0x76, 0x8f, 0x33, 0x05, 0x0a, 0xbb, 0xd0, 0x58,
	0x90, 0xbb, 0x86, 0x8f, 0x3a, 0x76, 0x58, 0x1e, 0x83, 0x1d, 0x25, 0x9b, 0xcf, 0x28, 0xef, 0x67,
	0x47, 0xac, 0x1c, 0xd9, 0x26, 0x54, 0x65, 0xae, 0xdf, 0x6b, 0xf5, 0xd6, 0x6f, 0xf9, 0x54, 0xa2,
	0x47, 0xa7, 0x39, 0x0d, 0xab, 0x32, 0x0f, 0x9e, 0x41, 0xeb, 0x2a, 0xfb, 0x81, 0xc4, 0x1c, 0x3b,
	0x60, 0x1c, 0xd3, 0x53, 0xcd, 0x60, 0x87, 0x2a, 0xc4, 0xab, 0x60, 0xce, 0x48, 0x32, 0x2d, 0x66,
	0x6c, 0x87, 0xc5, 0x21, 0x88, 0x97, 0x6f, 0xf6, 0x25, 0x4d, 0x71, 0xeb, 0xf2, 0x69, 0x5b, 0x71,
	0xab, 0xe6, 0x33, 0x92, 0x96, 0xd7, 0x74, 0x8c, 0xb7, 0xc1, 0xcc, 0x49, 0xcc, 0x8b, 0xb5, 0x68,
	0xde, 0xa3, 0x50, 0x69, 0x09, 0x0b, 0x6c, 0xd0, 0x07, 0xbc, 0xdc, 0xe8, 0x62, 0x40, 0xdb, 0x60,
	0xc6, 0x92, 0xa6, 0xe5, 0x74, 0xee, 0xa6, 0x52, 0xe2, 0xc2, 0x02, 0xbb, 0xf1, 0x09, 0xac, 0xd2,
	0x60, 0xdc, 0x84, 0xc6, 0xc7, 0xc1, 0xdb, 0xc1, 0xfb, 0xfd, 0x81, 0x53, 0xc1, 0x36, 0x98, 0xbb,
	0xf4, 0x70, 0x1a, 0x39, 0x48, 0x2d, 0x86, 0xba, 0xe6, 0x54, 0x55, 0xb4, 0x4f, 0x78, 0xe6, 0x18,
	0xaa, 0x3c, 0xe2, 0x64, 0x4c, 0x9d, 0x1a, 0x5e, 0x01, 0xeb, 0x25, 0x8f, 0x65, 0x3c, 0x26, 0x89,
	0x63, 0xaa, 0xc2, 0x2b, 0xce, 0x19, 0x77, 0xea, 0x1b, 0xfd, 0x65, 0x47, 0x94, 0xc3, 0xb8, 0x01,
	0xc6, 0x8b, 0x44, 0xed, 0x9a, 0x03, 0x2b, 0xaf, 0x09, 0x9f, 0x9c, 0x10, 0x4e, 0x35, 0x35, 0xc2,
	0x2d, 0x80, 0xe1, 0xa9, 0x90, 0x34, 0x5d, 0x3c, 0xb5, 0xa2, 0x84, 0x91, 0x89, 0x3e, 0x19, 0xbd,
	0x1f, 0x08, 0x9a, 0xbb, 0x57, 0xed, 0xe0, 0x21, 0x80, 0xd0, 0x1b, 0x7a, 0x90, 0xb0, 0x08, 0x3f,
	0xfc, 0xcb, 0xfe, 0xb7, 0xfd, 0xbb, 0x01, 0x85, 0x79, 0x41, 0xe5, 0x09, 0xc2, 0x23, 0x68, 0x0a,
	0xad, 0xf7, 0x20, 0xce, 0x8e, 0x18, 0xf6, 0xef, 0x34, 0xb0, 0xa4, 0x7d, 0x74, 0x0f, 0xa2, 0xe4,
	0xdd, 0xe9, 0x9d, 0x7f, 0xb7, 0xd0, 0xcf, 0x0b, 0x0f, 0x9d, 0x5d, 0x78, 0xe8, 0xd7, 0x85, 0x87,
	0xbe, 0xce, 0xbd, 0xca, 0xb7, 0xb9, 0x57, 0x39, 0x9b, 0x7b, 0x95, 0xf3, 0xb9, 0x57, 0x01, 0x87,
	0xf1, 0xa8, 0x2b, 0xe3, 0xe3, 0x59, 0xf7, 0x78, 0xa6, 0x7f, 0x3e, 0x87, 0x75, 0xfd, 0xd9, 0xfe,
	0x13, 0x00, 0x00, 0xff, 0xff, 0x13, 0x42, 0xcd, 0xf7, 0xd0, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DiagnosticsClient is the client API for Diagnostics service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DiagnosticsClient interface {
	// Searchs log in the target node
	SearchLog(ctx context.Context, in *SearchLogRequest, opts ...grpc.CallOption) (Diagnostics_SearchLogClient, error)
	// Retrieves server info in the target node
	ServerInfo(ctx context.Context, in *ServerInfoRequest, opts ...grpc.CallOption) (*ServerInfoResponse, error)
}

type diagnosticsClient struct {
	cc *grpc.ClientConn
}

func NewDiagnosticsClient(cc *grpc.ClientConn) DiagnosticsClient {
	return &diagnosticsClient{cc}
}

func (c *diagnosticsClient) SearchLog(ctx context.Context, in *SearchLogRequest, opts ...grpc.CallOption) (Diagnostics_SearchLogClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Diagnostics_serviceDesc.Streams[0], "/diagnosticspb.Diagnostics/search_log", opts...)
	if err != nil {
		return nil, err
	}
	x := &diagnosticsSearchLogClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Diagnostics_SearchLogClient interface {
	Recv() (*SearchLogResponse, error)
	grpc.ClientStream
}

type diagnosticsSearchLogClient struct {
	grpc.ClientStream
}

func (x *diagnosticsSearchLogClient) Recv() (*SearchLogResponse, error) {
	m := new(SearchLogResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *diagnosticsClient) ServerInfo(ctx context.Context, in *ServerInfoRequest, opts ...grpc.CallOption) (*ServerInfoResponse, error) {
	out := new(ServerInfoResponse)
	err := c.cc.Invoke(ctx, "/diagnosticspb.Diagnostics/server_info", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DiagnosticsServer is the server API for Diagnostics service.
type DiagnosticsServer interface {
	// Searchs log in the target node
	SearchLog(*SearchLogRequest, Diagnostics_SearchLogServer) error
	// Retrieves server info in the target node
	ServerInfo(context.Context, *ServerInfoRequest) (*ServerInfoResponse, error)
}

// UnimplementedDiagnosticsServer can be embedded to have forward compatible implementations.
type UnimplementedDiagnosticsServer struct {
}

func (*UnimplementedDiagnosticsServer) SearchLog(req *SearchLogRequest, srv Diagnostics_SearchLogServer) error {
	return status.Errorf(codes.Unimplemented, "method SearchLog not implemented")
}
func (*UnimplementedDiagnosticsServer) ServerInfo(ctx context.Context, req *ServerInfoRequest) (*ServerInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ServerInfo not implemented")
}

func RegisterDiagnosticsServer(s *grpc.Server, srv DiagnosticsServer) {
	s.RegisterService(&_Diagnostics_serviceDesc, srv)
}

func _Diagnostics_SearchLog_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(SearchLogRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(DiagnosticsServer).SearchLog(m, &diagnosticsSearchLogServer{stream})
}

type Diagnostics_SearchLogServer interface {
	Send(*SearchLogResponse) error
	grpc.ServerStream
}

type diagnosticsSearchLogServer struct {
	grpc.ServerStream
}

func (x *diagnosticsSearchLogServer) Send(m *SearchLogResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Diagnostics_ServerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServerInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiagnosticsServer).ServerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/diagnosticspb.Diagnostics/ServerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiagnosticsServer).ServerInfo(ctx, req.(*ServerInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Diagnostics_serviceDesc = grpc.ServiceDesc{
	ServiceName: "diagnosticspb.Diagnostics",
	HandlerType: (*DiagnosticsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "server_info",
			Handler:    _Diagnostics_ServerInfo_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "search_log",
			Handler:       _Diagnostics_SearchLog_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "diagnosticspb.proto",
}

func (m *SearchLogRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SearchLogRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SearchLogRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Target != 0 {
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(m.Target))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Patterns) > 0 {
		for iNdEx := len(m.Patterns) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Patterns[iNdEx])
			copy(dAtA[i:], m.Patterns[iNdEx])
			i = encodeVarintDiagnosticspb(dAtA, i, uint64(len(m.Patterns[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Levels) > 0 {
		dAtA2 := make([]byte, len(m.Levels)*10)
		var j1 int
		for _, num := range m.Levels {
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		i -= j1
		copy(dAtA[i:], dAtA2[:j1])
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(j1))
		i--
		dAtA[i] = 0x1a
	}
	if m.EndTime != 0 {
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(m.EndTime))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTime != 0 {
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(m.StartTime))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SearchLogResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SearchLogResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SearchLogResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Messages) > 0 {
		for iNdEx := len(m.Messages) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Messages[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDiagnosticspb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *LogMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LogMessage) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *LogMessage) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Level != 0 {
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(m.Level))
		i--
		dAtA[i] = 0x10
	}
	if m.Time != 0 {
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(m.Time))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ServerInfoRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServerInfoRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServerInfoRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Tp != 0 {
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(m.Tp))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ServerInfoPair) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServerInfoPair) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServerInfoPair) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ServerInfoItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServerInfoItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServerInfoItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Pairs) > 0 {
		for iNdEx := len(m.Pairs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Pairs[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDiagnosticspb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Tp) > 0 {
		i -= len(m.Tp)
		copy(dAtA[i:], m.Tp)
		i = encodeVarintDiagnosticspb(dAtA, i, uint64(len(m.Tp)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ServerInfoResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ServerInfoResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ServerInfoResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDiagnosticspb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintDiagnosticspb(dAtA []byte, offset int, v uint64) int {
	offset -= sovDiagnosticspb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *SearchLogRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTime != 0 {
		n += 1 + sovDiagnosticspb(uint64(m.StartTime))
	}
	if m.EndTime != 0 {
		n += 1 + sovDiagnosticspb(uint64(m.EndTime))
	}
	if len(m.Levels) > 0 {
		l = 0
		for _, e := range m.Levels {
			l += sovDiagnosticspb(uint64(e))
		}
		n += 1 + sovDiagnosticspb(uint64(l)) + l
	}
	if len(m.Patterns) > 0 {
		for _, s := range m.Patterns {
			l = len(s)
			n += 1 + l + sovDiagnosticspb(uint64(l))
		}
	}
	if m.Target != 0 {
		n += 1 + sovDiagnosticspb(uint64(m.Target))
	}
	return n
}

func (m *SearchLogResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Messages) > 0 {
		for _, e := range m.Messages {
			l = e.Size()
			n += 1 + l + sovDiagnosticspb(uint64(l))
		}
	}
	return n
}

func (m *LogMessage) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Time != 0 {
		n += 1 + sovDiagnosticspb(uint64(m.Time))
	}
	if m.Level != 0 {
		n += 1 + sovDiagnosticspb(uint64(m.Level))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovDiagnosticspb(uint64(l))
	}
	return n
}

func (m *ServerInfoRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Tp != 0 {
		n += 1 + sovDiagnosticspb(uint64(m.Tp))
	}
	return n
}

func (m *ServerInfoPair) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovDiagnosticspb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovDiagnosticspb(uint64(l))
	}
	return n
}

func (m *ServerInfoItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Tp)
	if l > 0 {
		n += 1 + l + sovDiagnosticspb(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovDiagnosticspb(uint64(l))
	}
	if len(m.Pairs) > 0 {
		for _, e := range m.Pairs {
			l = e.Size()
			n += 1 + l + sovDiagnosticspb(uint64(l))
		}
	}
	return n
}

func (m *ServerInfoResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovDiagnosticspb(uint64(l))
		}
	}
	return n
}

func sovDiagnosticspb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDiagnosticspb(x uint64) (n int) {
	return sovDiagnosticspb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SearchLogRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDiagnosticspb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SearchLogRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SearchLogRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v LogLevel
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowDiagnosticspb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= LogLevel(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Levels = append(m.Levels, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowDiagnosticspb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthDiagnosticspb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthDiagnosticspb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				if elementCount != 0 && len(m.Levels) == 0 {
					m.Levels = make([]LogLevel, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v LogLevel
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowDiagnosticspb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= LogLevel(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Levels = append(m.Levels, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Levels", wireType)
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Patterns", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Patterns = append(m.Patterns, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Target", wireType)
			}
			m.Target = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Target |= SearchLogRequest_Target(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDiagnosticspb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SearchLogResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDiagnosticspb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SearchLogResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SearchLogResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Messages", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Messages = append(m.Messages, &LogMessage{})
			if err := m.Messages[len(m.Messages)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDiagnosticspb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LogMessage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDiagnosticspb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LogMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LogMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Time", wireType)
			}
			m.Time = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Time |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= LogLevel(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDiagnosticspb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServerInfoRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDiagnosticspb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ServerInfoRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ServerInfoRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tp", wireType)
			}
			m.Tp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tp |= ServerInfoType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDiagnosticspb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServerInfoPair) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDiagnosticspb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ServerInfoPair: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ServerInfoPair: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDiagnosticspb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServerInfoItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDiagnosticspb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ServerInfoItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ServerInfoItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tp", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tp = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pairs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pairs = append(m.Pairs, &ServerInfoPair{})
			if err := m.Pairs[len(m.Pairs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDiagnosticspb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ServerInfoResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDiagnosticspb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ServerInfoResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ServerInfoResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, &ServerInfoItem{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDiagnosticspb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDiagnosticspb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDiagnosticspb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDiagnosticspb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDiagnosticspb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDiagnosticspb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDiagnosticspb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDiagnosticspb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDiagnosticspb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDiagnosticspb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDiagnosticspb = fmt.Errorf("proto: unexpected end of group")
)
