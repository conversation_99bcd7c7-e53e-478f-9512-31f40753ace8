// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: gcpb.proto

package gcpb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ErrorType int32

const (
	ErrorType_OK               ErrorType = 0
	ErrorType_UNKNOWN          ErrorType = 1
	ErrorType_NOT_BOOTSTRAPPED ErrorType = 2
	// revision supplied does not match the current etcd revision
	ErrorType_REVISION_MISMATCH ErrorType = 3
	// if the proposed safe point is earlier than old safe point or gc safe point
	ErrorType_SAFEPOINT_ROLLBACK ErrorType = 4
)

var ErrorType_name = map[int32]string{
	0: "OK",
	1: "UNKNOWN",
	2: "NOT_BOOTSTRAPPED",
	3: "REVISION_MISMATCH",
	4: "SAFEPOINT_ROLLBACK",
}

var ErrorType_value = map[string]int32{
	"OK":                 0,
	"UNKNOWN":            1,
	"NOT_BOOTSTRAPPED":   2,
	"REVISION_MISMATCH":  3,
	"SAFEPOINT_ROLLBACK": 4,
}

func (x ErrorType) String() string {
	return proto.EnumName(ErrorType_name, int32(x))
}

func (ErrorType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{0}
}

type RequestHeader struct {
	// cluster_id is the ID of the cluster which be sent to.
	ClusterId uint64 `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	// sender_id is the ID of the sender server, also member ID or etcd ID.
	SenderId uint64 `protobuf:"varint,2,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
}

func (m *RequestHeader) Reset()         { *m = RequestHeader{} }
func (m *RequestHeader) String() string { return proto.CompactTextString(m) }
func (*RequestHeader) ProtoMessage()    {}
func (*RequestHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{0}
}
func (m *RequestHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RequestHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RequestHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RequestHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestHeader.Merge(m, src)
}
func (m *RequestHeader) XXX_Size() int {
	return m.Size()
}
func (m *RequestHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestHeader.DiscardUnknown(m)
}

var xxx_messageInfo_RequestHeader proto.InternalMessageInfo

func (m *RequestHeader) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

func (m *RequestHeader) GetSenderId() uint64 {
	if m != nil {
		return m.SenderId
	}
	return 0
}

type ResponseHeader struct {
	// cluster_id is the ID of the cluster which sent the response.
	ClusterId uint64 `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Error     *Error `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *ResponseHeader) Reset()         { *m = ResponseHeader{} }
func (m *ResponseHeader) String() string { return proto.CompactTextString(m) }
func (*ResponseHeader) ProtoMessage()    {}
func (*ResponseHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{1}
}
func (m *ResponseHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResponseHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResponseHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResponseHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResponseHeader.Merge(m, src)
}
func (m *ResponseHeader) XXX_Size() int {
	return m.Size()
}
func (m *ResponseHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_ResponseHeader.DiscardUnknown(m)
}

var xxx_messageInfo_ResponseHeader proto.InternalMessageInfo

func (m *ResponseHeader) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

func (m *ResponseHeader) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type Error struct {
	Type    ErrorType `protobuf:"varint,1,opt,name=type,proto3,enum=gcpb.ErrorType" json:"type,omitempty"`
	Message string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{2}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetType() ErrorType {
	if m != nil {
		return m.Type
	}
	return ErrorType_OK
}

func (m *Error) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type KeySpace struct {
	SpaceId     []byte `protobuf:"bytes,1,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	GcSafePoint uint64 `protobuf:"varint,2,opt,name=gc_safe_point,json=gcSafePoint,proto3" json:"gc_safe_point,omitempty"`
}

func (m *KeySpace) Reset()         { *m = KeySpace{} }
func (m *KeySpace) String() string { return proto.CompactTextString(m) }
func (*KeySpace) ProtoMessage()    {}
func (*KeySpace) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{3}
}
func (m *KeySpace) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeySpace) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeySpace.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KeySpace) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeySpace.Merge(m, src)
}
func (m *KeySpace) XXX_Size() int {
	return m.Size()
}
func (m *KeySpace) XXX_DiscardUnknown() {
	xxx_messageInfo_KeySpace.DiscardUnknown(m)
}

var xxx_messageInfo_KeySpace proto.InternalMessageInfo

func (m *KeySpace) GetSpaceId() []byte {
	if m != nil {
		return m.SpaceId
	}
	return nil
}

func (m *KeySpace) GetGcSafePoint() uint64 {
	if m != nil {
		return m.GcSafePoint
	}
	return 0
}

type ListKeySpacesRequest struct {
	Header *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// set with_gc_safe_point to true to also receive gc safe point for each key space
	WithGcSafePoint bool `protobuf:"varint,2,opt,name=with_gc_safe_point,json=withGcSafePoint,proto3" json:"with_gc_safe_point,omitempty"`
}

func (m *ListKeySpacesRequest) Reset()         { *m = ListKeySpacesRequest{} }
func (m *ListKeySpacesRequest) String() string { return proto.CompactTextString(m) }
func (*ListKeySpacesRequest) ProtoMessage()    {}
func (*ListKeySpacesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{4}
}
func (m *ListKeySpacesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListKeySpacesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListKeySpacesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListKeySpacesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListKeySpacesRequest.Merge(m, src)
}
func (m *ListKeySpacesRequest) XXX_Size() int {
	return m.Size()
}
func (m *ListKeySpacesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListKeySpacesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListKeySpacesRequest proto.InternalMessageInfo

func (m *ListKeySpacesRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ListKeySpacesRequest) GetWithGcSafePoint() bool {
	if m != nil {
		return m.WithGcSafePoint
	}
	return false
}

type ListKeySpacesResponse struct {
	Header    *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	KeySpaces []*KeySpace     `protobuf:"bytes,2,rep,name=key_spaces,json=keySpaces,proto3" json:"key_spaces,omitempty"`
}

func (m *ListKeySpacesResponse) Reset()         { *m = ListKeySpacesResponse{} }
func (m *ListKeySpacesResponse) String() string { return proto.CompactTextString(m) }
func (*ListKeySpacesResponse) ProtoMessage()    {}
func (*ListKeySpacesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{5}
}
func (m *ListKeySpacesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListKeySpacesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListKeySpacesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListKeySpacesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListKeySpacesResponse.Merge(m, src)
}
func (m *ListKeySpacesResponse) XXX_Size() int {
	return m.Size()
}
func (m *ListKeySpacesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListKeySpacesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListKeySpacesResponse proto.InternalMessageInfo

func (m *ListKeySpacesResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *ListKeySpacesResponse) GetKeySpaces() []*KeySpace {
	if m != nil {
		return m.KeySpaces
	}
	return nil
}

type GetMinServiceSafePointRequest struct {
	Header  *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SpaceId []byte         `protobuf:"bytes,2,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
}

func (m *GetMinServiceSafePointRequest) Reset()         { *m = GetMinServiceSafePointRequest{} }
func (m *GetMinServiceSafePointRequest) String() string { return proto.CompactTextString(m) }
func (*GetMinServiceSafePointRequest) ProtoMessage()    {}
func (*GetMinServiceSafePointRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{6}
}
func (m *GetMinServiceSafePointRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMinServiceSafePointRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMinServiceSafePointRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetMinServiceSafePointRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinServiceSafePointRequest.Merge(m, src)
}
func (m *GetMinServiceSafePointRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetMinServiceSafePointRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinServiceSafePointRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinServiceSafePointRequest proto.InternalMessageInfo

func (m *GetMinServiceSafePointRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetMinServiceSafePointRequest) GetSpaceId() []byte {
	if m != nil {
		return m.SpaceId
	}
	return nil
}

type GetMinServiceSafePointResponse struct {
	Header    *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SafePoint uint64          `protobuf:"varint,2,opt,name=safe_point,json=safePoint,proto3" json:"safe_point,omitempty"`
	// revision here is to safeguard the validity of the obtained min,
	// preventing cases where new services register their safe points after min is obtained by gc worker
	Revision int64 `protobuf:"varint,3,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (m *GetMinServiceSafePointResponse) Reset()         { *m = GetMinServiceSafePointResponse{} }
func (m *GetMinServiceSafePointResponse) String() string { return proto.CompactTextString(m) }
func (*GetMinServiceSafePointResponse) ProtoMessage()    {}
func (*GetMinServiceSafePointResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{7}
}
func (m *GetMinServiceSafePointResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMinServiceSafePointResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMinServiceSafePointResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetMinServiceSafePointResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinServiceSafePointResponse.Merge(m, src)
}
func (m *GetMinServiceSafePointResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetMinServiceSafePointResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinServiceSafePointResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinServiceSafePointResponse proto.InternalMessageInfo

func (m *GetMinServiceSafePointResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GetMinServiceSafePointResponse) GetSafePoint() uint64 {
	if m != nil {
		return m.SafePoint
	}
	return 0
}

func (m *GetMinServiceSafePointResponse) GetRevision() int64 {
	if m != nil {
		return m.Revision
	}
	return 0
}

type UpdateGCSafePointRequest struct {
	Header    *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SpaceId   []byte         `protobuf:"bytes,2,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	SafePoint uint64         `protobuf:"varint,3,opt,name=safe_point,json=safePoint,proto3" json:"safe_point,omitempty"`
	// here client need to provide the revision obtained from GetMinServiceSafePoint,
	// so server can check if it's still valid
	Revision int64 `protobuf:"varint,4,opt,name=revision,proto3" json:"revision,omitempty"`
}

func (m *UpdateGCSafePointRequest) Reset()         { *m = UpdateGCSafePointRequest{} }
func (m *UpdateGCSafePointRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateGCSafePointRequest) ProtoMessage()    {}
func (*UpdateGCSafePointRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{8}
}
func (m *UpdateGCSafePointRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateGCSafePointRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateGCSafePointRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateGCSafePointRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGCSafePointRequest.Merge(m, src)
}
func (m *UpdateGCSafePointRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpdateGCSafePointRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGCSafePointRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGCSafePointRequest proto.InternalMessageInfo

func (m *UpdateGCSafePointRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *UpdateGCSafePointRequest) GetSpaceId() []byte {
	if m != nil {
		return m.SpaceId
	}
	return nil
}

func (m *UpdateGCSafePointRequest) GetSafePoint() uint64 {
	if m != nil {
		return m.SafePoint
	}
	return 0
}

func (m *UpdateGCSafePointRequest) GetRevision() int64 {
	if m != nil {
		return m.Revision
	}
	return 0
}

type UpdateGCSafePointResponse struct {
	Header *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// update will be successful if revision is valid and new safepoint > old safe point
	// if failed, previously obtained min might be incorrect, should retry from GetMinServiceSafePoint
	Succeeded    bool   `protobuf:"varint,2,opt,name=succeeded,proto3" json:"succeeded,omitempty"`
	NewSafePoint uint64 `protobuf:"varint,3,opt,name=new_safe_point,json=newSafePoint,proto3" json:"new_safe_point,omitempty"`
}

func (m *UpdateGCSafePointResponse) Reset()         { *m = UpdateGCSafePointResponse{} }
func (m *UpdateGCSafePointResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateGCSafePointResponse) ProtoMessage()    {}
func (*UpdateGCSafePointResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{9}
}
func (m *UpdateGCSafePointResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateGCSafePointResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateGCSafePointResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateGCSafePointResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGCSafePointResponse.Merge(m, src)
}
func (m *UpdateGCSafePointResponse) XXX_Size() int {
	return m.Size()
}
func (m *UpdateGCSafePointResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGCSafePointResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGCSafePointResponse proto.InternalMessageInfo

func (m *UpdateGCSafePointResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *UpdateGCSafePointResponse) GetSucceeded() bool {
	if m != nil {
		return m.Succeeded
	}
	return false
}

func (m *UpdateGCSafePointResponse) GetNewSafePoint() uint64 {
	if m != nil {
		return m.NewSafePoint
	}
	return 0
}

type UpdateServiceSafePointRequest struct {
	Header    *RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SpaceId   []byte         `protobuf:"bytes,2,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	ServiceId []byte         `protobuf:"bytes,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// safe point will be set to expire on (PD Server time + TTL)
	// pass in a ttl < 0 to remove target safe point
	// pass in MAX_INT64 to set a safe point that never expire
	TTL       int64  `protobuf:"varint,4,opt,name=TTL,proto3" json:"TTL,omitempty"`
	SafePoint uint64 `protobuf:"varint,5,opt,name=safe_point,json=safePoint,proto3" json:"safe_point,omitempty"`
}

func (m *UpdateServiceSafePointRequest) Reset()         { *m = UpdateServiceSafePointRequest{} }
func (m *UpdateServiceSafePointRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateServiceSafePointRequest) ProtoMessage()    {}
func (*UpdateServiceSafePointRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{10}
}
func (m *UpdateServiceSafePointRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateServiceSafePointRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateServiceSafePointRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateServiceSafePointRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateServiceSafePointRequest.Merge(m, src)
}
func (m *UpdateServiceSafePointRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpdateServiceSafePointRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateServiceSafePointRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateServiceSafePointRequest proto.InternalMessageInfo

func (m *UpdateServiceSafePointRequest) GetHeader() *RequestHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *UpdateServiceSafePointRequest) GetSpaceId() []byte {
	if m != nil {
		return m.SpaceId
	}
	return nil
}

func (m *UpdateServiceSafePointRequest) GetServiceId() []byte {
	if m != nil {
		return m.ServiceId
	}
	return nil
}

func (m *UpdateServiceSafePointRequest) GetTTL() int64 {
	if m != nil {
		return m.TTL
	}
	return 0
}

func (m *UpdateServiceSafePointRequest) GetSafePoint() uint64 {
	if m != nil {
		return m.SafePoint
	}
	return 0
}

type UpdateServiceSafePointResponse struct {
	Header *ResponseHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// update will be successful if ttl < 0 (a removal request)
	// or if new safe point >= old safe point and new safe point >= gc safe point
	Succeeded    bool   `protobuf:"varint,2,opt,name=succeeded,proto3" json:"succeeded,omitempty"`
	GcSafePoint  uint64 `protobuf:"varint,3,opt,name=gc_safe_point,json=gcSafePoint,proto3" json:"gc_safe_point,omitempty"`
	OldSafePoint uint64 `protobuf:"varint,4,opt,name=old_safe_point,json=oldSafePoint,proto3" json:"old_safe_point,omitempty"`
	NewSafePoint uint64 `protobuf:"varint,5,opt,name=new_safe_point,json=newSafePoint,proto3" json:"new_safe_point,omitempty"`
}

func (m *UpdateServiceSafePointResponse) Reset()         { *m = UpdateServiceSafePointResponse{} }
func (m *UpdateServiceSafePointResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateServiceSafePointResponse) ProtoMessage()    {}
func (*UpdateServiceSafePointResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_b5e0ef170d88dab2, []int{11}
}
func (m *UpdateServiceSafePointResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateServiceSafePointResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateServiceSafePointResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateServiceSafePointResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateServiceSafePointResponse.Merge(m, src)
}
func (m *UpdateServiceSafePointResponse) XXX_Size() int {
	return m.Size()
}
func (m *UpdateServiceSafePointResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateServiceSafePointResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateServiceSafePointResponse proto.InternalMessageInfo

func (m *UpdateServiceSafePointResponse) GetHeader() *ResponseHeader {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *UpdateServiceSafePointResponse) GetSucceeded() bool {
	if m != nil {
		return m.Succeeded
	}
	return false
}

func (m *UpdateServiceSafePointResponse) GetGcSafePoint() uint64 {
	if m != nil {
		return m.GcSafePoint
	}
	return 0
}

func (m *UpdateServiceSafePointResponse) GetOldSafePoint() uint64 {
	if m != nil {
		return m.OldSafePoint
	}
	return 0
}

func (m *UpdateServiceSafePointResponse) GetNewSafePoint() uint64 {
	if m != nil {
		return m.NewSafePoint
	}
	return 0
}

func init() {
	proto.RegisterEnum("gcpb.ErrorType", ErrorType_name, ErrorType_value)
	proto.RegisterType((*RequestHeader)(nil), "gcpb.RequestHeader")
	proto.RegisterType((*ResponseHeader)(nil), "gcpb.ResponseHeader")
	proto.RegisterType((*Error)(nil), "gcpb.Error")
	proto.RegisterType((*KeySpace)(nil), "gcpb.KeySpace")
	proto.RegisterType((*ListKeySpacesRequest)(nil), "gcpb.ListKeySpacesRequest")
	proto.RegisterType((*ListKeySpacesResponse)(nil), "gcpb.ListKeySpacesResponse")
	proto.RegisterType((*GetMinServiceSafePointRequest)(nil), "gcpb.GetMinServiceSafePointRequest")
	proto.RegisterType((*GetMinServiceSafePointResponse)(nil), "gcpb.GetMinServiceSafePointResponse")
	proto.RegisterType((*UpdateGCSafePointRequest)(nil), "gcpb.UpdateGCSafePointRequest")
	proto.RegisterType((*UpdateGCSafePointResponse)(nil), "gcpb.UpdateGCSafePointResponse")
	proto.RegisterType((*UpdateServiceSafePointRequest)(nil), "gcpb.UpdateServiceSafePointRequest")
	proto.RegisterType((*UpdateServiceSafePointResponse)(nil), "gcpb.UpdateServiceSafePointResponse")
}

func init() { proto.RegisterFile("gcpb.proto", fileDescriptor_b5e0ef170d88dab2) }

var fileDescriptor_b5e0ef170d88dab2 = []byte{
	// 762 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x55, 0xcd, 0x6e, 0xda, 0x4a,
	0x14, 0xc6, 0x18, 0x12, 0x7c, 0x48, 0x88, 0x33, 0x97, 0x44, 0x84, 0x5c, 0x7c, 0x73, 0x9d, 0x2c,
	0xa2, 0x9b, 0x5b, 0x2a, 0xd1, 0x27, 0x20, 0x94, 0x10, 0x17, 0x82, 0x91, 0x71, 0xd2, 0xa5, 0x45,
	0xec, 0x89, 0x63, 0x91, 0x62, 0xd7, 0x63, 0x88, 0x78, 0x84, 0x2e, 0x2a, 0x75, 0xd9, 0x4d, 0xf7,
	0x7d, 0x83, 0xbe, 0x42, 0x97, 0x59, 0x66, 0xd1, 0x45, 0x15, 0xde, 0xa1, 0xeb, 0xca, 0x63, 0x4c,
	0xf8, 0x57, 0x15, 0x29, 0x2b, 0x66, 0xce, 0x37, 0x73, 0xbe, 0xef, 0x7c, 0x73, 0x7c, 0x00, 0x30,
	0x75, 0xe7, 0x32, 0xef, 0xb8, 0xb6, 0x67, 0xa3, 0x98, 0xbf, 0xce, 0xa6, 0x4d, 0xdb, 0xb4, 0x69,
	0xe0, 0xa5, 0xbf, 0x0a, 0xb0, 0xec, 0x86, 0xdb, 0x25, 0x1e, 0x5d, 0x06, 0x01, 0xb1, 0x0a, 0xeb,
	0x0a, 0x7e, 0xdf, 0xc5, 0xc4, 0x3b, 0xc5, 0x2d, 0x03, 0xbb, 0x28, 0x07, 0xa0, 0xdf, 0x74, 0x89,
	0x87, 0x5d, 0xcd, 0x32, 0x32, 0xcc, 0x1e, 0x73, 0x18, 0x53, 0xb8, 0x61, 0x44, 0x32, 0xd0, 0x2e,
	0x70, 0x04, 0x77, 0x8c, 0x00, 0x8d, 0x52, 0x34, 0x11, 0x04, 0x24, 0x43, 0x54, 0x20, 0xa5, 0x60,
	0xe2, 0xd8, 0x1d, 0x82, 0xff, 0x2c, 0xdb, 0xbf, 0x10, 0xc7, 0xae, 0x6b, 0xbb, 0x34, 0x53, 0xb2,
	0x90, 0xcc, 0xd3, 0x32, 0xca, 0x7e, 0x48, 0x09, 0x10, 0xf1, 0x04, 0xe2, 0x74, 0x8f, 0xf6, 0x21,
	0xe6, 0xf5, 0x1d, 0x4c, 0x93, 0xa4, 0x0a, 0x1b, 0x63, 0x47, 0xd5, 0xbe, 0x83, 0x15, 0x0a, 0xa2,
	0x0c, 0xac, 0xbe, 0xc3, 0x84, 0xb4, 0x4c, 0x4c, 0x53, 0x72, 0x4a, 0xb8, 0x15, 0x25, 0x48, 0x54,
	0x71, 0xbf, 0xe9, 0xb4, 0x74, 0x8c, 0x76, 0x20, 0x41, 0xfc, 0x45, 0xa8, 0x69, 0x4d, 0x59, 0xa5,
	0x7b, 0xc9, 0x40, 0x22, 0xac, 0x9b, 0xba, 0x46, 0x5a, 0x57, 0x58, 0x73, 0x6c, 0xab, 0xe3, 0x0d,
	0x6b, 0x4c, 0x9a, 0x7a, 0xb3, 0x75, 0x85, 0x1b, 0x7e, 0x48, 0x74, 0x20, 0x5d, 0xb3, 0x88, 0x17,
	0xa6, 0x23, 0x43, 0x03, 0xd1, 0x11, 0xac, 0x5c, 0xd3, 0xb2, 0x69, 0xd2, 0x64, 0xe1, 0xaf, 0x40,
	0xe3, 0x84, 0xbf, 0xca, 0xf0, 0x08, 0x3a, 0x02, 0x74, 0x6b, 0x79, 0xd7, 0xda, 0x2c, 0x5b, 0x42,
	0xd9, 0xf0, 0x91, 0xca, 0x18, 0xa3, 0x07, 0x5b, 0x53, 0x8c, 0x81, 0xcb, 0xe8, 0xff, 0x29, 0xca,
	0x74, 0x48, 0x39, 0xfe, 0x0a, 0x23, 0xce, 0x17, 0x00, 0x6d, 0xdc, 0xd7, 0x68, 0xad, 0x24, 0x13,
	0xdd, 0x63, 0x0f, 0x93, 0x85, 0x54, 0x70, 0x23, 0x4c, 0xad, 0x70, 0xed, 0x90, 0x44, 0x34, 0x21,
	0x57, 0xc1, 0xde, 0x99, 0xd5, 0x69, 0x62, 0xb7, 0x67, 0xe9, 0x78, 0xa4, 0xe7, 0x49, 0x05, 0x8f,
	0x9b, 0x1e, 0x9d, 0x30, 0x5d, 0xfc, 0xc0, 0x80, 0xb0, 0x88, 0xe9, 0x49, 0x85, 0xe6, 0x00, 0x66,
	0x9e, 0x90, 0x23, 0x61, 0x52, 0x94, 0x85, 0x84, 0x8b, 0x7b, 0x16, 0xb1, 0xec, 0x4e, 0x86, 0xdd,
	0x63, 0x0e, 0x59, 0x65, 0xb4, 0x17, 0xbf, 0x30, 0x90, 0x39, 0x77, 0x8c, 0x96, 0x87, 0x2b, 0xa5,
	0xe7, 0x2a, 0x78, 0x4a, 0x1f, 0xbb, 0x4c, 0x5f, 0x6c, 0x4a, 0xdf, 0x47, 0x06, 0x76, 0xe6, 0xe8,
	0x7b, 0x92, 0x4d, 0x7f, 0x03, 0x47, 0xba, 0xba, 0x8e, 0xb1, 0x81, 0x8d, 0x61, 0xeb, 0x3d, 0x06,
	0xd0, 0x01, 0xa4, 0x3a, 0xf8, 0x56, 0x9b, 0x11, 0xba, 0xd6, 0xc1, 0xb7, 0x8f, 0xad, 0xf9, 0x8d,
	0x81, 0x5c, 0xa0, 0xe7, 0x99, 0xbb, 0x84, 0x9a, 0x16, 0x50, 0xf8, 0x20, 0x4b, 0x41, 0x6e, 0x18,
	0x91, 0x0c, 0xc4, 0x03, 0xab, 0xaa, 0xb5, 0xa1, 0x5f, 0xfe, 0x72, 0xca, 0xe5, 0xf8, 0x94, 0xcb,
	0xe2, 0x0f, 0x06, 0x84, 0x45, 0xca, 0x9f, 0xc1, 0xce, 0x99, 0xc9, 0xc2, 0xce, 0x4c, 0x16, 0xdf,
	0x72, 0xfb, 0xc6, 0x18, 0x3f, 0x14, 0x0b, 0x2c, 0xb7, 0x6f, 0x8c, 0x89, 0x53, 0x53, 0x0f, 0x13,
	0x9f, 0x7d, 0x98, 0xff, 0x30, 0x70, 0xa3, 0xe9, 0x88, 0x56, 0x20, 0x2a, 0x57, 0xf9, 0x08, 0x4a,
	0xc2, 0xea, 0x79, 0xbd, 0x5a, 0x97, 0xdf, 0xd6, 0x79, 0x06, 0xa5, 0x81, 0xaf, 0xcb, 0xaa, 0x76,
	0x2c, 0xcb, 0x6a, 0x53, 0x55, 0x8a, 0x8d, 0x46, 0xf9, 0x35, 0x1f, 0x45, 0x5b, 0xb0, 0xa9, 0x94,
	0x2f, 0xa4, 0xa6, 0x24, 0xd7, 0xb5, 0x33, 0xa9, 0x79, 0x56, 0x54, 0x4b, 0xa7, 0x3c, 0x8b, 0xb6,
	0x01, 0x35, 0x8b, 0x27, 0xe5, 0x86, 0x2c, 0xd5, 0x55, 0x4d, 0x91, 0x6b, 0xb5, 0xe3, 0x62, 0xa9,
	0xca, 0xc7, 0x0a, 0xbf, 0xa2, 0x10, 0xad, 0x94, 0xd0, 0x1b, 0x58, 0x9f, 0x98, 0x50, 0x28, 0x1b,
	0x58, 0x35, 0x6f, 0x50, 0x66, 0x77, 0xe7, 0x62, 0x81, 0xa7, 0x62, 0x04, 0x61, 0xd8, 0x9e, 0x3f,
	0x0d, 0xd0, 0x7e, 0x70, 0x71, 0xe9, 0x54, 0xca, 0x1e, 0x2c, 0x3f, 0x34, 0xa2, 0xb9, 0x80, 0xcd,
	0x99, 0x0f, 0x09, 0x09, 0xc1, 0xe5, 0x45, 0x13, 0x20, 0xfb, 0xcf, 0x42, 0x7c, 0x5c, 0xfe, 0xfc,
	0xb6, 0x0a, 0xe5, 0x2f, 0xfd, 0x5c, 0x42, 0xf9, 0xcb, 0x3b, 0x53, 0x8c, 0x1c, 0x17, 0xee, 0xbf,
	0x26, 0x98, 0xef, 0x0f, 0x02, 0x73, 0xf7, 0x20, 0x30, 0x3f, 0x1f, 0x04, 0xe6, 0xd3, 0x40, 0x88,
	0x7c, 0x1e, 0x08, 0x91, 0xbb, 0x81, 0x10, 0xb9, 0x1f, 0x08, 0x11, 0xe0, 0x6d, 0xd7, 0xcc, 0x7b,
	0x56, 0xbb, 0x97, 0x6f, 0xf7, 0xe8, 0xbf, 0xfd, 0xe5, 0x0a, 0xfd, 0x79, 0xf5, 0x3b, 0x00, 0x00,
	0xff, 0xff, 0x88, 0x1f, 0x85, 0x06, 0x2f, 0x08, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GCClient is the client API for GC service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GCClient interface {
	ListKeySpaces(ctx context.Context, in *ListKeySpacesRequest, opts ...grpc.CallOption) (*ListKeySpacesResponse, error)
	GetMinServiceSafePoint(ctx context.Context, in *GetMinServiceSafePointRequest, opts ...grpc.CallOption) (*GetMinServiceSafePointResponse, error)
	UpdateGCSafePoint(ctx context.Context, in *UpdateGCSafePointRequest, opts ...grpc.CallOption) (*UpdateGCSafePointResponse, error)
	UpdateServiceSafePoint(ctx context.Context, in *UpdateServiceSafePointRequest, opts ...grpc.CallOption) (*UpdateServiceSafePointResponse, error)
}

type gCClient struct {
	cc *grpc.ClientConn
}

func NewGCClient(cc *grpc.ClientConn) GCClient {
	return &gCClient{cc}
}

func (c *gCClient) ListKeySpaces(ctx context.Context, in *ListKeySpacesRequest, opts ...grpc.CallOption) (*ListKeySpacesResponse, error) {
	out := new(ListKeySpacesResponse)
	err := c.cc.Invoke(ctx, "/gcpb.GC/ListKeySpaces", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gCClient) GetMinServiceSafePoint(ctx context.Context, in *GetMinServiceSafePointRequest, opts ...grpc.CallOption) (*GetMinServiceSafePointResponse, error) {
	out := new(GetMinServiceSafePointResponse)
	err := c.cc.Invoke(ctx, "/gcpb.GC/GetMinServiceSafePoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gCClient) UpdateGCSafePoint(ctx context.Context, in *UpdateGCSafePointRequest, opts ...grpc.CallOption) (*UpdateGCSafePointResponse, error) {
	out := new(UpdateGCSafePointResponse)
	err := c.cc.Invoke(ctx, "/gcpb.GC/UpdateGCSafePoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gCClient) UpdateServiceSafePoint(ctx context.Context, in *UpdateServiceSafePointRequest, opts ...grpc.CallOption) (*UpdateServiceSafePointResponse, error) {
	out := new(UpdateServiceSafePointResponse)
	err := c.cc.Invoke(ctx, "/gcpb.GC/UpdateServiceSafePoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GCServer is the server API for GC service.
type GCServer interface {
	ListKeySpaces(context.Context, *ListKeySpacesRequest) (*ListKeySpacesResponse, error)
	GetMinServiceSafePoint(context.Context, *GetMinServiceSafePointRequest) (*GetMinServiceSafePointResponse, error)
	UpdateGCSafePoint(context.Context, *UpdateGCSafePointRequest) (*UpdateGCSafePointResponse, error)
	UpdateServiceSafePoint(context.Context, *UpdateServiceSafePointRequest) (*UpdateServiceSafePointResponse, error)
}

// UnimplementedGCServer can be embedded to have forward compatible implementations.
type UnimplementedGCServer struct {
}

func (*UnimplementedGCServer) ListKeySpaces(ctx context.Context, req *ListKeySpacesRequest) (*ListKeySpacesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListKeySpaces not implemented")
}
func (*UnimplementedGCServer) GetMinServiceSafePoint(ctx context.Context, req *GetMinServiceSafePointRequest) (*GetMinServiceSafePointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMinServiceSafePoint not implemented")
}
func (*UnimplementedGCServer) UpdateGCSafePoint(ctx context.Context, req *UpdateGCSafePointRequest) (*UpdateGCSafePointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGCSafePoint not implemented")
}
func (*UnimplementedGCServer) UpdateServiceSafePoint(ctx context.Context, req *UpdateServiceSafePointRequest) (*UpdateServiceSafePointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServiceSafePoint not implemented")
}

func RegisterGCServer(s *grpc.Server, srv GCServer) {
	s.RegisterService(&_GC_serviceDesc, srv)
}

func _GC_ListKeySpaces_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListKeySpacesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GCServer).ListKeySpaces(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gcpb.GC/ListKeySpaces",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GCServer).ListKeySpaces(ctx, req.(*ListKeySpacesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GC_GetMinServiceSafePoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMinServiceSafePointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GCServer).GetMinServiceSafePoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gcpb.GC/GetMinServiceSafePoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GCServer).GetMinServiceSafePoint(ctx, req.(*GetMinServiceSafePointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GC_UpdateGCSafePoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGCSafePointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GCServer).UpdateGCSafePoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gcpb.GC/UpdateGCSafePoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GCServer).UpdateGCSafePoint(ctx, req.(*UpdateGCSafePointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GC_UpdateServiceSafePoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateServiceSafePointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GCServer).UpdateServiceSafePoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/gcpb.GC/UpdateServiceSafePoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GCServer).UpdateServiceSafePoint(ctx, req.(*UpdateServiceSafePointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _GC_serviceDesc = grpc.ServiceDesc{
	ServiceName: "gcpb.GC",
	HandlerType: (*GCServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListKeySpaces",
			Handler:    _GC_ListKeySpaces_Handler,
		},
		{
			MethodName: "GetMinServiceSafePoint",
			Handler:    _GC_GetMinServiceSafePoint_Handler,
		},
		{
			MethodName: "UpdateGCSafePoint",
			Handler:    _GC_UpdateGCSafePoint_Handler,
		},
		{
			MethodName: "UpdateServiceSafePoint",
			Handler:    _GC_UpdateServiceSafePoint_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gcpb.proto",
}

func (m *RequestHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RequestHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RequestHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SenderId != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.SenderId))
		i--
		dAtA[i] = 0x10
	}
	if m.ClusterId != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.ClusterId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ResponseHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResponseHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResponseHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.ClusterId != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.ClusterId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintGcpb(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0x12
	}
	if m.Type != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *KeySpace) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeySpace) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KeySpace) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.GcSafePoint != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.GcSafePoint))
		i--
		dAtA[i] = 0x10
	}
	if len(m.SpaceId) > 0 {
		i -= len(m.SpaceId)
		copy(dAtA[i:], m.SpaceId)
		i = encodeVarintGcpb(dAtA, i, uint64(len(m.SpaceId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ListKeySpacesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListKeySpacesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListKeySpacesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.WithGcSafePoint {
		i--
		if m.WithGcSafePoint {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ListKeySpacesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListKeySpacesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListKeySpacesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.KeySpaces) > 0 {
		for iNdEx := len(m.KeySpaces) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.KeySpaces[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintGcpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetMinServiceSafePointRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMinServiceSafePointRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetMinServiceSafePointRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SpaceId) > 0 {
		i -= len(m.SpaceId)
		copy(dAtA[i:], m.SpaceId)
		i = encodeVarintGcpb(dAtA, i, uint64(len(m.SpaceId)))
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetMinServiceSafePointResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMinServiceSafePointResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetMinServiceSafePointResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Revision != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.Revision))
		i--
		dAtA[i] = 0x18
	}
	if m.SafePoint != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.SafePoint))
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpdateGCSafePointRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGCSafePointRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateGCSafePointRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Revision != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.Revision))
		i--
		dAtA[i] = 0x20
	}
	if m.SafePoint != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.SafePoint))
		i--
		dAtA[i] = 0x18
	}
	if len(m.SpaceId) > 0 {
		i -= len(m.SpaceId)
		copy(dAtA[i:], m.SpaceId)
		i = encodeVarintGcpb(dAtA, i, uint64(len(m.SpaceId)))
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpdateGCSafePointResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGCSafePointResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateGCSafePointResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.NewSafePoint != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.NewSafePoint))
		i--
		dAtA[i] = 0x18
	}
	if m.Succeeded {
		i--
		if m.Succeeded {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpdateServiceSafePointRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateServiceSafePointRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateServiceSafePointRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SafePoint != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.SafePoint))
		i--
		dAtA[i] = 0x28
	}
	if m.TTL != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.TTL))
		i--
		dAtA[i] = 0x20
	}
	if len(m.ServiceId) > 0 {
		i -= len(m.ServiceId)
		copy(dAtA[i:], m.ServiceId)
		i = encodeVarintGcpb(dAtA, i, uint64(len(m.ServiceId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SpaceId) > 0 {
		i -= len(m.SpaceId)
		copy(dAtA[i:], m.SpaceId)
		i = encodeVarintGcpb(dAtA, i, uint64(len(m.SpaceId)))
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpdateServiceSafePointResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateServiceSafePointResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateServiceSafePointResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.NewSafePoint != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.NewSafePoint))
		i--
		dAtA[i] = 0x28
	}
	if m.OldSafePoint != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.OldSafePoint))
		i--
		dAtA[i] = 0x20
	}
	if m.GcSafePoint != 0 {
		i = encodeVarintGcpb(dAtA, i, uint64(m.GcSafePoint))
		i--
		dAtA[i] = 0x18
	}
	if m.Succeeded {
		i--
		if m.Succeeded {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintGcpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintGcpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovGcpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *RequestHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovGcpb(uint64(m.ClusterId))
	}
	if m.SenderId != 0 {
		n += 1 + sovGcpb(uint64(m.SenderId))
	}
	return n
}

func (m *ResponseHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovGcpb(uint64(m.ClusterId))
	}
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovGcpb(uint64(m.Type))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovGcpb(uint64(l))
	}
	return n
}

func (m *KeySpace) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.SpaceId)
	if l > 0 {
		n += 1 + l + sovGcpb(uint64(l))
	}
	if m.GcSafePoint != 0 {
		n += 1 + sovGcpb(uint64(m.GcSafePoint))
	}
	return n
}

func (m *ListKeySpacesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	if m.WithGcSafePoint {
		n += 2
	}
	return n
}

func (m *ListKeySpacesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	if len(m.KeySpaces) > 0 {
		for _, e := range m.KeySpaces {
			l = e.Size()
			n += 1 + l + sovGcpb(uint64(l))
		}
	}
	return n
}

func (m *GetMinServiceSafePointRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	l = len(m.SpaceId)
	if l > 0 {
		n += 1 + l + sovGcpb(uint64(l))
	}
	return n
}

func (m *GetMinServiceSafePointResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	if m.SafePoint != 0 {
		n += 1 + sovGcpb(uint64(m.SafePoint))
	}
	if m.Revision != 0 {
		n += 1 + sovGcpb(uint64(m.Revision))
	}
	return n
}

func (m *UpdateGCSafePointRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	l = len(m.SpaceId)
	if l > 0 {
		n += 1 + l + sovGcpb(uint64(l))
	}
	if m.SafePoint != 0 {
		n += 1 + sovGcpb(uint64(m.SafePoint))
	}
	if m.Revision != 0 {
		n += 1 + sovGcpb(uint64(m.Revision))
	}
	return n
}

func (m *UpdateGCSafePointResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	if m.Succeeded {
		n += 2
	}
	if m.NewSafePoint != 0 {
		n += 1 + sovGcpb(uint64(m.NewSafePoint))
	}
	return n
}

func (m *UpdateServiceSafePointRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	l = len(m.SpaceId)
	if l > 0 {
		n += 1 + l + sovGcpb(uint64(l))
	}
	l = len(m.ServiceId)
	if l > 0 {
		n += 1 + l + sovGcpb(uint64(l))
	}
	if m.TTL != 0 {
		n += 1 + sovGcpb(uint64(m.TTL))
	}
	if m.SafePoint != 0 {
		n += 1 + sovGcpb(uint64(m.SafePoint))
	}
	return n
}

func (m *UpdateServiceSafePointResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovGcpb(uint64(l))
	}
	if m.Succeeded {
		n += 2
	}
	if m.GcSafePoint != 0 {
		n += 1 + sovGcpb(uint64(m.GcSafePoint))
	}
	if m.OldSafePoint != 0 {
		n += 1 + sovGcpb(uint64(m.OldSafePoint))
	}
	if m.NewSafePoint != 0 {
		n += 1 + sovGcpb(uint64(m.NewSafePoint))
	}
	return n
}

func sovGcpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozGcpb(x uint64) (n int) {
	return sovGcpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RequestHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RequestHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RequestHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SenderId", wireType)
			}
			m.SenderId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SenderId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResponseHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResponseHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResponseHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= ErrorType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KeySpace) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeySpace: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeySpace: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpaceId = append(m.SpaceId[:0], dAtA[iNdEx:postIndex]...)
			if m.SpaceId == nil {
				m.SpaceId = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GcSafePoint", wireType)
			}
			m.GcSafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GcSafePoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListKeySpacesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListKeySpacesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListKeySpacesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WithGcSafePoint", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithGcSafePoint = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListKeySpacesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListKeySpacesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListKeySpacesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeySpaces", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeySpaces = append(m.KeySpaces, &KeySpace{})
			if err := m.KeySpaces[len(m.KeySpaces)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMinServiceSafePointRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMinServiceSafePointRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMinServiceSafePointRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpaceId = append(m.SpaceId[:0], dAtA[iNdEx:postIndex]...)
			if m.SpaceId == nil {
				m.SpaceId = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMinServiceSafePointResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMinServiceSafePointResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMinServiceSafePointResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SafePoint", wireType)
			}
			m.SafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SafePoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Revision", wireType)
			}
			m.Revision = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Revision |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGCSafePointRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateGCSafePointRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateGCSafePointRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpaceId = append(m.SpaceId[:0], dAtA[iNdEx:postIndex]...)
			if m.SpaceId == nil {
				m.SpaceId = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SafePoint", wireType)
			}
			m.SafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SafePoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Revision", wireType)
			}
			m.Revision = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Revision |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGCSafePointResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateGCSafePointResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateGCSafePointResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Succeeded", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Succeeded = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewSafePoint", wireType)
			}
			m.NewSafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewSafePoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateServiceSafePointRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateServiceSafePointRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateServiceSafePointRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &RequestHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpaceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpaceId = append(m.SpaceId[:0], dAtA[iNdEx:postIndex]...)
			if m.SpaceId == nil {
				m.SpaceId = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServiceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ServiceId = append(m.ServiceId[:0], dAtA[iNdEx:postIndex]...)
			if m.ServiceId == nil {
				m.ServiceId = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TTL", wireType)
			}
			m.TTL = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TTL |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SafePoint", wireType)
			}
			m.SafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SafePoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateServiceSafePointResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateServiceSafePointResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateServiceSafePointResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGcpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthGcpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &ResponseHeader{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Succeeded", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Succeeded = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GcSafePoint", wireType)
			}
			m.GcSafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GcSafePoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OldSafePoint", wireType)
			}
			m.OldSafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OldSafePoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewSafePoint", wireType)
			}
			m.NewSafePoint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewSafePoint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGcpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthGcpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGcpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGcpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGcpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthGcpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupGcpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthGcpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthGcpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGcpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupGcpb = fmt.Errorf("proto: unexpected end of group")
)
