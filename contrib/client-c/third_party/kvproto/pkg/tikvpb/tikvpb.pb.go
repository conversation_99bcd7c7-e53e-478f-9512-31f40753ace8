// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: tikvpb.proto

package tikvpb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	coprocessor "github.com/pingcap/kvproto/pkg/coprocessor"
	disaggregated "github.com/pingcap/kvproto/pkg/disaggregated"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
	mpp "github.com/pingcap/kvproto/pkg/mpp"
	raft_serverpb "github.com/pingcap/kvproto/pkg/raft_serverpb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type BatchCommandsRequest struct {
	Requests   []*BatchCommandsRequest_Request `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	RequestIds []uint64                        `protobuf:"varint,2,rep,packed,name=request_ids,json=requestIds,proto3" json:"request_ids,omitempty"`
}

func (m *BatchCommandsRequest) Reset()         { *m = BatchCommandsRequest{} }
func (m *BatchCommandsRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCommandsRequest) ProtoMessage()    {}
func (*BatchCommandsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_274fe050f0c997b3, []int{0}
}
func (m *BatchCommandsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchCommandsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchCommandsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchCommandsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCommandsRequest.Merge(m, src)
}
func (m *BatchCommandsRequest) XXX_Size() int {
	return m.Size()
}
func (m *BatchCommandsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCommandsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCommandsRequest proto.InternalMessageInfo

func (m *BatchCommandsRequest) GetRequests() []*BatchCommandsRequest_Request {
	if m != nil {
		return m.Requests
	}
	return nil
}

func (m *BatchCommandsRequest) GetRequestIds() []uint64 {
	if m != nil {
		return m.RequestIds
	}
	return nil
}

type BatchCommandsRequest_Request struct {
	// Types that are valid to be assigned to Cmd:
	//	*BatchCommandsRequest_Request_Get
	//	*BatchCommandsRequest_Request_Scan
	//	*BatchCommandsRequest_Request_Prewrite
	//	*BatchCommandsRequest_Request_Commit
	//	*BatchCommandsRequest_Request_Import
	//	*BatchCommandsRequest_Request_Cleanup
	//	*BatchCommandsRequest_Request_BatchGet
	//	*BatchCommandsRequest_Request_BatchRollback
	//	*BatchCommandsRequest_Request_ScanLock
	//	*BatchCommandsRequest_Request_ResolveLock
	//	*BatchCommandsRequest_Request_GC
	//	*BatchCommandsRequest_Request_DeleteRange
	//	*BatchCommandsRequest_Request_RawGet
	//	*BatchCommandsRequest_Request_RawBatchGet
	//	*BatchCommandsRequest_Request_RawPut
	//	*BatchCommandsRequest_Request_RawBatchPut
	//	*BatchCommandsRequest_Request_RawDelete
	//	*BatchCommandsRequest_Request_RawBatchDelete
	//	*BatchCommandsRequest_Request_RawScan
	//	*BatchCommandsRequest_Request_RawDeleteRange
	//	*BatchCommandsRequest_Request_RawBatchScan
	//	*BatchCommandsRequest_Request_Coprocessor
	//	*BatchCommandsRequest_Request_PessimisticLock
	//	*BatchCommandsRequest_Request_PessimisticRollback
	//	*BatchCommandsRequest_Request_CheckTxnStatus
	//	*BatchCommandsRequest_Request_TxnHeartBeat
	//	*BatchCommandsRequest_Request_CheckSecondaryLocks
	//	*BatchCommandsRequest_Request_RawCoprocessor
	//	*BatchCommandsRequest_Request_FlashbackToVersion
	//	*BatchCommandsRequest_Request_PrepareFlashbackToVersion
	//	*BatchCommandsRequest_Request_Flush
	//	*BatchCommandsRequest_Request_BufferBatchGet
	//	*BatchCommandsRequest_Request_GetHealthFeedback
	//	*BatchCommandsRequest_Request_BroadcastTxnStatus
	//	*BatchCommandsRequest_Request_Empty
	Cmd isBatchCommandsRequest_Request_Cmd `protobuf_oneof:"cmd"`
}

func (m *BatchCommandsRequest_Request) Reset()         { *m = BatchCommandsRequest_Request{} }
func (m *BatchCommandsRequest_Request) String() string { return proto.CompactTextString(m) }
func (*BatchCommandsRequest_Request) ProtoMessage()    {}
func (*BatchCommandsRequest_Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_274fe050f0c997b3, []int{0, 0}
}
func (m *BatchCommandsRequest_Request) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchCommandsRequest_Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchCommandsRequest_Request.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchCommandsRequest_Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCommandsRequest_Request.Merge(m, src)
}
func (m *BatchCommandsRequest_Request) XXX_Size() int {
	return m.Size()
}
func (m *BatchCommandsRequest_Request) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCommandsRequest_Request.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCommandsRequest_Request proto.InternalMessageInfo

type isBatchCommandsRequest_Request_Cmd interface {
	isBatchCommandsRequest_Request_Cmd()
	MarshalTo([]byte) (int, error)
	Size() int
}

type BatchCommandsRequest_Request_Get struct {
	Get *kvrpcpb.GetRequest `protobuf:"bytes,1,opt,name=Get,proto3,oneof" json:"Get,omitempty"`
}
type BatchCommandsRequest_Request_Scan struct {
	Scan *kvrpcpb.ScanRequest `protobuf:"bytes,2,opt,name=Scan,proto3,oneof" json:"Scan,omitempty"`
}
type BatchCommandsRequest_Request_Prewrite struct {
	Prewrite *kvrpcpb.PrewriteRequest `protobuf:"bytes,3,opt,name=Prewrite,proto3,oneof" json:"Prewrite,omitempty"`
}
type BatchCommandsRequest_Request_Commit struct {
	Commit *kvrpcpb.CommitRequest `protobuf:"bytes,4,opt,name=Commit,proto3,oneof" json:"Commit,omitempty"`
}
type BatchCommandsRequest_Request_Import struct {
	Import *kvrpcpb.ImportRequest `protobuf:"bytes,5,opt,name=Import,proto3,oneof" json:"Import,omitempty"`
}
type BatchCommandsRequest_Request_Cleanup struct {
	Cleanup *kvrpcpb.CleanupRequest `protobuf:"bytes,6,opt,name=Cleanup,proto3,oneof" json:"Cleanup,omitempty"`
}
type BatchCommandsRequest_Request_BatchGet struct {
	BatchGet *kvrpcpb.BatchGetRequest `protobuf:"bytes,7,opt,name=BatchGet,proto3,oneof" json:"BatchGet,omitempty"`
}
type BatchCommandsRequest_Request_BatchRollback struct {
	BatchRollback *kvrpcpb.BatchRollbackRequest `protobuf:"bytes,8,opt,name=BatchRollback,proto3,oneof" json:"BatchRollback,omitempty"`
}
type BatchCommandsRequest_Request_ScanLock struct {
	ScanLock *kvrpcpb.ScanLockRequest `protobuf:"bytes,9,opt,name=ScanLock,proto3,oneof" json:"ScanLock,omitempty"`
}
type BatchCommandsRequest_Request_ResolveLock struct {
	ResolveLock *kvrpcpb.ResolveLockRequest `protobuf:"bytes,10,opt,name=ResolveLock,proto3,oneof" json:"ResolveLock,omitempty"`
}
type BatchCommandsRequest_Request_GC struct {
	GC *kvrpcpb.GCRequest `protobuf:"bytes,11,opt,name=GC,proto3,oneof" json:"GC,omitempty"`
}
type BatchCommandsRequest_Request_DeleteRange struct {
	DeleteRange *kvrpcpb.DeleteRangeRequest `protobuf:"bytes,12,opt,name=DeleteRange,proto3,oneof" json:"DeleteRange,omitempty"`
}
type BatchCommandsRequest_Request_RawGet struct {
	RawGet *kvrpcpb.RawGetRequest `protobuf:"bytes,13,opt,name=RawGet,proto3,oneof" json:"RawGet,omitempty"`
}
type BatchCommandsRequest_Request_RawBatchGet struct {
	RawBatchGet *kvrpcpb.RawBatchGetRequest `protobuf:"bytes,14,opt,name=RawBatchGet,proto3,oneof" json:"RawBatchGet,omitempty"`
}
type BatchCommandsRequest_Request_RawPut struct {
	RawPut *kvrpcpb.RawPutRequest `protobuf:"bytes,15,opt,name=RawPut,proto3,oneof" json:"RawPut,omitempty"`
}
type BatchCommandsRequest_Request_RawBatchPut struct {
	RawBatchPut *kvrpcpb.RawBatchPutRequest `protobuf:"bytes,16,opt,name=RawBatchPut,proto3,oneof" json:"RawBatchPut,omitempty"`
}
type BatchCommandsRequest_Request_RawDelete struct {
	RawDelete *kvrpcpb.RawDeleteRequest `protobuf:"bytes,17,opt,name=RawDelete,proto3,oneof" json:"RawDelete,omitempty"`
}
type BatchCommandsRequest_Request_RawBatchDelete struct {
	RawBatchDelete *kvrpcpb.RawBatchDeleteRequest `protobuf:"bytes,18,opt,name=RawBatchDelete,proto3,oneof" json:"RawBatchDelete,omitempty"`
}
type BatchCommandsRequest_Request_RawScan struct {
	RawScan *kvrpcpb.RawScanRequest `protobuf:"bytes,19,opt,name=RawScan,proto3,oneof" json:"RawScan,omitempty"`
}
type BatchCommandsRequest_Request_RawDeleteRange struct {
	RawDeleteRange *kvrpcpb.RawDeleteRangeRequest `protobuf:"bytes,20,opt,name=RawDeleteRange,proto3,oneof" json:"RawDeleteRange,omitempty"`
}
type BatchCommandsRequest_Request_RawBatchScan struct {
	RawBatchScan *kvrpcpb.RawBatchScanRequest `protobuf:"bytes,21,opt,name=RawBatchScan,proto3,oneof" json:"RawBatchScan,omitempty"`
}
type BatchCommandsRequest_Request_Coprocessor struct {
	Coprocessor *coprocessor.Request `protobuf:"bytes,22,opt,name=Coprocessor,proto3,oneof" json:"Coprocessor,omitempty"`
}
type BatchCommandsRequest_Request_PessimisticLock struct {
	PessimisticLock *kvrpcpb.PessimisticLockRequest `protobuf:"bytes,23,opt,name=PessimisticLock,proto3,oneof" json:"PessimisticLock,omitempty"`
}
type BatchCommandsRequest_Request_PessimisticRollback struct {
	PessimisticRollback *kvrpcpb.PessimisticRollbackRequest `protobuf:"bytes,24,opt,name=PessimisticRollback,proto3,oneof" json:"PessimisticRollback,omitempty"`
}
type BatchCommandsRequest_Request_CheckTxnStatus struct {
	CheckTxnStatus *kvrpcpb.CheckTxnStatusRequest `protobuf:"bytes,25,opt,name=CheckTxnStatus,proto3,oneof" json:"CheckTxnStatus,omitempty"`
}
type BatchCommandsRequest_Request_TxnHeartBeat struct {
	TxnHeartBeat *kvrpcpb.TxnHeartBeatRequest `protobuf:"bytes,26,opt,name=TxnHeartBeat,proto3,oneof" json:"TxnHeartBeat,omitempty"`
}
type BatchCommandsRequest_Request_CheckSecondaryLocks struct {
	CheckSecondaryLocks *kvrpcpb.CheckSecondaryLocksRequest `protobuf:"bytes,33,opt,name=CheckSecondaryLocks,proto3,oneof" json:"CheckSecondaryLocks,omitempty"`
}
type BatchCommandsRequest_Request_RawCoprocessor struct {
	RawCoprocessor *kvrpcpb.RawCoprocessorRequest `protobuf:"bytes,34,opt,name=RawCoprocessor,proto3,oneof" json:"RawCoprocessor,omitempty"`
}
type BatchCommandsRequest_Request_FlashbackToVersion struct {
	FlashbackToVersion *kvrpcpb.FlashbackToVersionRequest `protobuf:"bytes,35,opt,name=FlashbackToVersion,proto3,oneof" json:"FlashbackToVersion,omitempty"`
}
type BatchCommandsRequest_Request_PrepareFlashbackToVersion struct {
	PrepareFlashbackToVersion *kvrpcpb.PrepareFlashbackToVersionRequest `protobuf:"bytes,36,opt,name=PrepareFlashbackToVersion,proto3,oneof" json:"PrepareFlashbackToVersion,omitempty"`
}
type BatchCommandsRequest_Request_Flush struct {
	Flush *kvrpcpb.FlushRequest `protobuf:"bytes,37,opt,name=Flush,proto3,oneof" json:"Flush,omitempty"`
}
type BatchCommandsRequest_Request_BufferBatchGet struct {
	BufferBatchGet *kvrpcpb.BufferBatchGetRequest `protobuf:"bytes,38,opt,name=BufferBatchGet,proto3,oneof" json:"BufferBatchGet,omitempty"`
}
type BatchCommandsRequest_Request_GetHealthFeedback struct {
	GetHealthFeedback *kvrpcpb.GetHealthFeedbackRequest `protobuf:"bytes,39,opt,name=GetHealthFeedback,proto3,oneof" json:"GetHealthFeedback,omitempty"`
}
type BatchCommandsRequest_Request_BroadcastTxnStatus struct {
	BroadcastTxnStatus *kvrpcpb.BroadcastTxnStatusRequest `protobuf:"bytes,40,opt,name=BroadcastTxnStatus,proto3,oneof" json:"BroadcastTxnStatus,omitempty"`
}
type BatchCommandsRequest_Request_Empty struct {
	Empty *BatchCommandsEmptyRequest `protobuf:"bytes,255,opt,name=Empty,proto3,oneof" json:"Empty,omitempty"`
}

func (*BatchCommandsRequest_Request_Get) isBatchCommandsRequest_Request_Cmd()                       {}
func (*BatchCommandsRequest_Request_Scan) isBatchCommandsRequest_Request_Cmd()                      {}
func (*BatchCommandsRequest_Request_Prewrite) isBatchCommandsRequest_Request_Cmd()                  {}
func (*BatchCommandsRequest_Request_Commit) isBatchCommandsRequest_Request_Cmd()                    {}
func (*BatchCommandsRequest_Request_Import) isBatchCommandsRequest_Request_Cmd()                    {}
func (*BatchCommandsRequest_Request_Cleanup) isBatchCommandsRequest_Request_Cmd()                   {}
func (*BatchCommandsRequest_Request_BatchGet) isBatchCommandsRequest_Request_Cmd()                  {}
func (*BatchCommandsRequest_Request_BatchRollback) isBatchCommandsRequest_Request_Cmd()             {}
func (*BatchCommandsRequest_Request_ScanLock) isBatchCommandsRequest_Request_Cmd()                  {}
func (*BatchCommandsRequest_Request_ResolveLock) isBatchCommandsRequest_Request_Cmd()               {}
func (*BatchCommandsRequest_Request_GC) isBatchCommandsRequest_Request_Cmd()                        {}
func (*BatchCommandsRequest_Request_DeleteRange) isBatchCommandsRequest_Request_Cmd()               {}
func (*BatchCommandsRequest_Request_RawGet) isBatchCommandsRequest_Request_Cmd()                    {}
func (*BatchCommandsRequest_Request_RawBatchGet) isBatchCommandsRequest_Request_Cmd()               {}
func (*BatchCommandsRequest_Request_RawPut) isBatchCommandsRequest_Request_Cmd()                    {}
func (*BatchCommandsRequest_Request_RawBatchPut) isBatchCommandsRequest_Request_Cmd()               {}
func (*BatchCommandsRequest_Request_RawDelete) isBatchCommandsRequest_Request_Cmd()                 {}
func (*BatchCommandsRequest_Request_RawBatchDelete) isBatchCommandsRequest_Request_Cmd()            {}
func (*BatchCommandsRequest_Request_RawScan) isBatchCommandsRequest_Request_Cmd()                   {}
func (*BatchCommandsRequest_Request_RawDeleteRange) isBatchCommandsRequest_Request_Cmd()            {}
func (*BatchCommandsRequest_Request_RawBatchScan) isBatchCommandsRequest_Request_Cmd()              {}
func (*BatchCommandsRequest_Request_Coprocessor) isBatchCommandsRequest_Request_Cmd()               {}
func (*BatchCommandsRequest_Request_PessimisticLock) isBatchCommandsRequest_Request_Cmd()           {}
func (*BatchCommandsRequest_Request_PessimisticRollback) isBatchCommandsRequest_Request_Cmd()       {}
func (*BatchCommandsRequest_Request_CheckTxnStatus) isBatchCommandsRequest_Request_Cmd()            {}
func (*BatchCommandsRequest_Request_TxnHeartBeat) isBatchCommandsRequest_Request_Cmd()              {}
func (*BatchCommandsRequest_Request_CheckSecondaryLocks) isBatchCommandsRequest_Request_Cmd()       {}
func (*BatchCommandsRequest_Request_RawCoprocessor) isBatchCommandsRequest_Request_Cmd()            {}
func (*BatchCommandsRequest_Request_FlashbackToVersion) isBatchCommandsRequest_Request_Cmd()        {}
func (*BatchCommandsRequest_Request_PrepareFlashbackToVersion) isBatchCommandsRequest_Request_Cmd() {}
func (*BatchCommandsRequest_Request_Flush) isBatchCommandsRequest_Request_Cmd()                     {}
func (*BatchCommandsRequest_Request_BufferBatchGet) isBatchCommandsRequest_Request_Cmd()            {}
func (*BatchCommandsRequest_Request_GetHealthFeedback) isBatchCommandsRequest_Request_Cmd()         {}
func (*BatchCommandsRequest_Request_BroadcastTxnStatus) isBatchCommandsRequest_Request_Cmd()        {}
func (*BatchCommandsRequest_Request_Empty) isBatchCommandsRequest_Request_Cmd()                     {}

func (m *BatchCommandsRequest_Request) GetCmd() isBatchCommandsRequest_Request_Cmd {
	if m != nil {
		return m.Cmd
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetGet() *kvrpcpb.GetRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Get); ok {
		return x.Get
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetScan() *kvrpcpb.ScanRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Scan); ok {
		return x.Scan
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetPrewrite() *kvrpcpb.PrewriteRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Prewrite); ok {
		return x.Prewrite
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetCommit() *kvrpcpb.CommitRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Commit); ok {
		return x.Commit
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetImport() *kvrpcpb.ImportRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Import); ok {
		return x.Import
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetCleanup() *kvrpcpb.CleanupRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Cleanup); ok {
		return x.Cleanup
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetBatchGet() *kvrpcpb.BatchGetRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_BatchGet); ok {
		return x.BatchGet
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetBatchRollback() *kvrpcpb.BatchRollbackRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_BatchRollback); ok {
		return x.BatchRollback
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetScanLock() *kvrpcpb.ScanLockRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_ScanLock); ok {
		return x.ScanLock
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetResolveLock() *kvrpcpb.ResolveLockRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_ResolveLock); ok {
		return x.ResolveLock
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetGC() *kvrpcpb.GCRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_GC); ok {
		return x.GC
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetDeleteRange() *kvrpcpb.DeleteRangeRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_DeleteRange); ok {
		return x.DeleteRange
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawGet() *kvrpcpb.RawGetRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawGet); ok {
		return x.RawGet
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawBatchGet() *kvrpcpb.RawBatchGetRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawBatchGet); ok {
		return x.RawBatchGet
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawPut() *kvrpcpb.RawPutRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawPut); ok {
		return x.RawPut
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawBatchPut() *kvrpcpb.RawBatchPutRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawBatchPut); ok {
		return x.RawBatchPut
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawDelete() *kvrpcpb.RawDeleteRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawDelete); ok {
		return x.RawDelete
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawBatchDelete() *kvrpcpb.RawBatchDeleteRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawBatchDelete); ok {
		return x.RawBatchDelete
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawScan() *kvrpcpb.RawScanRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawScan); ok {
		return x.RawScan
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawDeleteRange() *kvrpcpb.RawDeleteRangeRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawDeleteRange); ok {
		return x.RawDeleteRange
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawBatchScan() *kvrpcpb.RawBatchScanRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawBatchScan); ok {
		return x.RawBatchScan
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetCoprocessor() *coprocessor.Request {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Coprocessor); ok {
		return x.Coprocessor
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetPessimisticLock() *kvrpcpb.PessimisticLockRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_PessimisticLock); ok {
		return x.PessimisticLock
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetPessimisticRollback() *kvrpcpb.PessimisticRollbackRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_PessimisticRollback); ok {
		return x.PessimisticRollback
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetCheckTxnStatus() *kvrpcpb.CheckTxnStatusRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_CheckTxnStatus); ok {
		return x.CheckTxnStatus
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetTxnHeartBeat() *kvrpcpb.TxnHeartBeatRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_TxnHeartBeat); ok {
		return x.TxnHeartBeat
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetCheckSecondaryLocks() *kvrpcpb.CheckSecondaryLocksRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_CheckSecondaryLocks); ok {
		return x.CheckSecondaryLocks
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetRawCoprocessor() *kvrpcpb.RawCoprocessorRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_RawCoprocessor); ok {
		return x.RawCoprocessor
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetFlashbackToVersion() *kvrpcpb.FlashbackToVersionRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_FlashbackToVersion); ok {
		return x.FlashbackToVersion
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetPrepareFlashbackToVersion() *kvrpcpb.PrepareFlashbackToVersionRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_PrepareFlashbackToVersion); ok {
		return x.PrepareFlashbackToVersion
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetFlush() *kvrpcpb.FlushRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Flush); ok {
		return x.Flush
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetBufferBatchGet() *kvrpcpb.BufferBatchGetRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_BufferBatchGet); ok {
		return x.BufferBatchGet
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetGetHealthFeedback() *kvrpcpb.GetHealthFeedbackRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_GetHealthFeedback); ok {
		return x.GetHealthFeedback
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetBroadcastTxnStatus() *kvrpcpb.BroadcastTxnStatusRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_BroadcastTxnStatus); ok {
		return x.BroadcastTxnStatus
	}
	return nil
}

func (m *BatchCommandsRequest_Request) GetEmpty() *BatchCommandsEmptyRequest {
	if x, ok := m.GetCmd().(*BatchCommandsRequest_Request_Empty); ok {
		return x.Empty
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*BatchCommandsRequest_Request) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*BatchCommandsRequest_Request_Get)(nil),
		(*BatchCommandsRequest_Request_Scan)(nil),
		(*BatchCommandsRequest_Request_Prewrite)(nil),
		(*BatchCommandsRequest_Request_Commit)(nil),
		(*BatchCommandsRequest_Request_Import)(nil),
		(*BatchCommandsRequest_Request_Cleanup)(nil),
		(*BatchCommandsRequest_Request_BatchGet)(nil),
		(*BatchCommandsRequest_Request_BatchRollback)(nil),
		(*BatchCommandsRequest_Request_ScanLock)(nil),
		(*BatchCommandsRequest_Request_ResolveLock)(nil),
		(*BatchCommandsRequest_Request_GC)(nil),
		(*BatchCommandsRequest_Request_DeleteRange)(nil),
		(*BatchCommandsRequest_Request_RawGet)(nil),
		(*BatchCommandsRequest_Request_RawBatchGet)(nil),
		(*BatchCommandsRequest_Request_RawPut)(nil),
		(*BatchCommandsRequest_Request_RawBatchPut)(nil),
		(*BatchCommandsRequest_Request_RawDelete)(nil),
		(*BatchCommandsRequest_Request_RawBatchDelete)(nil),
		(*BatchCommandsRequest_Request_RawScan)(nil),
		(*BatchCommandsRequest_Request_RawDeleteRange)(nil),
		(*BatchCommandsRequest_Request_RawBatchScan)(nil),
		(*BatchCommandsRequest_Request_Coprocessor)(nil),
		(*BatchCommandsRequest_Request_PessimisticLock)(nil),
		(*BatchCommandsRequest_Request_PessimisticRollback)(nil),
		(*BatchCommandsRequest_Request_CheckTxnStatus)(nil),
		(*BatchCommandsRequest_Request_TxnHeartBeat)(nil),
		(*BatchCommandsRequest_Request_CheckSecondaryLocks)(nil),
		(*BatchCommandsRequest_Request_RawCoprocessor)(nil),
		(*BatchCommandsRequest_Request_FlashbackToVersion)(nil),
		(*BatchCommandsRequest_Request_PrepareFlashbackToVersion)(nil),
		(*BatchCommandsRequest_Request_Flush)(nil),
		(*BatchCommandsRequest_Request_BufferBatchGet)(nil),
		(*BatchCommandsRequest_Request_GetHealthFeedback)(nil),
		(*BatchCommandsRequest_Request_BroadcastTxnStatus)(nil),
		(*BatchCommandsRequest_Request_Empty)(nil),
	}
}

type BatchCommandsResponse struct {
	Responses  []*BatchCommandsResponse_Response `protobuf:"bytes,1,rep,name=responses,proto3" json:"responses,omitempty"`
	RequestIds []uint64                          `protobuf:"varint,2,rep,packed,name=request_ids,json=requestIds,proto3" json:"request_ids,omitempty"`
	// 280 means TiKV gRPC cpu usage is 280%.
	TransportLayerLoad uint64                  `protobuf:"varint,3,opt,name=transport_layer_load,json=transportLayerLoad,proto3" json:"transport_layer_load,omitempty"`
	HealthFeedback     *kvrpcpb.HealthFeedback `protobuf:"bytes,4,opt,name=health_feedback,json=healthFeedback,proto3" json:"health_feedback,omitempty"`
}

func (m *BatchCommandsResponse) Reset()         { *m = BatchCommandsResponse{} }
func (m *BatchCommandsResponse) String() string { return proto.CompactTextString(m) }
func (*BatchCommandsResponse) ProtoMessage()    {}
func (*BatchCommandsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_274fe050f0c997b3, []int{1}
}
func (m *BatchCommandsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchCommandsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchCommandsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchCommandsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCommandsResponse.Merge(m, src)
}
func (m *BatchCommandsResponse) XXX_Size() int {
	return m.Size()
}
func (m *BatchCommandsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCommandsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCommandsResponse proto.InternalMessageInfo

func (m *BatchCommandsResponse) GetResponses() []*BatchCommandsResponse_Response {
	if m != nil {
		return m.Responses
	}
	return nil
}

func (m *BatchCommandsResponse) GetRequestIds() []uint64 {
	if m != nil {
		return m.RequestIds
	}
	return nil
}

func (m *BatchCommandsResponse) GetTransportLayerLoad() uint64 {
	if m != nil {
		return m.TransportLayerLoad
	}
	return 0
}

func (m *BatchCommandsResponse) GetHealthFeedback() *kvrpcpb.HealthFeedback {
	if m != nil {
		return m.HealthFeedback
	}
	return nil
}

type BatchCommandsResponse_Response struct {
	// Types that are valid to be assigned to Cmd:
	//	*BatchCommandsResponse_Response_Get
	//	*BatchCommandsResponse_Response_Scan
	//	*BatchCommandsResponse_Response_Prewrite
	//	*BatchCommandsResponse_Response_Commit
	//	*BatchCommandsResponse_Response_Import
	//	*BatchCommandsResponse_Response_Cleanup
	//	*BatchCommandsResponse_Response_BatchGet
	//	*BatchCommandsResponse_Response_BatchRollback
	//	*BatchCommandsResponse_Response_ScanLock
	//	*BatchCommandsResponse_Response_ResolveLock
	//	*BatchCommandsResponse_Response_GC
	//	*BatchCommandsResponse_Response_DeleteRange
	//	*BatchCommandsResponse_Response_RawGet
	//	*BatchCommandsResponse_Response_RawBatchGet
	//	*BatchCommandsResponse_Response_RawPut
	//	*BatchCommandsResponse_Response_RawBatchPut
	//	*BatchCommandsResponse_Response_RawDelete
	//	*BatchCommandsResponse_Response_RawBatchDelete
	//	*BatchCommandsResponse_Response_RawScan
	//	*BatchCommandsResponse_Response_RawDeleteRange
	//	*BatchCommandsResponse_Response_RawBatchScan
	//	*BatchCommandsResponse_Response_Coprocessor
	//	*BatchCommandsResponse_Response_PessimisticLock
	//	*BatchCommandsResponse_Response_PessimisticRollback
	//	*BatchCommandsResponse_Response_CheckTxnStatus
	//	*BatchCommandsResponse_Response_TxnHeartBeat
	//	*BatchCommandsResponse_Response_CheckSecondaryLocks
	//	*BatchCommandsResponse_Response_RawCoprocessor
	//	*BatchCommandsResponse_Response_FlashbackToVersion
	//	*BatchCommandsResponse_Response_PrepareFlashbackToVersion
	//	*BatchCommandsResponse_Response_Flush
	//	*BatchCommandsResponse_Response_BufferBatchGet
	//	*BatchCommandsResponse_Response_GetHealthFeedback
	//	*BatchCommandsResponse_Response_BroadcastTxnStatus
	//	*BatchCommandsResponse_Response_Empty
	Cmd isBatchCommandsResponse_Response_Cmd `protobuf_oneof:"cmd"`
}

func (m *BatchCommandsResponse_Response) Reset()         { *m = BatchCommandsResponse_Response{} }
func (m *BatchCommandsResponse_Response) String() string { return proto.CompactTextString(m) }
func (*BatchCommandsResponse_Response) ProtoMessage()    {}
func (*BatchCommandsResponse_Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_274fe050f0c997b3, []int{1, 0}
}
func (m *BatchCommandsResponse_Response) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchCommandsResponse_Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchCommandsResponse_Response.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchCommandsResponse_Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCommandsResponse_Response.Merge(m, src)
}
func (m *BatchCommandsResponse_Response) XXX_Size() int {
	return m.Size()
}
func (m *BatchCommandsResponse_Response) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCommandsResponse_Response.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCommandsResponse_Response proto.InternalMessageInfo

type isBatchCommandsResponse_Response_Cmd interface {
	isBatchCommandsResponse_Response_Cmd()
	MarshalTo([]byte) (int, error)
	Size() int
}

type BatchCommandsResponse_Response_Get struct {
	Get *kvrpcpb.GetResponse `protobuf:"bytes,1,opt,name=Get,proto3,oneof" json:"Get,omitempty"`
}
type BatchCommandsResponse_Response_Scan struct {
	Scan *kvrpcpb.ScanResponse `protobuf:"bytes,2,opt,name=Scan,proto3,oneof" json:"Scan,omitempty"`
}
type BatchCommandsResponse_Response_Prewrite struct {
	Prewrite *kvrpcpb.PrewriteResponse `protobuf:"bytes,3,opt,name=Prewrite,proto3,oneof" json:"Prewrite,omitempty"`
}
type BatchCommandsResponse_Response_Commit struct {
	Commit *kvrpcpb.CommitResponse `protobuf:"bytes,4,opt,name=Commit,proto3,oneof" json:"Commit,omitempty"`
}
type BatchCommandsResponse_Response_Import struct {
	Import *kvrpcpb.ImportResponse `protobuf:"bytes,5,opt,name=Import,proto3,oneof" json:"Import,omitempty"`
}
type BatchCommandsResponse_Response_Cleanup struct {
	Cleanup *kvrpcpb.CleanupResponse `protobuf:"bytes,6,opt,name=Cleanup,proto3,oneof" json:"Cleanup,omitempty"`
}
type BatchCommandsResponse_Response_BatchGet struct {
	BatchGet *kvrpcpb.BatchGetResponse `protobuf:"bytes,7,opt,name=BatchGet,proto3,oneof" json:"BatchGet,omitempty"`
}
type BatchCommandsResponse_Response_BatchRollback struct {
	BatchRollback *kvrpcpb.BatchRollbackResponse `protobuf:"bytes,8,opt,name=BatchRollback,proto3,oneof" json:"BatchRollback,omitempty"`
}
type BatchCommandsResponse_Response_ScanLock struct {
	ScanLock *kvrpcpb.ScanLockResponse `protobuf:"bytes,9,opt,name=ScanLock,proto3,oneof" json:"ScanLock,omitempty"`
}
type BatchCommandsResponse_Response_ResolveLock struct {
	ResolveLock *kvrpcpb.ResolveLockResponse `protobuf:"bytes,10,opt,name=ResolveLock,proto3,oneof" json:"ResolveLock,omitempty"`
}
type BatchCommandsResponse_Response_GC struct {
	GC *kvrpcpb.GCResponse `protobuf:"bytes,11,opt,name=GC,proto3,oneof" json:"GC,omitempty"`
}
type BatchCommandsResponse_Response_DeleteRange struct {
	DeleteRange *kvrpcpb.DeleteRangeResponse `protobuf:"bytes,12,opt,name=DeleteRange,proto3,oneof" json:"DeleteRange,omitempty"`
}
type BatchCommandsResponse_Response_RawGet struct {
	RawGet *kvrpcpb.RawGetResponse `protobuf:"bytes,13,opt,name=RawGet,proto3,oneof" json:"RawGet,omitempty"`
}
type BatchCommandsResponse_Response_RawBatchGet struct {
	RawBatchGet *kvrpcpb.RawBatchGetResponse `protobuf:"bytes,14,opt,name=RawBatchGet,proto3,oneof" json:"RawBatchGet,omitempty"`
}
type BatchCommandsResponse_Response_RawPut struct {
	RawPut *kvrpcpb.RawPutResponse `protobuf:"bytes,15,opt,name=RawPut,proto3,oneof" json:"RawPut,omitempty"`
}
type BatchCommandsResponse_Response_RawBatchPut struct {
	RawBatchPut *kvrpcpb.RawBatchPutResponse `protobuf:"bytes,16,opt,name=RawBatchPut,proto3,oneof" json:"RawBatchPut,omitempty"`
}
type BatchCommandsResponse_Response_RawDelete struct {
	RawDelete *kvrpcpb.RawDeleteResponse `protobuf:"bytes,17,opt,name=RawDelete,proto3,oneof" json:"RawDelete,omitempty"`
}
type BatchCommandsResponse_Response_RawBatchDelete struct {
	RawBatchDelete *kvrpcpb.RawBatchDeleteResponse `protobuf:"bytes,18,opt,name=RawBatchDelete,proto3,oneof" json:"RawBatchDelete,omitempty"`
}
type BatchCommandsResponse_Response_RawScan struct {
	RawScan *kvrpcpb.RawScanResponse `protobuf:"bytes,19,opt,name=RawScan,proto3,oneof" json:"RawScan,omitempty"`
}
type BatchCommandsResponse_Response_RawDeleteRange struct {
	RawDeleteRange *kvrpcpb.RawDeleteRangeResponse `protobuf:"bytes,20,opt,name=RawDeleteRange,proto3,oneof" json:"RawDeleteRange,omitempty"`
}
type BatchCommandsResponse_Response_RawBatchScan struct {
	RawBatchScan *kvrpcpb.RawBatchScanResponse `protobuf:"bytes,21,opt,name=RawBatchScan,proto3,oneof" json:"RawBatchScan,omitempty"`
}
type BatchCommandsResponse_Response_Coprocessor struct {
	Coprocessor *coprocessor.Response `protobuf:"bytes,22,opt,name=Coprocessor,proto3,oneof" json:"Coprocessor,omitempty"`
}
type BatchCommandsResponse_Response_PessimisticLock struct {
	PessimisticLock *kvrpcpb.PessimisticLockResponse `protobuf:"bytes,23,opt,name=PessimisticLock,proto3,oneof" json:"PessimisticLock,omitempty"`
}
type BatchCommandsResponse_Response_PessimisticRollback struct {
	PessimisticRollback *kvrpcpb.PessimisticRollbackResponse `protobuf:"bytes,24,opt,name=PessimisticRollback,proto3,oneof" json:"PessimisticRollback,omitempty"`
}
type BatchCommandsResponse_Response_CheckTxnStatus struct {
	CheckTxnStatus *kvrpcpb.CheckTxnStatusResponse `protobuf:"bytes,25,opt,name=CheckTxnStatus,proto3,oneof" json:"CheckTxnStatus,omitempty"`
}
type BatchCommandsResponse_Response_TxnHeartBeat struct {
	TxnHeartBeat *kvrpcpb.TxnHeartBeatResponse `protobuf:"bytes,26,opt,name=TxnHeartBeat,proto3,oneof" json:"TxnHeartBeat,omitempty"`
}
type BatchCommandsResponse_Response_CheckSecondaryLocks struct {
	CheckSecondaryLocks *kvrpcpb.CheckSecondaryLocksResponse `protobuf:"bytes,33,opt,name=CheckSecondaryLocks,proto3,oneof" json:"CheckSecondaryLocks,omitempty"`
}
type BatchCommandsResponse_Response_RawCoprocessor struct {
	RawCoprocessor *kvrpcpb.RawCoprocessorResponse `protobuf:"bytes,34,opt,name=RawCoprocessor,proto3,oneof" json:"RawCoprocessor,omitempty"`
}
type BatchCommandsResponse_Response_FlashbackToVersion struct {
	FlashbackToVersion *kvrpcpb.FlashbackToVersionResponse `protobuf:"bytes,35,opt,name=FlashbackToVersion,proto3,oneof" json:"FlashbackToVersion,omitempty"`
}
type BatchCommandsResponse_Response_PrepareFlashbackToVersion struct {
	PrepareFlashbackToVersion *kvrpcpb.PrepareFlashbackToVersionResponse `protobuf:"bytes,36,opt,name=PrepareFlashbackToVersion,proto3,oneof" json:"PrepareFlashbackToVersion,omitempty"`
}
type BatchCommandsResponse_Response_Flush struct {
	Flush *kvrpcpb.FlushResponse `protobuf:"bytes,37,opt,name=Flush,proto3,oneof" json:"Flush,omitempty"`
}
type BatchCommandsResponse_Response_BufferBatchGet struct {
	BufferBatchGet *kvrpcpb.BufferBatchGetResponse `protobuf:"bytes,38,opt,name=BufferBatchGet,proto3,oneof" json:"BufferBatchGet,omitempty"`
}
type BatchCommandsResponse_Response_GetHealthFeedback struct {
	GetHealthFeedback *kvrpcpb.GetHealthFeedbackResponse `protobuf:"bytes,39,opt,name=GetHealthFeedback,proto3,oneof" json:"GetHealthFeedback,omitempty"`
}
type BatchCommandsResponse_Response_BroadcastTxnStatus struct {
	BroadcastTxnStatus *kvrpcpb.BroadcastTxnStatusResponse `protobuf:"bytes,40,opt,name=BroadcastTxnStatus,proto3,oneof" json:"BroadcastTxnStatus,omitempty"`
}
type BatchCommandsResponse_Response_Empty struct {
	Empty *BatchCommandsEmptyResponse `protobuf:"bytes,255,opt,name=Empty,proto3,oneof" json:"Empty,omitempty"`
}

func (*BatchCommandsResponse_Response_Get) isBatchCommandsResponse_Response_Cmd()                 {}
func (*BatchCommandsResponse_Response_Scan) isBatchCommandsResponse_Response_Cmd()                {}
func (*BatchCommandsResponse_Response_Prewrite) isBatchCommandsResponse_Response_Cmd()            {}
func (*BatchCommandsResponse_Response_Commit) isBatchCommandsResponse_Response_Cmd()              {}
func (*BatchCommandsResponse_Response_Import) isBatchCommandsResponse_Response_Cmd()              {}
func (*BatchCommandsResponse_Response_Cleanup) isBatchCommandsResponse_Response_Cmd()             {}
func (*BatchCommandsResponse_Response_BatchGet) isBatchCommandsResponse_Response_Cmd()            {}
func (*BatchCommandsResponse_Response_BatchRollback) isBatchCommandsResponse_Response_Cmd()       {}
func (*BatchCommandsResponse_Response_ScanLock) isBatchCommandsResponse_Response_Cmd()            {}
func (*BatchCommandsResponse_Response_ResolveLock) isBatchCommandsResponse_Response_Cmd()         {}
func (*BatchCommandsResponse_Response_GC) isBatchCommandsResponse_Response_Cmd()                  {}
func (*BatchCommandsResponse_Response_DeleteRange) isBatchCommandsResponse_Response_Cmd()         {}
func (*BatchCommandsResponse_Response_RawGet) isBatchCommandsResponse_Response_Cmd()              {}
func (*BatchCommandsResponse_Response_RawBatchGet) isBatchCommandsResponse_Response_Cmd()         {}
func (*BatchCommandsResponse_Response_RawPut) isBatchCommandsResponse_Response_Cmd()              {}
func (*BatchCommandsResponse_Response_RawBatchPut) isBatchCommandsResponse_Response_Cmd()         {}
func (*BatchCommandsResponse_Response_RawDelete) isBatchCommandsResponse_Response_Cmd()           {}
func (*BatchCommandsResponse_Response_RawBatchDelete) isBatchCommandsResponse_Response_Cmd()      {}
func (*BatchCommandsResponse_Response_RawScan) isBatchCommandsResponse_Response_Cmd()             {}
func (*BatchCommandsResponse_Response_RawDeleteRange) isBatchCommandsResponse_Response_Cmd()      {}
func (*BatchCommandsResponse_Response_RawBatchScan) isBatchCommandsResponse_Response_Cmd()        {}
func (*BatchCommandsResponse_Response_Coprocessor) isBatchCommandsResponse_Response_Cmd()         {}
func (*BatchCommandsResponse_Response_PessimisticLock) isBatchCommandsResponse_Response_Cmd()     {}
func (*BatchCommandsResponse_Response_PessimisticRollback) isBatchCommandsResponse_Response_Cmd() {}
func (*BatchCommandsResponse_Response_CheckTxnStatus) isBatchCommandsResponse_Response_Cmd()      {}
func (*BatchCommandsResponse_Response_TxnHeartBeat) isBatchCommandsResponse_Response_Cmd()        {}
func (*BatchCommandsResponse_Response_CheckSecondaryLocks) isBatchCommandsResponse_Response_Cmd() {}
func (*BatchCommandsResponse_Response_RawCoprocessor) isBatchCommandsResponse_Response_Cmd()      {}
func (*BatchCommandsResponse_Response_FlashbackToVersion) isBatchCommandsResponse_Response_Cmd()  {}
func (*BatchCommandsResponse_Response_PrepareFlashbackToVersion) isBatchCommandsResponse_Response_Cmd() {
}
func (*BatchCommandsResponse_Response_Flush) isBatchCommandsResponse_Response_Cmd()              {}
func (*BatchCommandsResponse_Response_BufferBatchGet) isBatchCommandsResponse_Response_Cmd()     {}
func (*BatchCommandsResponse_Response_GetHealthFeedback) isBatchCommandsResponse_Response_Cmd()  {}
func (*BatchCommandsResponse_Response_BroadcastTxnStatus) isBatchCommandsResponse_Response_Cmd() {}
func (*BatchCommandsResponse_Response_Empty) isBatchCommandsResponse_Response_Cmd()              {}

func (m *BatchCommandsResponse_Response) GetCmd() isBatchCommandsResponse_Response_Cmd {
	if m != nil {
		return m.Cmd
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetGet() *kvrpcpb.GetResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Get); ok {
		return x.Get
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetScan() *kvrpcpb.ScanResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Scan); ok {
		return x.Scan
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetPrewrite() *kvrpcpb.PrewriteResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Prewrite); ok {
		return x.Prewrite
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetCommit() *kvrpcpb.CommitResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Commit); ok {
		return x.Commit
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetImport() *kvrpcpb.ImportResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Import); ok {
		return x.Import
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetCleanup() *kvrpcpb.CleanupResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Cleanup); ok {
		return x.Cleanup
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetBatchGet() *kvrpcpb.BatchGetResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_BatchGet); ok {
		return x.BatchGet
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetBatchRollback() *kvrpcpb.BatchRollbackResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_BatchRollback); ok {
		return x.BatchRollback
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetScanLock() *kvrpcpb.ScanLockResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_ScanLock); ok {
		return x.ScanLock
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetResolveLock() *kvrpcpb.ResolveLockResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_ResolveLock); ok {
		return x.ResolveLock
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetGC() *kvrpcpb.GCResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_GC); ok {
		return x.GC
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetDeleteRange() *kvrpcpb.DeleteRangeResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_DeleteRange); ok {
		return x.DeleteRange
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawGet() *kvrpcpb.RawGetResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawGet); ok {
		return x.RawGet
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawBatchGet() *kvrpcpb.RawBatchGetResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawBatchGet); ok {
		return x.RawBatchGet
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawPut() *kvrpcpb.RawPutResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawPut); ok {
		return x.RawPut
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawBatchPut() *kvrpcpb.RawBatchPutResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawBatchPut); ok {
		return x.RawBatchPut
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawDelete() *kvrpcpb.RawDeleteResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawDelete); ok {
		return x.RawDelete
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawBatchDelete() *kvrpcpb.RawBatchDeleteResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawBatchDelete); ok {
		return x.RawBatchDelete
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawScan() *kvrpcpb.RawScanResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawScan); ok {
		return x.RawScan
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawDeleteRange() *kvrpcpb.RawDeleteRangeResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawDeleteRange); ok {
		return x.RawDeleteRange
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawBatchScan() *kvrpcpb.RawBatchScanResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawBatchScan); ok {
		return x.RawBatchScan
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetCoprocessor() *coprocessor.Response {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Coprocessor); ok {
		return x.Coprocessor
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetPessimisticLock() *kvrpcpb.PessimisticLockResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_PessimisticLock); ok {
		return x.PessimisticLock
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetPessimisticRollback() *kvrpcpb.PessimisticRollbackResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_PessimisticRollback); ok {
		return x.PessimisticRollback
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetCheckTxnStatus() *kvrpcpb.CheckTxnStatusResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_CheckTxnStatus); ok {
		return x.CheckTxnStatus
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetTxnHeartBeat() *kvrpcpb.TxnHeartBeatResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_TxnHeartBeat); ok {
		return x.TxnHeartBeat
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetCheckSecondaryLocks() *kvrpcpb.CheckSecondaryLocksResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_CheckSecondaryLocks); ok {
		return x.CheckSecondaryLocks
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetRawCoprocessor() *kvrpcpb.RawCoprocessorResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_RawCoprocessor); ok {
		return x.RawCoprocessor
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetFlashbackToVersion() *kvrpcpb.FlashbackToVersionResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_FlashbackToVersion); ok {
		return x.FlashbackToVersion
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetPrepareFlashbackToVersion() *kvrpcpb.PrepareFlashbackToVersionResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_PrepareFlashbackToVersion); ok {
		return x.PrepareFlashbackToVersion
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetFlush() *kvrpcpb.FlushResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Flush); ok {
		return x.Flush
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetBufferBatchGet() *kvrpcpb.BufferBatchGetResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_BufferBatchGet); ok {
		return x.BufferBatchGet
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetGetHealthFeedback() *kvrpcpb.GetHealthFeedbackResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_GetHealthFeedback); ok {
		return x.GetHealthFeedback
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetBroadcastTxnStatus() *kvrpcpb.BroadcastTxnStatusResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_BroadcastTxnStatus); ok {
		return x.BroadcastTxnStatus
	}
	return nil
}

func (m *BatchCommandsResponse_Response) GetEmpty() *BatchCommandsEmptyResponse {
	if x, ok := m.GetCmd().(*BatchCommandsResponse_Response_Empty); ok {
		return x.Empty
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*BatchCommandsResponse_Response) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*BatchCommandsResponse_Response_Get)(nil),
		(*BatchCommandsResponse_Response_Scan)(nil),
		(*BatchCommandsResponse_Response_Prewrite)(nil),
		(*BatchCommandsResponse_Response_Commit)(nil),
		(*BatchCommandsResponse_Response_Import)(nil),
		(*BatchCommandsResponse_Response_Cleanup)(nil),
		(*BatchCommandsResponse_Response_BatchGet)(nil),
		(*BatchCommandsResponse_Response_BatchRollback)(nil),
		(*BatchCommandsResponse_Response_ScanLock)(nil),
		(*BatchCommandsResponse_Response_ResolveLock)(nil),
		(*BatchCommandsResponse_Response_GC)(nil),
		(*BatchCommandsResponse_Response_DeleteRange)(nil),
		(*BatchCommandsResponse_Response_RawGet)(nil),
		(*BatchCommandsResponse_Response_RawBatchGet)(nil),
		(*BatchCommandsResponse_Response_RawPut)(nil),
		(*BatchCommandsResponse_Response_RawBatchPut)(nil),
		(*BatchCommandsResponse_Response_RawDelete)(nil),
		(*BatchCommandsResponse_Response_RawBatchDelete)(nil),
		(*BatchCommandsResponse_Response_RawScan)(nil),
		(*BatchCommandsResponse_Response_RawDeleteRange)(nil),
		(*BatchCommandsResponse_Response_RawBatchScan)(nil),
		(*BatchCommandsResponse_Response_Coprocessor)(nil),
		(*BatchCommandsResponse_Response_PessimisticLock)(nil),
		(*BatchCommandsResponse_Response_PessimisticRollback)(nil),
		(*BatchCommandsResponse_Response_CheckTxnStatus)(nil),
		(*BatchCommandsResponse_Response_TxnHeartBeat)(nil),
		(*BatchCommandsResponse_Response_CheckSecondaryLocks)(nil),
		(*BatchCommandsResponse_Response_RawCoprocessor)(nil),
		(*BatchCommandsResponse_Response_FlashbackToVersion)(nil),
		(*BatchCommandsResponse_Response_PrepareFlashbackToVersion)(nil),
		(*BatchCommandsResponse_Response_Flush)(nil),
		(*BatchCommandsResponse_Response_BufferBatchGet)(nil),
		(*BatchCommandsResponse_Response_GetHealthFeedback)(nil),
		(*BatchCommandsResponse_Response_BroadcastTxnStatus)(nil),
		(*BatchCommandsResponse_Response_Empty)(nil),
	}
}

type BatchRaftMessage struct {
	Msgs []*raft_serverpb.RaftMessage `protobuf:"bytes,1,rep,name=msgs,proto3" json:"msgs,omitempty"`
	// Used for measure the send duration.
	LastObservedTime uint64 `protobuf:"varint,13,opt,name=last_observed_time,json=lastObservedTime,proto3" json:"last_observed_time,omitempty"`
}

func (m *BatchRaftMessage) Reset()         { *m = BatchRaftMessage{} }
func (m *BatchRaftMessage) String() string { return proto.CompactTextString(m) }
func (*BatchRaftMessage) ProtoMessage()    {}
func (*BatchRaftMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_274fe050f0c997b3, []int{2}
}
func (m *BatchRaftMessage) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchRaftMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchRaftMessage.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchRaftMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRaftMessage.Merge(m, src)
}
func (m *BatchRaftMessage) XXX_Size() int {
	return m.Size()
}
func (m *BatchRaftMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRaftMessage.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRaftMessage proto.InternalMessageInfo

func (m *BatchRaftMessage) GetMsgs() []*raft_serverpb.RaftMessage {
	if m != nil {
		return m.Msgs
	}
	return nil
}

func (m *BatchRaftMessage) GetLastObservedTime() uint64 {
	if m != nil {
		return m.LastObservedTime
	}
	return 0
}

type BatchCommandsEmptyRequest struct {
	// ID of the test request.
	TestId uint64 `protobuf:"varint,1,opt,name=test_id,json=testId,proto3" json:"test_id,omitempty"`
	// TiKV needs to delay at least such a time to response the client.
	DelayTime uint64 `protobuf:"varint,2,opt,name=delay_time,json=delayTime,proto3" json:"delay_time,omitempty"`
}

func (m *BatchCommandsEmptyRequest) Reset()         { *m = BatchCommandsEmptyRequest{} }
func (m *BatchCommandsEmptyRequest) String() string { return proto.CompactTextString(m) }
func (*BatchCommandsEmptyRequest) ProtoMessage()    {}
func (*BatchCommandsEmptyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_274fe050f0c997b3, []int{3}
}
func (m *BatchCommandsEmptyRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchCommandsEmptyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchCommandsEmptyRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchCommandsEmptyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCommandsEmptyRequest.Merge(m, src)
}
func (m *BatchCommandsEmptyRequest) XXX_Size() int {
	return m.Size()
}
func (m *BatchCommandsEmptyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCommandsEmptyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCommandsEmptyRequest proto.InternalMessageInfo

func (m *BatchCommandsEmptyRequest) GetTestId() uint64 {
	if m != nil {
		return m.TestId
	}
	return 0
}

func (m *BatchCommandsEmptyRequest) GetDelayTime() uint64 {
	if m != nil {
		return m.DelayTime
	}
	return 0
}

type BatchCommandsEmptyResponse struct {
	// ID of the test request.
	TestId uint64 `protobuf:"varint,1,opt,name=test_id,json=testId,proto3" json:"test_id,omitempty"`
}

func (m *BatchCommandsEmptyResponse) Reset()         { *m = BatchCommandsEmptyResponse{} }
func (m *BatchCommandsEmptyResponse) String() string { return proto.CompactTextString(m) }
func (*BatchCommandsEmptyResponse) ProtoMessage()    {}
func (*BatchCommandsEmptyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_274fe050f0c997b3, []int{4}
}
func (m *BatchCommandsEmptyResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchCommandsEmptyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchCommandsEmptyResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchCommandsEmptyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchCommandsEmptyResponse.Merge(m, src)
}
func (m *BatchCommandsEmptyResponse) XXX_Size() int {
	return m.Size()
}
func (m *BatchCommandsEmptyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchCommandsEmptyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchCommandsEmptyResponse proto.InternalMessageInfo

func (m *BatchCommandsEmptyResponse) GetTestId() uint64 {
	if m != nil {
		return m.TestId
	}
	return 0
}

func init() {
	proto.RegisterType((*BatchCommandsRequest)(nil), "tikvpb.BatchCommandsRequest")
	proto.RegisterType((*BatchCommandsRequest_Request)(nil), "tikvpb.BatchCommandsRequest.Request")
	proto.RegisterType((*BatchCommandsResponse)(nil), "tikvpb.BatchCommandsResponse")
	proto.RegisterType((*BatchCommandsResponse_Response)(nil), "tikvpb.BatchCommandsResponse.Response")
	proto.RegisterType((*BatchRaftMessage)(nil), "tikvpb.BatchRaftMessage")
	proto.RegisterType((*BatchCommandsEmptyRequest)(nil), "tikvpb.BatchCommandsEmptyRequest")
	proto.RegisterType((*BatchCommandsEmptyResponse)(nil), "tikvpb.BatchCommandsEmptyResponse")
}

func init() { proto.RegisterFile("tikvpb.proto", fileDescriptor_274fe050f0c997b3) }

var fileDescriptor_274fe050f0c997b3 = []byte{
	// 2806 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x9a, 0x4b, 0x73, 0xdb, 0xc6,
	0x1d, 0xc0, 0x41, 0x9b, 0xd6, 0x63, 0x15, 0xbd, 0x56, 0x92, 0x05, 0xc1, 0xb2, 0x64, 0xc3, 0xb2,
	0xad, 0xa4, 0xa9, 0x6a, 0x3b, 0x6e, 0xdc, 0x38, 0x69, 0x2a, 0x8b, 0xb2, 0x1e, 0xa1, 0x34, 0x61,
	0x49, 0x3a, 0x71, 0x3b, 0xed, 0x68, 0x56, 0xe4, 0x8a, 0x62, 0x45, 0x02, 0x2c, 0xb0, 0xa4, 0xcc,
	0x6f, 0xd1, 0xe9, 0xa9, 0x97, 0xde, 0xfb, 0x51, 0x7a, 0xcc, 0xa5, 0x33, 0x39, 0x76, 0xec, 0xcf,
	0xd0, 0x43, 0x4f, 0xed, 0xec, 0x02, 0xd8, 0x17, 0x16, 0x20, 0x73, 0x12, 0xb5, 0xff, 0xd7, 0x3e,
	0xff, 0xfb, 0xfb, 0x2f, 0x09, 0x3e, 0x22, 0xed, 0xab, 0x41, 0xef, 0x7c, 0xa7, 0x17, 0xf8, 0xc4,
	0x87, 0x13, 0xd1, 0x7f, 0xce, 0x62, 0xc3, 0xef, 0x05, 0x7e, 0x03, 0x87, 0xa1, 0x1f, 0x44, 0x22,
	0x67, 0xf6, 0x6a, 0x10, 0xf4, 0x1a, 0x89, 0xa6, 0x33, 0xdd, 0xed, 0xf5, 0xe2, 0x8f, 0x4b, 0x01,
	0xba, 0x20, 0x67, 0x21, 0x0e, 0x06, 0x38, 0xe0, 0xf2, 0xa5, 0x66, 0x3b, 0x44, 0xad, 0x56, 0x80,
	0x5b, 0x88, 0xe0, 0x66, 0xdc, 0xb8, 0xdc, 0xf2, 0x5b, 0x3e, 0xfb, 0xf8, 0x0b, 0xfa, 0x29, 0x6e,
	0x9d, 0x0f, 0xfa, 0x21, 0x61, 0x1f, 0xa3, 0x06, 0xf7, 0x3f, 0x10, 0x2c, 0xef, 0x21, 0xd2, 0xb8,
	0x2c, 0xf9, 0xdd, 0x2e, 0xf2, 0x9a, 0x61, 0x15, 0xff, 0xb9, 0x8f, 0x43, 0x02, 0x77, 0xc1, 0x54,
	0x10, 0x7d, 0x0c, 0xed, 0xc2, 0xbd, 0x9b, 0xdb, 0x33, 0xcf, 0xb6, 0x76, 0xe2, 0xfe, 0x9b, 0xf4,
	0x77, 0xe2, 0xbf, 0x55, 0x6e, 0x05, 0x37, 0xc1, 0x4c, 0xfc, 0xf9, 0xac, 0xdd, 0x0c, 0xed, 0x1b,
	0xf7, 0x6e, 0x6e, 0x17, 0xab, 0x20, 0x6e, 0x3a, 0x6e, 0x86, 0xce, 0x7f, 0x17, 0xc1, 0x64, 0x12,
	0xee, 0x31, 0xb8, 0x79, 0x88, 0x89, 0x5d, 0xb8, 0x57, 0xd8, 0x9e, 0x79, 0xb6, 0xb4, 0x93, 0x4c,
	0xc0, 0x21, 0x26, 0xb1, 0xc6, 0x91, 0x55, 0xa5, 0x1a, 0xf0, 0x13, 0x50, 0xac, 0x35, 0x90, 0x67,
	0xdf, 0x60, 0x9a, 0xcb, 0x5c, 0x93, 0x36, 0x0a, 0x55, 0xa6, 0x03, 0x3f, 0x07, 0x53, 0x95, 0x00,
	0x5f, 0x07, 0x6d, 0x82, 0xed, 0x9b, 0x4c, 0xdf, 0xe6, 0xfa, 0x89, 0x40, 0xd8, 0x70, 0x5d, 0xf8,
	0x04, 0x4c, 0xd0, 0xe1, 0xb5, 0x89, 0x5d, 0x64, 0x56, 0xb7, 0xb9, 0x55, 0xd4, 0x2c, 0x6c, 0x62,
	0x3d, 0x6a, 0x71, 0xdc, 0xed, 0xf9, 0x01, 0xb1, 0x6f, 0x69, 0x16, 0x51, 0xb3, 0x64, 0x11, 0x35,
	0xc0, 0xcf, 0xc0, 0x64, 0xa9, 0x83, 0x91, 0xd7, 0xef, 0xd9, 0x13, 0xcc, 0x64, 0x55, 0x04, 0x89,
	0xda, 0x85, 0x4d, 0xa2, 0x49, 0x07, 0xc4, 0x26, 0x9f, 0x4e, 0xd5, 0xa4, 0x36, 0xa0, 0x44, 0x20,
	0x0d, 0x28, 0x69, 0x82, 0xaf, 0xc1, 0x2c, 0xfb, 0x5c, 0xf5, 0x3b, 0x9d, 0x73, 0xd4, 0xb8, 0xb2,
	0xa7, 0x98, 0xf1, 0x5d, 0xd5, 0x38, 0x91, 0x0a, 0x0f, 0xaa, 0x15, 0x0d, 0x4f, 0xe7, 0xf5, 0xc4,
	0x6f, 0x5c, 0xd9, 0xd3, 0x5a, 0xf8, 0x44, 0x20, 0x85, 0x4f, 0x9a, 0xe0, 0x6f, 0xc0, 0x4c, 0x15,
	0x87, 0x7e, 0x67, 0x80, 0x99, 0x29, 0x60, 0xa6, 0x77, 0xb8, 0xa9, 0x24, 0x13, 0xd6, 0xb2, 0x05,
	0xdc, 0x02, 0x37, 0x0e, 0x4b, 0xf6, 0x0c, 0xb3, 0x83, 0x62, 0x73, 0x94, 0x84, 0xfa, 0x8d, 0xc3,
	0x12, 0x0d, 0xb3, 0x8f, 0x3b, 0x98, 0xe0, 0x2a, 0xf2, 0x5a, 0xd8, 0xfe, 0x48, 0x0b, 0x23, 0xc9,
	0xa4, 0x30, 0x52, 0x2b, 0x5d, 0xc5, 0x2a, 0xba, 0xa6, 0x93, 0x3b, 0xab, 0xad, 0x62, 0xd4, 0x2c,
	0xad, 0x62, 0xd4, 0xc0, 0x46, 0x86, 0xae, 0xf9, 0x9a, 0xcc, 0xe9, 0x23, 0x13, 0x32, 0x79, 0x64,
	0xa2, 0x35, 0x0e, 0x59, 0xe9, 0x13, 0x7b, 0x3e, 0x1d, 0xb2, 0xd2, 0xd7, 0x42, 0x56, 0xfa, 0x4a,
	0x48, 0x6a, 0xb6, 0x90, 0x11, 0x52, 0xb1, 0x95, 0x2d, 0xe0, 0x17, 0x60, 0xba, 0x8a, 0xae, 0xa3,
	0x71, 0xdb, 0x8b, 0xcc, 0x7c, 0x4d, 0x36, 0x8f, 0x67, 0x84, 0x1b, 0x0b, 0x6d, 0x78, 0x04, 0xe6,
	0x12, 0x4f, 0xb1, 0x3d, 0x64, 0xf6, 0x1b, 0xa9, 0xf0, 0xba, 0x13, 0xcd, 0x8e, 0x6e, 0xff, 0x2a,
	0xba, 0x66, 0x27, 0x79, 0x49, 0xdb, 0xfe, 0x71, 0xbb, 0xb4, 0xfd, 0xe3, 0x96, 0x38, 0xbc, 0xbc,
	0xc6, 0xcb, 0xe9, 0xf0, 0xc6, 0x65, 0xd6, 0xec, 0xe0, 0x1e, 0xf8, 0x28, 0xe9, 0x10, 0xeb, 0xc3,
	0x0a, 0xf3, 0xb3, 0x9e, 0x1a, 0x86, 0xda, 0x11, 0xc5, 0x06, 0xfe, 0x0a, 0xcc, 0x94, 0x44, 0xea,
	0xb6, 0x6f, 0xc7, 0x09, 0x49, 0x4e, 0xe7, 0xd2, 0x0a, 0x48, 0xaa, 0xb0, 0x0c, 0xe6, 0x2b, 0x38,
	0x0c, 0xdb, 0xdd, 0x76, 0x48, 0xda, 0x0d, 0x76, 0x26, 0x56, 0x99, 0xf5, 0xa6, 0x48, 0x4f, 0xaa,
	0x5c, 0x38, 0xd2, 0x2d, 0xe1, 0xf7, 0x60, 0x49, 0x6a, 0xe2, 0x27, 0xdc, 0x66, 0x0e, 0x1f, 0x98,
	0x1c, 0xa6, 0xcf, 0xb9, 0xc9, 0x03, 0x9d, 0xed, 0xd2, 0x25, 0x6e, 0x5c, 0xd5, 0xdf, 0x79, 0x35,
	0x82, 0x48, 0x3f, 0xb4, 0xd7, 0xb4, 0xd9, 0x56, 0xc5, 0xd2, 0x6c, 0xab, 0x02, 0x3a, 0xdb, 0xf5,
	0x77, 0xde, 0x11, 0x46, 0x01, 0xd9, 0xc3, 0x88, 0xd8, 0x8e, 0x36, 0xdb, 0xb2, 0x50, 0x9a, 0x6d,
	0xb9, 0x99, 0x0e, 0x93, 0x79, 0xad, 0xe1, 0x86, 0xef, 0x35, 0x51, 0x30, 0xa4, 0x83, 0x0f, 0xed,
	0xfb, 0xda, 0x30, 0x0d, 0x3a, 0xd2, 0x30, 0x0d, 0xd2, 0x78, 0x53, 0xc9, 0x2b, 0xe9, 0xa6, 0x37,
	0x95, 0x24, 0x56, 0x37, 0x95, 0xbc, 0xac, 0x75, 0x00, 0x0f, 0x3a, 0x28, 0xbc, 0xa4, 0xb3, 0x57,
	0xf7, 0xbf, 0xc3, 0x41, 0xd8, 0xf6, 0x3d, 0xfb, 0x01, 0xf3, 0xe6, 0x72, 0x6f, 0x69, 0x15, 0xe1,
	0xd1, 0x60, 0x0f, 0xdb, 0x60, 0xad, 0x12, 0xe0, 0x1e, 0x0a, 0xb0, 0xc1, 0xf9, 0x16, 0x73, 0xfe,
	0xb1, 0x7c, 0xab, 0x99, 0x35, 0x45, 0x8c, 0x6c, 0x6f, 0xf0, 0xe7, 0xe0, 0xd6, 0x41, 0xa7, 0x1f,
	0x5e, 0xda, 0x0f, 0x99, 0xdb, 0x15, 0xa9, 0xcf, 0xfd, 0xf0, 0x52, 0xb8, 0x88, 0xb4, 0xe8, 0xcc,
	0xed, 0xf5, 0x2f, 0x2e, 0x70, 0xc0, 0xf3, 0xdf, 0x23, 0x6d, 0xe6, 0x54, 0xb1, 0x34, 0x73, 0xaa,
	0x00, 0xfe, 0x16, 0x2c, 0x1e, 0x62, 0x72, 0x84, 0x51, 0x87, 0x5c, 0x1e, 0x60, 0xdc, 0x64, 0x3b,
	0xf8, 0x31, 0x73, 0x76, 0x5f, 0x66, 0x01, 0x55, 0x43, 0xf8, 0x4b, 0x5b, 0xd3, 0xc5, 0xd8, 0x0b,
	0x7c, 0xd4, 0x6c, 0xa0, 0x90, 0x88, 0x1d, 0xbc, 0xad, 0x2d, 0x46, 0x5a, 0x45, 0x5a, 0x8c, 0xb4,
	0x10, 0xbe, 0x04, 0xb7, 0x5e, 0x77, 0x7b, 0x64, 0x68, 0xff, 0xaf, 0x10, 0xf7, 0xce, 0xc4, 0x44,
	0x4c, 0x45, 0x9a, 0x2e, 0xf6, 0xff, 0xde, 0x2d, 0x70, 0xb3, 0xd1, 0x6d, 0x7e, 0x53, 0x9c, 0xba,
	0xb3, 0x70, 0xdf, 0xfd, 0xeb, 0x32, 0x58, 0xd1, 0x38, 0x2a, 0xec, 0xf9, 0x5e, 0x88, 0xe1, 0x3e,
	0x98, 0x0e, 0xe2, 0xcf, 0x09, 0x79, 0x3d, 0xca, 0x20, 0xaf, 0x48, 0x6b, 0x27, 0xf9, 0x50, 0x15,
	0x86, 0x23, 0xe1, 0x0b, 0x3e, 0x01, 0xcb, 0x24, 0x40, 0x5e, 0x48, 0x61, 0xe4, 0xac, 0x83, 0x86,
	0x38, 0x38, 0xeb, 0xf8, 0xa8, 0xc9, 0x38, 0xa9, 0x58, 0x85, 0x5c, 0x76, 0x42, 0x45, 0x27, 0x3e,
	0x6a, 0xc2, 0x5d, 0x30, 0x7f, 0xc9, 0xe6, 0xf8, 0xec, 0x22, 0x59, 0xa2, 0xa2, 0x96, 0xba, 0xb5,
	0xf5, 0x99, 0xbb, 0x54, 0xfe, 0x77, 0xfe, 0x0e, 0xc1, 0x14, 0x1f, 0xe7, 0xb6, 0x4c, 0x7c, 0xcb,
	0x2a, 0xf1, 0x45, 0x2a, 0x09, 0xf2, 0xfd, 0x4c, 0x41, 0xbe, 0x15, 0x0d, 0xf9, 0xb8, 0x6e, 0xc4,
	0x7c, 0x2f, 0x52, 0xcc, 0xb7, 0x66, 0x60, 0x3e, 0x6e, 0x24, 0xa0, 0xef, 0xa9, 0x06, 0x7d, 0xab,
	0x29, 0xe8, 0xe3, 0x46, 0x09, 0xf5, 0x3d, 0xd5, 0xa8, 0x6f, 0x35, 0x45, 0x7d, 0xc2, 0x24, 0xc6,
	0xbe, 0xe7, 0x3a, 0xf6, 0xd9, 0x69, 0xec, 0xe3, 0x46, 0x9c, 0xfb, 0x5e, 0xa4, 0xb8, 0x6f, 0xcd,
	0xc0, 0x7d, 0x62, 0x50, 0xfc, 0x60, 0x1d, 0x98, 0xc1, 0x6f, 0x23, 0x0b, 0xfc, 0xb8, 0x0b, 0x8d,
	0xfc, 0x5e, 0xa4, 0xc8, 0x6f, 0xcd, 0x40, 0x7e, 0xa2, 0x03, 0x1c, 0xfd, 0x76, 0x4d, 0xe8, 0xb7,
	0x6e, 0x46, 0x3f, 0x6e, 0xae, 0xb0, 0xdf, 0x43, 0x89, 0xfd, 0x96, 0x14, 0xf6, 0xe3, 0xfa, 0x14,
	0xfe, 0x76, 0x4d, 0xf0, 0xb7, 0x6e, 0x86, 0x3f, 0x11, 0x48, 0x66, 0x82, 0xa7, 0x1a, 0xfd, 0xad,
	0xa6, 0xe8, 0x4f, 0xac, 0x66, 0x8c, 0x7f, 0xbb, 0x26, 0xfc, 0x5b, 0x37, 0xe3, 0x9f, 0x34, 0x3a,
	0x89, 0xff, 0x9e, 0x6a, 0xfc, 0xb7, 0x9a, 0xe2, 0x3f, 0x25, 0x28, 0xe5, 0xb7, 0x5d, 0x13, 0x00,
	0xae, 0x9b, 0x01, 0x30, 0x1d, 0x94, 0x7a, 0x78, 0x99, 0x26, 0x40, 0xc7, 0x44, 0x80, 0xdc, 0x5a,
	0x42, 0xc0, 0xe3, 0x0c, 0x04, 0xdc, 0xcc, 0x44, 0x40, 0xee, 0x45, 0x67, 0xc0, 0xe7, 0x3a, 0x03,
	0xda, 0x69, 0x06, 0x14, 0x67, 0x21, 0x81, 0xc0, 0xe3, 0x0c, 0x08, 0xdc, 0xcc, 0x84, 0x40, 0xa5,
	0x03, 0xf2, 0x8a, 0x97, 0x8c, 0x14, 0x78, 0x37, 0x83, 0x02, 0xb9, 0x1b, 0x15, 0x03, 0xbf, 0x30,
	0x61, 0xe0, 0x8a, 0x86, 0x81, 0x62, 0x1d, 0x64, 0x60, 0x38, 0xc9, 0xe2, 0xc0, 0x7b, 0xd9, 0x1c,
	0xc8, 0x3d, 0xa5, 0x40, 0xf0, 0x6d, 0x1e, 0x08, 0x6e, 0xe5, 0x83, 0x20, 0xf7, 0x6a, 0x24, 0xc1,
	0xe3, 0x0c, 0x12, 0xdc, 0xcc, 0x24, 0x41, 0x31, 0xe5, 0x1a, 0x0a, 0x96, 0x8c, 0x28, 0x78, 0x37,
	0x03, 0x05, 0xc5, 0x94, 0x2b, 0x2c, 0xf8, 0x36, 0x8f, 0x05, 0xb7, 0xf2, 0x59, 0x50, 0x8c, 0xd4,
	0x04, 0x83, 0xc7, 0x19, 0x30, 0xb8, 0x99, 0x09, 0x83, 0xca, 0xe6, 0x92, 0x17, 0xf7, 0x4d, 0x0e,
	0x0d, 0x3e, 0xc8, 0xa5, 0x41, 0xee, 0xd2, 0x84, 0x83, 0x7f, 0x1a, 0x8d, 0x83, 0x9f, 0x8c, 0x83,
	0x83, 0x3c, 0x48, 0x0e, 0x0f, 0xee, 0xa8, 0x3c, 0x78, 0x5b, 0xe7, 0x41, 0xee, 0x23, 0x06, 0xc2,
	0xe3, 0x0c, 0x20, 0xdc, 0xcc, 0x04, 0x42, 0x31, 0x7b, 0x1a, 0x11, 0x56, 0xb3, 0x89, 0xd0, 0xcd,
	0x23, 0x42, 0xee, 0xd0, 0x80, 0x84, 0x6f, 0x72, 0x90, 0xf0, 0x41, 0x2e, 0x12, 0x8a, 0x15, 0x31,
	0x30, 0xe1, 0x97, 0x1a, 0x13, 0xba, 0x79, 0x4c, 0x28, 0xa6, 0xcc, 0x00, 0x85, 0x3d, 0xb0, 0x10,
	0x5d, 0xbb, 0xe8, 0x82, 0x9c, 0xe2, 0x30, 0x44, 0x2d, 0x0c, 0x77, 0x40, 0xb1, 0x1b, 0xb6, 0x12,
	0x12, 0x74, 0x76, 0xd4, 0x07, 0x40, 0x49, 0xb3, 0xca, 0xf4, 0xe0, 0xa7, 0x00, 0x76, 0x50, 0x48,
	0xce, 0xfc, 0x73, 0xa6, 0xd4, 0x3c, 0x23, 0xed, 0x2e, 0x66, 0x37, 0x5a, 0xb1, 0xba, 0x40, 0x25,
	0xdf, 0xc6, 0x82, 0x7a, 0xbb, 0x8b, 0xdd, 0x1a, 0x58, 0xcb, 0x24, 0x57, 0xb8, 0x0a, 0x26, 0x49,
	0x04, 0x90, 0x8c, 0xd2, 0x8a, 0xd5, 0x09, 0xc2, 0xe0, 0x11, 0xde, 0x05, 0xa0, 0x89, 0x3b, 0x68,
	0x18, 0xf9, 0xbe, 0xc1, 0x64, 0xd3, 0xac, 0x85, 0x39, 0xfd, 0x25, 0x70, 0xb2, 0x87, 0x9e, 0xe9,
	0xf5, 0xd9, 0xbf, 0x3e, 0x05, 0xc5, 0x7a, 0xfb, 0x6a, 0x00, 0x9f, 0x83, 0x5b, 0xe5, 0x01, 0xdd,
	0x04, 0xa6, 0x77, 0x40, 0xc7, 0x88, 0x8a, 0xae, 0x05, 0x5f, 0x80, 0x89, 0xf2, 0x80, 0x65, 0x64,
	0xe3, 0xa3, 0xa0, 0x63, 0xe6, 0x46, 0xd7, 0x82, 0x25, 0x00, 0xca, 0x03, 0x8e, 0x81, 0x99, 0x2f,
	0x84, 0x4e, 0x36, 0x47, 0xba, 0x16, 0x7c, 0x0b, 0x16, 0xcb, 0x03, 0x3d, 0x23, 0x8f, 0x2a, 0xe7,
	0x9d, 0x91, 0x79, 0xde, 0xb5, 0x60, 0x13, 0xac, 0x94, 0xbf, 0x33, 0x65, 0xe5, 0x71, 0x6a, 0x7b,
	0x67, 0xac, 0xbc, 0xef, 0x5a, 0xf0, 0x5b, 0x30, 0x57, 0x1e, 0x28, 0x49, 0x36, 0xb7, 0x3c, 0x77,
	0xf2, 0x33, 0xb6, 0x6b, 0xc1, 0x37, 0x60, 0xa1, 0x3c, 0xd0, 0x92, 0xff, 0x88, 0x97, 0x03, 0x67,
	0xd4, 0x7d, 0x12, 0xcf, 0xc6, 0xc0, 0x94, 0xb9, 0xc7, 0x79, 0x02, 0x70, 0xc6, 0xba, 0x1b, 0x5c,
	0x0b, 0xfe, 0x1a, 0x4c, 0x95, 0x07, 0x31, 0xe4, 0x67, 0x3c, 0xfe, 0x3a, 0x59, 0xf5, 0x41, 0x62,
	0x1e, 0x03, 0x7f, 0xc6, 0x4b, 0xb0, 0x93, 0x55, 0x2b, 0xb8, 0x16, 0xdc, 0x05, 0xd3, 0xe5, 0x41,
	0x82, 0xfe, 0x59, 0xcf, 0xc2, 0x4e, 0x66, 0xe1, 0x90, 0x6c, 0x69, 0x9e, 0x4b, 0x33, 0xdf, 0x88,
	0x9d, 0xec, 0x2a, 0xc2, 0xb5, 0x60, 0x15, 0xcc, 0xc7, 0x4e, 0xf8, 0x96, 0xcb, 0x7f, 0x30, 0x76,
	0x46, 0x94, 0x15, 0x49, 0xc7, 0x78, 0x71, 0x90, 0xf9, 0x7a, 0xec, 0x64, 0x57, 0x17, 0xae, 0x05,
	0x4f, 0xc0, 0x6c, 0x79, 0x20, 0x97, 0x08, 0x79, 0x4f, 0xc9, 0x4e, 0x6e, 0xb1, 0xe1, 0x5a, 0xf0,
	0x29, 0x28, 0x96, 0x07, 0x87, 0x25, 0x68, 0x78, 0x57, 0x76, 0x4c, 0xf5, 0x46, 0xd2, 0x01, 0x19,
	0x24, 0xf3, 0x1e, 0x99, 0x9d, 0xdc, 0x22, 0xc4, 0xb5, 0xe0, 0x00, 0xdc, 0x61, 0xf9, 0x27, 0xe3,
	0x12, 0x1e, 0xff, 0x71, 0xc7, 0xf9, 0x09, 0x17, 0xbf, 0x6b, 0x41, 0x04, 0x96, 0xcb, 0x03, 0x43,
	0xc0, 0x31, 0x9e, 0xaa, 0x9c, 0x71, 0x00, 0xc6, 0xb5, 0xe0, 0x4b, 0x30, 0x49, 0x43, 0x50, 0x36,
	0x30, 0x3f, 0x26, 0x39, 0x19, 0x4c, 0x91, 0x24, 0x10, 0x8d, 0x0a, 0x46, 0xbc, 0x2c, 0x39, 0xa3,
	0x40, 0xc3, 0xb5, 0xe0, 0x97, 0x49, 0x95, 0x07, 0x33, 0x5e, 0xf7, 0x9d, 0xac, 0xba, 0xcf, 0xb5,
	0xe0, 0x37, 0x4a, 0xbd, 0x07, 0xf3, 0x1e, 0xfa, 0x9d, 0xdc, 0x32, 0x90, 0x77, 0x84, 0x96, 0x63,
	0x19, 0x6f, 0xfe, 0x4e, 0x56, 0x2d, 0xa8, 0x76, 0x84, 0x7a, 0xc8, 0x7b, 0xfe, 0x77, 0x72, 0x4b,
	0x43, 0xd7, 0x82, 0xfb, 0x52, 0x35, 0x08, 0xb3, 0xbf, 0x09, 0x70, 0x72, 0x4a, 0x44, 0xd7, 0x82,
	0x35, 0xbd, 0x2e, 0x84, 0x23, 0xbe, 0x14, 0x70, 0x46, 0x55, 0x8c, 0xae, 0x05, 0xbf, 0xe6, 0x15,
	0x22, 0xcc, 0xfa, 0x7e, 0xc0, 0xc9, 0x2c, 0x1a, 0x79, 0xa7, 0xe4, 0x93, 0x3a, 0xe2, 0xab, 0x02,
	0x67, 0x54, 0x15, 0xe9, 0x5a, 0xf0, 0x54, 0xad, 0x1a, 0x61, 0xee, 0xb7, 0x06, 0x4e, 0x7e, 0x35,
	0xc9, 0xdd, 0x1d, 0x62, 0x52, 0xc6, 0xc3, 0x7a, 0xfd, 0x44, 0x75, 0xc7, 0x9b, 0x8d, 0xee, 0x24,
	0x29, 0x77, 0x77, 0x00, 0x16, 0x59, 0x21, 0xd2, 0xa5, 0xe7, 0xff, 0x95, 0xd7, 0xac, 0x5d, 0xa3,
	0x9e, 0xba, 0xc3, 0x4a, 0xaf, 0x6a, 0xc6, 0x1d, 0xc6, 0xda, 0xb5, 0x1d, 0xc6, 0xae, 0xc9, 0xb0,
	0xdf, 0x55, 0x77, 0x58, 0xd2, 0x6a, 0xdc, 0x61, 0x42, 0xc8, 0x7d, 0x9d, 0x01, 0xf8, 0xc6, 0x0b,
	0xd1, 0x05, 0xde, 0xc7, 0x21, 0x09, 0xfc, 0x61, 0xb4, 0x14, 0x22, 0xcf, 0xa4, 0x85, 0xe9, 0x3c,
	0x63, 0xd2, 0xe1, 0x01, 0x30, 0x58, 0xae, 0xe2, 0x56, 0x3b, 0x24, 0x38, 0xa0, 0xd9, 0x3d, 0x46,
	0xdc, 0x00, 0x6e, 0x49, 0xb9, 0x3f, 0x2d, 0x4e, 0x82, 0x3c, 0x1c, 0xa1, 0xc5, 0xc3, 0xfc, 0x01,
	0x2c, 0xb2, 0xd1, 0x29, 0x31, 0xee, 0xab, 0x4c, 0x61, 0x0a, 0xe0, 0xe6, 0xa9, 0xc8, 0xb3, 0x54,
	0xc5, 0x5d, 0x3f, 0xba, 0xa0, 0xb8, 0x7b, 0x57, 0xea, 0x9c, 0x2e, 0x4c, 0xcf, 0x92, 0x49, 0x87,
	0x07, 0xf8, 0x1d, 0x58, 0xa8, 0x5c, 0x0e, 0xc3, 0x76, 0x03, 0x75, 0xf8, 0x15, 0x2c, 0x11, 0xa8,
	0x26, 0x4a, 0x9c, 0xdf, 0xcf, 0xd1, 0xe0, 0xae, 0xbf, 0x52, 0x1e, 0x41, 0xa0, 0xf1, 0x5b, 0x30,
	0xc7, 0xfc, 0x28, 0xc2, 0x32, 0xd0, 0xa2, 0x64, 0x5d, 0x23, 0x01, 0x46, 0xdd, 0x9f, 0xe8, 0xe3,
	0x49, 0x01, 0x9e, 0xc6, 0xd5, 0x93, 0xdc, 0x91, 0x35, 0x45, 0x3d, 0xa2, 0x12, 0x9e, 0xce, 0x0c,
	0x22, 0xc9, 0x5d, 0x1d, 0x2c, 0xd1, 0xf3, 0xdf, 0x42, 0x04, 0xcb, 0x1e, 0xd7, 0x15, 0xb3, 0x44,
	0x43, 0x1c, 0x4f, 0xb3, 0x54, 0xcb, 0x48, 0xb2, 0xc3, 0x11, 0xdf, 0x33, 0x39, 0xa3, 0x9e, 0x1e,
	0xd8, 0x55, 0x52, 0xa4, 0x85, 0x20, 0xcc, 0xa9, 0x0e, 0x9d, 0x25, 0x4d, 0xb6, 0xef, 0x7b, 0xd8,
	0xb5, 0xb6, 0x0b, 0xf0, 0x6b, 0x30, 0xcd, 0x8b, 0x4e, 0x68, 0x2b, 0xb5, 0xeb, 0x58, 0xf6, 0xaf,
	0xc0, 0x54, 0xcd, 0x43, 0xbd, 0xf0, 0xd2, 0xa7, 0x35, 0x83, 0xaa, 0x94, 0x08, 0x4a, 0x97, 0x7d,
	0xef, 0x2a, 0xdb, 0x45, 0x03, 0xcc, 0xd5, 0xd1, 0x79, 0x07, 0x13, 0xee, 0x68, 0x4b, 0x53, 0x55,
	0xc5, 0xe2, 0xe0, 0xe6, 0x6b, 0x25, 0x13, 0xb4, 0x5d, 0x78, 0x52, 0xa0, 0x09, 0xad, 0xd6, 0xeb,
	0x50, 0x4e, 0x6f, 0x51, 0xca, 0x11, 0x09, 0x4d, 0x6a, 0x4d, 0x27, 0x34, 0x45, 0xa8, 0x5c, 0x99,
	0x18, 0x35, 0x8f, 0xbd, 0x26, 0x7e, 0x27, 0x5f, 0x99, 0x49, 0x9b, 0xe1, 0xca, 0x14, 0x22, 0x39,
	0xf3, 0x9f, 0x0e, 0x1a, 0x8d, 0x43, 0x4c, 0xf6, 0x86, 0x65, 0x3c, 0x94, 0x32, 0xbf, 0xdc, 0x9c,
	0xce, 0xfc, 0xaa, 0x54, 0x3e, 0xde, 0x5c, 0x52, 0x23, 0x28, 0x20, 0xf5, 0x50, 0x3a, 0xde, 0xba,
	0x28, 0x7d, 0xbc, 0xd3, 0x1a, 0x52, 0x29, 0x30, 0xab, 0x54, 0xf4, 0x70, 0x3d, 0xef, 0xb7, 0x40,
	0xce, 0xdd, 0xdc, 0xef, 0xab, 0xe2, 0xf5, 0x38, 0x02, 0xf3, 0xfb, 0xed, 0xb0, 0x47, 0x35, 0x4e,
	0x2b, 0x95, 0x3a, 0x0a, 0x69, 0x3d, 0xd0, 0xed, 0xf5, 0x76, 0x92, 0x56, 0xda, 0x24, 0xea, 0x81,
	0xb4, 0x84, 0xf7, 0x6e, 0x0f, 0xcc, 0x96, 0x90, 0xd7, 0xc0, 0x9d, 0xc4, 0xcf, 0x6d, 0xa6, 0x1d,
	0xb5, 0xc9, 0x5e, 0x56, 0x53, 0xed, 0xdc, 0x47, 0x1d, 0xdc, 0x7e, 0x1d, 0x12, 0x74, 0xde, 0x69,
	0x87, 0xb4, 0x3b, 0x25, 0xdf, 0xf3, 0x70, 0x83, 0x44, 0x38, 0x4c, 0x8d, 0xcc, 0xc2, 0xc4, 0x31,
	0x64, 0x3a, 0xa7, 0x95, 0xca, 0x3e, 0x22, 0xa8, 0x82, 0x1a, 0x57, 0x98, 0xb0, 0x1c, 0xf2, 0x39,
	0x98, 0x3c, 0x0e, 0x5f, 0x75, 0xda, 0x03, 0x0c, 0x97, 0x98, 0x4a, 0xfc, 0x9f, 0x78, 0xcb, 0x50,
	0x1a, 0xa5, 0xde, 0x2c, 0x55, 0x31, 0xad, 0x0a, 0xe3, 0x11, 0xc5, 0xf5, 0xf3, 0x3a, 0x53, 0x8f,
	0x24, 0xa2, 0x59, 0xcc, 0xba, 0x59, 0xca, 0xbd, 0x1e, 0x81, 0x99, 0xe8, 0xfe, 0xc1, 0xa8, 0x89,
	0x03, 0xe9, 0x04, 0x48, 0xad, 0xe9, 0x13, 0xa0, 0x08, 0xe3, 0x37, 0x9c, 0x32, 0x98, 0x3b, 0xc4,
	0xa4, 0x46, 0xfc, 0x00, 0xd7, 0xd0, 0x05, 0xae, 0xd7, 0xe4, 0xe3, 0x24, 0x5a, 0x0d, 0xc7, 0x49,
	0x16, 0xc6, 0xce, 0xea, 0x60, 0xfe, 0x10, 0x13, 0x7a, 0xa1, 0x7c, 0x8f, 0xda, 0xe4, 0xd8, 0xbb,
	0xf0, 0xa5, 0x87, 0x13, 0x4d, 0x92, 0x7e, 0x38, 0x49, 0x29, 0xc4, 0x5e, 0xbf, 0x02, 0x93, 0x0c,
	0x82, 0x1a, 0x04, 0x2a, 0x95, 0x3a, 0x6d, 0x31, 0x94, 0xd0, 0x89, 0x20, 0xb6, 0xfe, 0x23, 0x80,
	0x92, 0xe3, 0xa3, 0x76, 0x48, 0xfc, 0x60, 0x08, 0x5d, 0x53, 0xd4, 0x58, 0x98, 0xbe, 0x8b, 0x4d,
	0x3a, 0xb1, 0xfb, 0x73, 0xb0, 0x72, 0x88, 0x49, 0xbd, 0xcd, 0x8a, 0xa7, 0xda, 0x30, 0x24, 0xb8,
	0xcb, 0x72, 0x97, 0x14, 0x21, 0x2d, 0x4c, 0x47, 0x30, 0xe9, 0x48, 0x37, 0x0d, 0x20, 0xc1, 0xf0,
	0x55, 0xb3, 0x19, 0xdf, 0xf3, 0xea, 0x8f, 0x04, 0xeb, 0x5c, 0x24, 0x12, 0x41, 0xb6, 0x86, 0x44,
	0x40, 0xb3, 0x24, 0x18, 0x9e, 0xa2, 0xe0, 0x2a, 0x86, 0xfc, 0x07, 0x69, 0x2b, 0x21, 0x15, 0xcf,
	0x2e, 0xb9, 0x4a, 0xdc, 0xbb, 0x07, 0x96, 0xf8, 0x39, 0xdb, 0x67, 0x16, 0xec, 0x38, 0x7f, 0xac,
	0x99, 0x1b, 0x74, 0x44, 0x05, 0x3c, 0x86, 0xaa, 0x84, 0x8d, 0x0b, 0x51, 0x32, 0x90, 0x82, 0x3d,
	0xd2, 0x3c, 0xe8, 0x0a, 0x49, 0xa4, 0xc7, 0x23, 0xf5, 0x78, 0x98, 0xdf, 0x83, 0x85, 0x03, 0x4c,
	0xcb, 0x1b, 0x26, 0xac, 0xa0, 0x16, 0x0e, 0x53, 0x61, 0x74, 0x05, 0x71, 0x83, 0xa8, 0x7a, 0x4c,
	0x28, 0x65, 0x98, 0x73, 0x76, 0x78, 0x22, 0xc3, 0x92, 0xef, 0x5d, 0xb4, 0x5b, 0xf0, 0xa1, 0x66,
	0xa2, 0xc9, 0x13, 0xcf, 0x8f, 0x46, 0xa9, 0xc9, 0xd8, 0x9b, 0x7e, 0x4c, 0x1f, 0xfd, 0xbb, 0x0c,
	0x67, 0x8c, 0x87, 0xfa, 0x08, 0x7b, 0x0d, 0x8f, 0xea, 0x63, 0xfc, 0x44, 0xc3, 0x19, 0xe7, 0xcd,
	0xde, 0xb5, 0xf6, 0x9e, 0xfd, 0xf8, 0x8f, 0xa9, 0xc2, 0x3f, 0xdf, 0x6f, 0x14, 0x7e, 0x78, 0xbf,
	0x51, 0xf8, 0xf7, 0xfb, 0x8d, 0xc2, 0x5f, 0x3e, 0x6c, 0x58, 0x7f, 0xfb, 0xb0, 0x61, 0xfd, 0xf0,
	0x61, 0xc3, 0xfa, 0xf1, 0xc3, 0x86, 0x05, 0x16, 0xfc, 0xa0, 0xc5, 0xae, 0xac, 0x9d, 0xab, 0x01,
	0xfb, 0x59, 0xec, 0xf9, 0x04, 0xfb, 0xf3, 0xd9, 0xff, 0x03, 0x00, 0x00, 0xff, 0xff, 0x7c, 0x48,
	0x22, 0x8d, 0xb3, 0x2b, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TikvClient is the client API for Tikv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TikvClient interface {
	// Commands using a transactional interface.
	KvGet(ctx context.Context, in *kvrpcpb.GetRequest, opts ...grpc.CallOption) (*kvrpcpb.GetResponse, error)
	KvScan(ctx context.Context, in *kvrpcpb.ScanRequest, opts ...grpc.CallOption) (*kvrpcpb.ScanResponse, error)
	KvPrewrite(ctx context.Context, in *kvrpcpb.PrewriteRequest, opts ...grpc.CallOption) (*kvrpcpb.PrewriteResponse, error)
	KvPessimisticLock(ctx context.Context, in *kvrpcpb.PessimisticLockRequest, opts ...grpc.CallOption) (*kvrpcpb.PessimisticLockResponse, error)
	KVPessimisticRollback(ctx context.Context, in *kvrpcpb.PessimisticRollbackRequest, opts ...grpc.CallOption) (*kvrpcpb.PessimisticRollbackResponse, error)
	KvTxnHeartBeat(ctx context.Context, in *kvrpcpb.TxnHeartBeatRequest, opts ...grpc.CallOption) (*kvrpcpb.TxnHeartBeatResponse, error)
	KvCheckTxnStatus(ctx context.Context, in *kvrpcpb.CheckTxnStatusRequest, opts ...grpc.CallOption) (*kvrpcpb.CheckTxnStatusResponse, error)
	KvCheckSecondaryLocks(ctx context.Context, in *kvrpcpb.CheckSecondaryLocksRequest, opts ...grpc.CallOption) (*kvrpcpb.CheckSecondaryLocksResponse, error)
	KvCommit(ctx context.Context, in *kvrpcpb.CommitRequest, opts ...grpc.CallOption) (*kvrpcpb.CommitResponse, error)
	KvImport(ctx context.Context, in *kvrpcpb.ImportRequest, opts ...grpc.CallOption) (*kvrpcpb.ImportResponse, error)
	KvCleanup(ctx context.Context, in *kvrpcpb.CleanupRequest, opts ...grpc.CallOption) (*kvrpcpb.CleanupResponse, error)
	KvBatchGet(ctx context.Context, in *kvrpcpb.BatchGetRequest, opts ...grpc.CallOption) (*kvrpcpb.BatchGetResponse, error)
	KvBatchRollback(ctx context.Context, in *kvrpcpb.BatchRollbackRequest, opts ...grpc.CallOption) (*kvrpcpb.BatchRollbackResponse, error)
	KvScanLock(ctx context.Context, in *kvrpcpb.ScanLockRequest, opts ...grpc.CallOption) (*kvrpcpb.ScanLockResponse, error)
	KvResolveLock(ctx context.Context, in *kvrpcpb.ResolveLockRequest, opts ...grpc.CallOption) (*kvrpcpb.ResolveLockResponse, error)
	KvGC(ctx context.Context, in *kvrpcpb.GCRequest, opts ...grpc.CallOption) (*kvrpcpb.GCResponse, error)
	KvDeleteRange(ctx context.Context, in *kvrpcpb.DeleteRangeRequest, opts ...grpc.CallOption) (*kvrpcpb.DeleteRangeResponse, error)
	KvPrepareFlashbackToVersion(ctx context.Context, in *kvrpcpb.PrepareFlashbackToVersionRequest, opts ...grpc.CallOption) (*kvrpcpb.PrepareFlashbackToVersionResponse, error)
	KvFlashbackToVersion(ctx context.Context, in *kvrpcpb.FlashbackToVersionRequest, opts ...grpc.CallOption) (*kvrpcpb.FlashbackToVersionResponse, error)
	KvFlush(ctx context.Context, in *kvrpcpb.FlushRequest, opts ...grpc.CallOption) (*kvrpcpb.FlushResponse, error)
	KvBufferBatchGet(ctx context.Context, in *kvrpcpb.BufferBatchGetRequest, opts ...grpc.CallOption) (*kvrpcpb.BufferBatchGetResponse, error)
	// Raw commands; no transaction support.
	RawGet(ctx context.Context, in *kvrpcpb.RawGetRequest, opts ...grpc.CallOption) (*kvrpcpb.RawGetResponse, error)
	RawBatchGet(ctx context.Context, in *kvrpcpb.RawBatchGetRequest, opts ...grpc.CallOption) (*kvrpcpb.RawBatchGetResponse, error)
	RawPut(ctx context.Context, in *kvrpcpb.RawPutRequest, opts ...grpc.CallOption) (*kvrpcpb.RawPutResponse, error)
	RawBatchPut(ctx context.Context, in *kvrpcpb.RawBatchPutRequest, opts ...grpc.CallOption) (*kvrpcpb.RawBatchPutResponse, error)
	RawDelete(ctx context.Context, in *kvrpcpb.RawDeleteRequest, opts ...grpc.CallOption) (*kvrpcpb.RawDeleteResponse, error)
	RawBatchDelete(ctx context.Context, in *kvrpcpb.RawBatchDeleteRequest, opts ...grpc.CallOption) (*kvrpcpb.RawBatchDeleteResponse, error)
	RawScan(ctx context.Context, in *kvrpcpb.RawScanRequest, opts ...grpc.CallOption) (*kvrpcpb.RawScanResponse, error)
	RawDeleteRange(ctx context.Context, in *kvrpcpb.RawDeleteRangeRequest, opts ...grpc.CallOption) (*kvrpcpb.RawDeleteRangeResponse, error)
	RawBatchScan(ctx context.Context, in *kvrpcpb.RawBatchScanRequest, opts ...grpc.CallOption) (*kvrpcpb.RawBatchScanResponse, error)
	// Get TTL of the key. Returns 0 if TTL is not set for the key.
	RawGetKeyTTL(ctx context.Context, in *kvrpcpb.RawGetKeyTTLRequest, opts ...grpc.CallOption) (*kvrpcpb.RawGetKeyTTLResponse, error)
	// Compare if the value in database equals to `RawCASRequest.previous_value` before putting the new value. If not, this request will have no effect and the value in the database will be returned.
	RawCompareAndSwap(ctx context.Context, in *kvrpcpb.RawCASRequest, opts ...grpc.CallOption) (*kvrpcpb.RawCASResponse, error)
	RawChecksum(ctx context.Context, in *kvrpcpb.RawChecksumRequest, opts ...grpc.CallOption) (*kvrpcpb.RawChecksumResponse, error)
	// Store commands (sent to a each TiKV node in a cluster, rather than a certain region).
	UnsafeDestroyRange(ctx context.Context, in *kvrpcpb.UnsafeDestroyRangeRequest, opts ...grpc.CallOption) (*kvrpcpb.UnsafeDestroyRangeResponse, error)
	RegisterLockObserver(ctx context.Context, in *kvrpcpb.RegisterLockObserverRequest, opts ...grpc.CallOption) (*kvrpcpb.RegisterLockObserverResponse, error)
	CheckLockObserver(ctx context.Context, in *kvrpcpb.CheckLockObserverRequest, opts ...grpc.CallOption) (*kvrpcpb.CheckLockObserverResponse, error)
	RemoveLockObserver(ctx context.Context, in *kvrpcpb.RemoveLockObserverRequest, opts ...grpc.CallOption) (*kvrpcpb.RemoveLockObserverResponse, error)
	PhysicalScanLock(ctx context.Context, in *kvrpcpb.PhysicalScanLockRequest, opts ...grpc.CallOption) (*kvrpcpb.PhysicalScanLockResponse, error)
	// Commands for executing SQL in the TiKV coprocessor (i.e., 'pushed down' to TiKV rather than
	// executed in TiDB).
	Coprocessor(ctx context.Context, in *coprocessor.Request, opts ...grpc.CallOption) (*coprocessor.Response, error)
	CoprocessorStream(ctx context.Context, in *coprocessor.Request, opts ...grpc.CallOption) (Tikv_CoprocessorStreamClient, error)
	BatchCoprocessor(ctx context.Context, in *coprocessor.BatchRequest, opts ...grpc.CallOption) (Tikv_BatchCoprocessorClient, error)
	// Command send by remote coprocessor to TiKV for executing coprocessor request.
	DelegateCoprocessor(ctx context.Context, in *coprocessor.DelegateRequest, opts ...grpc.CallOption) (*coprocessor.DelegateResponse, error)
	// Command for executing custom user requests in TiKV coprocessor_v2.
	RawCoprocessor(ctx context.Context, in *kvrpcpb.RawCoprocessorRequest, opts ...grpc.CallOption) (*kvrpcpb.RawCoprocessorResponse, error)
	// Raft commands (sent between TiKV nodes).
	Raft(ctx context.Context, opts ...grpc.CallOption) (Tikv_RaftClient, error)
	BatchRaft(ctx context.Context, opts ...grpc.CallOption) (Tikv_BatchRaftClient, error)
	Snapshot(ctx context.Context, opts ...grpc.CallOption) (Tikv_SnapshotClient, error)
	TabletSnapshot(ctx context.Context, opts ...grpc.CallOption) (Tikv_TabletSnapshotClient, error)
	// Sent from PD or TiDB to a TiKV node.
	SplitRegion(ctx context.Context, in *kvrpcpb.SplitRegionRequest, opts ...grpc.CallOption) (*kvrpcpb.SplitRegionResponse, error)
	// Sent from TiFlash or TiKV to a TiKV node.
	ReadIndex(ctx context.Context, in *kvrpcpb.ReadIndexRequest, opts ...grpc.CallOption) (*kvrpcpb.ReadIndexResponse, error)
	// Commands for debugging transactions.
	MvccGetByKey(ctx context.Context, in *kvrpcpb.MvccGetByKeyRequest, opts ...grpc.CallOption) (*kvrpcpb.MvccGetByKeyResponse, error)
	MvccGetByStartTs(ctx context.Context, in *kvrpcpb.MvccGetByStartTsRequest, opts ...grpc.CallOption) (*kvrpcpb.MvccGetByStartTsResponse, error)
	// Batched commands.
	BatchCommands(ctx context.Context, opts ...grpc.CallOption) (Tikv_BatchCommandsClient, error)
	// These are for mpp execution.
	DispatchMPPTask(ctx context.Context, in *mpp.DispatchTaskRequest, opts ...grpc.CallOption) (*mpp.DispatchTaskResponse, error)
	CancelMPPTask(ctx context.Context, in *mpp.CancelTaskRequest, opts ...grpc.CallOption) (*mpp.CancelTaskResponse, error)
	EstablishMPPConnection(ctx context.Context, in *mpp.EstablishMPPConnectionRequest, opts ...grpc.CallOption) (Tikv_EstablishMPPConnectionClient, error)
	IsAlive(ctx context.Context, in *mpp.IsAliveRequest, opts ...grpc.CallOption) (*mpp.IsAliveResponse, error)
	ReportMPPTaskStatus(ctx context.Context, in *mpp.ReportTaskStatusRequest, opts ...grpc.CallOption) (*mpp.ReportTaskStatusResponse, error)
	/// CheckLeader sends all information (includes region term and epoch) to other stores.
	/// Once a store receives a request, it checks term and epoch for each region, and sends the regions whose
	/// term and epoch match with local information in the store.
	/// After the client collected all responses from all stores, it checks if got a quorum of responses from
	/// other stores for every region, and decides to advance resolved ts from these regions.
	CheckLeader(ctx context.Context, in *kvrpcpb.CheckLeaderRequest, opts ...grpc.CallOption) (*kvrpcpb.CheckLeaderResponse, error)
	/// Get the minimal `safe_ts` from regions at the store
	GetStoreSafeTS(ctx context.Context, in *kvrpcpb.StoreSafeTSRequest, opts ...grpc.CallOption) (*kvrpcpb.StoreSafeTSResponse, error)
	/// Get the information about lock waiting from TiKV.
	GetLockWaitInfo(ctx context.Context, in *kvrpcpb.GetLockWaitInfoRequest, opts ...grpc.CallOption) (*kvrpcpb.GetLockWaitInfoResponse, error)
	/// Compact a specified key range. This request is not restricted to raft leaders and will not be replicated.
	/// It only compacts data on this node.
	/// TODO: Currently this RPC is designed to be only compatible with TiFlash.
	/// Shall be move out in https://github.com/pingcap/kvproto/issues/912
	Compact(ctx context.Context, in *kvrpcpb.CompactRequest, opts ...grpc.CallOption) (*kvrpcpb.CompactResponse, error)
	/// Get the information about history lock waiting from TiKV.
	GetLockWaitHistory(ctx context.Context, in *kvrpcpb.GetLockWaitHistoryRequest, opts ...grpc.CallOption) (*kvrpcpb.GetLockWaitHistoryResponse, error)
	/// Get system table from TiFlash
	GetTiFlashSystemTable(ctx context.Context, in *kvrpcpb.TiFlashSystemTableRequest, opts ...grpc.CallOption) (*kvrpcpb.TiFlashSystemTableResponse, error)
	// These are for TiFlash disaggregated architecture
	/// Try to lock a S3 object, atomically
	TryAddLock(ctx context.Context, in *disaggregated.TryAddLockRequest, opts ...grpc.CallOption) (*disaggregated.TryAddLockResponse, error)
	/// Try to delete a S3 object, atomically
	TryMarkDelete(ctx context.Context, in *disaggregated.TryMarkDeleteRequest, opts ...grpc.CallOption) (*disaggregated.TryMarkDeleteResponse, error)
	/// Build the disaggregated task on TiFlash write node
	EstablishDisaggTask(ctx context.Context, in *disaggregated.EstablishDisaggTaskRequest, opts ...grpc.CallOption) (*disaggregated.EstablishDisaggTaskResponse, error)
	/// Cancel the disaggregated task on TiFlash write node
	CancelDisaggTask(ctx context.Context, in *disaggregated.CancelDisaggTaskRequest, opts ...grpc.CallOption) (*disaggregated.CancelDisaggTaskResponse, error)
	/// Exchange page data between TiFlash write node and compute node
	FetchDisaggPages(ctx context.Context, in *disaggregated.FetchDisaggPagesRequest, opts ...grpc.CallOption) (Tikv_FetchDisaggPagesClient, error)
	/// Compute node get configuration from Write node
	GetDisaggConfig(ctx context.Context, in *disaggregated.GetDisaggConfigRequest, opts ...grpc.CallOption) (*disaggregated.GetDisaggConfigResponse, error)
	/// Get health feedback info from the TiKV node.
	GetHealthFeedback(ctx context.Context, in *kvrpcpb.GetHealthFeedbackRequest, opts ...grpc.CallOption) (*kvrpcpb.GetHealthFeedbackResponse, error)
	/// Broadcast the transaction status to all TiKV nodes
	BroadcastTxnStatus(ctx context.Context, in *kvrpcpb.BroadcastTxnStatusRequest, opts ...grpc.CallOption) (*kvrpcpb.BroadcastTxnStatusResponse, error)
}

type tikvClient struct {
	cc *grpc.ClientConn
}

func NewTikvClient(cc *grpc.ClientConn) TikvClient {
	return &tikvClient{cc}
}

func (c *tikvClient) KvGet(ctx context.Context, in *kvrpcpb.GetRequest, opts ...grpc.CallOption) (*kvrpcpb.GetResponse, error) {
	out := new(kvrpcpb.GetResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvScan(ctx context.Context, in *kvrpcpb.ScanRequest, opts ...grpc.CallOption) (*kvrpcpb.ScanResponse, error) {
	out := new(kvrpcpb.ScanResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvScan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvPrewrite(ctx context.Context, in *kvrpcpb.PrewriteRequest, opts ...grpc.CallOption) (*kvrpcpb.PrewriteResponse, error) {
	out := new(kvrpcpb.PrewriteResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvPrewrite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvPessimisticLock(ctx context.Context, in *kvrpcpb.PessimisticLockRequest, opts ...grpc.CallOption) (*kvrpcpb.PessimisticLockResponse, error) {
	out := new(kvrpcpb.PessimisticLockResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvPessimisticLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KVPessimisticRollback(ctx context.Context, in *kvrpcpb.PessimisticRollbackRequest, opts ...grpc.CallOption) (*kvrpcpb.PessimisticRollbackResponse, error) {
	out := new(kvrpcpb.PessimisticRollbackResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KVPessimisticRollback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvTxnHeartBeat(ctx context.Context, in *kvrpcpb.TxnHeartBeatRequest, opts ...grpc.CallOption) (*kvrpcpb.TxnHeartBeatResponse, error) {
	out := new(kvrpcpb.TxnHeartBeatResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvTxnHeartBeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvCheckTxnStatus(ctx context.Context, in *kvrpcpb.CheckTxnStatusRequest, opts ...grpc.CallOption) (*kvrpcpb.CheckTxnStatusResponse, error) {
	out := new(kvrpcpb.CheckTxnStatusResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvCheckTxnStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvCheckSecondaryLocks(ctx context.Context, in *kvrpcpb.CheckSecondaryLocksRequest, opts ...grpc.CallOption) (*kvrpcpb.CheckSecondaryLocksResponse, error) {
	out := new(kvrpcpb.CheckSecondaryLocksResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvCheckSecondaryLocks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvCommit(ctx context.Context, in *kvrpcpb.CommitRequest, opts ...grpc.CallOption) (*kvrpcpb.CommitResponse, error) {
	out := new(kvrpcpb.CommitResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvCommit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvImport(ctx context.Context, in *kvrpcpb.ImportRequest, opts ...grpc.CallOption) (*kvrpcpb.ImportResponse, error) {
	out := new(kvrpcpb.ImportResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvImport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvCleanup(ctx context.Context, in *kvrpcpb.CleanupRequest, opts ...grpc.CallOption) (*kvrpcpb.CleanupResponse, error) {
	out := new(kvrpcpb.CleanupResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvCleanup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvBatchGet(ctx context.Context, in *kvrpcpb.BatchGetRequest, opts ...grpc.CallOption) (*kvrpcpb.BatchGetResponse, error) {
	out := new(kvrpcpb.BatchGetResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvBatchGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvBatchRollback(ctx context.Context, in *kvrpcpb.BatchRollbackRequest, opts ...grpc.CallOption) (*kvrpcpb.BatchRollbackResponse, error) {
	out := new(kvrpcpb.BatchRollbackResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvBatchRollback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvScanLock(ctx context.Context, in *kvrpcpb.ScanLockRequest, opts ...grpc.CallOption) (*kvrpcpb.ScanLockResponse, error) {
	out := new(kvrpcpb.ScanLockResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvScanLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvResolveLock(ctx context.Context, in *kvrpcpb.ResolveLockRequest, opts ...grpc.CallOption) (*kvrpcpb.ResolveLockResponse, error) {
	out := new(kvrpcpb.ResolveLockResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvResolveLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvGC(ctx context.Context, in *kvrpcpb.GCRequest, opts ...grpc.CallOption) (*kvrpcpb.GCResponse, error) {
	out := new(kvrpcpb.GCResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvGC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvDeleteRange(ctx context.Context, in *kvrpcpb.DeleteRangeRequest, opts ...grpc.CallOption) (*kvrpcpb.DeleteRangeResponse, error) {
	out := new(kvrpcpb.DeleteRangeResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvDeleteRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvPrepareFlashbackToVersion(ctx context.Context, in *kvrpcpb.PrepareFlashbackToVersionRequest, opts ...grpc.CallOption) (*kvrpcpb.PrepareFlashbackToVersionResponse, error) {
	out := new(kvrpcpb.PrepareFlashbackToVersionResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvPrepareFlashbackToVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvFlashbackToVersion(ctx context.Context, in *kvrpcpb.FlashbackToVersionRequest, opts ...grpc.CallOption) (*kvrpcpb.FlashbackToVersionResponse, error) {
	out := new(kvrpcpb.FlashbackToVersionResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvFlashbackToVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvFlush(ctx context.Context, in *kvrpcpb.FlushRequest, opts ...grpc.CallOption) (*kvrpcpb.FlushResponse, error) {
	out := new(kvrpcpb.FlushResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvFlush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) KvBufferBatchGet(ctx context.Context, in *kvrpcpb.BufferBatchGetRequest, opts ...grpc.CallOption) (*kvrpcpb.BufferBatchGetResponse, error) {
	out := new(kvrpcpb.BufferBatchGetResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/KvBufferBatchGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawGet(ctx context.Context, in *kvrpcpb.RawGetRequest, opts ...grpc.CallOption) (*kvrpcpb.RawGetResponse, error) {
	out := new(kvrpcpb.RawGetResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawBatchGet(ctx context.Context, in *kvrpcpb.RawBatchGetRequest, opts ...grpc.CallOption) (*kvrpcpb.RawBatchGetResponse, error) {
	out := new(kvrpcpb.RawBatchGetResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawBatchGet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawPut(ctx context.Context, in *kvrpcpb.RawPutRequest, opts ...grpc.CallOption) (*kvrpcpb.RawPutResponse, error) {
	out := new(kvrpcpb.RawPutResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawPut", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawBatchPut(ctx context.Context, in *kvrpcpb.RawBatchPutRequest, opts ...grpc.CallOption) (*kvrpcpb.RawBatchPutResponse, error) {
	out := new(kvrpcpb.RawBatchPutResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawBatchPut", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawDelete(ctx context.Context, in *kvrpcpb.RawDeleteRequest, opts ...grpc.CallOption) (*kvrpcpb.RawDeleteResponse, error) {
	out := new(kvrpcpb.RawDeleteResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawBatchDelete(ctx context.Context, in *kvrpcpb.RawBatchDeleteRequest, opts ...grpc.CallOption) (*kvrpcpb.RawBatchDeleteResponse, error) {
	out := new(kvrpcpb.RawBatchDeleteResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawBatchDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawScan(ctx context.Context, in *kvrpcpb.RawScanRequest, opts ...grpc.CallOption) (*kvrpcpb.RawScanResponse, error) {
	out := new(kvrpcpb.RawScanResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawScan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawDeleteRange(ctx context.Context, in *kvrpcpb.RawDeleteRangeRequest, opts ...grpc.CallOption) (*kvrpcpb.RawDeleteRangeResponse, error) {
	out := new(kvrpcpb.RawDeleteRangeResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawDeleteRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawBatchScan(ctx context.Context, in *kvrpcpb.RawBatchScanRequest, opts ...grpc.CallOption) (*kvrpcpb.RawBatchScanResponse, error) {
	out := new(kvrpcpb.RawBatchScanResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawBatchScan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawGetKeyTTL(ctx context.Context, in *kvrpcpb.RawGetKeyTTLRequest, opts ...grpc.CallOption) (*kvrpcpb.RawGetKeyTTLResponse, error) {
	out := new(kvrpcpb.RawGetKeyTTLResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawGetKeyTTL", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawCompareAndSwap(ctx context.Context, in *kvrpcpb.RawCASRequest, opts ...grpc.CallOption) (*kvrpcpb.RawCASResponse, error) {
	out := new(kvrpcpb.RawCASResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawCompareAndSwap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawChecksum(ctx context.Context, in *kvrpcpb.RawChecksumRequest, opts ...grpc.CallOption) (*kvrpcpb.RawChecksumResponse, error) {
	out := new(kvrpcpb.RawChecksumResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawChecksum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) UnsafeDestroyRange(ctx context.Context, in *kvrpcpb.UnsafeDestroyRangeRequest, opts ...grpc.CallOption) (*kvrpcpb.UnsafeDestroyRangeResponse, error) {
	out := new(kvrpcpb.UnsafeDestroyRangeResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/UnsafeDestroyRange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RegisterLockObserver(ctx context.Context, in *kvrpcpb.RegisterLockObserverRequest, opts ...grpc.CallOption) (*kvrpcpb.RegisterLockObserverResponse, error) {
	out := new(kvrpcpb.RegisterLockObserverResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RegisterLockObserver", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) CheckLockObserver(ctx context.Context, in *kvrpcpb.CheckLockObserverRequest, opts ...grpc.CallOption) (*kvrpcpb.CheckLockObserverResponse, error) {
	out := new(kvrpcpb.CheckLockObserverResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/CheckLockObserver", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RemoveLockObserver(ctx context.Context, in *kvrpcpb.RemoveLockObserverRequest, opts ...grpc.CallOption) (*kvrpcpb.RemoveLockObserverResponse, error) {
	out := new(kvrpcpb.RemoveLockObserverResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RemoveLockObserver", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) PhysicalScanLock(ctx context.Context, in *kvrpcpb.PhysicalScanLockRequest, opts ...grpc.CallOption) (*kvrpcpb.PhysicalScanLockResponse, error) {
	out := new(kvrpcpb.PhysicalScanLockResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/PhysicalScanLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) Coprocessor(ctx context.Context, in *coprocessor.Request, opts ...grpc.CallOption) (*coprocessor.Response, error) {
	out := new(coprocessor.Response)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/Coprocessor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) CoprocessorStream(ctx context.Context, in *coprocessor.Request, opts ...grpc.CallOption) (Tikv_CoprocessorStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[0], "/tikvpb.Tikv/CoprocessorStream", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvCoprocessorStreamClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Tikv_CoprocessorStreamClient interface {
	Recv() (*coprocessor.Response, error)
	grpc.ClientStream
}

type tikvCoprocessorStreamClient struct {
	grpc.ClientStream
}

func (x *tikvCoprocessorStreamClient) Recv() (*coprocessor.Response, error) {
	m := new(coprocessor.Response)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) BatchCoprocessor(ctx context.Context, in *coprocessor.BatchRequest, opts ...grpc.CallOption) (Tikv_BatchCoprocessorClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[1], "/tikvpb.Tikv/BatchCoprocessor", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvBatchCoprocessorClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Tikv_BatchCoprocessorClient interface {
	Recv() (*coprocessor.BatchResponse, error)
	grpc.ClientStream
}

type tikvBatchCoprocessorClient struct {
	grpc.ClientStream
}

func (x *tikvBatchCoprocessorClient) Recv() (*coprocessor.BatchResponse, error) {
	m := new(coprocessor.BatchResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) DelegateCoprocessor(ctx context.Context, in *coprocessor.DelegateRequest, opts ...grpc.CallOption) (*coprocessor.DelegateResponse, error) {
	out := new(coprocessor.DelegateResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/DelegateCoprocessor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) RawCoprocessor(ctx context.Context, in *kvrpcpb.RawCoprocessorRequest, opts ...grpc.CallOption) (*kvrpcpb.RawCoprocessorResponse, error) {
	out := new(kvrpcpb.RawCoprocessorResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/RawCoprocessor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) Raft(ctx context.Context, opts ...grpc.CallOption) (Tikv_RaftClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[2], "/tikvpb.Tikv/Raft", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvRaftClient{stream}
	return x, nil
}

type Tikv_RaftClient interface {
	Send(*raft_serverpb.RaftMessage) error
	CloseAndRecv() (*raft_serverpb.Done, error)
	grpc.ClientStream
}

type tikvRaftClient struct {
	grpc.ClientStream
}

func (x *tikvRaftClient) Send(m *raft_serverpb.RaftMessage) error {
	return x.ClientStream.SendMsg(m)
}

func (x *tikvRaftClient) CloseAndRecv() (*raft_serverpb.Done, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(raft_serverpb.Done)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) BatchRaft(ctx context.Context, opts ...grpc.CallOption) (Tikv_BatchRaftClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[3], "/tikvpb.Tikv/BatchRaft", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvBatchRaftClient{stream}
	return x, nil
}

type Tikv_BatchRaftClient interface {
	Send(*BatchRaftMessage) error
	CloseAndRecv() (*raft_serverpb.Done, error)
	grpc.ClientStream
}

type tikvBatchRaftClient struct {
	grpc.ClientStream
}

func (x *tikvBatchRaftClient) Send(m *BatchRaftMessage) error {
	return x.ClientStream.SendMsg(m)
}

func (x *tikvBatchRaftClient) CloseAndRecv() (*raft_serverpb.Done, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(raft_serverpb.Done)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) Snapshot(ctx context.Context, opts ...grpc.CallOption) (Tikv_SnapshotClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[4], "/tikvpb.Tikv/Snapshot", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvSnapshotClient{stream}
	return x, nil
}

type Tikv_SnapshotClient interface {
	Send(*raft_serverpb.SnapshotChunk) error
	CloseAndRecv() (*raft_serverpb.Done, error)
	grpc.ClientStream
}

type tikvSnapshotClient struct {
	grpc.ClientStream
}

func (x *tikvSnapshotClient) Send(m *raft_serverpb.SnapshotChunk) error {
	return x.ClientStream.SendMsg(m)
}

func (x *tikvSnapshotClient) CloseAndRecv() (*raft_serverpb.Done, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(raft_serverpb.Done)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) TabletSnapshot(ctx context.Context, opts ...grpc.CallOption) (Tikv_TabletSnapshotClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[5], "/tikvpb.Tikv/TabletSnapshot", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvTabletSnapshotClient{stream}
	return x, nil
}

type Tikv_TabletSnapshotClient interface {
	Send(*raft_serverpb.TabletSnapshotRequest) error
	Recv() (*raft_serverpb.TabletSnapshotResponse, error)
	grpc.ClientStream
}

type tikvTabletSnapshotClient struct {
	grpc.ClientStream
}

func (x *tikvTabletSnapshotClient) Send(m *raft_serverpb.TabletSnapshotRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *tikvTabletSnapshotClient) Recv() (*raft_serverpb.TabletSnapshotResponse, error) {
	m := new(raft_serverpb.TabletSnapshotResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) SplitRegion(ctx context.Context, in *kvrpcpb.SplitRegionRequest, opts ...grpc.CallOption) (*kvrpcpb.SplitRegionResponse, error) {
	out := new(kvrpcpb.SplitRegionResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/SplitRegion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) ReadIndex(ctx context.Context, in *kvrpcpb.ReadIndexRequest, opts ...grpc.CallOption) (*kvrpcpb.ReadIndexResponse, error) {
	out := new(kvrpcpb.ReadIndexResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/ReadIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) MvccGetByKey(ctx context.Context, in *kvrpcpb.MvccGetByKeyRequest, opts ...grpc.CallOption) (*kvrpcpb.MvccGetByKeyResponse, error) {
	out := new(kvrpcpb.MvccGetByKeyResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/MvccGetByKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) MvccGetByStartTs(ctx context.Context, in *kvrpcpb.MvccGetByStartTsRequest, opts ...grpc.CallOption) (*kvrpcpb.MvccGetByStartTsResponse, error) {
	out := new(kvrpcpb.MvccGetByStartTsResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/MvccGetByStartTs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) BatchCommands(ctx context.Context, opts ...grpc.CallOption) (Tikv_BatchCommandsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[6], "/tikvpb.Tikv/BatchCommands", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvBatchCommandsClient{stream}
	return x, nil
}

type Tikv_BatchCommandsClient interface {
	Send(*BatchCommandsRequest) error
	Recv() (*BatchCommandsResponse, error)
	grpc.ClientStream
}

type tikvBatchCommandsClient struct {
	grpc.ClientStream
}

func (x *tikvBatchCommandsClient) Send(m *BatchCommandsRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *tikvBatchCommandsClient) Recv() (*BatchCommandsResponse, error) {
	m := new(BatchCommandsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) DispatchMPPTask(ctx context.Context, in *mpp.DispatchTaskRequest, opts ...grpc.CallOption) (*mpp.DispatchTaskResponse, error) {
	out := new(mpp.DispatchTaskResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/DispatchMPPTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) CancelMPPTask(ctx context.Context, in *mpp.CancelTaskRequest, opts ...grpc.CallOption) (*mpp.CancelTaskResponse, error) {
	out := new(mpp.CancelTaskResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/CancelMPPTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) EstablishMPPConnection(ctx context.Context, in *mpp.EstablishMPPConnectionRequest, opts ...grpc.CallOption) (Tikv_EstablishMPPConnectionClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[7], "/tikvpb.Tikv/EstablishMPPConnection", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvEstablishMPPConnectionClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Tikv_EstablishMPPConnectionClient interface {
	Recv() (*mpp.MPPDataPacket, error)
	grpc.ClientStream
}

type tikvEstablishMPPConnectionClient struct {
	grpc.ClientStream
}

func (x *tikvEstablishMPPConnectionClient) Recv() (*mpp.MPPDataPacket, error) {
	m := new(mpp.MPPDataPacket)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) IsAlive(ctx context.Context, in *mpp.IsAliveRequest, opts ...grpc.CallOption) (*mpp.IsAliveResponse, error) {
	out := new(mpp.IsAliveResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/IsAlive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) ReportMPPTaskStatus(ctx context.Context, in *mpp.ReportTaskStatusRequest, opts ...grpc.CallOption) (*mpp.ReportTaskStatusResponse, error) {
	out := new(mpp.ReportTaskStatusResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/ReportMPPTaskStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) CheckLeader(ctx context.Context, in *kvrpcpb.CheckLeaderRequest, opts ...grpc.CallOption) (*kvrpcpb.CheckLeaderResponse, error) {
	out := new(kvrpcpb.CheckLeaderResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/CheckLeader", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) GetStoreSafeTS(ctx context.Context, in *kvrpcpb.StoreSafeTSRequest, opts ...grpc.CallOption) (*kvrpcpb.StoreSafeTSResponse, error) {
	out := new(kvrpcpb.StoreSafeTSResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/GetStoreSafeTS", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) GetLockWaitInfo(ctx context.Context, in *kvrpcpb.GetLockWaitInfoRequest, opts ...grpc.CallOption) (*kvrpcpb.GetLockWaitInfoResponse, error) {
	out := new(kvrpcpb.GetLockWaitInfoResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/GetLockWaitInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) Compact(ctx context.Context, in *kvrpcpb.CompactRequest, opts ...grpc.CallOption) (*kvrpcpb.CompactResponse, error) {
	out := new(kvrpcpb.CompactResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/Compact", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) GetLockWaitHistory(ctx context.Context, in *kvrpcpb.GetLockWaitHistoryRequest, opts ...grpc.CallOption) (*kvrpcpb.GetLockWaitHistoryResponse, error) {
	out := new(kvrpcpb.GetLockWaitHistoryResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/GetLockWaitHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) GetTiFlashSystemTable(ctx context.Context, in *kvrpcpb.TiFlashSystemTableRequest, opts ...grpc.CallOption) (*kvrpcpb.TiFlashSystemTableResponse, error) {
	out := new(kvrpcpb.TiFlashSystemTableResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/GetTiFlashSystemTable", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) TryAddLock(ctx context.Context, in *disaggregated.TryAddLockRequest, opts ...grpc.CallOption) (*disaggregated.TryAddLockResponse, error) {
	out := new(disaggregated.TryAddLockResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/tryAddLock", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) TryMarkDelete(ctx context.Context, in *disaggregated.TryMarkDeleteRequest, opts ...grpc.CallOption) (*disaggregated.TryMarkDeleteResponse, error) {
	out := new(disaggregated.TryMarkDeleteResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/tryMarkDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) EstablishDisaggTask(ctx context.Context, in *disaggregated.EstablishDisaggTaskRequest, opts ...grpc.CallOption) (*disaggregated.EstablishDisaggTaskResponse, error) {
	out := new(disaggregated.EstablishDisaggTaskResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/EstablishDisaggTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) CancelDisaggTask(ctx context.Context, in *disaggregated.CancelDisaggTaskRequest, opts ...grpc.CallOption) (*disaggregated.CancelDisaggTaskResponse, error) {
	out := new(disaggregated.CancelDisaggTaskResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/CancelDisaggTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) FetchDisaggPages(ctx context.Context, in *disaggregated.FetchDisaggPagesRequest, opts ...grpc.CallOption) (Tikv_FetchDisaggPagesClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Tikv_serviceDesc.Streams[8], "/tikvpb.Tikv/FetchDisaggPages", opts...)
	if err != nil {
		return nil, err
	}
	x := &tikvFetchDisaggPagesClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Tikv_FetchDisaggPagesClient interface {
	Recv() (*disaggregated.PagesPacket, error)
	grpc.ClientStream
}

type tikvFetchDisaggPagesClient struct {
	grpc.ClientStream
}

func (x *tikvFetchDisaggPagesClient) Recv() (*disaggregated.PagesPacket, error) {
	m := new(disaggregated.PagesPacket)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *tikvClient) GetDisaggConfig(ctx context.Context, in *disaggregated.GetDisaggConfigRequest, opts ...grpc.CallOption) (*disaggregated.GetDisaggConfigResponse, error) {
	out := new(disaggregated.GetDisaggConfigResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/GetDisaggConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) GetHealthFeedback(ctx context.Context, in *kvrpcpb.GetHealthFeedbackRequest, opts ...grpc.CallOption) (*kvrpcpb.GetHealthFeedbackResponse, error) {
	out := new(kvrpcpb.GetHealthFeedbackResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/GetHealthFeedback", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tikvClient) BroadcastTxnStatus(ctx context.Context, in *kvrpcpb.BroadcastTxnStatusRequest, opts ...grpc.CallOption) (*kvrpcpb.BroadcastTxnStatusResponse, error) {
	out := new(kvrpcpb.BroadcastTxnStatusResponse)
	err := c.cc.Invoke(ctx, "/tikvpb.Tikv/BroadcastTxnStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TikvServer is the server API for Tikv service.
type TikvServer interface {
	// Commands using a transactional interface.
	KvGet(context.Context, *kvrpcpb.GetRequest) (*kvrpcpb.GetResponse, error)
	KvScan(context.Context, *kvrpcpb.ScanRequest) (*kvrpcpb.ScanResponse, error)
	KvPrewrite(context.Context, *kvrpcpb.PrewriteRequest) (*kvrpcpb.PrewriteResponse, error)
	KvPessimisticLock(context.Context, *kvrpcpb.PessimisticLockRequest) (*kvrpcpb.PessimisticLockResponse, error)
	KVPessimisticRollback(context.Context, *kvrpcpb.PessimisticRollbackRequest) (*kvrpcpb.PessimisticRollbackResponse, error)
	KvTxnHeartBeat(context.Context, *kvrpcpb.TxnHeartBeatRequest) (*kvrpcpb.TxnHeartBeatResponse, error)
	KvCheckTxnStatus(context.Context, *kvrpcpb.CheckTxnStatusRequest) (*kvrpcpb.CheckTxnStatusResponse, error)
	KvCheckSecondaryLocks(context.Context, *kvrpcpb.CheckSecondaryLocksRequest) (*kvrpcpb.CheckSecondaryLocksResponse, error)
	KvCommit(context.Context, *kvrpcpb.CommitRequest) (*kvrpcpb.CommitResponse, error)
	KvImport(context.Context, *kvrpcpb.ImportRequest) (*kvrpcpb.ImportResponse, error)
	KvCleanup(context.Context, *kvrpcpb.CleanupRequest) (*kvrpcpb.CleanupResponse, error)
	KvBatchGet(context.Context, *kvrpcpb.BatchGetRequest) (*kvrpcpb.BatchGetResponse, error)
	KvBatchRollback(context.Context, *kvrpcpb.BatchRollbackRequest) (*kvrpcpb.BatchRollbackResponse, error)
	KvScanLock(context.Context, *kvrpcpb.ScanLockRequest) (*kvrpcpb.ScanLockResponse, error)
	KvResolveLock(context.Context, *kvrpcpb.ResolveLockRequest) (*kvrpcpb.ResolveLockResponse, error)
	KvGC(context.Context, *kvrpcpb.GCRequest) (*kvrpcpb.GCResponse, error)
	KvDeleteRange(context.Context, *kvrpcpb.DeleteRangeRequest) (*kvrpcpb.DeleteRangeResponse, error)
	KvPrepareFlashbackToVersion(context.Context, *kvrpcpb.PrepareFlashbackToVersionRequest) (*kvrpcpb.PrepareFlashbackToVersionResponse, error)
	KvFlashbackToVersion(context.Context, *kvrpcpb.FlashbackToVersionRequest) (*kvrpcpb.FlashbackToVersionResponse, error)
	KvFlush(context.Context, *kvrpcpb.FlushRequest) (*kvrpcpb.FlushResponse, error)
	KvBufferBatchGet(context.Context, *kvrpcpb.BufferBatchGetRequest) (*kvrpcpb.BufferBatchGetResponse, error)
	// Raw commands; no transaction support.
	RawGet(context.Context, *kvrpcpb.RawGetRequest) (*kvrpcpb.RawGetResponse, error)
	RawBatchGet(context.Context, *kvrpcpb.RawBatchGetRequest) (*kvrpcpb.RawBatchGetResponse, error)
	RawPut(context.Context, *kvrpcpb.RawPutRequest) (*kvrpcpb.RawPutResponse, error)
	RawBatchPut(context.Context, *kvrpcpb.RawBatchPutRequest) (*kvrpcpb.RawBatchPutResponse, error)
	RawDelete(context.Context, *kvrpcpb.RawDeleteRequest) (*kvrpcpb.RawDeleteResponse, error)
	RawBatchDelete(context.Context, *kvrpcpb.RawBatchDeleteRequest) (*kvrpcpb.RawBatchDeleteResponse, error)
	RawScan(context.Context, *kvrpcpb.RawScanRequest) (*kvrpcpb.RawScanResponse, error)
	RawDeleteRange(context.Context, *kvrpcpb.RawDeleteRangeRequest) (*kvrpcpb.RawDeleteRangeResponse, error)
	RawBatchScan(context.Context, *kvrpcpb.RawBatchScanRequest) (*kvrpcpb.RawBatchScanResponse, error)
	// Get TTL of the key. Returns 0 if TTL is not set for the key.
	RawGetKeyTTL(context.Context, *kvrpcpb.RawGetKeyTTLRequest) (*kvrpcpb.RawGetKeyTTLResponse, error)
	// Compare if the value in database equals to `RawCASRequest.previous_value` before putting the new value. If not, this request will have no effect and the value in the database will be returned.
	RawCompareAndSwap(context.Context, *kvrpcpb.RawCASRequest) (*kvrpcpb.RawCASResponse, error)
	RawChecksum(context.Context, *kvrpcpb.RawChecksumRequest) (*kvrpcpb.RawChecksumResponse, error)
	// Store commands (sent to a each TiKV node in a cluster, rather than a certain region).
	UnsafeDestroyRange(context.Context, *kvrpcpb.UnsafeDestroyRangeRequest) (*kvrpcpb.UnsafeDestroyRangeResponse, error)
	RegisterLockObserver(context.Context, *kvrpcpb.RegisterLockObserverRequest) (*kvrpcpb.RegisterLockObserverResponse, error)
	CheckLockObserver(context.Context, *kvrpcpb.CheckLockObserverRequest) (*kvrpcpb.CheckLockObserverResponse, error)
	RemoveLockObserver(context.Context, *kvrpcpb.RemoveLockObserverRequest) (*kvrpcpb.RemoveLockObserverResponse, error)
	PhysicalScanLock(context.Context, *kvrpcpb.PhysicalScanLockRequest) (*kvrpcpb.PhysicalScanLockResponse, error)
	// Commands for executing SQL in the TiKV coprocessor (i.e., 'pushed down' to TiKV rather than
	// executed in TiDB).
	Coprocessor(context.Context, *coprocessor.Request) (*coprocessor.Response, error)
	CoprocessorStream(*coprocessor.Request, Tikv_CoprocessorStreamServer) error
	BatchCoprocessor(*coprocessor.BatchRequest, Tikv_BatchCoprocessorServer) error
	// Command send by remote coprocessor to TiKV for executing coprocessor request.
	DelegateCoprocessor(context.Context, *coprocessor.DelegateRequest) (*coprocessor.DelegateResponse, error)
	// Command for executing custom user requests in TiKV coprocessor_v2.
	RawCoprocessor(context.Context, *kvrpcpb.RawCoprocessorRequest) (*kvrpcpb.RawCoprocessorResponse, error)
	// Raft commands (sent between TiKV nodes).
	Raft(Tikv_RaftServer) error
	BatchRaft(Tikv_BatchRaftServer) error
	Snapshot(Tikv_SnapshotServer) error
	TabletSnapshot(Tikv_TabletSnapshotServer) error
	// Sent from PD or TiDB to a TiKV node.
	SplitRegion(context.Context, *kvrpcpb.SplitRegionRequest) (*kvrpcpb.SplitRegionResponse, error)
	// Sent from TiFlash or TiKV to a TiKV node.
	ReadIndex(context.Context, *kvrpcpb.ReadIndexRequest) (*kvrpcpb.ReadIndexResponse, error)
	// Commands for debugging transactions.
	MvccGetByKey(context.Context, *kvrpcpb.MvccGetByKeyRequest) (*kvrpcpb.MvccGetByKeyResponse, error)
	MvccGetByStartTs(context.Context, *kvrpcpb.MvccGetByStartTsRequest) (*kvrpcpb.MvccGetByStartTsResponse, error)
	// Batched commands.
	BatchCommands(Tikv_BatchCommandsServer) error
	// These are for mpp execution.
	DispatchMPPTask(context.Context, *mpp.DispatchTaskRequest) (*mpp.DispatchTaskResponse, error)
	CancelMPPTask(context.Context, *mpp.CancelTaskRequest) (*mpp.CancelTaskResponse, error)
	EstablishMPPConnection(*mpp.EstablishMPPConnectionRequest, Tikv_EstablishMPPConnectionServer) error
	IsAlive(context.Context, *mpp.IsAliveRequest) (*mpp.IsAliveResponse, error)
	ReportMPPTaskStatus(context.Context, *mpp.ReportTaskStatusRequest) (*mpp.ReportTaskStatusResponse, error)
	/// CheckLeader sends all information (includes region term and epoch) to other stores.
	/// Once a store receives a request, it checks term and epoch for each region, and sends the regions whose
	/// term and epoch match with local information in the store.
	/// After the client collected all responses from all stores, it checks if got a quorum of responses from
	/// other stores for every region, and decides to advance resolved ts from these regions.
	CheckLeader(context.Context, *kvrpcpb.CheckLeaderRequest) (*kvrpcpb.CheckLeaderResponse, error)
	/// Get the minimal `safe_ts` from regions at the store
	GetStoreSafeTS(context.Context, *kvrpcpb.StoreSafeTSRequest) (*kvrpcpb.StoreSafeTSResponse, error)
	/// Get the information about lock waiting from TiKV.
	GetLockWaitInfo(context.Context, *kvrpcpb.GetLockWaitInfoRequest) (*kvrpcpb.GetLockWaitInfoResponse, error)
	/// Compact a specified key range. This request is not restricted to raft leaders and will not be replicated.
	/// It only compacts data on this node.
	/// TODO: Currently this RPC is designed to be only compatible with TiFlash.
	/// Shall be move out in https://github.com/pingcap/kvproto/issues/912
	Compact(context.Context, *kvrpcpb.CompactRequest) (*kvrpcpb.CompactResponse, error)
	/// Get the information about history lock waiting from TiKV.
	GetLockWaitHistory(context.Context, *kvrpcpb.GetLockWaitHistoryRequest) (*kvrpcpb.GetLockWaitHistoryResponse, error)
	/// Get system table from TiFlash
	GetTiFlashSystemTable(context.Context, *kvrpcpb.TiFlashSystemTableRequest) (*kvrpcpb.TiFlashSystemTableResponse, error)
	// These are for TiFlash disaggregated architecture
	/// Try to lock a S3 object, atomically
	TryAddLock(context.Context, *disaggregated.TryAddLockRequest) (*disaggregated.TryAddLockResponse, error)
	/// Try to delete a S3 object, atomically
	TryMarkDelete(context.Context, *disaggregated.TryMarkDeleteRequest) (*disaggregated.TryMarkDeleteResponse, error)
	/// Build the disaggregated task on TiFlash write node
	EstablishDisaggTask(context.Context, *disaggregated.EstablishDisaggTaskRequest) (*disaggregated.EstablishDisaggTaskResponse, error)
	/// Cancel the disaggregated task on TiFlash write node
	CancelDisaggTask(context.Context, *disaggregated.CancelDisaggTaskRequest) (*disaggregated.CancelDisaggTaskResponse, error)
	/// Exchange page data between TiFlash write node and compute node
	FetchDisaggPages(*disaggregated.FetchDisaggPagesRequest, Tikv_FetchDisaggPagesServer) error
	/// Compute node get configuration from Write node
	GetDisaggConfig(context.Context, *disaggregated.GetDisaggConfigRequest) (*disaggregated.GetDisaggConfigResponse, error)
	/// Get health feedback info from the TiKV node.
	GetHealthFeedback(context.Context, *kvrpcpb.GetHealthFeedbackRequest) (*kvrpcpb.GetHealthFeedbackResponse, error)
	/// Broadcast the transaction status to all TiKV nodes
	BroadcastTxnStatus(context.Context, *kvrpcpb.BroadcastTxnStatusRequest) (*kvrpcpb.BroadcastTxnStatusResponse, error)
}

// UnimplementedTikvServer can be embedded to have forward compatible implementations.
type UnimplementedTikvServer struct {
}

func (*UnimplementedTikvServer) KvGet(ctx context.Context, req *kvrpcpb.GetRequest) (*kvrpcpb.GetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvGet not implemented")
}
func (*UnimplementedTikvServer) KvScan(ctx context.Context, req *kvrpcpb.ScanRequest) (*kvrpcpb.ScanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvScan not implemented")
}
func (*UnimplementedTikvServer) KvPrewrite(ctx context.Context, req *kvrpcpb.PrewriteRequest) (*kvrpcpb.PrewriteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvPrewrite not implemented")
}
func (*UnimplementedTikvServer) KvPessimisticLock(ctx context.Context, req *kvrpcpb.PessimisticLockRequest) (*kvrpcpb.PessimisticLockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvPessimisticLock not implemented")
}
func (*UnimplementedTikvServer) KVPessimisticRollback(ctx context.Context, req *kvrpcpb.PessimisticRollbackRequest) (*kvrpcpb.PessimisticRollbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KVPessimisticRollback not implemented")
}
func (*UnimplementedTikvServer) KvTxnHeartBeat(ctx context.Context, req *kvrpcpb.TxnHeartBeatRequest) (*kvrpcpb.TxnHeartBeatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvTxnHeartBeat not implemented")
}
func (*UnimplementedTikvServer) KvCheckTxnStatus(ctx context.Context, req *kvrpcpb.CheckTxnStatusRequest) (*kvrpcpb.CheckTxnStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvCheckTxnStatus not implemented")
}
func (*UnimplementedTikvServer) KvCheckSecondaryLocks(ctx context.Context, req *kvrpcpb.CheckSecondaryLocksRequest) (*kvrpcpb.CheckSecondaryLocksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvCheckSecondaryLocks not implemented")
}
func (*UnimplementedTikvServer) KvCommit(ctx context.Context, req *kvrpcpb.CommitRequest) (*kvrpcpb.CommitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvCommit not implemented")
}
func (*UnimplementedTikvServer) KvImport(ctx context.Context, req *kvrpcpb.ImportRequest) (*kvrpcpb.ImportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvImport not implemented")
}
func (*UnimplementedTikvServer) KvCleanup(ctx context.Context, req *kvrpcpb.CleanupRequest) (*kvrpcpb.CleanupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvCleanup not implemented")
}
func (*UnimplementedTikvServer) KvBatchGet(ctx context.Context, req *kvrpcpb.BatchGetRequest) (*kvrpcpb.BatchGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvBatchGet not implemented")
}
func (*UnimplementedTikvServer) KvBatchRollback(ctx context.Context, req *kvrpcpb.BatchRollbackRequest) (*kvrpcpb.BatchRollbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvBatchRollback not implemented")
}
func (*UnimplementedTikvServer) KvScanLock(ctx context.Context, req *kvrpcpb.ScanLockRequest) (*kvrpcpb.ScanLockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvScanLock not implemented")
}
func (*UnimplementedTikvServer) KvResolveLock(ctx context.Context, req *kvrpcpb.ResolveLockRequest) (*kvrpcpb.ResolveLockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvResolveLock not implemented")
}
func (*UnimplementedTikvServer) KvGC(ctx context.Context, req *kvrpcpb.GCRequest) (*kvrpcpb.GCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvGC not implemented")
}
func (*UnimplementedTikvServer) KvDeleteRange(ctx context.Context, req *kvrpcpb.DeleteRangeRequest) (*kvrpcpb.DeleteRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvDeleteRange not implemented")
}
func (*UnimplementedTikvServer) KvPrepareFlashbackToVersion(ctx context.Context, req *kvrpcpb.PrepareFlashbackToVersionRequest) (*kvrpcpb.PrepareFlashbackToVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvPrepareFlashbackToVersion not implemented")
}
func (*UnimplementedTikvServer) KvFlashbackToVersion(ctx context.Context, req *kvrpcpb.FlashbackToVersionRequest) (*kvrpcpb.FlashbackToVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvFlashbackToVersion not implemented")
}
func (*UnimplementedTikvServer) KvFlush(ctx context.Context, req *kvrpcpb.FlushRequest) (*kvrpcpb.FlushResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvFlush not implemented")
}
func (*UnimplementedTikvServer) KvBufferBatchGet(ctx context.Context, req *kvrpcpb.BufferBatchGetRequest) (*kvrpcpb.BufferBatchGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KvBufferBatchGet not implemented")
}
func (*UnimplementedTikvServer) RawGet(ctx context.Context, req *kvrpcpb.RawGetRequest) (*kvrpcpb.RawGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawGet not implemented")
}
func (*UnimplementedTikvServer) RawBatchGet(ctx context.Context, req *kvrpcpb.RawBatchGetRequest) (*kvrpcpb.RawBatchGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawBatchGet not implemented")
}
func (*UnimplementedTikvServer) RawPut(ctx context.Context, req *kvrpcpb.RawPutRequest) (*kvrpcpb.RawPutResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawPut not implemented")
}
func (*UnimplementedTikvServer) RawBatchPut(ctx context.Context, req *kvrpcpb.RawBatchPutRequest) (*kvrpcpb.RawBatchPutResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawBatchPut not implemented")
}
func (*UnimplementedTikvServer) RawDelete(ctx context.Context, req *kvrpcpb.RawDeleteRequest) (*kvrpcpb.RawDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawDelete not implemented")
}
func (*UnimplementedTikvServer) RawBatchDelete(ctx context.Context, req *kvrpcpb.RawBatchDeleteRequest) (*kvrpcpb.RawBatchDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawBatchDelete not implemented")
}
func (*UnimplementedTikvServer) RawScan(ctx context.Context, req *kvrpcpb.RawScanRequest) (*kvrpcpb.RawScanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawScan not implemented")
}
func (*UnimplementedTikvServer) RawDeleteRange(ctx context.Context, req *kvrpcpb.RawDeleteRangeRequest) (*kvrpcpb.RawDeleteRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawDeleteRange not implemented")
}
func (*UnimplementedTikvServer) RawBatchScan(ctx context.Context, req *kvrpcpb.RawBatchScanRequest) (*kvrpcpb.RawBatchScanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawBatchScan not implemented")
}
func (*UnimplementedTikvServer) RawGetKeyTTL(ctx context.Context, req *kvrpcpb.RawGetKeyTTLRequest) (*kvrpcpb.RawGetKeyTTLResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawGetKeyTTL not implemented")
}
func (*UnimplementedTikvServer) RawCompareAndSwap(ctx context.Context, req *kvrpcpb.RawCASRequest) (*kvrpcpb.RawCASResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawCompareAndSwap not implemented")
}
func (*UnimplementedTikvServer) RawChecksum(ctx context.Context, req *kvrpcpb.RawChecksumRequest) (*kvrpcpb.RawChecksumResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawChecksum not implemented")
}
func (*UnimplementedTikvServer) UnsafeDestroyRange(ctx context.Context, req *kvrpcpb.UnsafeDestroyRangeRequest) (*kvrpcpb.UnsafeDestroyRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnsafeDestroyRange not implemented")
}
func (*UnimplementedTikvServer) RegisterLockObserver(ctx context.Context, req *kvrpcpb.RegisterLockObserverRequest) (*kvrpcpb.RegisterLockObserverResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterLockObserver not implemented")
}
func (*UnimplementedTikvServer) CheckLockObserver(ctx context.Context, req *kvrpcpb.CheckLockObserverRequest) (*kvrpcpb.CheckLockObserverResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLockObserver not implemented")
}
func (*UnimplementedTikvServer) RemoveLockObserver(ctx context.Context, req *kvrpcpb.RemoveLockObserverRequest) (*kvrpcpb.RemoveLockObserverResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveLockObserver not implemented")
}
func (*UnimplementedTikvServer) PhysicalScanLock(ctx context.Context, req *kvrpcpb.PhysicalScanLockRequest) (*kvrpcpb.PhysicalScanLockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PhysicalScanLock not implemented")
}
func (*UnimplementedTikvServer) Coprocessor(ctx context.Context, req *coprocessor.Request) (*coprocessor.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Coprocessor not implemented")
}
func (*UnimplementedTikvServer) CoprocessorStream(req *coprocessor.Request, srv Tikv_CoprocessorStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method CoprocessorStream not implemented")
}
func (*UnimplementedTikvServer) BatchCoprocessor(req *coprocessor.BatchRequest, srv Tikv_BatchCoprocessorServer) error {
	return status.Errorf(codes.Unimplemented, "method BatchCoprocessor not implemented")
}
func (*UnimplementedTikvServer) DelegateCoprocessor(ctx context.Context, req *coprocessor.DelegateRequest) (*coprocessor.DelegateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelegateCoprocessor not implemented")
}
func (*UnimplementedTikvServer) RawCoprocessor(ctx context.Context, req *kvrpcpb.RawCoprocessorRequest) (*kvrpcpb.RawCoprocessorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RawCoprocessor not implemented")
}
func (*UnimplementedTikvServer) Raft(srv Tikv_RaftServer) error {
	return status.Errorf(codes.Unimplemented, "method Raft not implemented")
}
func (*UnimplementedTikvServer) BatchRaft(srv Tikv_BatchRaftServer) error {
	return status.Errorf(codes.Unimplemented, "method BatchRaft not implemented")
}
func (*UnimplementedTikvServer) Snapshot(srv Tikv_SnapshotServer) error {
	return status.Errorf(codes.Unimplemented, "method Snapshot not implemented")
}
func (*UnimplementedTikvServer) TabletSnapshot(srv Tikv_TabletSnapshotServer) error {
	return status.Errorf(codes.Unimplemented, "method TabletSnapshot not implemented")
}
func (*UnimplementedTikvServer) SplitRegion(ctx context.Context, req *kvrpcpb.SplitRegionRequest) (*kvrpcpb.SplitRegionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SplitRegion not implemented")
}
func (*UnimplementedTikvServer) ReadIndex(ctx context.Context, req *kvrpcpb.ReadIndexRequest) (*kvrpcpb.ReadIndexResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReadIndex not implemented")
}
func (*UnimplementedTikvServer) MvccGetByKey(ctx context.Context, req *kvrpcpb.MvccGetByKeyRequest) (*kvrpcpb.MvccGetByKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MvccGetByKey not implemented")
}
func (*UnimplementedTikvServer) MvccGetByStartTs(ctx context.Context, req *kvrpcpb.MvccGetByStartTsRequest) (*kvrpcpb.MvccGetByStartTsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MvccGetByStartTs not implemented")
}
func (*UnimplementedTikvServer) BatchCommands(srv Tikv_BatchCommandsServer) error {
	return status.Errorf(codes.Unimplemented, "method BatchCommands not implemented")
}
func (*UnimplementedTikvServer) DispatchMPPTask(ctx context.Context, req *mpp.DispatchTaskRequest) (*mpp.DispatchTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DispatchMPPTask not implemented")
}
func (*UnimplementedTikvServer) CancelMPPTask(ctx context.Context, req *mpp.CancelTaskRequest) (*mpp.CancelTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelMPPTask not implemented")
}
func (*UnimplementedTikvServer) EstablishMPPConnection(req *mpp.EstablishMPPConnectionRequest, srv Tikv_EstablishMPPConnectionServer) error {
	return status.Errorf(codes.Unimplemented, "method EstablishMPPConnection not implemented")
}
func (*UnimplementedTikvServer) IsAlive(ctx context.Context, req *mpp.IsAliveRequest) (*mpp.IsAliveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsAlive not implemented")
}
func (*UnimplementedTikvServer) ReportMPPTaskStatus(ctx context.Context, req *mpp.ReportTaskStatusRequest) (*mpp.ReportTaskStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportMPPTaskStatus not implemented")
}
func (*UnimplementedTikvServer) CheckLeader(ctx context.Context, req *kvrpcpb.CheckLeaderRequest) (*kvrpcpb.CheckLeaderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckLeader not implemented")
}
func (*UnimplementedTikvServer) GetStoreSafeTS(ctx context.Context, req *kvrpcpb.StoreSafeTSRequest) (*kvrpcpb.StoreSafeTSResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStoreSafeTS not implemented")
}
func (*UnimplementedTikvServer) GetLockWaitInfo(ctx context.Context, req *kvrpcpb.GetLockWaitInfoRequest) (*kvrpcpb.GetLockWaitInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLockWaitInfo not implemented")
}
func (*UnimplementedTikvServer) Compact(ctx context.Context, req *kvrpcpb.CompactRequest) (*kvrpcpb.CompactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Compact not implemented")
}
func (*UnimplementedTikvServer) GetLockWaitHistory(ctx context.Context, req *kvrpcpb.GetLockWaitHistoryRequest) (*kvrpcpb.GetLockWaitHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLockWaitHistory not implemented")
}
func (*UnimplementedTikvServer) GetTiFlashSystemTable(ctx context.Context, req *kvrpcpb.TiFlashSystemTableRequest) (*kvrpcpb.TiFlashSystemTableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTiFlashSystemTable not implemented")
}
func (*UnimplementedTikvServer) TryAddLock(ctx context.Context, req *disaggregated.TryAddLockRequest) (*disaggregated.TryAddLockResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TryAddLock not implemented")
}
func (*UnimplementedTikvServer) TryMarkDelete(ctx context.Context, req *disaggregated.TryMarkDeleteRequest) (*disaggregated.TryMarkDeleteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TryMarkDelete not implemented")
}
func (*UnimplementedTikvServer) EstablishDisaggTask(ctx context.Context, req *disaggregated.EstablishDisaggTaskRequest) (*disaggregated.EstablishDisaggTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EstablishDisaggTask not implemented")
}
func (*UnimplementedTikvServer) CancelDisaggTask(ctx context.Context, req *disaggregated.CancelDisaggTaskRequest) (*disaggregated.CancelDisaggTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelDisaggTask not implemented")
}
func (*UnimplementedTikvServer) FetchDisaggPages(req *disaggregated.FetchDisaggPagesRequest, srv Tikv_FetchDisaggPagesServer) error {
	return status.Errorf(codes.Unimplemented, "method FetchDisaggPages not implemented")
}
func (*UnimplementedTikvServer) GetDisaggConfig(ctx context.Context, req *disaggregated.GetDisaggConfigRequest) (*disaggregated.GetDisaggConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDisaggConfig not implemented")
}
func (*UnimplementedTikvServer) GetHealthFeedback(ctx context.Context, req *kvrpcpb.GetHealthFeedbackRequest) (*kvrpcpb.GetHealthFeedbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHealthFeedback not implemented")
}
func (*UnimplementedTikvServer) BroadcastTxnStatus(ctx context.Context, req *kvrpcpb.BroadcastTxnStatusRequest) (*kvrpcpb.BroadcastTxnStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BroadcastTxnStatus not implemented")
}

func RegisterTikvServer(s *grpc.Server, srv TikvServer) {
	s.RegisterService(&_Tikv_serviceDesc, srv)
}

func _Tikv_KvGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.GetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvGet(ctx, req.(*kvrpcpb.GetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvScan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.ScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvScan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvScan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvScan(ctx, req.(*kvrpcpb.ScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvPrewrite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.PrewriteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvPrewrite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvPrewrite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvPrewrite(ctx, req.(*kvrpcpb.PrewriteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvPessimisticLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.PessimisticLockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvPessimisticLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvPessimisticLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvPessimisticLock(ctx, req.(*kvrpcpb.PessimisticLockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KVPessimisticRollback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.PessimisticRollbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KVPessimisticRollback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KVPessimisticRollback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KVPessimisticRollback(ctx, req.(*kvrpcpb.PessimisticRollbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvTxnHeartBeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.TxnHeartBeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvTxnHeartBeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvTxnHeartBeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvTxnHeartBeat(ctx, req.(*kvrpcpb.TxnHeartBeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvCheckTxnStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.CheckTxnStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvCheckTxnStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvCheckTxnStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvCheckTxnStatus(ctx, req.(*kvrpcpb.CheckTxnStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvCheckSecondaryLocks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.CheckSecondaryLocksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvCheckSecondaryLocks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvCheckSecondaryLocks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvCheckSecondaryLocks(ctx, req.(*kvrpcpb.CheckSecondaryLocksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvCommit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.CommitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvCommit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvCommit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvCommit(ctx, req.(*kvrpcpb.CommitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.ImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvImport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvImport(ctx, req.(*kvrpcpb.ImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvCleanup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.CleanupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvCleanup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvCleanup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvCleanup(ctx, req.(*kvrpcpb.CleanupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvBatchGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.BatchGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvBatchGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvBatchGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvBatchGet(ctx, req.(*kvrpcpb.BatchGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvBatchRollback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.BatchRollbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvBatchRollback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvBatchRollback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvBatchRollback(ctx, req.(*kvrpcpb.BatchRollbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvScanLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.ScanLockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvScanLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvScanLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvScanLock(ctx, req.(*kvrpcpb.ScanLockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvResolveLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.ResolveLockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvResolveLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvResolveLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvResolveLock(ctx, req.(*kvrpcpb.ResolveLockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvGC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.GCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvGC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvGC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvGC(ctx, req.(*kvrpcpb.GCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvDeleteRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.DeleteRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvDeleteRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvDeleteRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvDeleteRange(ctx, req.(*kvrpcpb.DeleteRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvPrepareFlashbackToVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.PrepareFlashbackToVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvPrepareFlashbackToVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvPrepareFlashbackToVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvPrepareFlashbackToVersion(ctx, req.(*kvrpcpb.PrepareFlashbackToVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvFlashbackToVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.FlashbackToVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvFlashbackToVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvFlashbackToVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvFlashbackToVersion(ctx, req.(*kvrpcpb.FlashbackToVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvFlush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.FlushRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvFlush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvFlush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvFlush(ctx, req.(*kvrpcpb.FlushRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_KvBufferBatchGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.BufferBatchGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).KvBufferBatchGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/KvBufferBatchGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).KvBufferBatchGet(ctx, req.(*kvrpcpb.BufferBatchGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawGet(ctx, req.(*kvrpcpb.RawGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawBatchGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawBatchGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawBatchGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawBatchGet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawBatchGet(ctx, req.(*kvrpcpb.RawBatchGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawPut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawPutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawPut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawPut",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawPut(ctx, req.(*kvrpcpb.RawPutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawBatchPut_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawBatchPutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawBatchPut(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawBatchPut",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawBatchPut(ctx, req.(*kvrpcpb.RawBatchPutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawDelete(ctx, req.(*kvrpcpb.RawDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawBatchDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawBatchDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawBatchDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawBatchDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawBatchDelete(ctx, req.(*kvrpcpb.RawBatchDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawScan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawScan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawScan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawScan(ctx, req.(*kvrpcpb.RawScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawDeleteRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawDeleteRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawDeleteRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawDeleteRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawDeleteRange(ctx, req.(*kvrpcpb.RawDeleteRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawBatchScan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawBatchScanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawBatchScan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawBatchScan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawBatchScan(ctx, req.(*kvrpcpb.RawBatchScanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawGetKeyTTL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawGetKeyTTLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawGetKeyTTL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawGetKeyTTL",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawGetKeyTTL(ctx, req.(*kvrpcpb.RawGetKeyTTLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawCompareAndSwap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawCASRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawCompareAndSwap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawCompareAndSwap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawCompareAndSwap(ctx, req.(*kvrpcpb.RawCASRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawChecksum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawChecksumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawChecksum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawChecksum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawChecksum(ctx, req.(*kvrpcpb.RawChecksumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_UnsafeDestroyRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.UnsafeDestroyRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).UnsafeDestroyRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/UnsafeDestroyRange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).UnsafeDestroyRange(ctx, req.(*kvrpcpb.UnsafeDestroyRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RegisterLockObserver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RegisterLockObserverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RegisterLockObserver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RegisterLockObserver",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RegisterLockObserver(ctx, req.(*kvrpcpb.RegisterLockObserverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_CheckLockObserver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.CheckLockObserverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).CheckLockObserver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/CheckLockObserver",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).CheckLockObserver(ctx, req.(*kvrpcpb.CheckLockObserverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RemoveLockObserver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RemoveLockObserverRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RemoveLockObserver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RemoveLockObserver",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RemoveLockObserver(ctx, req.(*kvrpcpb.RemoveLockObserverRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_PhysicalScanLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.PhysicalScanLockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).PhysicalScanLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/PhysicalScanLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).PhysicalScanLock(ctx, req.(*kvrpcpb.PhysicalScanLockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_Coprocessor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(coprocessor.Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).Coprocessor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/Coprocessor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).Coprocessor(ctx, req.(*coprocessor.Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_CoprocessorStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(coprocessor.Request)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TikvServer).CoprocessorStream(m, &tikvCoprocessorStreamServer{stream})
}

type Tikv_CoprocessorStreamServer interface {
	Send(*coprocessor.Response) error
	grpc.ServerStream
}

type tikvCoprocessorStreamServer struct {
	grpc.ServerStream
}

func (x *tikvCoprocessorStreamServer) Send(m *coprocessor.Response) error {
	return x.ServerStream.SendMsg(m)
}

func _Tikv_BatchCoprocessor_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(coprocessor.BatchRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TikvServer).BatchCoprocessor(m, &tikvBatchCoprocessorServer{stream})
}

type Tikv_BatchCoprocessorServer interface {
	Send(*coprocessor.BatchResponse) error
	grpc.ServerStream
}

type tikvBatchCoprocessorServer struct {
	grpc.ServerStream
}

func (x *tikvBatchCoprocessorServer) Send(m *coprocessor.BatchResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Tikv_DelegateCoprocessor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(coprocessor.DelegateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).DelegateCoprocessor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/DelegateCoprocessor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).DelegateCoprocessor(ctx, req.(*coprocessor.DelegateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_RawCoprocessor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.RawCoprocessorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).RawCoprocessor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/RawCoprocessor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).RawCoprocessor(ctx, req.(*kvrpcpb.RawCoprocessorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_Raft_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(TikvServer).Raft(&tikvRaftServer{stream})
}

type Tikv_RaftServer interface {
	SendAndClose(*raft_serverpb.Done) error
	Recv() (*raft_serverpb.RaftMessage, error)
	grpc.ServerStream
}

type tikvRaftServer struct {
	grpc.ServerStream
}

func (x *tikvRaftServer) SendAndClose(m *raft_serverpb.Done) error {
	return x.ServerStream.SendMsg(m)
}

func (x *tikvRaftServer) Recv() (*raft_serverpb.RaftMessage, error) {
	m := new(raft_serverpb.RaftMessage)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Tikv_BatchRaft_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(TikvServer).BatchRaft(&tikvBatchRaftServer{stream})
}

type Tikv_BatchRaftServer interface {
	SendAndClose(*raft_serverpb.Done) error
	Recv() (*BatchRaftMessage, error)
	grpc.ServerStream
}

type tikvBatchRaftServer struct {
	grpc.ServerStream
}

func (x *tikvBatchRaftServer) SendAndClose(m *raft_serverpb.Done) error {
	return x.ServerStream.SendMsg(m)
}

func (x *tikvBatchRaftServer) Recv() (*BatchRaftMessage, error) {
	m := new(BatchRaftMessage)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Tikv_Snapshot_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(TikvServer).Snapshot(&tikvSnapshotServer{stream})
}

type Tikv_SnapshotServer interface {
	SendAndClose(*raft_serverpb.Done) error
	Recv() (*raft_serverpb.SnapshotChunk, error)
	grpc.ServerStream
}

type tikvSnapshotServer struct {
	grpc.ServerStream
}

func (x *tikvSnapshotServer) SendAndClose(m *raft_serverpb.Done) error {
	return x.ServerStream.SendMsg(m)
}

func (x *tikvSnapshotServer) Recv() (*raft_serverpb.SnapshotChunk, error) {
	m := new(raft_serverpb.SnapshotChunk)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Tikv_TabletSnapshot_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(TikvServer).TabletSnapshot(&tikvTabletSnapshotServer{stream})
}

type Tikv_TabletSnapshotServer interface {
	Send(*raft_serverpb.TabletSnapshotResponse) error
	Recv() (*raft_serverpb.TabletSnapshotRequest, error)
	grpc.ServerStream
}

type tikvTabletSnapshotServer struct {
	grpc.ServerStream
}

func (x *tikvTabletSnapshotServer) Send(m *raft_serverpb.TabletSnapshotResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *tikvTabletSnapshotServer) Recv() (*raft_serverpb.TabletSnapshotRequest, error) {
	m := new(raft_serverpb.TabletSnapshotRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Tikv_SplitRegion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.SplitRegionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).SplitRegion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/SplitRegion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).SplitRegion(ctx, req.(*kvrpcpb.SplitRegionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_ReadIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.ReadIndexRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).ReadIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/ReadIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).ReadIndex(ctx, req.(*kvrpcpb.ReadIndexRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_MvccGetByKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.MvccGetByKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).MvccGetByKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/MvccGetByKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).MvccGetByKey(ctx, req.(*kvrpcpb.MvccGetByKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_MvccGetByStartTs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.MvccGetByStartTsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).MvccGetByStartTs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/MvccGetByStartTs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).MvccGetByStartTs(ctx, req.(*kvrpcpb.MvccGetByStartTsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_BatchCommands_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(TikvServer).BatchCommands(&tikvBatchCommandsServer{stream})
}

type Tikv_BatchCommandsServer interface {
	Send(*BatchCommandsResponse) error
	Recv() (*BatchCommandsRequest, error)
	grpc.ServerStream
}

type tikvBatchCommandsServer struct {
	grpc.ServerStream
}

func (x *tikvBatchCommandsServer) Send(m *BatchCommandsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *tikvBatchCommandsServer) Recv() (*BatchCommandsRequest, error) {
	m := new(BatchCommandsRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _Tikv_DispatchMPPTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mpp.DispatchTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).DispatchMPPTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/DispatchMPPTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).DispatchMPPTask(ctx, req.(*mpp.DispatchTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_CancelMPPTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mpp.CancelTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).CancelMPPTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/CancelMPPTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).CancelMPPTask(ctx, req.(*mpp.CancelTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_EstablishMPPConnection_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(mpp.EstablishMPPConnectionRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TikvServer).EstablishMPPConnection(m, &tikvEstablishMPPConnectionServer{stream})
}

type Tikv_EstablishMPPConnectionServer interface {
	Send(*mpp.MPPDataPacket) error
	grpc.ServerStream
}

type tikvEstablishMPPConnectionServer struct {
	grpc.ServerStream
}

func (x *tikvEstablishMPPConnectionServer) Send(m *mpp.MPPDataPacket) error {
	return x.ServerStream.SendMsg(m)
}

func _Tikv_IsAlive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mpp.IsAliveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).IsAlive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/IsAlive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).IsAlive(ctx, req.(*mpp.IsAliveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_ReportMPPTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mpp.ReportTaskStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).ReportMPPTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/ReportMPPTaskStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).ReportMPPTaskStatus(ctx, req.(*mpp.ReportTaskStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_CheckLeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.CheckLeaderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).CheckLeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/CheckLeader",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).CheckLeader(ctx, req.(*kvrpcpb.CheckLeaderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_GetStoreSafeTS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.StoreSafeTSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).GetStoreSafeTS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/GetStoreSafeTS",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).GetStoreSafeTS(ctx, req.(*kvrpcpb.StoreSafeTSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_GetLockWaitInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.GetLockWaitInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).GetLockWaitInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/GetLockWaitInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).GetLockWaitInfo(ctx, req.(*kvrpcpb.GetLockWaitInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_Compact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.CompactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).Compact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/Compact",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).Compact(ctx, req.(*kvrpcpb.CompactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_GetLockWaitHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.GetLockWaitHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).GetLockWaitHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/GetLockWaitHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).GetLockWaitHistory(ctx, req.(*kvrpcpb.GetLockWaitHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_GetTiFlashSystemTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.TiFlashSystemTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).GetTiFlashSystemTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/GetTiFlashSystemTable",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).GetTiFlashSystemTable(ctx, req.(*kvrpcpb.TiFlashSystemTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_TryAddLock_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(disaggregated.TryAddLockRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).TryAddLock(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/TryAddLock",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).TryAddLock(ctx, req.(*disaggregated.TryAddLockRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_TryMarkDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(disaggregated.TryMarkDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).TryMarkDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/TryMarkDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).TryMarkDelete(ctx, req.(*disaggregated.TryMarkDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_EstablishDisaggTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(disaggregated.EstablishDisaggTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).EstablishDisaggTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/EstablishDisaggTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).EstablishDisaggTask(ctx, req.(*disaggregated.EstablishDisaggTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_CancelDisaggTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(disaggregated.CancelDisaggTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).CancelDisaggTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/CancelDisaggTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).CancelDisaggTask(ctx, req.(*disaggregated.CancelDisaggTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_FetchDisaggPages_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(disaggregated.FetchDisaggPagesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(TikvServer).FetchDisaggPages(m, &tikvFetchDisaggPagesServer{stream})
}

type Tikv_FetchDisaggPagesServer interface {
	Send(*disaggregated.PagesPacket) error
	grpc.ServerStream
}

type tikvFetchDisaggPagesServer struct {
	grpc.ServerStream
}

func (x *tikvFetchDisaggPagesServer) Send(m *disaggregated.PagesPacket) error {
	return x.ServerStream.SendMsg(m)
}

func _Tikv_GetDisaggConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(disaggregated.GetDisaggConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).GetDisaggConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/GetDisaggConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).GetDisaggConfig(ctx, req.(*disaggregated.GetDisaggConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_GetHealthFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.GetHealthFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).GetHealthFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/GetHealthFeedback",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).GetHealthFeedback(ctx, req.(*kvrpcpb.GetHealthFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tikv_BroadcastTxnStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kvrpcpb.BroadcastTxnStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TikvServer).BroadcastTxnStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tikvpb.Tikv/BroadcastTxnStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TikvServer).BroadcastTxnStatus(ctx, req.(*kvrpcpb.BroadcastTxnStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Tikv_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tikvpb.Tikv",
	HandlerType: (*TikvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "KvGet",
			Handler:    _Tikv_KvGet_Handler,
		},
		{
			MethodName: "KvScan",
			Handler:    _Tikv_KvScan_Handler,
		},
		{
			MethodName: "KvPrewrite",
			Handler:    _Tikv_KvPrewrite_Handler,
		},
		{
			MethodName: "KvPessimisticLock",
			Handler:    _Tikv_KvPessimisticLock_Handler,
		},
		{
			MethodName: "KVPessimisticRollback",
			Handler:    _Tikv_KVPessimisticRollback_Handler,
		},
		{
			MethodName: "KvTxnHeartBeat",
			Handler:    _Tikv_KvTxnHeartBeat_Handler,
		},
		{
			MethodName: "KvCheckTxnStatus",
			Handler:    _Tikv_KvCheckTxnStatus_Handler,
		},
		{
			MethodName: "KvCheckSecondaryLocks",
			Handler:    _Tikv_KvCheckSecondaryLocks_Handler,
		},
		{
			MethodName: "KvCommit",
			Handler:    _Tikv_KvCommit_Handler,
		},
		{
			MethodName: "KvImport",
			Handler:    _Tikv_KvImport_Handler,
		},
		{
			MethodName: "KvCleanup",
			Handler:    _Tikv_KvCleanup_Handler,
		},
		{
			MethodName: "KvBatchGet",
			Handler:    _Tikv_KvBatchGet_Handler,
		},
		{
			MethodName: "KvBatchRollback",
			Handler:    _Tikv_KvBatchRollback_Handler,
		},
		{
			MethodName: "KvScanLock",
			Handler:    _Tikv_KvScanLock_Handler,
		},
		{
			MethodName: "KvResolveLock",
			Handler:    _Tikv_KvResolveLock_Handler,
		},
		{
			MethodName: "KvGC",
			Handler:    _Tikv_KvGC_Handler,
		},
		{
			MethodName: "KvDeleteRange",
			Handler:    _Tikv_KvDeleteRange_Handler,
		},
		{
			MethodName: "KvPrepareFlashbackToVersion",
			Handler:    _Tikv_KvPrepareFlashbackToVersion_Handler,
		},
		{
			MethodName: "KvFlashbackToVersion",
			Handler:    _Tikv_KvFlashbackToVersion_Handler,
		},
		{
			MethodName: "KvFlush",
			Handler:    _Tikv_KvFlush_Handler,
		},
		{
			MethodName: "KvBufferBatchGet",
			Handler:    _Tikv_KvBufferBatchGet_Handler,
		},
		{
			MethodName: "RawGet",
			Handler:    _Tikv_RawGet_Handler,
		},
		{
			MethodName: "RawBatchGet",
			Handler:    _Tikv_RawBatchGet_Handler,
		},
		{
			MethodName: "RawPut",
			Handler:    _Tikv_RawPut_Handler,
		},
		{
			MethodName: "RawBatchPut",
			Handler:    _Tikv_RawBatchPut_Handler,
		},
		{
			MethodName: "RawDelete",
			Handler:    _Tikv_RawDelete_Handler,
		},
		{
			MethodName: "RawBatchDelete",
			Handler:    _Tikv_RawBatchDelete_Handler,
		},
		{
			MethodName: "RawScan",
			Handler:    _Tikv_RawScan_Handler,
		},
		{
			MethodName: "RawDeleteRange",
			Handler:    _Tikv_RawDeleteRange_Handler,
		},
		{
			MethodName: "RawBatchScan",
			Handler:    _Tikv_RawBatchScan_Handler,
		},
		{
			MethodName: "RawGetKeyTTL",
			Handler:    _Tikv_RawGetKeyTTL_Handler,
		},
		{
			MethodName: "RawCompareAndSwap",
			Handler:    _Tikv_RawCompareAndSwap_Handler,
		},
		{
			MethodName: "RawChecksum",
			Handler:    _Tikv_RawChecksum_Handler,
		},
		{
			MethodName: "UnsafeDestroyRange",
			Handler:    _Tikv_UnsafeDestroyRange_Handler,
		},
		{
			MethodName: "RegisterLockObserver",
			Handler:    _Tikv_RegisterLockObserver_Handler,
		},
		{
			MethodName: "CheckLockObserver",
			Handler:    _Tikv_CheckLockObserver_Handler,
		},
		{
			MethodName: "RemoveLockObserver",
			Handler:    _Tikv_RemoveLockObserver_Handler,
		},
		{
			MethodName: "PhysicalScanLock",
			Handler:    _Tikv_PhysicalScanLock_Handler,
		},
		{
			MethodName: "Coprocessor",
			Handler:    _Tikv_Coprocessor_Handler,
		},
		{
			MethodName: "DelegateCoprocessor",
			Handler:    _Tikv_DelegateCoprocessor_Handler,
		},
		{
			MethodName: "RawCoprocessor",
			Handler:    _Tikv_RawCoprocessor_Handler,
		},
		{
			MethodName: "SplitRegion",
			Handler:    _Tikv_SplitRegion_Handler,
		},
		{
			MethodName: "ReadIndex",
			Handler:    _Tikv_ReadIndex_Handler,
		},
		{
			MethodName: "MvccGetByKey",
			Handler:    _Tikv_MvccGetByKey_Handler,
		},
		{
			MethodName: "MvccGetByStartTs",
			Handler:    _Tikv_MvccGetByStartTs_Handler,
		},
		{
			MethodName: "DispatchMPPTask",
			Handler:    _Tikv_DispatchMPPTask_Handler,
		},
		{
			MethodName: "CancelMPPTask",
			Handler:    _Tikv_CancelMPPTask_Handler,
		},
		{
			MethodName: "IsAlive",
			Handler:    _Tikv_IsAlive_Handler,
		},
		{
			MethodName: "ReportMPPTaskStatus",
			Handler:    _Tikv_ReportMPPTaskStatus_Handler,
		},
		{
			MethodName: "CheckLeader",
			Handler:    _Tikv_CheckLeader_Handler,
		},
		{
			MethodName: "GetStoreSafeTS",
			Handler:    _Tikv_GetStoreSafeTS_Handler,
		},
		{
			MethodName: "GetLockWaitInfo",
			Handler:    _Tikv_GetLockWaitInfo_Handler,
		},
		{
			MethodName: "Compact",
			Handler:    _Tikv_Compact_Handler,
		},
		{
			MethodName: "GetLockWaitHistory",
			Handler:    _Tikv_GetLockWaitHistory_Handler,
		},
		{
			MethodName: "GetTiFlashSystemTable",
			Handler:    _Tikv_GetTiFlashSystemTable_Handler,
		},
		{
			MethodName: "tryAddLock",
			Handler:    _Tikv_TryAddLock_Handler,
		},
		{
			MethodName: "tryMarkDelete",
			Handler:    _Tikv_TryMarkDelete_Handler,
		},
		{
			MethodName: "EstablishDisaggTask",
			Handler:    _Tikv_EstablishDisaggTask_Handler,
		},
		{
			MethodName: "CancelDisaggTask",
			Handler:    _Tikv_CancelDisaggTask_Handler,
		},
		{
			MethodName: "GetDisaggConfig",
			Handler:    _Tikv_GetDisaggConfig_Handler,
		},
		{
			MethodName: "GetHealthFeedback",
			Handler:    _Tikv_GetHealthFeedback_Handler,
		},
		{
			MethodName: "BroadcastTxnStatus",
			Handler:    _Tikv_BroadcastTxnStatus_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "CoprocessorStream",
			Handler:       _Tikv_CoprocessorStream_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "BatchCoprocessor",
			Handler:       _Tikv_BatchCoprocessor_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Raft",
			Handler:       _Tikv_Raft_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "BatchRaft",
			Handler:       _Tikv_BatchRaft_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "Snapshot",
			Handler:       _Tikv_Snapshot_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "TabletSnapshot",
			Handler:       _Tikv_TabletSnapshot_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "BatchCommands",
			Handler:       _Tikv_BatchCommands_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "EstablishMPPConnection",
			Handler:       _Tikv_EstablishMPPConnection_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "FetchDisaggPages",
			Handler:       _Tikv_FetchDisaggPages_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "tikvpb.proto",
}

func (m *BatchCommandsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchCommandsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RequestIds) > 0 {
		dAtA2 := make([]byte, len(m.RequestIds)*10)
		var j1 int
		for _, num := range m.RequestIds {
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		i -= j1
		copy(dAtA[i:], dAtA2[:j1])
		i = encodeVarintTikvpb(dAtA, i, uint64(j1))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Requests) > 0 {
		for iNdEx := len(m.Requests) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Requests[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTikvpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BatchCommandsRequest_Request) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchCommandsRequest_Request) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Cmd != nil {
		{
			size := m.Cmd.Size()
			i -= size
			if _, err := m.Cmd.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *BatchCommandsRequest_Request_Get) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Get) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Get != nil {
		{
			size, err := m.Get.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_Scan) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Scan) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Scan != nil {
		{
			size, err := m.Scan.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_Prewrite) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Prewrite) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Prewrite != nil {
		{
			size, err := m.Prewrite.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_Commit) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Commit) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Commit != nil {
		{
			size, err := m.Commit.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_Import) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Import) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Import != nil {
		{
			size, err := m.Import.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_Cleanup) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Cleanup) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Cleanup != nil {
		{
			size, err := m.Cleanup.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_BatchGet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_BatchGet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BatchGet != nil {
		{
			size, err := m.BatchGet.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_BatchRollback) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_BatchRollback) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BatchRollback != nil {
		{
			size, err := m.BatchRollback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_ScanLock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_ScanLock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ScanLock != nil {
		{
			size, err := m.ScanLock.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_ResolveLock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_ResolveLock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ResolveLock != nil {
		{
			size, err := m.ResolveLock.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_GC) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_GC) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.GC != nil {
		{
			size, err := m.GC.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_DeleteRange) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_DeleteRange) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.DeleteRange != nil {
		{
			size, err := m.DeleteRange.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawGet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawGet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawGet != nil {
		{
			size, err := m.RawGet.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawBatchGet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawBatchGet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawBatchGet != nil {
		{
			size, err := m.RawBatchGet.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawPut) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawPut) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawPut != nil {
		{
			size, err := m.RawPut.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x7a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawBatchPut) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawBatchPut) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawBatchPut != nil {
		{
			size, err := m.RawBatchPut.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawDelete) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawDelete) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawDelete != nil {
		{
			size, err := m.RawDelete.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawBatchDelete) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawBatchDelete) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawBatchDelete != nil {
		{
			size, err := m.RawBatchDelete.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawScan) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawScan) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawScan != nil {
		{
			size, err := m.RawScan.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawDeleteRange) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawDeleteRange) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawDeleteRange != nil {
		{
			size, err := m.RawDeleteRange.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawBatchScan) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawBatchScan) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawBatchScan != nil {
		{
			size, err := m.RawBatchScan.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_Coprocessor) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Coprocessor) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Coprocessor != nil {
		{
			size, err := m.Coprocessor.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_PessimisticLock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_PessimisticLock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.PessimisticLock != nil {
		{
			size, err := m.PessimisticLock.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xba
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_PessimisticRollback) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_PessimisticRollback) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.PessimisticRollback != nil {
		{
			size, err := m.PessimisticRollback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_CheckTxnStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_CheckTxnStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.CheckTxnStatus != nil {
		{
			size, err := m.CheckTxnStatus.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xca
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_TxnHeartBeat) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_TxnHeartBeat) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.TxnHeartBeat != nil {
		{
			size, err := m.TxnHeartBeat.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_CheckSecondaryLocks) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_CheckSecondaryLocks) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.CheckSecondaryLocks != nil {
		{
			size, err := m.CheckSecondaryLocks.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x8a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_RawCoprocessor) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_RawCoprocessor) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawCoprocessor != nil {
		{
			size, err := m.RawCoprocessor.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x92
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_FlashbackToVersion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_FlashbackToVersion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.FlashbackToVersion != nil {
		{
			size, err := m.FlashbackToVersion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x9a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_PrepareFlashbackToVersion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_PrepareFlashbackToVersion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.PrepareFlashbackToVersion != nil {
		{
			size, err := m.PrepareFlashbackToVersion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xa2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_Flush) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Flush) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Flush != nil {
		{
			size, err := m.Flush.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xaa
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_BufferBatchGet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_BufferBatchGet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BufferBatchGet != nil {
		{
			size, err := m.BufferBatchGet.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xb2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_GetHealthFeedback) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_GetHealthFeedback) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.GetHealthFeedback != nil {
		{
			size, err := m.GetHealthFeedback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xba
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_BroadcastTxnStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_BroadcastTxnStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BroadcastTxnStatus != nil {
		{
			size, err := m.BroadcastTxnStatus.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xc2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsRequest_Request_Empty) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsRequest_Request_Empty) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Empty != nil {
		{
			size, err := m.Empty.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xf
		i--
		dAtA[i] = 0xfa
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchCommandsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.HealthFeedback != nil {
		{
			size, err := m.HealthFeedback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.TransportLayerLoad != 0 {
		i = encodeVarintTikvpb(dAtA, i, uint64(m.TransportLayerLoad))
		i--
		dAtA[i] = 0x18
	}
	if len(m.RequestIds) > 0 {
		dAtA40 := make([]byte, len(m.RequestIds)*10)
		var j39 int
		for _, num := range m.RequestIds {
			for num >= 1<<7 {
				dAtA40[j39] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j39++
			}
			dAtA40[j39] = uint8(num)
			j39++
		}
		i -= j39
		copy(dAtA[i:], dAtA40[:j39])
		i = encodeVarintTikvpb(dAtA, i, uint64(j39))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Responses) > 0 {
		for iNdEx := len(m.Responses) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Responses[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTikvpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BatchCommandsResponse_Response) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchCommandsResponse_Response) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Cmd != nil {
		{
			size := m.Cmd.Size()
			i -= size
			if _, err := m.Cmd.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *BatchCommandsResponse_Response_Get) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Get) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Get != nil {
		{
			size, err := m.Get.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_Scan) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Scan) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Scan != nil {
		{
			size, err := m.Scan.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_Prewrite) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Prewrite) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Prewrite != nil {
		{
			size, err := m.Prewrite.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_Commit) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Commit) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Commit != nil {
		{
			size, err := m.Commit.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_Import) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Import) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Import != nil {
		{
			size, err := m.Import.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_Cleanup) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Cleanup) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Cleanup != nil {
		{
			size, err := m.Cleanup.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_BatchGet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_BatchGet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BatchGet != nil {
		{
			size, err := m.BatchGet.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_BatchRollback) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_BatchRollback) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BatchRollback != nil {
		{
			size, err := m.BatchRollback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_ScanLock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_ScanLock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ScanLock != nil {
		{
			size, err := m.ScanLock.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_ResolveLock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_ResolveLock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ResolveLock != nil {
		{
			size, err := m.ResolveLock.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_GC) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_GC) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.GC != nil {
		{
			size, err := m.GC.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_DeleteRange) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_DeleteRange) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.DeleteRange != nil {
		{
			size, err := m.DeleteRange.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawGet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawGet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawGet != nil {
		{
			size, err := m.RawGet.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawBatchGet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawBatchGet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawBatchGet != nil {
		{
			size, err := m.RawBatchGet.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawPut) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawPut) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawPut != nil {
		{
			size, err := m.RawPut.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x7a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawBatchPut) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawBatchPut) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawBatchPut != nil {
		{
			size, err := m.RawBatchPut.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawDelete) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawDelete) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawDelete != nil {
		{
			size, err := m.RawDelete.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawBatchDelete) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawBatchDelete) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawBatchDelete != nil {
		{
			size, err := m.RawBatchDelete.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawScan) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawScan) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawScan != nil {
		{
			size, err := m.RawScan.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawDeleteRange) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawDeleteRange) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawDeleteRange != nil {
		{
			size, err := m.RawDeleteRange.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawBatchScan) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawBatchScan) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawBatchScan != nil {
		{
			size, err := m.RawBatchScan.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_Coprocessor) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Coprocessor) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Coprocessor != nil {
		{
			size, err := m.Coprocessor.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_PessimisticLock) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_PessimisticLock) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.PessimisticLock != nil {
		{
			size, err := m.PessimisticLock.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xba
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_PessimisticRollback) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_PessimisticRollback) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.PessimisticRollback != nil {
		{
			size, err := m.PessimisticRollback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_CheckTxnStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_CheckTxnStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.CheckTxnStatus != nil {
		{
			size, err := m.CheckTxnStatus.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xca
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_TxnHeartBeat) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_TxnHeartBeat) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.TxnHeartBeat != nil {
		{
			size, err := m.TxnHeartBeat.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_CheckSecondaryLocks) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_CheckSecondaryLocks) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.CheckSecondaryLocks != nil {
		{
			size, err := m.CheckSecondaryLocks.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x8a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_RawCoprocessor) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_RawCoprocessor) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawCoprocessor != nil {
		{
			size, err := m.RawCoprocessor.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x92
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_FlashbackToVersion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_FlashbackToVersion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.FlashbackToVersion != nil {
		{
			size, err := m.FlashbackToVersion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x9a
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_PrepareFlashbackToVersion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_PrepareFlashbackToVersion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.PrepareFlashbackToVersion != nil {
		{
			size, err := m.PrepareFlashbackToVersion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xa2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_Flush) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Flush) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Flush != nil {
		{
			size, err := m.Flush.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xaa
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_BufferBatchGet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_BufferBatchGet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BufferBatchGet != nil {
		{
			size, err := m.BufferBatchGet.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xb2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_GetHealthFeedback) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_GetHealthFeedback) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.GetHealthFeedback != nil {
		{
			size, err := m.GetHealthFeedback.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xba
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_BroadcastTxnStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_BroadcastTxnStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BroadcastTxnStatus != nil {
		{
			size, err := m.BroadcastTxnStatus.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0xc2
	}
	return len(dAtA) - i, nil
}
func (m *BatchCommandsResponse_Response_Empty) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsResponse_Response_Empty) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Empty != nil {
		{
			size, err := m.Empty.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintTikvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xf
		i--
		dAtA[i] = 0xfa
	}
	return len(dAtA) - i, nil
}
func (m *BatchRaftMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchRaftMessage) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchRaftMessage) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LastObservedTime != 0 {
		i = encodeVarintTikvpb(dAtA, i, uint64(m.LastObservedTime))
		i--
		dAtA[i] = 0x68
	}
	if len(m.Msgs) > 0 {
		for iNdEx := len(m.Msgs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Msgs[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintTikvpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *BatchCommandsEmptyRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchCommandsEmptyRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsEmptyRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DelayTime != 0 {
		i = encodeVarintTikvpb(dAtA, i, uint64(m.DelayTime))
		i--
		dAtA[i] = 0x10
	}
	if m.TestId != 0 {
		i = encodeVarintTikvpb(dAtA, i, uint64(m.TestId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BatchCommandsEmptyResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchCommandsEmptyResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchCommandsEmptyResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TestId != 0 {
		i = encodeVarintTikvpb(dAtA, i, uint64(m.TestId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintTikvpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovTikvpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *BatchCommandsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Requests) > 0 {
		for _, e := range m.Requests {
			l = e.Size()
			n += 1 + l + sovTikvpb(uint64(l))
		}
	}
	if len(m.RequestIds) > 0 {
		l = 0
		for _, e := range m.RequestIds {
			l += sovTikvpb(uint64(e))
		}
		n += 1 + sovTikvpb(uint64(l)) + l
	}
	return n
}

func (m *BatchCommandsRequest_Request) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Cmd != nil {
		n += m.Cmd.Size()
	}
	return n
}

func (m *BatchCommandsRequest_Request_Get) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Get != nil {
		l = m.Get.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_Scan) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Scan != nil {
		l = m.Scan.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_Prewrite) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Prewrite != nil {
		l = m.Prewrite.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_Commit) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Commit != nil {
		l = m.Commit.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_Import) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Import != nil {
		l = m.Import.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_Cleanup) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Cleanup != nil {
		l = m.Cleanup.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_BatchGet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchGet != nil {
		l = m.BatchGet.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_BatchRollback) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchRollback != nil {
		l = m.BatchRollback.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_ScanLock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ScanLock != nil {
		l = m.ScanLock.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_ResolveLock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ResolveLock != nil {
		l = m.ResolveLock.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_GC) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GC != nil {
		l = m.GC.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_DeleteRange) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeleteRange != nil {
		l = m.DeleteRange.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawGet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawGet != nil {
		l = m.RawGet.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawBatchGet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawBatchGet != nil {
		l = m.RawBatchGet.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawPut) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawPut != nil {
		l = m.RawPut.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawBatchPut) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawBatchPut != nil {
		l = m.RawBatchPut.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawDelete) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawDelete != nil {
		l = m.RawDelete.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawBatchDelete) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawBatchDelete != nil {
		l = m.RawBatchDelete.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawScan) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawScan != nil {
		l = m.RawScan.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawDeleteRange) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawDeleteRange != nil {
		l = m.RawDeleteRange.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawBatchScan) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawBatchScan != nil {
		l = m.RawBatchScan.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_Coprocessor) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Coprocessor != nil {
		l = m.Coprocessor.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_PessimisticLock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PessimisticLock != nil {
		l = m.PessimisticLock.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_PessimisticRollback) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PessimisticRollback != nil {
		l = m.PessimisticRollback.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_CheckTxnStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CheckTxnStatus != nil {
		l = m.CheckTxnStatus.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_TxnHeartBeat) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TxnHeartBeat != nil {
		l = m.TxnHeartBeat.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_CheckSecondaryLocks) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CheckSecondaryLocks != nil {
		l = m.CheckSecondaryLocks.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_RawCoprocessor) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawCoprocessor != nil {
		l = m.RawCoprocessor.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_FlashbackToVersion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FlashbackToVersion != nil {
		l = m.FlashbackToVersion.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_PrepareFlashbackToVersion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PrepareFlashbackToVersion != nil {
		l = m.PrepareFlashbackToVersion.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_Flush) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Flush != nil {
		l = m.Flush.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_BufferBatchGet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BufferBatchGet != nil {
		l = m.BufferBatchGet.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_GetHealthFeedback) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GetHealthFeedback != nil {
		l = m.GetHealthFeedback.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_BroadcastTxnStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BroadcastTxnStatus != nil {
		l = m.BroadcastTxnStatus.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsRequest_Request_Empty) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Empty != nil {
		l = m.Empty.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Responses) > 0 {
		for _, e := range m.Responses {
			l = e.Size()
			n += 1 + l + sovTikvpb(uint64(l))
		}
	}
	if len(m.RequestIds) > 0 {
		l = 0
		for _, e := range m.RequestIds {
			l += sovTikvpb(uint64(e))
		}
		n += 1 + sovTikvpb(uint64(l)) + l
	}
	if m.TransportLayerLoad != 0 {
		n += 1 + sovTikvpb(uint64(m.TransportLayerLoad))
	}
	if m.HealthFeedback != nil {
		l = m.HealthFeedback.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}

func (m *BatchCommandsResponse_Response) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Cmd != nil {
		n += m.Cmd.Size()
	}
	return n
}

func (m *BatchCommandsResponse_Response_Get) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Get != nil {
		l = m.Get.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_Scan) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Scan != nil {
		l = m.Scan.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_Prewrite) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Prewrite != nil {
		l = m.Prewrite.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_Commit) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Commit != nil {
		l = m.Commit.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_Import) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Import != nil {
		l = m.Import.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_Cleanup) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Cleanup != nil {
		l = m.Cleanup.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_BatchGet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchGet != nil {
		l = m.BatchGet.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_BatchRollback) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchRollback != nil {
		l = m.BatchRollback.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_ScanLock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ScanLock != nil {
		l = m.ScanLock.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_ResolveLock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ResolveLock != nil {
		l = m.ResolveLock.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_GC) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GC != nil {
		l = m.GC.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_DeleteRange) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeleteRange != nil {
		l = m.DeleteRange.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawGet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawGet != nil {
		l = m.RawGet.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawBatchGet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawBatchGet != nil {
		l = m.RawBatchGet.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawPut) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawPut != nil {
		l = m.RawPut.Size()
		n += 1 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawBatchPut) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawBatchPut != nil {
		l = m.RawBatchPut.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawDelete) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawDelete != nil {
		l = m.RawDelete.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawBatchDelete) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawBatchDelete != nil {
		l = m.RawBatchDelete.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawScan) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawScan != nil {
		l = m.RawScan.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawDeleteRange) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawDeleteRange != nil {
		l = m.RawDeleteRange.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawBatchScan) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawBatchScan != nil {
		l = m.RawBatchScan.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_Coprocessor) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Coprocessor != nil {
		l = m.Coprocessor.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_PessimisticLock) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PessimisticLock != nil {
		l = m.PessimisticLock.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_PessimisticRollback) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PessimisticRollback != nil {
		l = m.PessimisticRollback.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_CheckTxnStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CheckTxnStatus != nil {
		l = m.CheckTxnStatus.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_TxnHeartBeat) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TxnHeartBeat != nil {
		l = m.TxnHeartBeat.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_CheckSecondaryLocks) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CheckSecondaryLocks != nil {
		l = m.CheckSecondaryLocks.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_RawCoprocessor) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawCoprocessor != nil {
		l = m.RawCoprocessor.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_FlashbackToVersion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FlashbackToVersion != nil {
		l = m.FlashbackToVersion.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_PrepareFlashbackToVersion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PrepareFlashbackToVersion != nil {
		l = m.PrepareFlashbackToVersion.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_Flush) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Flush != nil {
		l = m.Flush.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_BufferBatchGet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BufferBatchGet != nil {
		l = m.BufferBatchGet.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_GetHealthFeedback) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GetHealthFeedback != nil {
		l = m.GetHealthFeedback.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_BroadcastTxnStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BroadcastTxnStatus != nil {
		l = m.BroadcastTxnStatus.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchCommandsResponse_Response_Empty) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Empty != nil {
		l = m.Empty.Size()
		n += 2 + l + sovTikvpb(uint64(l))
	}
	return n
}
func (m *BatchRaftMessage) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Msgs) > 0 {
		for _, e := range m.Msgs {
			l = e.Size()
			n += 1 + l + sovTikvpb(uint64(l))
		}
	}
	if m.LastObservedTime != 0 {
		n += 1 + sovTikvpb(uint64(m.LastObservedTime))
	}
	return n
}

func (m *BatchCommandsEmptyRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TestId != 0 {
		n += 1 + sovTikvpb(uint64(m.TestId))
	}
	if m.DelayTime != 0 {
		n += 1 + sovTikvpb(uint64(m.DelayTime))
	}
	return n
}

func (m *BatchCommandsEmptyResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TestId != 0 {
		n += 1 + sovTikvpb(uint64(m.TestId))
	}
	return n
}

func sovTikvpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozTikvpb(x uint64) (n int) {
	return sovTikvpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *BatchCommandsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTikvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchCommandsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchCommandsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Requests", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Requests = append(m.Requests, &BatchCommandsRequest_Request{})
			if err := m.Requests[len(m.Requests)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTikvpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RequestIds = append(m.RequestIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTikvpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTikvpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthTikvpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.RequestIds) == 0 {
					m.RequestIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTikvpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RequestIds = append(m.RequestIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTikvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTikvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchCommandsRequest_Request) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTikvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Request: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Request: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Get", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.GetRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Get{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Scan", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.ScanRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Scan{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Prewrite", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.PrewriteRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Prewrite{v}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commit", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.CommitRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Commit{v}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Import", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.ImportRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Import{v}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cleanup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.CleanupRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Cleanup{v}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchGet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.BatchGetRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_BatchGet{v}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchRollback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.BatchRollbackRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_BatchRollback{v}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ScanLock", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.ScanLockRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_ScanLock{v}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolveLock", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.ResolveLockRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_ResolveLock{v}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GC", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.GCRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_GC{v}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeleteRange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.DeleteRangeRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_DeleteRange{v}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawGet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawGetRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawGet{v}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawBatchGet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawBatchGetRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawBatchGet{v}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawPut", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawPutRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawPut{v}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawBatchPut", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawBatchPutRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawBatchPut{v}
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawDelete", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawDeleteRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawDelete{v}
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawBatchDelete", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawBatchDeleteRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawBatchDelete{v}
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawScan", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawScanRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawScan{v}
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawDeleteRange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawDeleteRangeRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawDeleteRange{v}
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawBatchScan", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawBatchScanRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawBatchScan{v}
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Coprocessor", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &coprocessor.Request{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Coprocessor{v}
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PessimisticLock", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.PessimisticLockRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_PessimisticLock{v}
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PessimisticRollback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.PessimisticRollbackRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_PessimisticRollback{v}
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckTxnStatus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.CheckTxnStatusRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_CheckTxnStatus{v}
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxnHeartBeat", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.TxnHeartBeatRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_TxnHeartBeat{v}
			iNdEx = postIndex
		case 33:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckSecondaryLocks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.CheckSecondaryLocksRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_CheckSecondaryLocks{v}
			iNdEx = postIndex
		case 34:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawCoprocessor", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawCoprocessorRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_RawCoprocessor{v}
			iNdEx = postIndex
		case 35:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlashbackToVersion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.FlashbackToVersionRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_FlashbackToVersion{v}
			iNdEx = postIndex
		case 36:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrepareFlashbackToVersion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.PrepareFlashbackToVersionRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_PrepareFlashbackToVersion{v}
			iNdEx = postIndex
		case 37:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Flush", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.FlushRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Flush{v}
			iNdEx = postIndex
		case 38:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BufferBatchGet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.BufferBatchGetRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_BufferBatchGet{v}
			iNdEx = postIndex
		case 39:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GetHealthFeedback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.GetHealthFeedbackRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_GetHealthFeedback{v}
			iNdEx = postIndex
		case 40:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BroadcastTxnStatus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.BroadcastTxnStatusRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_BroadcastTxnStatus{v}
			iNdEx = postIndex
		case 255:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Empty", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &BatchCommandsEmptyRequest{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsRequest_Request_Empty{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTikvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTikvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchCommandsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTikvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchCommandsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchCommandsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Responses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Responses = append(m.Responses, &BatchCommandsResponse_Response{})
			if err := m.Responses[len(m.Responses)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTikvpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RequestIds = append(m.RequestIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTikvpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTikvpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthTikvpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.RequestIds) == 0 {
					m.RequestIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTikvpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RequestIds = append(m.RequestIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestIds", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TransportLayerLoad", wireType)
			}
			m.TransportLayerLoad = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TransportLayerLoad |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HealthFeedback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.HealthFeedback == nil {
				m.HealthFeedback = &kvrpcpb.HealthFeedback{}
			}
			if err := m.HealthFeedback.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTikvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTikvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchCommandsResponse_Response) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTikvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Response: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Response: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Get", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.GetResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Get{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Scan", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.ScanResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Scan{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Prewrite", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.PrewriteResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Prewrite{v}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commit", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.CommitResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Commit{v}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Import", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.ImportResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Import{v}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cleanup", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.CleanupResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Cleanup{v}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchGet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.BatchGetResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_BatchGet{v}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchRollback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.BatchRollbackResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_BatchRollback{v}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ScanLock", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.ScanLockResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_ScanLock{v}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolveLock", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.ResolveLockResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_ResolveLock{v}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GC", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.GCResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_GC{v}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeleteRange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.DeleteRangeResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_DeleteRange{v}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawGet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawGetResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawGet{v}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawBatchGet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawBatchGetResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawBatchGet{v}
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawPut", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawPutResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawPut{v}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawBatchPut", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawBatchPutResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawBatchPut{v}
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawDelete", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawDeleteResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawDelete{v}
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawBatchDelete", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawBatchDeleteResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawBatchDelete{v}
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawScan", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawScanResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawScan{v}
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawDeleteRange", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawDeleteRangeResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawDeleteRange{v}
			iNdEx = postIndex
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawBatchScan", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawBatchScanResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawBatchScan{v}
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Coprocessor", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &coprocessor.Response{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Coprocessor{v}
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PessimisticLock", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.PessimisticLockResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_PessimisticLock{v}
			iNdEx = postIndex
		case 24:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PessimisticRollback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.PessimisticRollbackResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_PessimisticRollback{v}
			iNdEx = postIndex
		case 25:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckTxnStatus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.CheckTxnStatusResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_CheckTxnStatus{v}
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TxnHeartBeat", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.TxnHeartBeatResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_TxnHeartBeat{v}
			iNdEx = postIndex
		case 33:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckSecondaryLocks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.CheckSecondaryLocksResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_CheckSecondaryLocks{v}
			iNdEx = postIndex
		case 34:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawCoprocessor", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.RawCoprocessorResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_RawCoprocessor{v}
			iNdEx = postIndex
		case 35:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlashbackToVersion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.FlashbackToVersionResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_FlashbackToVersion{v}
			iNdEx = postIndex
		case 36:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrepareFlashbackToVersion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.PrepareFlashbackToVersionResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_PrepareFlashbackToVersion{v}
			iNdEx = postIndex
		case 37:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Flush", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.FlushResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Flush{v}
			iNdEx = postIndex
		case 38:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BufferBatchGet", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.BufferBatchGetResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_BufferBatchGet{v}
			iNdEx = postIndex
		case 39:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GetHealthFeedback", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.GetHealthFeedbackResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_GetHealthFeedback{v}
			iNdEx = postIndex
		case 40:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BroadcastTxnStatus", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &kvrpcpb.BroadcastTxnStatusResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_BroadcastTxnStatus{v}
			iNdEx = postIndex
		case 255:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Empty", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &BatchCommandsEmptyResponse{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Cmd = &BatchCommandsResponse_Response_Empty{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTikvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTikvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchRaftMessage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTikvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchRaftMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchRaftMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msgs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTikvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthTikvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msgs = append(m.Msgs, &raft_serverpb.RaftMessage{})
			if err := m.Msgs[len(m.Msgs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastObservedTime", wireType)
			}
			m.LastObservedTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastObservedTime |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTikvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTikvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchCommandsEmptyRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTikvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchCommandsEmptyRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchCommandsEmptyRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TestId", wireType)
			}
			m.TestId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TestId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DelayTime", wireType)
			}
			m.DelayTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DelayTime |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTikvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTikvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchCommandsEmptyResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTikvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchCommandsEmptyResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchCommandsEmptyResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TestId", wireType)
			}
			m.TestId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TestId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTikvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthTikvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipTikvpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTikvpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTikvpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthTikvpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupTikvpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthTikvpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthTikvpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTikvpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupTikvpb = fmt.Errorf("proto: unexpected end of group")
)
