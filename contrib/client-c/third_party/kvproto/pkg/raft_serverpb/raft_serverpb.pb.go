// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: raft_serverpb.proto

package raft_serverpb

import (
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	disk_usage "github.com/pingcap/kvproto/pkg/disk_usage"
	encryptionpb "github.com/pingcap/kvproto/pkg/encryptionpb"
	eraftpb "github.com/pingcap/kvproto/pkg/eraftpb"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type PeerState int32

const (
	PeerState_Normal    PeerState = 0
	PeerState_Applying  PeerState = 1
	PeerState_Tombstone PeerState = 2
	PeerState_Merging   PeerState = 3
	// Currently used for witness to non-witness conversion: When a witness
	// has just become a non-witness, we need to set and persist this state,
	// so that when the service restarts before applying snapshot, we can
	// actively request snapshot when initializing this peer.
	PeerState_Unavailable PeerState = 4
)

var PeerState_name = map[int32]string{
	0: "Normal",
	1: "Applying",
	2: "Tombstone",
	3: "Merging",
	4: "Unavailable",
}

var PeerState_value = map[string]int32{
	"Normal":      0,
	"Applying":    1,
	"Tombstone":   2,
	"Merging":     3,
	"Unavailable": 4,
}

func (x PeerState) String() string {
	return proto.EnumName(PeerState_name, int32(x))
}

func (PeerState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{0}
}

type ExtraMessageType int32

const (
	ExtraMessageType_MsgRegionWakeUp           ExtraMessageType = 0
	ExtraMessageType_MsgWantRollbackMerge      ExtraMessageType = 1
	ExtraMessageType_MsgCheckStalePeer         ExtraMessageType = 2
	ExtraMessageType_MsgCheckStalePeerResponse ExtraMessageType = 3
	// If leader is going to sleep, it will send requests to all its followers
	// to make sure they all agree to sleep.
	ExtraMessageType_MsgHibernateRequest                 ExtraMessageType = 4
	ExtraMessageType_MsgHibernateResponse                ExtraMessageType = 5
	ExtraMessageType_MsgRejectRaftLogCausedByMemoryUsage ExtraMessageType = 6
	ExtraMessageType_MsgAvailabilityRequest              ExtraMessageType = 7
	ExtraMessageType_MsgAvailabilityResponse             ExtraMessageType = 8
	ExtraMessageType_MsgVoterReplicatedIndexRequest      ExtraMessageType = 9
	ExtraMessageType_MsgVoterReplicatedIndexResponse     ExtraMessageType = 10
	// Message means that `from` is tombstone. Leader can then update removed_records.
	ExtraMessageType_MsgGcPeerRequest  ExtraMessageType = 11
	ExtraMessageType_MsgGcPeerResponse ExtraMessageType = 12
	ExtraMessageType_MsgFlushMemtable  ExtraMessageType = 13
	ExtraMessageType_MsgRefreshBuckets ExtraMessageType = 14
	// Messages for the snapshot gen precheck process.
	ExtraMessageType_MsgSnapGenPrecheckRequest  ExtraMessageType = 15
	ExtraMessageType_MsgSnapGenPrecheckResponse ExtraMessageType = 16
	// Used in transfer leader process for leader to inform follower to load the
	// region into in-memory engine if the relevant region is cached.
	ExtraMessageType_MsgPreLoadRegionRequest ExtraMessageType = 17
	// Used in transfer leader process for follower to inform leader the completes
	// of the region cache
	ExtraMessageType_MsgPreLoadRegionResponse ExtraMessageType = 18
)

var ExtraMessageType_name = map[int32]string{
	0:  "MsgRegionWakeUp",
	1:  "MsgWantRollbackMerge",
	2:  "MsgCheckStalePeer",
	3:  "MsgCheckStalePeerResponse",
	4:  "MsgHibernateRequest",
	5:  "MsgHibernateResponse",
	6:  "MsgRejectRaftLogCausedByMemoryUsage",
	7:  "MsgAvailabilityRequest",
	8:  "MsgAvailabilityResponse",
	9:  "MsgVoterReplicatedIndexRequest",
	10: "MsgVoterReplicatedIndexResponse",
	11: "MsgGcPeerRequest",
	12: "MsgGcPeerResponse",
	13: "MsgFlushMemtable",
	14: "MsgRefreshBuckets",
	15: "MsgSnapGenPrecheckRequest",
	16: "MsgSnapGenPrecheckResponse",
	17: "MsgPreLoadRegionRequest",
	18: "MsgPreLoadRegionResponse",
}

var ExtraMessageType_value = map[string]int32{
	"MsgRegionWakeUp":                     0,
	"MsgWantRollbackMerge":                1,
	"MsgCheckStalePeer":                   2,
	"MsgCheckStalePeerResponse":           3,
	"MsgHibernateRequest":                 4,
	"MsgHibernateResponse":                5,
	"MsgRejectRaftLogCausedByMemoryUsage": 6,
	"MsgAvailabilityRequest":              7,
	"MsgAvailabilityResponse":             8,
	"MsgVoterReplicatedIndexRequest":      9,
	"MsgVoterReplicatedIndexResponse":     10,
	"MsgGcPeerRequest":                    11,
	"MsgGcPeerResponse":                   12,
	"MsgFlushMemtable":                    13,
	"MsgRefreshBuckets":                   14,
	"MsgSnapGenPrecheckRequest":           15,
	"MsgSnapGenPrecheckResponse":          16,
	"MsgPreLoadRegionRequest":             17,
	"MsgPreLoadRegionResponse":            18,
}

func (x ExtraMessageType) String() string {
	return proto.EnumName(ExtraMessageType_name, int32(x))
}

func (ExtraMessageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{1}
}

type RaftMessage struct {
	RegionId    uint64              `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	FromPeer    *metapb.Peer        `protobuf:"bytes,2,opt,name=from_peer,json=fromPeer,proto3" json:"from_peer,omitempty"`
	ToPeer      *metapb.Peer        `protobuf:"bytes,3,opt,name=to_peer,json=toPeer,proto3" json:"to_peer,omitempty"`
	Message     *eraftpb.Message    `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	RegionEpoch *metapb.RegionEpoch `protobuf:"bytes,5,opt,name=region_epoch,json=regionEpoch,proto3" json:"region_epoch,omitempty"`
	// true means to_peer is a tombstone peer and it should remove itself.
	IsTombstone bool `protobuf:"varint,6,opt,name=is_tombstone,json=isTombstone,proto3" json:"is_tombstone,omitempty"`
	// Region key range [start_key, end_key).
	StartKey []byte `protobuf:"bytes,7,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey   []byte `protobuf:"bytes,8,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	// If it has value, to_peer should be removed if merge is never going to complete.
	MergeTarget *metapb.Region       `protobuf:"bytes,9,opt,name=merge_target,json=mergeTarget,proto3" json:"merge_target,omitempty"`
	ExtraMsg    *ExtraMessage        `protobuf:"bytes,10,opt,name=extra_msg,json=extraMsg,proto3" json:"extra_msg,omitempty"`
	ExtraCtx    []byte               `protobuf:"bytes,11,opt,name=extra_ctx,json=extraCtx,proto3" json:"extra_ctx,omitempty"`
	DiskUsage   disk_usage.DiskUsage `protobuf:"varint,12,opt,name=disk_usage,json=diskUsage,proto3,enum=disk_usage.DiskUsage" json:"disk_usage,omitempty"`
}

func (m *RaftMessage) Reset()         { *m = RaftMessage{} }
func (m *RaftMessage) String() string { return proto.CompactTextString(m) }
func (*RaftMessage) ProtoMessage()    {}
func (*RaftMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{0}
}
func (m *RaftMessage) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftMessage.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftMessage.Merge(m, src)
}
func (m *RaftMessage) XXX_Size() int {
	return m.Size()
}
func (m *RaftMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftMessage.DiscardUnknown(m)
}

var xxx_messageInfo_RaftMessage proto.InternalMessageInfo

func (m *RaftMessage) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RaftMessage) GetFromPeer() *metapb.Peer {
	if m != nil {
		return m.FromPeer
	}
	return nil
}

func (m *RaftMessage) GetToPeer() *metapb.Peer {
	if m != nil {
		return m.ToPeer
	}
	return nil
}

func (m *RaftMessage) GetMessage() *eraftpb.Message {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *RaftMessage) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *RaftMessage) GetIsTombstone() bool {
	if m != nil {
		return m.IsTombstone
	}
	return false
}

func (m *RaftMessage) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *RaftMessage) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (m *RaftMessage) GetMergeTarget() *metapb.Region {
	if m != nil {
		return m.MergeTarget
	}
	return nil
}

func (m *RaftMessage) GetExtraMsg() *ExtraMessage {
	if m != nil {
		return m.ExtraMsg
	}
	return nil
}

func (m *RaftMessage) GetExtraCtx() []byte {
	if m != nil {
		return m.ExtraCtx
	}
	return nil
}

func (m *RaftMessage) GetDiskUsage() disk_usage.DiskUsage {
	if m != nil {
		return m.DiskUsage
	}
	return disk_usage.DiskUsage_Normal
}

type RaftTruncatedState struct {
	Index uint64 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Term  uint64 `protobuf:"varint,2,opt,name=term,proto3" json:"term,omitempty"`
}

func (m *RaftTruncatedState) Reset()         { *m = RaftTruncatedState{} }
func (m *RaftTruncatedState) String() string { return proto.CompactTextString(m) }
func (*RaftTruncatedState) ProtoMessage()    {}
func (*RaftTruncatedState) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{1}
}
func (m *RaftTruncatedState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftTruncatedState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftTruncatedState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftTruncatedState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftTruncatedState.Merge(m, src)
}
func (m *RaftTruncatedState) XXX_Size() int {
	return m.Size()
}
func (m *RaftTruncatedState) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftTruncatedState.DiscardUnknown(m)
}

var xxx_messageInfo_RaftTruncatedState proto.InternalMessageInfo

func (m *RaftTruncatedState) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *RaftTruncatedState) GetTerm() uint64 {
	if m != nil {
		return m.Term
	}
	return 0
}

type SnapshotCFFile struct {
	Cf       string `protobuf:"bytes,1,opt,name=cf,proto3" json:"cf,omitempty"`
	Size_    uint64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Checksum uint32 `protobuf:"varint,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (m *SnapshotCFFile) Reset()         { *m = SnapshotCFFile{} }
func (m *SnapshotCFFile) String() string { return proto.CompactTextString(m) }
func (*SnapshotCFFile) ProtoMessage()    {}
func (*SnapshotCFFile) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{2}
}
func (m *SnapshotCFFile) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotCFFile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotCFFile.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapshotCFFile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotCFFile.Merge(m, src)
}
func (m *SnapshotCFFile) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotCFFile) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotCFFile.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotCFFile proto.InternalMessageInfo

func (m *SnapshotCFFile) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *SnapshotCFFile) GetSize_() uint64 {
	if m != nil {
		return m.Size_
	}
	return 0
}

func (m *SnapshotCFFile) GetChecksum() uint32 {
	if m != nil {
		return m.Checksum
	}
	return 0
}

type SnapshotMeta struct {
	CfFiles []*SnapshotCFFile `protobuf:"bytes,1,rep,name=cf_files,json=cfFiles,proto3" json:"cf_files,omitempty"`
	// true means this snapshot is triggered for load balance
	ForBalance bool `protobuf:"varint,2,opt,name=for_balance,json=forBalance,proto3" json:"for_balance,omitempty"`
	// true means this is an empty snapshot for witness
	ForWitness bool `protobuf:"varint,3,opt,name=for_witness,json=forWitness,proto3" json:"for_witness,omitempty"`
	// the timestamp second to generate snapshot
	Start uint64 `protobuf:"varint,4,opt,name=start,proto3" json:"start,omitempty"`
	// the duration of generating snapshot
	GenerateDurationSec uint64 `protobuf:"varint,5,opt,name=generate_duration_sec,json=generateDurationSec,proto3" json:"generate_duration_sec,omitempty"`
	// the path of the tablet snapshot, it should only be used for v1 to receive
	// snapshot from v2
	TabletSnapPath string `protobuf:"bytes,6,opt,name=tablet_snap_path,json=tabletSnapPath,proto3" json:"tablet_snap_path,omitempty"`
	// A hint of the latest commit index on leader when sending snapshot.
	// It should only be used for v2 to send snapshot to v1.
	// See https://github.com/pingcap/tiflash/issues/7568
	CommitIndexHint uint64 `protobuf:"varint,7,opt,name=commit_index_hint,json=commitIndexHint,proto3" json:"commit_index_hint,omitempty"`
}

func (m *SnapshotMeta) Reset()         { *m = SnapshotMeta{} }
func (m *SnapshotMeta) String() string { return proto.CompactTextString(m) }
func (*SnapshotMeta) ProtoMessage()    {}
func (*SnapshotMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{3}
}
func (m *SnapshotMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapshotMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotMeta.Merge(m, src)
}
func (m *SnapshotMeta) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotMeta.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotMeta proto.InternalMessageInfo

func (m *SnapshotMeta) GetCfFiles() []*SnapshotCFFile {
	if m != nil {
		return m.CfFiles
	}
	return nil
}

func (m *SnapshotMeta) GetForBalance() bool {
	if m != nil {
		return m.ForBalance
	}
	return false
}

func (m *SnapshotMeta) GetForWitness() bool {
	if m != nil {
		return m.ForWitness
	}
	return false
}

func (m *SnapshotMeta) GetStart() uint64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *SnapshotMeta) GetGenerateDurationSec() uint64 {
	if m != nil {
		return m.GenerateDurationSec
	}
	return 0
}

func (m *SnapshotMeta) GetTabletSnapPath() string {
	if m != nil {
		return m.TabletSnapPath
	}
	return ""
}

func (m *SnapshotMeta) GetCommitIndexHint() uint64 {
	if m != nil {
		return m.CommitIndexHint
	}
	return 0
}

type SnapshotChunk struct {
	Message *RaftMessage `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Data    []byte       `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (m *SnapshotChunk) Reset()         { *m = SnapshotChunk{} }
func (m *SnapshotChunk) String() string { return proto.CompactTextString(m) }
func (*SnapshotChunk) ProtoMessage()    {}
func (*SnapshotChunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{4}
}
func (m *SnapshotChunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SnapshotChunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SnapshotChunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SnapshotChunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SnapshotChunk.Merge(m, src)
}
func (m *SnapshotChunk) XXX_Size() int {
	return m.Size()
}
func (m *SnapshotChunk) XXX_DiscardUnknown() {
	xxx_messageInfo_SnapshotChunk.DiscardUnknown(m)
}

var xxx_messageInfo_SnapshotChunk proto.InternalMessageInfo

func (m *SnapshotChunk) GetMessage() *RaftMessage {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *SnapshotChunk) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type Done struct {
}

func (m *Done) Reset()         { *m = Done{} }
func (m *Done) String() string { return proto.CompactTextString(m) }
func (*Done) ProtoMessage()    {}
func (*Done) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{5}
}
func (m *Done) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Done) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Done.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Done) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Done.Merge(m, src)
}
func (m *Done) XXX_Size() int {
	return m.Size()
}
func (m *Done) XXX_DiscardUnknown() {
	xxx_messageInfo_Done.DiscardUnknown(m)
}

var xxx_messageInfo_Done proto.InternalMessageInfo

type TabletSnapshotFileMeta struct {
	FileSize uint64 `protobuf:"varint,1,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileName string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// Some block data. Unencrypted.
	HeadChunk []byte `protobuf:"bytes,3,opt,name=head_chunk,json=headChunk,proto3" json:"head_chunk,omitempty"`
	// trailing data including checksum. Unencrypted.
	TrailingChunk []byte `protobuf:"bytes,4,opt,name=trailing_chunk,json=trailingChunk,proto3" json:"trailing_chunk,omitempty"`
}

func (m *TabletSnapshotFileMeta) Reset()         { *m = TabletSnapshotFileMeta{} }
func (m *TabletSnapshotFileMeta) String() string { return proto.CompactTextString(m) }
func (*TabletSnapshotFileMeta) ProtoMessage()    {}
func (*TabletSnapshotFileMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{6}
}
func (m *TabletSnapshotFileMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TabletSnapshotFileMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TabletSnapshotFileMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TabletSnapshotFileMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabletSnapshotFileMeta.Merge(m, src)
}
func (m *TabletSnapshotFileMeta) XXX_Size() int {
	return m.Size()
}
func (m *TabletSnapshotFileMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_TabletSnapshotFileMeta.DiscardUnknown(m)
}

var xxx_messageInfo_TabletSnapshotFileMeta proto.InternalMessageInfo

func (m *TabletSnapshotFileMeta) GetFileSize() uint64 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *TabletSnapshotFileMeta) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *TabletSnapshotFileMeta) GetHeadChunk() []byte {
	if m != nil {
		return m.HeadChunk
	}
	return nil
}

func (m *TabletSnapshotFileMeta) GetTrailingChunk() []byte {
	if m != nil {
		return m.TrailingChunk
	}
	return nil
}

// Snapshot preview for server to decide whether skip some files.
// Server should send back an `AcceptedSnapshotFile` to let client
// keep sending specified files. Only SST files can be skipped, all
// other files should always be sent.
type TabletSnapshotPreview struct {
	Metas []*TabletSnapshotFileMeta `protobuf:"bytes,1,rep,name=metas,proto3" json:"metas,omitempty"`
	// There may be too many metas, use a flag to indicate all metas
	// are sent.
	End bool `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (m *TabletSnapshotPreview) Reset()         { *m = TabletSnapshotPreview{} }
func (m *TabletSnapshotPreview) String() string { return proto.CompactTextString(m) }
func (*TabletSnapshotPreview) ProtoMessage()    {}
func (*TabletSnapshotPreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{7}
}
func (m *TabletSnapshotPreview) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TabletSnapshotPreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TabletSnapshotPreview.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TabletSnapshotPreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabletSnapshotPreview.Merge(m, src)
}
func (m *TabletSnapshotPreview) XXX_Size() int {
	return m.Size()
}
func (m *TabletSnapshotPreview) XXX_DiscardUnknown() {
	xxx_messageInfo_TabletSnapshotPreview.DiscardUnknown(m)
}

var xxx_messageInfo_TabletSnapshotPreview proto.InternalMessageInfo

func (m *TabletSnapshotPreview) GetMetas() []*TabletSnapshotFileMeta {
	if m != nil {
		return m.Metas
	}
	return nil
}

func (m *TabletSnapshotPreview) GetEnd() bool {
	if m != nil {
		return m.End
	}
	return false
}

type TabletSnapshotFileChunk struct {
	FileSize uint64 `protobuf:"varint,1,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileName string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// Encrypted.
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	// Initial vector if encryption is enabled.
	Iv  []byte                `protobuf:"bytes,4,opt,name=iv,proto3" json:"iv,omitempty"`
	Key *encryptionpb.DataKey `protobuf:"bytes,5,opt,name=key,proto3" json:"key,omitempty"`
}

func (m *TabletSnapshotFileChunk) Reset()         { *m = TabletSnapshotFileChunk{} }
func (m *TabletSnapshotFileChunk) String() string { return proto.CompactTextString(m) }
func (*TabletSnapshotFileChunk) ProtoMessage()    {}
func (*TabletSnapshotFileChunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{8}
}
func (m *TabletSnapshotFileChunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TabletSnapshotFileChunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TabletSnapshotFileChunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TabletSnapshotFileChunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabletSnapshotFileChunk.Merge(m, src)
}
func (m *TabletSnapshotFileChunk) XXX_Size() int {
	return m.Size()
}
func (m *TabletSnapshotFileChunk) XXX_DiscardUnknown() {
	xxx_messageInfo_TabletSnapshotFileChunk.DiscardUnknown(m)
}

var xxx_messageInfo_TabletSnapshotFileChunk proto.InternalMessageInfo

func (m *TabletSnapshotFileChunk) GetFileSize() uint64 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *TabletSnapshotFileChunk) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *TabletSnapshotFileChunk) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *TabletSnapshotFileChunk) GetIv() []byte {
	if m != nil {
		return m.Iv
	}
	return nil
}

func (m *TabletSnapshotFileChunk) GetKey() *encryptionpb.DataKey {
	if m != nil {
		return m.Key
	}
	return nil
}

type TabletSnapshotHead struct {
	Message  *RaftMessage `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	UseCache bool         `protobuf:"varint,2,opt,name=use_cache,json=useCache,proto3" json:"use_cache,omitempty"`
}

func (m *TabletSnapshotHead) Reset()         { *m = TabletSnapshotHead{} }
func (m *TabletSnapshotHead) String() string { return proto.CompactTextString(m) }
func (*TabletSnapshotHead) ProtoMessage()    {}
func (*TabletSnapshotHead) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{9}
}
func (m *TabletSnapshotHead) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TabletSnapshotHead) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TabletSnapshotHead.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TabletSnapshotHead) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabletSnapshotHead.Merge(m, src)
}
func (m *TabletSnapshotHead) XXX_Size() int {
	return m.Size()
}
func (m *TabletSnapshotHead) XXX_DiscardUnknown() {
	xxx_messageInfo_TabletSnapshotHead.DiscardUnknown(m)
}

var xxx_messageInfo_TabletSnapshotHead proto.InternalMessageInfo

func (m *TabletSnapshotHead) GetMessage() *RaftMessage {
	if m != nil {
		return m.Message
	}
	return nil
}

func (m *TabletSnapshotHead) GetUseCache() bool {
	if m != nil {
		return m.UseCache
	}
	return false
}

type TabletSnapshotEnd struct {
	// Checksum of all data sent in `TabletSnapshotFileChunk.data` and
	// `TabletSnapshotFileChunk.file_name`.
	Checksum uint64 `protobuf:"varint,1,opt,name=checksum,proto3" json:"checksum,omitempty"`
}

func (m *TabletSnapshotEnd) Reset()         { *m = TabletSnapshotEnd{} }
func (m *TabletSnapshotEnd) String() string { return proto.CompactTextString(m) }
func (*TabletSnapshotEnd) ProtoMessage()    {}
func (*TabletSnapshotEnd) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{10}
}
func (m *TabletSnapshotEnd) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TabletSnapshotEnd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TabletSnapshotEnd.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TabletSnapshotEnd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabletSnapshotEnd.Merge(m, src)
}
func (m *TabletSnapshotEnd) XXX_Size() int {
	return m.Size()
}
func (m *TabletSnapshotEnd) XXX_DiscardUnknown() {
	xxx_messageInfo_TabletSnapshotEnd.DiscardUnknown(m)
}

var xxx_messageInfo_TabletSnapshotEnd proto.InternalMessageInfo

func (m *TabletSnapshotEnd) GetChecksum() uint64 {
	if m != nil {
		return m.Checksum
	}
	return 0
}

type TabletSnapshotRequest struct {
	// Types that are valid to be assigned to Payload:
	//	*TabletSnapshotRequest_Head
	//	*TabletSnapshotRequest_Preview
	//	*TabletSnapshotRequest_Chunk
	//	*TabletSnapshotRequest_End
	Payload isTabletSnapshotRequest_Payload `protobuf_oneof:"payload"`
}

func (m *TabletSnapshotRequest) Reset()         { *m = TabletSnapshotRequest{} }
func (m *TabletSnapshotRequest) String() string { return proto.CompactTextString(m) }
func (*TabletSnapshotRequest) ProtoMessage()    {}
func (*TabletSnapshotRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{11}
}
func (m *TabletSnapshotRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TabletSnapshotRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TabletSnapshotRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TabletSnapshotRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabletSnapshotRequest.Merge(m, src)
}
func (m *TabletSnapshotRequest) XXX_Size() int {
	return m.Size()
}
func (m *TabletSnapshotRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TabletSnapshotRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TabletSnapshotRequest proto.InternalMessageInfo

type isTabletSnapshotRequest_Payload interface {
	isTabletSnapshotRequest_Payload()
	MarshalTo([]byte) (int, error)
	Size() int
}

type TabletSnapshotRequest_Head struct {
	Head *TabletSnapshotHead `protobuf:"bytes,1,opt,name=head,proto3,oneof" json:"head,omitempty"`
}
type TabletSnapshotRequest_Preview struct {
	Preview *TabletSnapshotPreview `protobuf:"bytes,2,opt,name=preview,proto3,oneof" json:"preview,omitempty"`
}
type TabletSnapshotRequest_Chunk struct {
	Chunk *TabletSnapshotFileChunk `protobuf:"bytes,3,opt,name=chunk,proto3,oneof" json:"chunk,omitempty"`
}
type TabletSnapshotRequest_End struct {
	End *TabletSnapshotEnd `protobuf:"bytes,4,opt,name=end,proto3,oneof" json:"end,omitempty"`
}

func (*TabletSnapshotRequest_Head) isTabletSnapshotRequest_Payload()    {}
func (*TabletSnapshotRequest_Preview) isTabletSnapshotRequest_Payload() {}
func (*TabletSnapshotRequest_Chunk) isTabletSnapshotRequest_Payload()   {}
func (*TabletSnapshotRequest_End) isTabletSnapshotRequest_Payload()     {}

func (m *TabletSnapshotRequest) GetPayload() isTabletSnapshotRequest_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (m *TabletSnapshotRequest) GetHead() *TabletSnapshotHead {
	if x, ok := m.GetPayload().(*TabletSnapshotRequest_Head); ok {
		return x.Head
	}
	return nil
}

func (m *TabletSnapshotRequest) GetPreview() *TabletSnapshotPreview {
	if x, ok := m.GetPayload().(*TabletSnapshotRequest_Preview); ok {
		return x.Preview
	}
	return nil
}

func (m *TabletSnapshotRequest) GetChunk() *TabletSnapshotFileChunk {
	if x, ok := m.GetPayload().(*TabletSnapshotRequest_Chunk); ok {
		return x.Chunk
	}
	return nil
}

func (m *TabletSnapshotRequest) GetEnd() *TabletSnapshotEnd {
	if x, ok := m.GetPayload().(*TabletSnapshotRequest_End); ok {
		return x.End
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*TabletSnapshotRequest) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*TabletSnapshotRequest_Head)(nil),
		(*TabletSnapshotRequest_Preview)(nil),
		(*TabletSnapshotRequest_Chunk)(nil),
		(*TabletSnapshotRequest_End)(nil),
	}
}

type AcceptedSnapshotFiles struct {
	FileName []string `protobuf:"bytes,1,rep,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
}

func (m *AcceptedSnapshotFiles) Reset()         { *m = AcceptedSnapshotFiles{} }
func (m *AcceptedSnapshotFiles) String() string { return proto.CompactTextString(m) }
func (*AcceptedSnapshotFiles) ProtoMessage()    {}
func (*AcceptedSnapshotFiles) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{12}
}
func (m *AcceptedSnapshotFiles) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AcceptedSnapshotFiles) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AcceptedSnapshotFiles.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AcceptedSnapshotFiles) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptedSnapshotFiles.Merge(m, src)
}
func (m *AcceptedSnapshotFiles) XXX_Size() int {
	return m.Size()
}
func (m *AcceptedSnapshotFiles) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptedSnapshotFiles.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptedSnapshotFiles proto.InternalMessageInfo

func (m *AcceptedSnapshotFiles) GetFileName() []string {
	if m != nil {
		return m.FileName
	}
	return nil
}

type TabletSnapshotResponse struct {
	Files *AcceptedSnapshotFiles `protobuf:"bytes,1,opt,name=files,proto3" json:"files,omitempty"`
}

func (m *TabletSnapshotResponse) Reset()         { *m = TabletSnapshotResponse{} }
func (m *TabletSnapshotResponse) String() string { return proto.CompactTextString(m) }
func (*TabletSnapshotResponse) ProtoMessage()    {}
func (*TabletSnapshotResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{13}
}
func (m *TabletSnapshotResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TabletSnapshotResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TabletSnapshotResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TabletSnapshotResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabletSnapshotResponse.Merge(m, src)
}
func (m *TabletSnapshotResponse) XXX_Size() int {
	return m.Size()
}
func (m *TabletSnapshotResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TabletSnapshotResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TabletSnapshotResponse proto.InternalMessageInfo

func (m *TabletSnapshotResponse) GetFiles() *AcceptedSnapshotFiles {
	if m != nil {
		return m.Files
	}
	return nil
}

type KeyValue struct {
	Key   []byte `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *KeyValue) Reset()         { *m = KeyValue{} }
func (m *KeyValue) String() string { return proto.CompactTextString(m) }
func (*KeyValue) ProtoMessage()    {}
func (*KeyValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{14}
}
func (m *KeyValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeyValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeyValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KeyValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyValue.Merge(m, src)
}
func (m *KeyValue) XXX_Size() int {
	return m.Size()
}
func (m *KeyValue) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyValue.DiscardUnknown(m)
}

var xxx_messageInfo_KeyValue proto.InternalMessageInfo

func (m *KeyValue) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *KeyValue) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type RaftSnapshotData struct {
	Region         *metapb.Region  `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	FileSize       uint64          `protobuf:"varint,2,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	Data           []*KeyValue     `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	Version        uint64          `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	Meta           *SnapshotMeta   `protobuf:"bytes,5,opt,name=meta,proto3" json:"meta,omitempty"`
	RemovedRecords []*metapb.Peer  `protobuf:"bytes,6,rep,name=removed_records,json=removedRecords,proto3" json:"removed_records,omitempty"`
	MergedRecords  []*MergedRecord `protobuf:"bytes,7,rep,name=merged_records,json=mergedRecords,proto3" json:"merged_records,omitempty"`
}

func (m *RaftSnapshotData) Reset()         { *m = RaftSnapshotData{} }
func (m *RaftSnapshotData) String() string { return proto.CompactTextString(m) }
func (*RaftSnapshotData) ProtoMessage()    {}
func (*RaftSnapshotData) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{15}
}
func (m *RaftSnapshotData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftSnapshotData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftSnapshotData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftSnapshotData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftSnapshotData.Merge(m, src)
}
func (m *RaftSnapshotData) XXX_Size() int {
	return m.Size()
}
func (m *RaftSnapshotData) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftSnapshotData.DiscardUnknown(m)
}

var xxx_messageInfo_RaftSnapshotData proto.InternalMessageInfo

func (m *RaftSnapshotData) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *RaftSnapshotData) GetFileSize() uint64 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *RaftSnapshotData) GetData() []*KeyValue {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *RaftSnapshotData) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *RaftSnapshotData) GetMeta() *SnapshotMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

func (m *RaftSnapshotData) GetRemovedRecords() []*metapb.Peer {
	if m != nil {
		return m.RemovedRecords
	}
	return nil
}

func (m *RaftSnapshotData) GetMergedRecords() []*MergedRecord {
	if m != nil {
		return m.MergedRecords
	}
	return nil
}

type StoreIdent struct {
	ClusterId  uint64             `protobuf:"varint,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	StoreId    uint64             `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	ApiVersion kvrpcpb.APIVersion `protobuf:"varint,3,opt,name=api_version,json=apiVersion,proto3,enum=kvrpcpb.APIVersion" json:"api_version,omitempty"`
}

func (m *StoreIdent) Reset()         { *m = StoreIdent{} }
func (m *StoreIdent) String() string { return proto.CompactTextString(m) }
func (*StoreIdent) ProtoMessage()    {}
func (*StoreIdent) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{16}
}
func (m *StoreIdent) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreIdent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreIdent.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreIdent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreIdent.Merge(m, src)
}
func (m *StoreIdent) XXX_Size() int {
	return m.Size()
}
func (m *StoreIdent) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreIdent.DiscardUnknown(m)
}

var xxx_messageInfo_StoreIdent proto.InternalMessageInfo

func (m *StoreIdent) GetClusterId() uint64 {
	if m != nil {
		return m.ClusterId
	}
	return 0
}

func (m *StoreIdent) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *StoreIdent) GetApiVersion() kvrpcpb.APIVersion {
	if m != nil {
		return m.ApiVersion
	}
	return kvrpcpb.APIVersion_V1
}

type StoreRecoverState struct {
	// Used for TiKV start recovery when WAL of KVDB was disabled.
	// TiKV may read all relations between seqno and raft log index, and replay
	// all raft logs which corresponding seqno smaller than the seqno here.
	// After TiKV replays all raft logs and flushed KV data, the seqno here must
	// be updated.
	Seqno uint64 `protobuf:"varint,1,opt,name=seqno,proto3" json:"seqno,omitempty"`
}

func (m *StoreRecoverState) Reset()         { *m = StoreRecoverState{} }
func (m *StoreRecoverState) String() string { return proto.CompactTextString(m) }
func (*StoreRecoverState) ProtoMessage()    {}
func (*StoreRecoverState) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{17}
}
func (m *StoreRecoverState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreRecoverState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreRecoverState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreRecoverState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreRecoverState.Merge(m, src)
}
func (m *StoreRecoverState) XXX_Size() int {
	return m.Size()
}
func (m *StoreRecoverState) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreRecoverState.DiscardUnknown(m)
}

var xxx_messageInfo_StoreRecoverState proto.InternalMessageInfo

func (m *StoreRecoverState) GetSeqno() uint64 {
	if m != nil {
		return m.Seqno
	}
	return 0
}

type RaftLocalState struct {
	HardState *eraftpb.HardState `protobuf:"bytes,1,opt,name=hard_state,json=hardState,proto3" json:"hard_state,omitempty"`
	LastIndex uint64             `protobuf:"varint,2,opt,name=last_index,json=lastIndex,proto3" json:"last_index,omitempty"`
}

func (m *RaftLocalState) Reset()         { *m = RaftLocalState{} }
func (m *RaftLocalState) String() string { return proto.CompactTextString(m) }
func (*RaftLocalState) ProtoMessage()    {}
func (*RaftLocalState) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{18}
}
func (m *RaftLocalState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftLocalState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftLocalState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftLocalState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftLocalState.Merge(m, src)
}
func (m *RaftLocalState) XXX_Size() int {
	return m.Size()
}
func (m *RaftLocalState) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftLocalState.DiscardUnknown(m)
}

var xxx_messageInfo_RaftLocalState proto.InternalMessageInfo

func (m *RaftLocalState) GetHardState() *eraftpb.HardState {
	if m != nil {
		return m.HardState
	}
	return nil
}

func (m *RaftLocalState) GetLastIndex() uint64 {
	if m != nil {
		return m.LastIndex
	}
	return 0
}

type RaftApplyState struct {
	AppliedIndex    uint64              `protobuf:"varint,1,opt,name=applied_index,json=appliedIndex,proto3" json:"applied_index,omitempty"`
	LastCommitIndex uint64              `protobuf:"varint,3,opt,name=last_commit_index,json=lastCommitIndex,proto3" json:"last_commit_index,omitempty"`
	CommitIndex     uint64              `protobuf:"varint,4,opt,name=commit_index,json=commitIndex,proto3" json:"commit_index,omitempty"`
	CommitTerm      uint64              `protobuf:"varint,5,opt,name=commit_term,json=commitTerm,proto3" json:"commit_term,omitempty"`
	TruncatedState  *RaftTruncatedState `protobuf:"bytes,2,opt,name=truncated_state,json=truncatedState,proto3" json:"truncated_state,omitempty"`
}

func (m *RaftApplyState) Reset()         { *m = RaftApplyState{} }
func (m *RaftApplyState) String() string { return proto.CompactTextString(m) }
func (*RaftApplyState) ProtoMessage()    {}
func (*RaftApplyState) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{19}
}
func (m *RaftApplyState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RaftApplyState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RaftApplyState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RaftApplyState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftApplyState.Merge(m, src)
}
func (m *RaftApplyState) XXX_Size() int {
	return m.Size()
}
func (m *RaftApplyState) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftApplyState.DiscardUnknown(m)
}

var xxx_messageInfo_RaftApplyState proto.InternalMessageInfo

func (m *RaftApplyState) GetAppliedIndex() uint64 {
	if m != nil {
		return m.AppliedIndex
	}
	return 0
}

func (m *RaftApplyState) GetLastCommitIndex() uint64 {
	if m != nil {
		return m.LastCommitIndex
	}
	return 0
}

func (m *RaftApplyState) GetCommitIndex() uint64 {
	if m != nil {
		return m.CommitIndex
	}
	return 0
}

func (m *RaftApplyState) GetCommitTerm() uint64 {
	if m != nil {
		return m.CommitTerm
	}
	return 0
}

func (m *RaftApplyState) GetTruncatedState() *RaftTruncatedState {
	if m != nil {
		return m.TruncatedState
	}
	return nil
}

type MergeState struct {
	MinIndex uint64         `protobuf:"varint,1,opt,name=min_index,json=minIndex,proto3" json:"min_index,omitempty"`
	Target   *metapb.Region `protobuf:"bytes,2,opt,name=target,proto3" json:"target,omitempty"`
	Commit   uint64         `protobuf:"varint,3,opt,name=commit,proto3" json:"commit,omitempty"`
}

func (m *MergeState) Reset()         { *m = MergeState{} }
func (m *MergeState) String() string { return proto.CompactTextString(m) }
func (*MergeState) ProtoMessage()    {}
func (*MergeState) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{20}
}
func (m *MergeState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MergeState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MergeState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MergeState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MergeState.Merge(m, src)
}
func (m *MergeState) XXX_Size() int {
	return m.Size()
}
func (m *MergeState) XXX_DiscardUnknown() {
	xxx_messageInfo_MergeState.DiscardUnknown(m)
}

var xxx_messageInfo_MergeState proto.InternalMessageInfo

func (m *MergeState) GetMinIndex() uint64 {
	if m != nil {
		return m.MinIndex
	}
	return 0
}

func (m *MergeState) GetTarget() *metapb.Region {
	if m != nil {
		return m.Target
	}
	return nil
}

func (m *MergeState) GetCommit() uint64 {
	if m != nil {
		return m.Commit
	}
	return 0
}

type MergedRecord struct {
	SourceRegionId uint64              `protobuf:"varint,1,opt,name=source_region_id,json=sourceRegionId,proto3" json:"source_region_id,omitempty"`
	SourceEpoch    *metapb.RegionEpoch `protobuf:"bytes,2,opt,name=source_epoch,json=sourceEpoch,proto3" json:"source_epoch,omitempty"`
	// Peers of source region when merge is committed.
	SourcePeers []*metapb.Peer `protobuf:"bytes,3,rep,name=source_peers,json=sourcePeers,proto3" json:"source_peers,omitempty"`
	// Removed peers (by confchange) of source region when merge is committed.
	SourceRemovedRecords []*metapb.Peer      `protobuf:"bytes,9,rep,name=source_removed_records,json=sourceRemovedRecords,proto3" json:"source_removed_records,omitempty"`
	TargetRegionId       uint64              `protobuf:"varint,4,opt,name=target_region_id,json=targetRegionId,proto3" json:"target_region_id,omitempty"`
	TargetEpoch          *metapb.RegionEpoch `protobuf:"bytes,5,opt,name=target_epoch,json=targetEpoch,proto3" json:"target_epoch,omitempty"`
	TargetPeers          []*metapb.Peer      `protobuf:"bytes,6,rep,name=target_peers,json=targetPeers,proto3" json:"target_peers,omitempty"`
	// Commit merge index.
	Index uint64 `protobuf:"varint,7,opt,name=index,proto3" json:"index,omitempty"`
	// Prepare merge index.
	SourceIndex uint64 `protobuf:"varint,8,opt,name=source_index,json=sourceIndex,proto3" json:"source_index,omitempty"`
}

func (m *MergedRecord) Reset()         { *m = MergedRecord{} }
func (m *MergedRecord) String() string { return proto.CompactTextString(m) }
func (*MergedRecord) ProtoMessage()    {}
func (*MergedRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{21}
}
func (m *MergedRecord) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MergedRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MergedRecord.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MergedRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MergedRecord.Merge(m, src)
}
func (m *MergedRecord) XXX_Size() int {
	return m.Size()
}
func (m *MergedRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_MergedRecord.DiscardUnknown(m)
}

var xxx_messageInfo_MergedRecord proto.InternalMessageInfo

func (m *MergedRecord) GetSourceRegionId() uint64 {
	if m != nil {
		return m.SourceRegionId
	}
	return 0
}

func (m *MergedRecord) GetSourceEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.SourceEpoch
	}
	return nil
}

func (m *MergedRecord) GetSourcePeers() []*metapb.Peer {
	if m != nil {
		return m.SourcePeers
	}
	return nil
}

func (m *MergedRecord) GetSourceRemovedRecords() []*metapb.Peer {
	if m != nil {
		return m.SourceRemovedRecords
	}
	return nil
}

func (m *MergedRecord) GetTargetRegionId() uint64 {
	if m != nil {
		return m.TargetRegionId
	}
	return 0
}

func (m *MergedRecord) GetTargetEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.TargetEpoch
	}
	return nil
}

func (m *MergedRecord) GetTargetPeers() []*metapb.Peer {
	if m != nil {
		return m.TargetPeers
	}
	return nil
}

func (m *MergedRecord) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *MergedRecord) GetSourceIndex() uint64 {
	if m != nil {
		return m.SourceIndex
	}
	return 0
}

type RegionLocalState struct {
	State      PeerState      `protobuf:"varint,1,opt,name=state,proto3,enum=raft_serverpb.PeerState" json:"state,omitempty"`
	Region     *metapb.Region `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	MergeState *MergeState    `protobuf:"bytes,3,opt,name=merge_state,json=mergeState,proto3" json:"merge_state,omitempty"`
	// The apply index corresponding to the storage when it's initialized.
	TabletIndex uint64 `protobuf:"varint,4,opt,name=tablet_index,json=tabletIndex,proto3" json:"tablet_index,omitempty"`
	// Raft doesn't guarantee peer will be removed in the end. In v1, peer finds
	// out its destiny by logs or broadcast; in v2, leader is responsible to
	// ensure removed peers are destroyed.
	// Note: only peers who has been part of this region can be in this list.
	RemovedRecords []*metapb.Peer `protobuf:"bytes,5,rep,name=removed_records,json=removedRecords,proto3" json:"removed_records,omitempty"`
	// Merged peer can't be deleted like gc peers. Instead, leader needs to
	// query target peer to decide whether source peer can be destroyed.
	MergedRecords []*MergedRecord `protobuf:"bytes,6,rep,name=merged_records,json=mergedRecords,proto3" json:"merged_records,omitempty"`
}

func (m *RegionLocalState) Reset()         { *m = RegionLocalState{} }
func (m *RegionLocalState) String() string { return proto.CompactTextString(m) }
func (*RegionLocalState) ProtoMessage()    {}
func (*RegionLocalState) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{22}
}
func (m *RegionLocalState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionLocalState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionLocalState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionLocalState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionLocalState.Merge(m, src)
}
func (m *RegionLocalState) XXX_Size() int {
	return m.Size()
}
func (m *RegionLocalState) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionLocalState.DiscardUnknown(m)
}

var xxx_messageInfo_RegionLocalState proto.InternalMessageInfo

func (m *RegionLocalState) GetState() PeerState {
	if m != nil {
		return m.State
	}
	return PeerState_Normal
}

func (m *RegionLocalState) GetRegion() *metapb.Region {
	if m != nil {
		return m.Region
	}
	return nil
}

func (m *RegionLocalState) GetMergeState() *MergeState {
	if m != nil {
		return m.MergeState
	}
	return nil
}

func (m *RegionLocalState) GetTabletIndex() uint64 {
	if m != nil {
		return m.TabletIndex
	}
	return 0
}

func (m *RegionLocalState) GetRemovedRecords() []*metapb.Peer {
	if m != nil {
		return m.RemovedRecords
	}
	return nil
}

func (m *RegionLocalState) GetMergedRecords() []*MergedRecord {
	if m != nil {
		return m.MergedRecords
	}
	return nil
}

type RegionSequenceNumberRelation struct {
	RegionId       uint64            `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	SequenceNumber uint64            `protobuf:"varint,2,opt,name=sequence_number,json=sequenceNumber,proto3" json:"sequence_number,omitempty"`
	ApplyState     *RaftApplyState   `protobuf:"bytes,3,opt,name=apply_state,json=applyState,proto3" json:"apply_state,omitempty"`
	RegionState    *RegionLocalState `protobuf:"bytes,4,opt,name=region_state,json=regionState,proto3" json:"region_state,omitempty"`
}

func (m *RegionSequenceNumberRelation) Reset()         { *m = RegionSequenceNumberRelation{} }
func (m *RegionSequenceNumberRelation) String() string { return proto.CompactTextString(m) }
func (*RegionSequenceNumberRelation) ProtoMessage()    {}
func (*RegionSequenceNumberRelation) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{23}
}
func (m *RegionSequenceNumberRelation) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionSequenceNumberRelation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionSequenceNumberRelation.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionSequenceNumberRelation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionSequenceNumberRelation.Merge(m, src)
}
func (m *RegionSequenceNumberRelation) XXX_Size() int {
	return m.Size()
}
func (m *RegionSequenceNumberRelation) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionSequenceNumberRelation.DiscardUnknown(m)
}

var xxx_messageInfo_RegionSequenceNumberRelation proto.InternalMessageInfo

func (m *RegionSequenceNumberRelation) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RegionSequenceNumberRelation) GetSequenceNumber() uint64 {
	if m != nil {
		return m.SequenceNumber
	}
	return 0
}

func (m *RegionSequenceNumberRelation) GetApplyState() *RaftApplyState {
	if m != nil {
		return m.ApplyState
	}
	return nil
}

func (m *RegionSequenceNumberRelation) GetRegionState() *RegionLocalState {
	if m != nil {
		return m.RegionState
	}
	return nil
}

type AvailabilityContext struct {
	FromRegionId    uint64              `protobuf:"varint,1,opt,name=from_region_id,json=fromRegionId,proto3" json:"from_region_id,omitempty"`
	FromRegionEpoch *metapb.RegionEpoch `protobuf:"bytes,2,opt,name=from_region_epoch,json=fromRegionEpoch,proto3" json:"from_region_epoch,omitempty"`
	Unavailable     bool                `protobuf:"varint,3,opt,name=unavailable,proto3" json:"unavailable,omitempty"`
	Trimmed         bool                `protobuf:"varint,4,opt,name=trimmed,proto3" json:"trimmed,omitempty"`
}

func (m *AvailabilityContext) Reset()         { *m = AvailabilityContext{} }
func (m *AvailabilityContext) String() string { return proto.CompactTextString(m) }
func (*AvailabilityContext) ProtoMessage()    {}
func (*AvailabilityContext) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{24}
}
func (m *AvailabilityContext) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *AvailabilityContext) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_AvailabilityContext.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *AvailabilityContext) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AvailabilityContext.Merge(m, src)
}
func (m *AvailabilityContext) XXX_Size() int {
	return m.Size()
}
func (m *AvailabilityContext) XXX_DiscardUnknown() {
	xxx_messageInfo_AvailabilityContext.DiscardUnknown(m)
}

var xxx_messageInfo_AvailabilityContext proto.InternalMessageInfo

func (m *AvailabilityContext) GetFromRegionId() uint64 {
	if m != nil {
		return m.FromRegionId
	}
	return 0
}

func (m *AvailabilityContext) GetFromRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.FromRegionEpoch
	}
	return nil
}

func (m *AvailabilityContext) GetUnavailable() bool {
	if m != nil {
		return m.Unavailable
	}
	return false
}

func (m *AvailabilityContext) GetTrimmed() bool {
	if m != nil {
		return m.Trimmed
	}
	return false
}

type FlushMemtable struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
}

func (m *FlushMemtable) Reset()         { *m = FlushMemtable{} }
func (m *FlushMemtable) String() string { return proto.CompactTextString(m) }
func (*FlushMemtable) ProtoMessage()    {}
func (*FlushMemtable) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{25}
}
func (m *FlushMemtable) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FlushMemtable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FlushMemtable.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FlushMemtable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlushMemtable.Merge(m, src)
}
func (m *FlushMemtable) XXX_Size() int {
	return m.Size()
}
func (m *FlushMemtable) XXX_DiscardUnknown() {
	xxx_messageInfo_FlushMemtable.DiscardUnknown(m)
}

var xxx_messageInfo_FlushMemtable proto.InternalMessageInfo

func (m *FlushMemtable) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

type RefreshBuckets struct {
	Version uint64   `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Keys    [][]byte `protobuf:"bytes,2,rep,name=keys,proto3" json:"keys,omitempty"`
	Sizes   []uint64 `protobuf:"varint,3,rep,packed,name=sizes,proto3" json:"sizes,omitempty"`
}

func (m *RefreshBuckets) Reset()         { *m = RefreshBuckets{} }
func (m *RefreshBuckets) String() string { return proto.CompactTextString(m) }
func (*RefreshBuckets) ProtoMessage()    {}
func (*RefreshBuckets) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{26}
}
func (m *RefreshBuckets) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RefreshBuckets) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RefreshBuckets.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RefreshBuckets) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RefreshBuckets.Merge(m, src)
}
func (m *RefreshBuckets) XXX_Size() int {
	return m.Size()
}
func (m *RefreshBuckets) XXX_DiscardUnknown() {
	xxx_messageInfo_RefreshBuckets.DiscardUnknown(m)
}

var xxx_messageInfo_RefreshBuckets proto.InternalMessageInfo

func (m *RefreshBuckets) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *RefreshBuckets) GetKeys() [][]byte {
	if m != nil {
		return m.Keys
	}
	return nil
}

func (m *RefreshBuckets) GetSizes() []uint64 {
	if m != nil {
		return m.Sizes
	}
	return nil
}

type CheckGcPeer struct {
	// The region ID who triggers the check and wait for report. It should be
	// the ID of RaftMessage.from.
	FromRegionId uint64 `protobuf:"varint,1,opt,name=from_region_id,json=fromRegionId,proto3" json:"from_region_id,omitempty"`
	// The region ID to be checked if should be destroyed.
	CheckRegionId uint64 `protobuf:"varint,2,opt,name=check_region_id,json=checkRegionId,proto3" json:"check_region_id,omitempty"`
	// The epoch of the region to be checked.
	CheckRegionEpoch *metapb.RegionEpoch `protobuf:"bytes,3,opt,name=check_region_epoch,json=checkRegionEpoch,proto3" json:"check_region_epoch,omitempty"`
	// The peer to be checked.
	CheckPeer *metapb.Peer `protobuf:"bytes,4,opt,name=check_peer,json=checkPeer,proto3" json:"check_peer,omitempty"`
}

func (m *CheckGcPeer) Reset()         { *m = CheckGcPeer{} }
func (m *CheckGcPeer) String() string { return proto.CompactTextString(m) }
func (*CheckGcPeer) ProtoMessage()    {}
func (*CheckGcPeer) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{27}
}
func (m *CheckGcPeer) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckGcPeer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckGcPeer.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckGcPeer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckGcPeer.Merge(m, src)
}
func (m *CheckGcPeer) XXX_Size() int {
	return m.Size()
}
func (m *CheckGcPeer) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckGcPeer.DiscardUnknown(m)
}

var xxx_messageInfo_CheckGcPeer proto.InternalMessageInfo

func (m *CheckGcPeer) GetFromRegionId() uint64 {
	if m != nil {
		return m.FromRegionId
	}
	return 0
}

func (m *CheckGcPeer) GetCheckRegionId() uint64 {
	if m != nil {
		return m.CheckRegionId
	}
	return 0
}

func (m *CheckGcPeer) GetCheckRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.CheckRegionEpoch
	}
	return nil
}

func (m *CheckGcPeer) GetCheckPeer() *metapb.Peer {
	if m != nil {
		return m.CheckPeer
	}
	return nil
}

type ExtraMessage struct {
	Type ExtraMessageType `protobuf:"varint,1,opt,name=type,proto3,enum=raft_serverpb.ExtraMessageType" json:"type,omitempty"`
	// It's merge related index. In `WantRollbackMerge`, it's prepare merge index. In
	// `MsgGcPeerRequest`, it's the commit merge index. In `MsgVoterReplicatedIndexRequest`
	// it's the voter_replicated_index.
	Index uint64 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	// In `MsgCheckStalePeerResponse`, it's the peers that receiver can continue to query.
	CheckPeers []*metapb.Peer `protobuf:"bytes,3,rep,name=check_peers,json=checkPeers,proto3" json:"check_peers,omitempty"`
	WaitData   bool           `protobuf:"varint,4,opt,name=wait_data,json=waitData,proto3" json:"wait_data,omitempty"`
	// Flag for forcely wake up hibernate regions if true.
	ForcelyAwaken bool           `protobuf:"varint,5,opt,name=forcely_awaken,json=forcelyAwaken,proto3" json:"forcely_awaken,omitempty"`
	CheckGcPeer   *CheckGcPeer   `protobuf:"bytes,6,opt,name=check_gc_peer,json=checkGcPeer,proto3" json:"check_gc_peer,omitempty"`
	FlushMemtable *FlushMemtable `protobuf:"bytes,7,opt,name=flush_memtable,json=flushMemtable,proto3" json:"flush_memtable,omitempty"`
	// Used by `MsgAvailabilityRequest` and `MsgAvailabilityResponse` in v2.
	AvailabilityContext *AvailabilityContext `protobuf:"bytes,8,opt,name=availability_context,json=availabilityContext,proto3" json:"availability_context,omitempty"`
	// notice the peer to refresh buckets version
	RefreshBuckets *RefreshBuckets `protobuf:"bytes,9,opt,name=refresh_buckets,json=refreshBuckets,proto3" json:"refresh_buckets,omitempty"`
	// snap_gen_precheck_passed is used to indicate the precheck result when
	// a follower responds to a leader's snapshot gen precheck request.
	SnapGenPrecheckPassed bool `protobuf:"varint,10,opt,name=snap_gen_precheck_passed,json=snapGenPrecheckPassed,proto3" json:"snap_gen_precheck_passed,omitempty"`
}

func (m *ExtraMessage) Reset()         { *m = ExtraMessage{} }
func (m *ExtraMessage) String() string { return proto.CompactTextString(m) }
func (*ExtraMessage) ProtoMessage()    {}
func (*ExtraMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_130ebc2f2c37a342, []int{28}
}
func (m *ExtraMessage) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExtraMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExtraMessage.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExtraMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtraMessage.Merge(m, src)
}
func (m *ExtraMessage) XXX_Size() int {
	return m.Size()
}
func (m *ExtraMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtraMessage.DiscardUnknown(m)
}

var xxx_messageInfo_ExtraMessage proto.InternalMessageInfo

func (m *ExtraMessage) GetType() ExtraMessageType {
	if m != nil {
		return m.Type
	}
	return ExtraMessageType_MsgRegionWakeUp
}

func (m *ExtraMessage) GetIndex() uint64 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *ExtraMessage) GetCheckPeers() []*metapb.Peer {
	if m != nil {
		return m.CheckPeers
	}
	return nil
}

func (m *ExtraMessage) GetWaitData() bool {
	if m != nil {
		return m.WaitData
	}
	return false
}

func (m *ExtraMessage) GetForcelyAwaken() bool {
	if m != nil {
		return m.ForcelyAwaken
	}
	return false
}

func (m *ExtraMessage) GetCheckGcPeer() *CheckGcPeer {
	if m != nil {
		return m.CheckGcPeer
	}
	return nil
}

func (m *ExtraMessage) GetFlushMemtable() *FlushMemtable {
	if m != nil {
		return m.FlushMemtable
	}
	return nil
}

func (m *ExtraMessage) GetAvailabilityContext() *AvailabilityContext {
	if m != nil {
		return m.AvailabilityContext
	}
	return nil
}

func (m *ExtraMessage) GetRefreshBuckets() *RefreshBuckets {
	if m != nil {
		return m.RefreshBuckets
	}
	return nil
}

func (m *ExtraMessage) GetSnapGenPrecheckPassed() bool {
	if m != nil {
		return m.SnapGenPrecheckPassed
	}
	return false
}

func init() {
	proto.RegisterEnum("raft_serverpb.PeerState", PeerState_name, PeerState_value)
	proto.RegisterEnum("raft_serverpb.ExtraMessageType", ExtraMessageType_name, ExtraMessageType_value)
	proto.RegisterType((*RaftMessage)(nil), "raft_serverpb.RaftMessage")
	proto.RegisterType((*RaftTruncatedState)(nil), "raft_serverpb.RaftTruncatedState")
	proto.RegisterType((*SnapshotCFFile)(nil), "raft_serverpb.SnapshotCFFile")
	proto.RegisterType((*SnapshotMeta)(nil), "raft_serverpb.SnapshotMeta")
	proto.RegisterType((*SnapshotChunk)(nil), "raft_serverpb.SnapshotChunk")
	proto.RegisterType((*Done)(nil), "raft_serverpb.Done")
	proto.RegisterType((*TabletSnapshotFileMeta)(nil), "raft_serverpb.TabletSnapshotFileMeta")
	proto.RegisterType((*TabletSnapshotPreview)(nil), "raft_serverpb.TabletSnapshotPreview")
	proto.RegisterType((*TabletSnapshotFileChunk)(nil), "raft_serverpb.TabletSnapshotFileChunk")
	proto.RegisterType((*TabletSnapshotHead)(nil), "raft_serverpb.TabletSnapshotHead")
	proto.RegisterType((*TabletSnapshotEnd)(nil), "raft_serverpb.TabletSnapshotEnd")
	proto.RegisterType((*TabletSnapshotRequest)(nil), "raft_serverpb.TabletSnapshotRequest")
	proto.RegisterType((*AcceptedSnapshotFiles)(nil), "raft_serverpb.AcceptedSnapshotFiles")
	proto.RegisterType((*TabletSnapshotResponse)(nil), "raft_serverpb.TabletSnapshotResponse")
	proto.RegisterType((*KeyValue)(nil), "raft_serverpb.KeyValue")
	proto.RegisterType((*RaftSnapshotData)(nil), "raft_serverpb.RaftSnapshotData")
	proto.RegisterType((*StoreIdent)(nil), "raft_serverpb.StoreIdent")
	proto.RegisterType((*StoreRecoverState)(nil), "raft_serverpb.StoreRecoverState")
	proto.RegisterType((*RaftLocalState)(nil), "raft_serverpb.RaftLocalState")
	proto.RegisterType((*RaftApplyState)(nil), "raft_serverpb.RaftApplyState")
	proto.RegisterType((*MergeState)(nil), "raft_serverpb.MergeState")
	proto.RegisterType((*MergedRecord)(nil), "raft_serverpb.MergedRecord")
	proto.RegisterType((*RegionLocalState)(nil), "raft_serverpb.RegionLocalState")
	proto.RegisterType((*RegionSequenceNumberRelation)(nil), "raft_serverpb.RegionSequenceNumberRelation")
	proto.RegisterType((*AvailabilityContext)(nil), "raft_serverpb.AvailabilityContext")
	proto.RegisterType((*FlushMemtable)(nil), "raft_serverpb.FlushMemtable")
	proto.RegisterType((*RefreshBuckets)(nil), "raft_serverpb.RefreshBuckets")
	proto.RegisterType((*CheckGcPeer)(nil), "raft_serverpb.CheckGcPeer")
	proto.RegisterType((*ExtraMessage)(nil), "raft_serverpb.ExtraMessage")
}

func init() { proto.RegisterFile("raft_serverpb.proto", fileDescriptor_130ebc2f2c37a342) }

var fileDescriptor_130ebc2f2c37a342 = []byte{
	// 2411 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x58, 0x4b, 0x73, 0x1b, 0xc7,
	0x11, 0x26, 0x1e, 0xc4, 0xa3, 0xf1, 0x20, 0x38, 0x24, 0x25, 0x98, 0xb4, 0x28, 0x6a, 0x6d, 0xc9,
	0xb4, 0x9c, 0x50, 0x65, 0x5a, 0xb1, 0x5d, 0x4e, 0x95, 0x12, 0x92, 0x92, 0x4c, 0xc5, 0x86, 0x8a,
	0xb5, 0xa4, 0xa4, 0xca, 0x69, 0x6b, 0xb8, 0xdb, 0x58, 0x6c, 0xb0, 0x2f, 0xef, 0x0c, 0x20, 0xc1,
	0x95, 0x1f, 0x91, 0xaa, 0x5c, 0x7c, 0xcc, 0x31, 0xce, 0x2d, 0xb7, 0xfc, 0x80, 0x1c, 0x52, 0xc9,
	0xc5, 0x47, 0x1f, 0x53, 0xd2, 0x25, 0xd7, 0x1c, 0x73, 0x4b, 0xcd, 0x63, 0x81, 0x5d, 0x10, 0xa2,
	0x12, 0x9d, 0x76, 0xa6, 0x1f, 0x33, 0xdd, 0x3d, 0x5f, 0xf7, 0xf4, 0x0e, 0xac, 0x25, 0xb4, 0xcf,
	0x2d, 0x86, 0xc9, 0x18, 0x93, 0xf8, 0x7c, 0x2f, 0x4e, 0x22, 0x1e, 0x91, 0x56, 0x8e, 0xb8, 0xd9,
	0x42, 0x31, 0x4f, 0xb9, 0x9b, 0xcd, 0x00, 0x39, 0x9d, 0xce, 0x5a, 0xc3, 0x71, 0x12, 0xdb, 0xd3,
	0x69, 0xc7, 0xf1, 0xd8, 0xd0, 0x1a, 0x31, 0xea, 0xa2, 0xa6, 0x10, 0x0c, 0xed, 0x64, 0x12, 0x73,
	0x2f, 0x0a, 0xa7, 0x52, 0xeb, 0x6e, 0xe4, 0x46, 0x72, 0x78, 0x47, 0x8c, 0x34, 0x75, 0x25, 0x19,
	0x31, 0x2e, 0x87, 0x8a, 0x60, 0xfc, 0xa7, 0x04, 0x0d, 0x93, 0xf6, 0x79, 0x0f, 0x99, 0x58, 0x90,
	0x6c, 0x41, 0x3d, 0x41, 0xd7, 0x8b, 0x42, 0xcb, 0x73, 0xba, 0x85, 0x9d, 0xc2, 0x6e, 0xd9, 0xac,
	0x29, 0xc2, 0x23, 0x87, 0x7c, 0x08, 0xf5, 0x7e, 0x12, 0x05, 0x56, 0x8c, 0x98, 0x74, 0x8b, 0x3b,
	0x85, 0xdd, 0xc6, 0x7e, 0x73, 0x4f, 0x9b, 0x7a, 0x82, 0x98, 0x98, 0x35, 0xc1, 0x16, 0x23, 0x72,
	0x13, 0xaa, 0x3c, 0x52, 0x82, 0xa5, 0x05, 0x82, 0x15, 0x1e, 0x49, 0xb1, 0xdb, 0x50, 0x0d, 0xd4,
	0xce, 0xdd, 0xb2, 0x14, 0xeb, 0xec, 0xa5, 0x91, 0xd0, 0x16, 0x99, 0xa9, 0x00, 0xf9, 0x14, 0x9a,
	0xda, 0x34, 0x8c, 0x23, 0x7b, 0xd0, 0x5d, 0x96, 0x0a, 0x6b, 0xe9, 0xba, 0xa6, 0xe4, 0x3d, 0x10,
	0x2c, 0xb3, 0x91, 0xcc, 0x26, 0xe4, 0x06, 0x34, 0x3d, 0x66, 0xf1, 0x28, 0x38, 0x67, 0x3c, 0x0a,
	0xb1, 0x5b, 0xd9, 0x29, 0xec, 0xd6, 0xcc, 0x86, 0xc7, 0xce, 0x52, 0x92, 0xf0, 0x9a, 0x71, 0x9a,
	0x70, 0x6b, 0x88, 0x93, 0x6e, 0x75, 0xa7, 0xb0, 0xdb, 0x34, 0x6b, 0x92, 0xf0, 0x15, 0x4e, 0xc8,
	0x55, 0xa8, 0x62, 0xe8, 0x48, 0x56, 0x4d, 0xb2, 0x2a, 0x18, 0x3a, 0x82, 0xf1, 0x31, 0x34, 0x03,
	0x4c, 0x5c, 0xb4, 0x38, 0x4d, 0x5c, 0xe4, 0xdd, 0xba, 0x34, 0xa8, 0x9d, 0x37, 0xc8, 0x6c, 0x48,
	0x99, 0x33, 0x29, 0x42, 0x3e, 0x87, 0x3a, 0xbe, 0xe0, 0x09, 0xb5, 0x02, 0xe6, 0x76, 0x41, 0xca,
	0x6f, 0xed, 0xe5, 0xf1, 0xf1, 0x40, 0xf0, 0x53, 0xe7, 0x6b, 0x52, 0xba, 0xc7, 0x5c, 0x61, 0xa2,
	0xd2, 0xb4, 0xf9, 0x8b, 0x6e, 0x43, 0x99, 0x28, 0x09, 0x47, 0xfc, 0x05, 0xb9, 0x0b, 0x30, 0x03,
	0x45, 0xb7, 0xb9, 0x53, 0xd8, 0x6d, 0xef, 0x6f, 0xec, 0x65, 0x70, 0x72, 0xdf, 0x63, 0xc3, 0x27,
	0x72, 0xc5, 0xba, 0x93, 0x0e, 0x8d, 0x7b, 0x40, 0xc4, 0xd1, 0x9f, 0x25, 0xa3, 0xd0, 0xa6, 0x1c,
	0x9d, 0x53, 0x4e, 0x39, 0x92, 0x75, 0x58, 0xf6, 0x42, 0x07, 0x5f, 0xe8, 0xd3, 0x57, 0x13, 0x42,
	0xa0, 0xcc, 0x31, 0x09, 0xe4, 0xa9, 0x97, 0x4d, 0x39, 0x36, 0x4e, 0xa0, 0x7d, 0x1a, 0xd2, 0x98,
	0x0d, 0x22, 0x7e, 0xf4, 0xf0, 0xa1, 0xe7, 0x23, 0x69, 0x43, 0xd1, 0xee, 0x4b, 0xc5, 0xba, 0x59,
	0xb4, 0xfb, 0x42, 0x8b, 0x79, 0xdf, 0x62, 0xaa, 0x25, 0xc6, 0x64, 0x13, 0x6a, 0xf6, 0x00, 0xed,
	0x21, 0x1b, 0x05, 0x12, 0x1a, 0x2d, 0x73, 0x3a, 0x37, 0xfe, 0x54, 0x84, 0x66, 0xba, 0x64, 0x0f,
	0x39, 0x25, 0x9f, 0x43, 0xcd, 0xee, 0x5b, 0x7d, 0xcf, 0x47, 0xd6, 0x2d, 0xec, 0x94, 0x76, 0x1b,
	0xfb, 0xd7, 0xe6, 0xc2, 0x95, 0xb7, 0xc0, 0xac, 0xda, 0x7d, 0xf1, 0x65, 0xe4, 0x3a, 0x34, 0xfa,
	0x51, 0x62, 0x9d, 0x53, 0x9f, 0x86, 0xb6, 0xb2, 0xa0, 0x66, 0x42, 0x3f, 0x4a, 0x0e, 0x15, 0x25,
	0x15, 0x78, 0xee, 0xf1, 0x10, 0x19, 0x93, 0xa6, 0x28, 0x81, 0x67, 0x8a, 0x22, 0x02, 0x21, 0x31,
	0x20, 0x91, 0x59, 0x36, 0xd5, 0x84, 0xec, 0xc3, 0x86, 0x8b, 0x21, 0x26, 0x94, 0xa3, 0xe5, 0x8c,
	0x12, 0x2a, 0x92, 0xce, 0x62, 0x68, 0x4b, 0x38, 0x96, 0xcd, 0xb5, 0x94, 0x79, 0x5f, 0xf3, 0x4e,
	0xd1, 0x26, 0xbb, 0xd0, 0xe1, 0xf4, 0xdc, 0x47, 0x6e, 0xb1, 0x90, 0xc6, 0x56, 0x4c, 0xf9, 0x40,
	0xa2, 0xb0, 0x6e, 0xb6, 0x15, 0x5d, 0x38, 0x71, 0x42, 0xf9, 0x80, 0xdc, 0x86, 0x55, 0x3b, 0x0a,
	0x02, 0x8f, 0x5b, 0x32, 0xec, 0xd6, 0xc0, 0x0b, 0xb9, 0x04, 0x64, 0xd9, 0x5c, 0x51, 0x8c, 0x47,
	0x82, 0x7e, 0xec, 0x85, 0xdc, 0xf8, 0x35, 0xb4, 0xa6, 0xce, 0x0f, 0x46, 0xe1, 0x90, 0xdc, 0x9d,
	0x25, 0x53, 0x41, 0x42, 0x6b, 0x73, 0x2e, 0x56, 0x99, 0x44, 0x9f, 0xa5, 0x15, 0x81, 0xb2, 0x43,
	0x39, 0x95, 0x11, 0x6a, 0x9a, 0x72, 0x6c, 0x54, 0xa0, 0x7c, 0x3f, 0x0a, 0xd1, 0xf8, 0xae, 0x00,
	0x57, 0xce, 0xa6, 0x16, 0x8a, 0x9d, 0x44, 0x70, 0xe5, 0xc9, 0x6c, 0x41, 0x5d, 0x1c, 0x8b, 0x25,
	0xcf, 0x57, 0x17, 0x0a, 0x41, 0x38, 0x15, 0x67, 0x9c, 0x32, 0x43, 0x1a, 0xa8, 0xd0, 0xd7, 0x15,
	0xf3, 0x31, 0x0d, 0x90, 0x5c, 0x03, 0x18, 0x20, 0x75, 0x2c, 0x5b, 0x18, 0x2d, 0xe3, 0xde, 0x34,
	0xeb, 0x82, 0xa2, 0xbc, 0xb8, 0x09, 0x6d, 0x9e, 0x50, 0xcf, 0xf7, 0x42, 0x57, 0x8b, 0x94, 0xa5,
	0x48, 0x2b, 0xa5, 0x4a, 0x31, 0xa3, 0x0f, 0x1b, 0x79, 0xcb, 0x4e, 0x12, 0x1c, 0x7b, 0xf8, 0x9c,
	0xfc, 0x1c, 0x96, 0x45, 0x02, 0xa6, 0x78, 0xb9, 0x39, 0x17, 0x83, 0xc5, 0xee, 0x98, 0x4a, 0x87,
	0x74, 0xa0, 0x84, 0xa1, 0xa3, 0xd1, 0x22, 0x86, 0xc6, 0x1f, 0x0a, 0x70, 0xf5, 0xa2, 0x8e, 0x32,
	0xf5, 0xed, 0x63, 0x90, 0x06, 0xbd, 0x34, 0x0b, 0xba, 0x48, 0x1e, 0x6f, 0xac, 0x9d, 0x2d, 0x7a,
	0x63, 0xf2, 0x01, 0x94, 0x44, 0xcd, 0x51, 0x65, 0x6e, 0x63, 0x2f, 0x57, 0xe3, 0xef, 0x53, 0x4e,
	0xbf, 0xc2, 0x89, 0x29, 0x24, 0x0c, 0x17, 0x48, 0xde, 0xc2, 0x63, 0xa4, 0xce, 0x5b, 0xa2, 0x61,
	0x0b, 0xea, 0x23, 0x86, 0x96, 0x4d, 0xed, 0x41, 0x9a, 0x34, 0xb5, 0x11, 0xc3, 0x23, 0x31, 0x37,
	0xee, 0xc0, 0x6a, 0x7e, 0xa3, 0x07, 0xa1, 0x93, 0xcb, 0x67, 0x1d, 0x83, 0x69, 0x3e, 0xff, 0xbe,
	0x38, 0x7f, 0x4a, 0x26, 0x7e, 0x33, 0x42, 0xc6, 0xc9, 0x67, 0x50, 0x16, 0x47, 0xae, 0x4d, 0xbb,
	0x71, 0xe9, 0x21, 0x09, 0x77, 0x8e, 0x97, 0x4c, 0xa9, 0x40, 0x7e, 0x09, 0xd5, 0x58, 0x9d, 0xb4,
	0xbe, 0x81, 0xde, 0xbf, 0x54, 0x57, 0xa3, 0xe2, 0x78, 0xc9, 0x4c, 0xd5, 0xc8, 0x3d, 0x58, 0x9e,
	0x41, 0xaf, 0xb1, 0x7f, 0xeb, 0x8d, 0x00, 0x91, 0x87, 0x7d, 0xbc, 0x64, 0x2a, 0x35, 0x72, 0x57,
	0x61, 0x44, 0xdd, 0x57, 0x3b, 0x97, 0x6a, 0x3f, 0x08, 0x85, 0xe1, 0x42, 0xfc, 0xb0, 0x0e, 0xd5,
	0x98, 0x4e, 0xfc, 0x88, 0x3a, 0xc6, 0x5d, 0xd8, 0x38, 0xb0, 0x6d, 0x8c, 0x45, 0xc9, 0xcd, 0x6c,
	0xc3, 0xf2, 0x90, 0x11, 0xf0, 0xcd, 0x40, 0xc6, 0x38, 0x9b, 0x4f, 0x45, 0x13, 0x59, 0x1c, 0x85,
	0x0c, 0xc9, 0x17, 0xb0, 0x9c, 0x56, 0xc8, 0x45, 0x01, 0x59, 0xb8, 0x97, 0xa9, 0x54, 0x8c, 0x7d,
	0xa8, 0x7d, 0x85, 0x93, 0xa7, 0xd4, 0x1f, 0xa1, 0x00, 0xbf, 0x00, 0x5c, 0x41, 0x22, 0x50, 0x0c,
	0x45, 0x09, 0x1c, 0x0b, 0x96, 0x2e, 0x0e, 0x6a, 0x62, 0xfc, 0xbd, 0x08, 0x1d, 0x01, 0x9e, 0x74,
	0x41, 0x01, 0x46, 0x72, 0x0b, 0x2a, 0xea, 0xd2, 0xd5, 0x56, 0xcc, 0x5f, 0x83, 0x9a, 0x9b, 0xcf,
	0x99, 0xe2, 0x5c, 0xce, 0x7c, 0x34, 0x4d, 0x0b, 0x91, 0xba, 0x57, 0xe7, 0x1c, 0x49, 0x0d, 0xd5,
	0xf9, 0xd2, 0x85, 0xea, 0x18, 0x13, 0x26, 0xb6, 0x54, 0x15, 0x3a, 0x9d, 0x92, 0x3b, 0x50, 0x16,
	0x9b, 0xeb, 0xd4, 0xd9, 0x7a, 0xcd, 0x8d, 0x21, 0xf3, 0x5e, 0x0a, 0x92, 0x9f, 0xc1, 0x4a, 0x82,
	0x41, 0x34, 0x46, 0xc7, 0x4a, 0xd0, 0x8e, 0x12, 0x87, 0x75, 0x2b, 0xd2, 0x84, 0x7c, 0xd7, 0xd2,
	0xd6, 0x42, 0xa6, 0x92, 0x21, 0x87, 0xd0, 0x96, 0x97, 0xfb, 0x4c, 0xab, 0x2a, 0xb5, 0xe6, 0x77,
	0xec, 0x49, 0x21, 0xa5, 0x65, 0xb6, 0x82, 0xcc, 0x8c, 0x19, 0xbf, 0x05, 0x38, 0xe5, 0x51, 0x82,
	0x8f, 0x1c, 0x0c, 0xb9, 0xa8, 0x8d, 0xb6, 0x3f, 0x62, 0x1c, 0x93, 0x59, 0xff, 0x55, 0xd7, 0x94,
	0x47, 0x0e, 0x79, 0x07, 0x6a, 0x4c, 0x08, 0x0b, 0xa6, 0x8a, 0x5d, 0x95, 0x29, 0x65, 0x72, 0x17,
	0x1a, 0x34, 0xf6, 0xac, 0x34, 0x22, 0x25, 0xd9, 0x03, 0xac, 0xed, 0xa5, 0xad, 0xe3, 0xc1, 0xc9,
	0xa3, 0xa7, 0x8a, 0x65, 0x02, 0x8d, 0x3d, 0x3d, 0x36, 0x3e, 0x84, 0x55, 0xb9, 0xbb, 0xb0, 0x66,
	0x8c, 0xc9, 0xb4, 0x03, 0x60, 0xf8, 0x4d, 0x18, 0xa5, 0x1d, 0x80, 0x9c, 0x18, 0xe7, 0xd0, 0x16,
	0x87, 0xfe, 0x75, 0x64, 0x53, 0x5f, 0xc9, 0x7d, 0x0c, 0x30, 0xa0, 0x89, 0x63, 0x31, 0x31, 0xd3,
	0xc7, 0x4e, 0xa6, 0xfd, 0xdb, 0x31, 0x4d, 0x54, 0x47, 0x61, 0xd6, 0x07, 0xe9, 0x50, 0xf8, 0xe7,
	0x53, 0xa6, 0x6f, 0x37, 0xed, 0x42, 0x5d, 0x50, 0xe4, 0xb5, 0x66, 0xfc, 0xbb, 0xa0, 0x36, 0x39,
	0x88, 0x63, 0x7f, 0xa2, 0x34, 0xde, 0x83, 0x16, 0x8d, 0x63, 0xdf, 0x43, 0xc7, 0xca, 0xb6, 0x25,
	0x4d, 0x4d, 0x94, 0x7a, 0xe2, 0xda, 0x94, 0xcb, 0x66, 0xef, 0x4e, 0x19, 0x82, 0xb2, 0xb9, 0x22,
	0x18, 0x47, 0xb3, 0xab, 0x53, 0xb4, 0x83, 0x39, 0x31, 0x85, 0x9d, 0x46, 0xe6, 0x76, 0x15, 0xad,
	0x81, 0x16, 0x91, 0x3d, 0x8f, 0xba, 0xd9, 0x41, 0x91, 0xce, 0x30, 0x09, 0xc8, 0xaf, 0x60, 0x85,
	0xa7, 0x5d, 0x93, 0x76, 0xbf, 0xb8, 0xb0, 0x90, 0x5d, 0xec, 0xaf, 0xcc, 0x36, 0xcf, 0xcd, 0x0d,
	0x0f, 0x40, 0xe2, 0x43, 0xb9, 0xbb, 0x05, 0xf5, 0xc0, 0x0b, 0x73, 0xae, 0xd6, 0x02, 0x2f, 0x54,
	0x76, 0xdd, 0x82, 0x8a, 0x6e, 0x35, 0x8b, 0x8b, 0x73, 0x4c, 0x71, 0xc9, 0x15, 0xa8, 0x28, 0x63,
	0x75, 0x0c, 0xf4, 0xcc, 0xf8, 0x73, 0x09, 0x9a, 0x59, 0x2c, 0x8a, 0xc6, 0x84, 0x45, 0xa3, 0xc4,
	0x46, 0x6b, 0xbe, 0xe9, 0x6f, 0x2b, 0xba, 0x99, 0xb6, 0xfe, 0x9f, 0x42, 0x53, 0x4b, 0xaa, 0xe6,
	0xbb, 0x78, 0x49, 0xf3, 0xad, 0x04, 0x55, 0xf3, 0x7d, 0x67, 0xaa, 0x27, 0xfe, 0x05, 0x98, 0xce,
	0xec, 0x7c, 0x5a, 0x69, 0x05, 0x31, 0x16, 0x39, 0x75, 0x65, 0x6a, 0x52, 0x3e, 0x23, 0xeb, 0x0b,
	0x54, 0xd7, 0x53, 0x33, 0x73, 0x79, 0x29, 0xfb, 0x2d, 0x11, 0x89, 0x8c, 0x5b, 0xea, 0x98, 0xdb,
	0x8a, 0x9e, 0x75, 0x4b, 0x4b, 0xbe, 0xf9, 0x9f, 0x42, 0x09, 0x4e, 0xdd, 0xd2, 0x7a, 0xca, 0xad,
	0x45, 0xd5, 0x42, 0x2b, 0x28, 0xb7, 0xa6, 0x5d, 0x75, 0x35, 0xdb, 0x55, 0xdf, 0x98, 0x46, 0x47,
	0x31, 0x6b, 0x0a, 0x8b, 0x8a, 0xa6, 0x52, 0xe2, 0xaf, 0xa2, 0xd8, 0x4a, 0x33, 0x32, 0x99, 0xb7,
	0x27, 0x5b, 0x53, 0x9d, 0x74, 0xed, 0xfd, 0xee, 0x1c, 0xea, 0xc4, 0x96, 0x0a, 0x6c, 0x4a, 0x2c,
	0x53, 0x9c, 0x8b, 0x97, 0x16, 0xe7, 0x2f, 0x40, 0xfd, 0xad, 0x68, 0x4c, 0xab, 0x0b, 0xf2, 0x9d,
	0x45, 0xd5, 0x4c, 0x2d, 0x0f, 0xc1, 0x0c, 0xb9, 0x37, 0x44, 0x48, 0x64, 0x93, 0x9b, 0xcb, 0x2b,
	0x45, 0x53, 0xf8, 0x5d, 0x50, 0x66, 0x97, 0xdf, 0xaa, 0xcc, 0x56, 0xfe, 0xef, 0x32, 0xfb, 0xaf,
	0x02, 0xbc, 0xab, 0x9c, 0x3d, 0x15, 0x1d, 0x48, 0x68, 0xe3, 0xe3, 0x51, 0x70, 0x8e, 0x89, 0x89,
	0xbe, 0x6c, 0xd3, 0x2f, 0xff, 0xf1, 0xfd, 0x00, 0x56, 0x98, 0x56, 0xb3, 0x42, 0xa9, 0xa7, 0x6b,
	0x57, 0x9b, 0xe5, 0x56, 0x23, 0xf7, 0x44, 0x15, 0x8e, 0xfd, 0x49, 0x2e, 0x80, 0xd7, 0x16, 0x14,
	0x85, 0x59, 0x85, 0x13, 0xf5, 0x78, 0x5a, 0xed, 0x0e, 0xa7, 0xff, 0xb8, 0x6a, 0x01, 0xd5, 0x64,
	0x5c, 0x9f, 0x5f, 0x60, 0x0e, 0x0f, 0xe9, 0xff, 0xae, 0x2a, 0x28, 0x7f, 0x29, 0xc0, 0xda, 0xc1,
	0x98, 0x7a, 0x3e, 0x3d, 0xf7, 0x7c, 0x8f, 0x4f, 0x8e, 0xa2, 0x90, 0xe3, 0x0b, 0x4e, 0xde, 0x87,
	0xb6, 0xfc, 0x7b, 0x9f, 0x77, 0xb3, 0x29, 0xa8, 0xd3, 0x8c, 0xf8, 0x05, 0xac, 0x66, 0xa5, 0xde,
	0x98, 0xed, 0x2b, 0x33, 0x6d, 0x95, 0x1a, 0x3b, 0xd0, 0x18, 0x85, 0x54, 0xed, 0xef, 0xa3, 0xfe,
	0xaf, 0xca, 0x92, 0xc4, 0xc5, 0xcd, 0x13, 0x2f, 0x08, 0x50, 0x65, 0x65, 0xcd, 0x4c, 0xa7, 0xc6,
	0x4f, 0xa0, 0xf5, 0xd0, 0x1f, 0xb1, 0x41, 0x0f, 0x03, 0x89, 0x9b, 0x4b, 0x4f, 0xc5, 0x38, 0x83,
	0xb6, 0x89, 0xfd, 0x04, 0xd9, 0xe0, 0x70, 0x64, 0x0f, 0x91, 0xb3, 0x6c, 0x4b, 0x50, 0xc8, 0xb7,
	0x04, 0x04, 0xca, 0x43, 0x9c, 0xb0, 0x6e, 0x71, 0xa7, 0x24, 0x1a, 0x6e, 0x31, 0x96, 0xf7, 0x9c,
	0xf7, 0x2d, 0xaa, 0xa2, 0x24, 0xee, 0x39, 0x31, 0x31, 0xfe, 0x51, 0x80, 0xc6, 0x91, 0x68, 0x60,
	0xbf, 0xb4, 0xe5, 0x13, 0xc5, 0xff, 0x16, 0xb6, 0x5b, 0xb0, 0x22, 0xbb, 0xde, 0x8c, 0x98, 0x42,
	0x48, 0x4b, 0x92, 0xa7, 0x72, 0x07, 0x40, 0x72, 0x72, 0x2a, 0xbe, 0xa5, 0xd7, 0xc7, 0xb7, 0x93,
	0xd1, 0x57, 0x01, 0xfe, 0x08, 0x40, 0x2d, 0x21, 0x5f, 0x57, 0xca, 0x0b, 0x5e, 0x57, 0xea, 0x92,
	0x2f, 0x86, 0xc6, 0xf7, 0x65, 0x68, 0x66, 0x5f, 0x14, 0xc8, 0x27, 0x50, 0xe6, 0x93, 0x38, 0xad,
	0x1c, 0xd7, 0x2f, 0x79, 0x7c, 0x38, 0x9b, 0xc4, 0x68, 0x4a, 0xe1, 0x59, 0xf5, 0x2a, 0x66, 0xab,
	0xd7, 0x4f, 0xa1, 0x31, 0x33, 0x64, 0x71, 0x69, 0x87, 0xa9, 0x25, 0xb2, 0xbb, 0x7d, 0x4e, 0x3d,
	0x6e, 0xc9, 0x0e, 0x4f, 0x1d, 0x7c, 0x4d, 0x10, 0x64, 0xfb, 0x78, 0x13, 0xda, 0xfd, 0x28, 0xb1,
	0xd1, 0x9f, 0x58, 0xf4, 0x39, 0x1d, 0x62, 0x28, 0x4b, 0x71, 0xcd, 0x6c, 0x69, 0xea, 0x81, 0x24,
	0x92, 0x7b, 0xa0, 0xe2, 0x69, 0xb9, 0xb6, 0x72, 0xbf, 0xb2, 0xf0, 0xd7, 0x26, 0x73, 0x7e, 0xa6,
	0xb2, 0x51, 0x1f, 0xe6, 0x11, 0xb4, 0xfb, 0x02, 0x60, 0x56, 0xa0, 0x11, 0x26, 0xeb, 0x71, 0x63,
	0xff, 0xdd, 0xb9, 0x05, 0x72, 0x28, 0x34, 0x5b, 0xfd, 0x1c, 0x28, 0x9f, 0xc0, 0x3a, 0xcd, 0xe4,
	0x97, 0x65, 0xab, 0x04, 0x93, 0xd5, 0xbb, 0xb1, 0x6f, 0xcc, 0xb7, 0xdf, 0x17, 0x53, 0xd1, 0x5c,
	0xa3, 0x0b, 0xf2, 0xf3, 0xa1, 0xa8, 0x8e, 0x12, 0xce, 0xd6, 0xb9, 0xc2, 0xb3, 0x7e, 0x51, 0xba,
	0x50, 0x3f, 0x72, 0xa0, 0x17, 0xe5, 0x32, 0x97, 0x04, 0x9f, 0x41, 0x57, 0x3e, 0x33, 0xb8, 0x18,
	0x5a, 0x71, 0x82, 0xfa, 0x88, 0x28, 0x63, 0xe8, 0xc8, 0x27, 0xa7, 0x9a, 0xb9, 0x21, 0xf8, 0x5f,
	0x62, 0x78, 0xa2, 0xb9, 0x27, 0x92, 0x79, 0xdb, 0x84, 0xfa, 0xf4, 0xe6, 0x20, 0x00, 0x95, 0xc7,
	0x51, 0x12, 0x50, 0xbf, 0xb3, 0x44, 0x9a, 0x50, 0x93, 0xf5, 0xca, 0x0b, 0xdd, 0x4e, 0x81, 0xb4,
	0xa0, 0x3e, 0x7d, 0x39, 0xeb, 0x14, 0x49, 0x03, 0xaa, 0xa2, 0xf0, 0x0a, 0x5e, 0x89, 0xac, 0x40,
	0xe3, 0xc9, 0x2c, 0xd3, 0x3b, 0xe5, 0xdb, 0xdf, 0x97, 0xa1, 0x33, 0x0f, 0x2a, 0xb2, 0x06, 0x2b,
	0x3d, 0xe6, 0x2a, 0x4c, 0x3f, 0xa3, 0x43, 0x7c, 0x12, 0x77, 0x96, 0x48, 0x17, 0xd6, 0x7b, 0xcc,
	0x7d, 0x46, 0x43, 0x6e, 0x46, 0xbe, 0x7f, 0x4e, 0xed, 0xa1, 0xac, 0xe7, 0x9d, 0x02, 0xd9, 0x80,
	0xd5, 0x1e, 0x73, 0xe5, 0x99, 0x9e, 0x72, 0xea, 0xcb, 0x46, 0xa1, 0x53, 0x24, 0xd7, 0xe0, 0x9d,
	0x0b, 0xe4, 0xf4, 0x9f, 0xa8, 0x53, 0x22, 0x57, 0x61, 0xad, 0xc7, 0xdc, 0x63, 0xef, 0x1c, 0x93,
	0x50, 0xd4, 0x48, 0xf5, 0xe3, 0xd9, 0x29, 0xeb, 0x8d, 0x32, 0x0c, 0xad, 0xb2, 0x4c, 0x3e, 0x80,
	0xf7, 0xa4, 0x5d, 0xbf, 0x41, 0x9b, 0xab, 0x5e, 0xd7, 0x3d, 0xa2, 0x23, 0x86, 0xce, 0xe1, 0xa4,
	0x87, 0x41, 0x94, 0x4c, 0xe4, 0xbb, 0x59, 0xa7, 0x42, 0x36, 0xe1, 0x4a, 0x8f, 0xb9, 0xd9, 0x93,
	0x4d, 0x97, 0xaf, 0x92, 0x2d, 0xb8, 0x7a, 0x81, 0xa7, 0x77, 0xa8, 0x11, 0x03, 0xb6, 0x7b, 0xcc,
	0x7d, 0x1a, 0x71, 0x61, 0x6a, 0xec, 0x7b, 0xb2, 0x0f, 0x94, 0x97, 0x63, 0xba, 0x40, 0x9d, 0xbc,
	0x07, 0xd7, 0x5f, 0x2b, 0xa3, 0x17, 0x02, 0xb2, 0x0e, 0x9d, 0x1e, 0x73, 0x35, 0xc4, 0xb5, 0x6a,
	0x43, 0x47, 0x2a, 0xa5, 0x6a, 0xe1, 0xa6, 0x16, 0xce, 0x61, 0xba, 0xd3, 0xd2, 0xc2, 0x79, 0x30,
	0x75, 0xda, 0x3a, 0xac, 0xa7, 0x79, 0x84, 0xa4, 0x5b, 0xac, 0x90, 0x6d, 0xd8, 0x5c, 0xc4, 0xd6,
	0x7b, 0x75, 0xb4, 0xfb, 0x27, 0x09, 0x7e, 0x1d, 0x51, 0x47, 0xb7, 0x17, 0x5a, 0x79, 0x95, 0xbc,
	0x0b, 0xdd, 0x8b, 0x4c, 0xad, 0x4a, 0x0e, 0xf7, 0x7f, 0xfc, 0x63, 0xad, 0xf0, 0xb7, 0x97, 0xdb,
	0x85, 0x1f, 0x5e, 0x6e, 0x17, 0xfe, 0xf9, 0x72, 0xbb, 0xf0, 0xbb, 0x57, 0xdb, 0x4b, 0xdf, 0xbd,
	0xda, 0x5e, 0xfa, 0xe1, 0xd5, 0xf6, 0xd2, 0x8f, 0xaf, 0xb6, 0x97, 0xa0, 0x13, 0x25, 0xee, 0x1e,
	0xf7, 0x86, 0xe3, 0xbd, 0xe1, 0x58, 0xbe, 0x5f, 0x9f, 0x57, 0xe4, 0xe7, 0x93, 0xff, 0x06, 0x00,
	0x00, 0xff, 0xff, 0xa8, 0xae, 0xc8, 0xaf, 0x65, 0x17, 0x00, 0x00,
}

func (m *RaftMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftMessage) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftMessage) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DiskUsage != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.DiskUsage))
		i--
		dAtA[i] = 0x60
	}
	if len(m.ExtraCtx) > 0 {
		i -= len(m.ExtraCtx)
		copy(dAtA[i:], m.ExtraCtx)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.ExtraCtx)))
		i--
		dAtA[i] = 0x5a
	}
	if m.ExtraMsg != nil {
		{
			size, err := m.ExtraMsg.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x52
	}
	if m.MergeTarget != nil {
		{
			size, err := m.MergeTarget.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x3a
	}
	if m.IsTombstone {
		i--
		if m.IsTombstone {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	if m.RegionEpoch != nil {
		{
			size, err := m.RegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.Message != nil {
		{
			size, err := m.Message.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.ToPeer != nil {
		{
			size, err := m.ToPeer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.FromPeer != nil {
		{
			size, err := m.FromPeer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.RegionId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RaftTruncatedState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftTruncatedState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftTruncatedState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Term != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Term))
		i--
		dAtA[i] = 0x10
	}
	if m.Index != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Index))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SnapshotCFFile) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotCFFile) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotCFFile) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Checksum != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Checksum))
		i--
		dAtA[i] = 0x18
	}
	if m.Size_ != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Size_))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SnapshotMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CommitIndexHint != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.CommitIndexHint))
		i--
		dAtA[i] = 0x38
	}
	if len(m.TabletSnapPath) > 0 {
		i -= len(m.TabletSnapPath)
		copy(dAtA[i:], m.TabletSnapPath)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.TabletSnapPath)))
		i--
		dAtA[i] = 0x32
	}
	if m.GenerateDurationSec != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.GenerateDurationSec))
		i--
		dAtA[i] = 0x28
	}
	if m.Start != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Start))
		i--
		dAtA[i] = 0x20
	}
	if m.ForWitness {
		i--
		if m.ForWitness {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.ForBalance {
		i--
		if m.ForBalance {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.CfFiles) > 0 {
		for iNdEx := len(m.CfFiles) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.CfFiles[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SnapshotChunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SnapshotChunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SnapshotChunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x12
	}
	if m.Message != nil {
		{
			size, err := m.Message.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Done) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Done) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Done) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *TabletSnapshotFileMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TabletSnapshotFileMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotFileMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TrailingChunk) > 0 {
		i -= len(m.TrailingChunk)
		copy(dAtA[i:], m.TrailingChunk)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.TrailingChunk)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.HeadChunk) > 0 {
		i -= len(m.HeadChunk)
		copy(dAtA[i:], m.HeadChunk)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.HeadChunk)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.FileName) > 0 {
		i -= len(m.FileName)
		copy(dAtA[i:], m.FileName)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.FileName)))
		i--
		dAtA[i] = 0x12
	}
	if m.FileSize != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.FileSize))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TabletSnapshotPreview) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TabletSnapshotPreview) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotPreview) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.End {
		i--
		if m.End {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.Metas) > 0 {
		for iNdEx := len(m.Metas) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Metas[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TabletSnapshotFileChunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TabletSnapshotFileChunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotFileChunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Key != nil {
		{
			size, err := m.Key.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Iv) > 0 {
		i -= len(m.Iv)
		copy(dAtA[i:], m.Iv)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Iv)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.FileName) > 0 {
		i -= len(m.FileName)
		copy(dAtA[i:], m.FileName)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.FileName)))
		i--
		dAtA[i] = 0x12
	}
	if m.FileSize != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.FileSize))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TabletSnapshotHead) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TabletSnapshotHead) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotHead) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.UseCache {
		i--
		if m.UseCache {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.Message != nil {
		{
			size, err := m.Message.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TabletSnapshotEnd) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TabletSnapshotEnd) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotEnd) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Checksum != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Checksum))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TabletSnapshotRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TabletSnapshotRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Payload != nil {
		{
			size := m.Payload.Size()
			i -= size
			if _, err := m.Payload.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *TabletSnapshotRequest_Head) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotRequest_Head) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Head != nil {
		{
			size, err := m.Head.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *TabletSnapshotRequest_Preview) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotRequest_Preview) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Preview != nil {
		{
			size, err := m.Preview.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *TabletSnapshotRequest_Chunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotRequest_Chunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Chunk != nil {
		{
			size, err := m.Chunk.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *TabletSnapshotRequest_End) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotRequest_End) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.End != nil {
		{
			size, err := m.End.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	return len(dAtA) - i, nil
}
func (m *AcceptedSnapshotFiles) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AcceptedSnapshotFiles) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AcceptedSnapshotFiles) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.FileName) > 0 {
		for iNdEx := len(m.FileName) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.FileName[iNdEx])
			copy(dAtA[i:], m.FileName[iNdEx])
			i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.FileName[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TabletSnapshotResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TabletSnapshotResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TabletSnapshotResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Files != nil {
		{
			size, err := m.Files.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *KeyValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeyValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KeyValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RaftSnapshotData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftSnapshotData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftSnapshotData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.MergedRecords) > 0 {
		for iNdEx := len(m.MergedRecords) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MergedRecords[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x3a
		}
	}
	if len(m.RemovedRecords) > 0 {
		for iNdEx := len(m.RemovedRecords) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RemovedRecords[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.Version != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Data) > 0 {
		for iNdEx := len(m.Data) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Data[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.FileSize != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.FileSize))
		i--
		dAtA[i] = 0x10
	}
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StoreIdent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreIdent) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreIdent) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ApiVersion != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.ApiVersion))
		i--
		dAtA[i] = 0x18
	}
	if m.StoreId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x10
	}
	if m.ClusterId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.ClusterId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *StoreRecoverState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreRecoverState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreRecoverState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Seqno != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Seqno))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RaftLocalState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftLocalState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftLocalState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LastIndex != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.LastIndex))
		i--
		dAtA[i] = 0x10
	}
	if m.HardState != nil {
		{
			size, err := m.HardState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RaftApplyState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RaftApplyState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RaftApplyState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CommitTerm != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.CommitTerm))
		i--
		dAtA[i] = 0x28
	}
	if m.CommitIndex != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.CommitIndex))
		i--
		dAtA[i] = 0x20
	}
	if m.LastCommitIndex != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.LastCommitIndex))
		i--
		dAtA[i] = 0x18
	}
	if m.TruncatedState != nil {
		{
			size, err := m.TruncatedState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.AppliedIndex != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.AppliedIndex))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MergeState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MergeState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MergeState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Commit != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Commit))
		i--
		dAtA[i] = 0x18
	}
	if m.Target != nil {
		{
			size, err := m.Target.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.MinIndex != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.MinIndex))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MergedRecord) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MergedRecord) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MergedRecord) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SourceRemovedRecords) > 0 {
		for iNdEx := len(m.SourceRemovedRecords) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.SourceRemovedRecords[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x4a
		}
	}
	if m.SourceIndex != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.SourceIndex))
		i--
		dAtA[i] = 0x40
	}
	if m.Index != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Index))
		i--
		dAtA[i] = 0x38
	}
	if len(m.TargetPeers) > 0 {
		for iNdEx := len(m.TargetPeers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TargetPeers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if m.TargetEpoch != nil {
		{
			size, err := m.TargetEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.TargetRegionId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.TargetRegionId))
		i--
		dAtA[i] = 0x20
	}
	if len(m.SourcePeers) > 0 {
		for iNdEx := len(m.SourcePeers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.SourcePeers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.SourceEpoch != nil {
		{
			size, err := m.SourceEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.SourceRegionId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.SourceRegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionLocalState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionLocalState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionLocalState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.MergedRecords) > 0 {
		for iNdEx := len(m.MergedRecords) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MergedRecords[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.RemovedRecords) > 0 {
		for iNdEx := len(m.RemovedRecords) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RemovedRecords[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.TabletIndex != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.TabletIndex))
		i--
		dAtA[i] = 0x20
	}
	if m.MergeState != nil {
		{
			size, err := m.MergeState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Region != nil {
		{
			size, err := m.Region.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.State != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.State))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionSequenceNumberRelation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionSequenceNumberRelation) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionSequenceNumberRelation) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionState != nil {
		{
			size, err := m.RegionState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.ApplyState != nil {
		{
			size, err := m.ApplyState.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.SequenceNumber != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.SequenceNumber))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *AvailabilityContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AvailabilityContext) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *AvailabilityContext) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Trimmed {
		i--
		if m.Trimmed {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if m.Unavailable {
		i--
		if m.Unavailable {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.FromRegionEpoch != nil {
		{
			size, err := m.FromRegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.FromRegionId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.FromRegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *FlushMemtable) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FlushMemtable) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FlushMemtable) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RegionId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RefreshBuckets) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RefreshBuckets) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RefreshBuckets) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Sizes) > 0 {
		dAtA28 := make([]byte, len(m.Sizes)*10)
		var j27 int
		for _, num := range m.Sizes {
			for num >= 1<<7 {
				dAtA28[j27] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j27++
			}
			dAtA28[j27] = uint8(num)
			j27++
		}
		i -= j27
		copy(dAtA[i:], dAtA28[:j27])
		i = encodeVarintRaftServerpb(dAtA, i, uint64(j27))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Keys) > 0 {
		for iNdEx := len(m.Keys) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Keys[iNdEx])
			copy(dAtA[i:], m.Keys[iNdEx])
			i = encodeVarintRaftServerpb(dAtA, i, uint64(len(m.Keys[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Version != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CheckGcPeer) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckGcPeer) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckGcPeer) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CheckPeer != nil {
		{
			size, err := m.CheckPeer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.CheckRegionEpoch != nil {
		{
			size, err := m.CheckRegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.CheckRegionId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.CheckRegionId))
		i--
		dAtA[i] = 0x10
	}
	if m.FromRegionId != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.FromRegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ExtraMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExtraMessage) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExtraMessage) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SnapGenPrecheckPassed {
		i--
		if m.SnapGenPrecheckPassed {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x50
	}
	if m.RefreshBuckets != nil {
		{
			size, err := m.RefreshBuckets.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.AvailabilityContext != nil {
		{
			size, err := m.AvailabilityContext.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.FlushMemtable != nil {
		{
			size, err := m.FlushMemtable.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.CheckGcPeer != nil {
		{
			size, err := m.CheckGcPeer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.ForcelyAwaken {
		i--
		if m.ForcelyAwaken {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if m.WaitData {
		i--
		if m.WaitData {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if len(m.CheckPeers) > 0 {
		for iNdEx := len(m.CheckPeers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.CheckPeers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintRaftServerpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Index != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Index))
		i--
		dAtA[i] = 0x10
	}
	if m.Type != 0 {
		i = encodeVarintRaftServerpb(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintRaftServerpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovRaftServerpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *RaftMessage) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.RegionId))
	}
	if m.FromPeer != nil {
		l = m.FromPeer.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.ToPeer != nil {
		l = m.ToPeer.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.Message != nil {
		l = m.Message.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.IsTombstone {
		n += 2
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.MergeTarget != nil {
		l = m.MergeTarget.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.ExtraMsg != nil {
		l = m.ExtraMsg.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.ExtraCtx)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.DiskUsage != 0 {
		n += 1 + sovRaftServerpb(uint64(m.DiskUsage))
	}
	return n
}

func (m *RaftTruncatedState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Index != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Index))
	}
	if m.Term != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Term))
	}
	return n
}

func (m *SnapshotCFFile) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.Size_ != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Size_))
	}
	if m.Checksum != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Checksum))
	}
	return n
}

func (m *SnapshotMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.CfFiles) > 0 {
		for _, e := range m.CfFiles {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if m.ForBalance {
		n += 2
	}
	if m.ForWitness {
		n += 2
	}
	if m.Start != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Start))
	}
	if m.GenerateDurationSec != 0 {
		n += 1 + sovRaftServerpb(uint64(m.GenerateDurationSec))
	}
	l = len(m.TabletSnapPath)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.CommitIndexHint != 0 {
		n += 1 + sovRaftServerpb(uint64(m.CommitIndexHint))
	}
	return n
}

func (m *SnapshotChunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Message != nil {
		l = m.Message.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}

func (m *Done) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *TabletSnapshotFileMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FileSize != 0 {
		n += 1 + sovRaftServerpb(uint64(m.FileSize))
	}
	l = len(m.FileName)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.HeadChunk)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.TrailingChunk)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}

func (m *TabletSnapshotPreview) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Metas) > 0 {
		for _, e := range m.Metas {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if m.End {
		n += 2
	}
	return n
}

func (m *TabletSnapshotFileChunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FileSize != 0 {
		n += 1 + sovRaftServerpb(uint64(m.FileSize))
	}
	l = len(m.FileName)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.Iv)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.Key != nil {
		l = m.Key.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}

func (m *TabletSnapshotHead) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Message != nil {
		l = m.Message.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.UseCache {
		n += 2
	}
	return n
}

func (m *TabletSnapshotEnd) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Checksum != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Checksum))
	}
	return n
}

func (m *TabletSnapshotRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Payload != nil {
		n += m.Payload.Size()
	}
	return n
}

func (m *TabletSnapshotRequest_Head) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Head != nil {
		l = m.Head.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}
func (m *TabletSnapshotRequest_Preview) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Preview != nil {
		l = m.Preview.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}
func (m *TabletSnapshotRequest_Chunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Chunk != nil {
		l = m.Chunk.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}
func (m *TabletSnapshotRequest_End) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.End != nil {
		l = m.End.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}
func (m *AcceptedSnapshotFiles) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.FileName) > 0 {
		for _, s := range m.FileName {
			l = len(s)
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	return n
}

func (m *TabletSnapshotResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Files != nil {
		l = m.Files.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}

func (m *KeyValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}

func (m *RaftSnapshotData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.FileSize != 0 {
		n += 1 + sovRaftServerpb(uint64(m.FileSize))
	}
	if len(m.Data) > 0 {
		for _, e := range m.Data {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if m.Version != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Version))
	}
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if len(m.RemovedRecords) > 0 {
		for _, e := range m.RemovedRecords {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if len(m.MergedRecords) > 0 {
		for _, e := range m.MergedRecords {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	return n
}

func (m *StoreIdent) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ClusterId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.ClusterId))
	}
	if m.StoreId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.StoreId))
	}
	if m.ApiVersion != 0 {
		n += 1 + sovRaftServerpb(uint64(m.ApiVersion))
	}
	return n
}

func (m *StoreRecoverState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Seqno != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Seqno))
	}
	return n
}

func (m *RaftLocalState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HardState != nil {
		l = m.HardState.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.LastIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.LastIndex))
	}
	return n
}

func (m *RaftApplyState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.AppliedIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.AppliedIndex))
	}
	if m.TruncatedState != nil {
		l = m.TruncatedState.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.LastCommitIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.LastCommitIndex))
	}
	if m.CommitIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.CommitIndex))
	}
	if m.CommitTerm != 0 {
		n += 1 + sovRaftServerpb(uint64(m.CommitTerm))
	}
	return n
}

func (m *MergeState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MinIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.MinIndex))
	}
	if m.Target != nil {
		l = m.Target.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.Commit != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Commit))
	}
	return n
}

func (m *MergedRecord) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SourceRegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.SourceRegionId))
	}
	if m.SourceEpoch != nil {
		l = m.SourceEpoch.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if len(m.SourcePeers) > 0 {
		for _, e := range m.SourcePeers {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if m.TargetRegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.TargetRegionId))
	}
	if m.TargetEpoch != nil {
		l = m.TargetEpoch.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if len(m.TargetPeers) > 0 {
		for _, e := range m.TargetPeers {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if m.Index != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Index))
	}
	if m.SourceIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.SourceIndex))
	}
	if len(m.SourceRemovedRecords) > 0 {
		for _, e := range m.SourceRemovedRecords {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	return n
}

func (m *RegionLocalState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.State != 0 {
		n += 1 + sovRaftServerpb(uint64(m.State))
	}
	if m.Region != nil {
		l = m.Region.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.MergeState != nil {
		l = m.MergeState.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.TabletIndex != 0 {
		n += 1 + sovRaftServerpb(uint64(m.TabletIndex))
	}
	if len(m.RemovedRecords) > 0 {
		for _, e := range m.RemovedRecords {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if len(m.MergedRecords) > 0 {
		for _, e := range m.MergedRecords {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	return n
}

func (m *RegionSequenceNumberRelation) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.RegionId))
	}
	if m.SequenceNumber != 0 {
		n += 1 + sovRaftServerpb(uint64(m.SequenceNumber))
	}
	if m.ApplyState != nil {
		l = m.ApplyState.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.RegionState != nil {
		l = m.RegionState.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}

func (m *AvailabilityContext) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FromRegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.FromRegionId))
	}
	if m.FromRegionEpoch != nil {
		l = m.FromRegionEpoch.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.Unavailable {
		n += 2
	}
	if m.Trimmed {
		n += 2
	}
	return n
}

func (m *FlushMemtable) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.RegionId))
	}
	return n
}

func (m *RefreshBuckets) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Version != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Version))
	}
	if len(m.Keys) > 0 {
		for _, b := range m.Keys {
			l = len(b)
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if len(m.Sizes) > 0 {
		l = 0
		for _, e := range m.Sizes {
			l += sovRaftServerpb(uint64(e))
		}
		n += 1 + sovRaftServerpb(uint64(l)) + l
	}
	return n
}

func (m *CheckGcPeer) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FromRegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.FromRegionId))
	}
	if m.CheckRegionId != 0 {
		n += 1 + sovRaftServerpb(uint64(m.CheckRegionId))
	}
	if m.CheckRegionEpoch != nil {
		l = m.CheckRegionEpoch.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.CheckPeer != nil {
		l = m.CheckPeer.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	return n
}

func (m *ExtraMessage) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Type))
	}
	if m.Index != 0 {
		n += 1 + sovRaftServerpb(uint64(m.Index))
	}
	if len(m.CheckPeers) > 0 {
		for _, e := range m.CheckPeers {
			l = e.Size()
			n += 1 + l + sovRaftServerpb(uint64(l))
		}
	}
	if m.WaitData {
		n += 2
	}
	if m.ForcelyAwaken {
		n += 2
	}
	if m.CheckGcPeer != nil {
		l = m.CheckGcPeer.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.FlushMemtable != nil {
		l = m.FlushMemtable.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.AvailabilityContext != nil {
		l = m.AvailabilityContext.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.RefreshBuckets != nil {
		l = m.RefreshBuckets.Size()
		n += 1 + l + sovRaftServerpb(uint64(l))
	}
	if m.SnapGenPrecheckPassed {
		n += 2
	}
	return n
}

func sovRaftServerpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozRaftServerpb(x uint64) (n int) {
	return sovRaftServerpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RaftMessage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromPeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FromPeer == nil {
				m.FromPeer = &metapb.Peer{}
			}
			if err := m.FromPeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ToPeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ToPeer == nil {
				m.ToPeer = &metapb.Peer{}
			}
			if err := m.ToPeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Message == nil {
				m.Message = &eraftpb.Message{}
			}
			if err := m.Message.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsTombstone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsTombstone = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MergeTarget", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MergeTarget == nil {
				m.MergeTarget = &metapb.Region{}
			}
			if err := m.MergeTarget.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExtraMsg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ExtraMsg == nil {
				m.ExtraMsg = &ExtraMessage{}
			}
			if err := m.ExtraMsg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExtraCtx", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ExtraCtx = append(m.ExtraCtx[:0], dAtA[iNdEx:postIndex]...)
			if m.ExtraCtx == nil {
				m.ExtraCtx = []byte{}
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DiskUsage", wireType)
			}
			m.DiskUsage = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DiskUsage |= disk_usage.DiskUsage(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftTruncatedState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftTruncatedState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftTruncatedState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Term", wireType)
			}
			m.Term = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Term |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotCFFile) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotCFFile: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotCFFile: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Size_", wireType)
			}
			m.Size_ = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Size_ |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checksum", wireType)
			}
			m.Checksum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Checksum |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CfFiles", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CfFiles = append(m.CfFiles, &SnapshotCFFile{})
			if err := m.CfFiles[len(m.CfFiles)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ForBalance", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForBalance = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ForWitness", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForWitness = bool(v != 0)
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GenerateDurationSec", wireType)
			}
			m.GenerateDurationSec = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GenerateDurationSec |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TabletSnapPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TabletSnapPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitIndexHint", wireType)
			}
			m.CommitIndexHint = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitIndexHint |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SnapshotChunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SnapshotChunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SnapshotChunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Message == nil {
				m.Message = &RaftMessage{}
			}
			if err := m.Message.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Done) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Done: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Done: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TabletSnapshotFileMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TabletSnapshotFileMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TabletSnapshotFileMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileSize", wireType)
			}
			m.FileSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FileSize |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HeadChunk", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HeadChunk = append(m.HeadChunk[:0], dAtA[iNdEx:postIndex]...)
			if m.HeadChunk == nil {
				m.HeadChunk = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrailingChunk", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TrailingChunk = append(m.TrailingChunk[:0], dAtA[iNdEx:postIndex]...)
			if m.TrailingChunk == nil {
				m.TrailingChunk = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TabletSnapshotPreview) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TabletSnapshotPreview: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TabletSnapshotPreview: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metas", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metas = append(m.Metas, &TabletSnapshotFileMeta{})
			if err := m.Metas[len(m.Metas)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.End = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TabletSnapshotFileChunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TabletSnapshotFileChunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TabletSnapshotFileChunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileSize", wireType)
			}
			m.FileSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FileSize |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Iv", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Iv = append(m.Iv[:0], dAtA[iNdEx:postIndex]...)
			if m.Iv == nil {
				m.Iv = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Key == nil {
				m.Key = &encryptionpb.DataKey{}
			}
			if err := m.Key.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TabletSnapshotHead) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TabletSnapshotHead: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TabletSnapshotHead: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Message == nil {
				m.Message = &RaftMessage{}
			}
			if err := m.Message.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UseCache", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UseCache = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TabletSnapshotEnd) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TabletSnapshotEnd: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TabletSnapshotEnd: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Checksum", wireType)
			}
			m.Checksum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Checksum |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TabletSnapshotRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TabletSnapshotRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TabletSnapshotRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Head", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &TabletSnapshotHead{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Payload = &TabletSnapshotRequest_Head{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Preview", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &TabletSnapshotPreview{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Payload = &TabletSnapshotRequest_Preview{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunk", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &TabletSnapshotFileChunk{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Payload = &TabletSnapshotRequest_Chunk{v}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &TabletSnapshotEnd{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Payload = &TabletSnapshotRequest_End{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AcceptedSnapshotFiles) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AcceptedSnapshotFiles: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AcceptedSnapshotFiles: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileName = append(m.FileName, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TabletSnapshotResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TabletSnapshotResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TabletSnapshotResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Files", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Files == nil {
				m.Files = &AcceptedSnapshotFiles{}
			}
			if err := m.Files.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KeyValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeyValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeyValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftSnapshotData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftSnapshotData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftSnapshotData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileSize", wireType)
			}
			m.FileSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FileSize |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data, &KeyValue{})
			if err := m.Data[len(m.Data)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &SnapshotMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RemovedRecords", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RemovedRecords = append(m.RemovedRecords, &metapb.Peer{})
			if err := m.RemovedRecords[len(m.RemovedRecords)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MergedRecords", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MergedRecords = append(m.MergedRecords, &MergedRecord{})
			if err := m.MergedRecords[len(m.MergedRecords)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreIdent) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreIdent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreIdent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClusterId", wireType)
			}
			m.ClusterId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClusterId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiVersion", wireType)
			}
			m.ApiVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApiVersion |= kvrpcpb.APIVersion(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreRecoverState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreRecoverState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreRecoverState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Seqno", wireType)
			}
			m.Seqno = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqno |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftLocalState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftLocalState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftLocalState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HardState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.HardState == nil {
				m.HardState = &eraftpb.HardState{}
			}
			if err := m.HardState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastIndex", wireType)
			}
			m.LastIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RaftApplyState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RaftApplyState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RaftApplyState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppliedIndex", wireType)
			}
			m.AppliedIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppliedIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TruncatedState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TruncatedState == nil {
				m.TruncatedState = &RaftTruncatedState{}
			}
			if err := m.TruncatedState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastCommitIndex", wireType)
			}
			m.LastCommitIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastCommitIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitIndex", wireType)
			}
			m.CommitIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitTerm", wireType)
			}
			m.CommitTerm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitTerm |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MergeState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MergeState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MergeState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinIndex", wireType)
			}
			m.MinIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Target", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Target == nil {
				m.Target = &metapb.Region{}
			}
			if err := m.Target.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commit", wireType)
			}
			m.Commit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Commit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MergedRecord) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MergedRecord: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MergedRecord: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRegionId", wireType)
			}
			m.SourceRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceRegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SourceEpoch == nil {
				m.SourceEpoch = &metapb.RegionEpoch{}
			}
			if err := m.SourceEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourcePeers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SourcePeers = append(m.SourcePeers, &metapb.Peer{})
			if err := m.SourcePeers[len(m.SourcePeers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetRegionId", wireType)
			}
			m.TargetRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetRegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TargetEpoch == nil {
				m.TargetEpoch = &metapb.RegionEpoch{}
			}
			if err := m.TargetEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetPeers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TargetPeers = append(m.TargetPeers, &metapb.Peer{})
			if err := m.TargetPeers[len(m.TargetPeers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceIndex", wireType)
			}
			m.SourceIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SourceRemovedRecords", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SourceRemovedRecords = append(m.SourceRemovedRecords, &metapb.Peer{})
			if err := m.SourceRemovedRecords[len(m.SourceRemovedRecords)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionLocalState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionLocalState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionLocalState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= PeerState(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Region == nil {
				m.Region = &metapb.Region{}
			}
			if err := m.Region.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MergeState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MergeState == nil {
				m.MergeState = &MergeState{}
			}
			if err := m.MergeState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TabletIndex", wireType)
			}
			m.TabletIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TabletIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RemovedRecords", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RemovedRecords = append(m.RemovedRecords, &metapb.Peer{})
			if err := m.RemovedRecords[len(m.RemovedRecords)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MergedRecords", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MergedRecords = append(m.MergedRecords, &MergedRecord{})
			if err := m.MergedRecords[len(m.MergedRecords)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionSequenceNumberRelation) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionSequenceNumberRelation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionSequenceNumberRelation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SequenceNumber", wireType)
			}
			m.SequenceNumber = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SequenceNumber |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApplyState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ApplyState == nil {
				m.ApplyState = &RaftApplyState{}
			}
			if err := m.ApplyState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionState", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionState == nil {
				m.RegionState = &RegionLocalState{}
			}
			if err := m.RegionState.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *AvailabilityContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: AvailabilityContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: AvailabilityContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromRegionId", wireType)
			}
			m.FromRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromRegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromRegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FromRegionEpoch == nil {
				m.FromRegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.FromRegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Unavailable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Unavailable = bool(v != 0)
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Trimmed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Trimmed = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FlushMemtable) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FlushMemtable: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FlushMemtable: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RefreshBuckets) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RefreshBuckets: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RefreshBuckets: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keys", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Keys = append(m.Keys, make([]byte, postIndex-iNdEx))
			copy(m.Keys[len(m.Keys)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRaftServerpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Sizes = append(m.Sizes, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowRaftServerpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthRaftServerpb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthRaftServerpb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Sizes) == 0 {
					m.Sizes = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowRaftServerpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Sizes = append(m.Sizes, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Sizes", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckGcPeer) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckGcPeer: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckGcPeer: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromRegionId", wireType)
			}
			m.FromRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromRegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckRegionId", wireType)
			}
			m.CheckRegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckRegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckRegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CheckRegionEpoch == nil {
				m.CheckRegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.CheckRegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckPeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CheckPeer == nil {
				m.CheckPeer = &metapb.Peer{}
			}
			if err := m.CheckPeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExtraMessage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExtraMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExtraMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= ExtraMessageType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckPeers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckPeers = append(m.CheckPeers, &metapb.Peer{})
			if err := m.CheckPeers[len(m.CheckPeers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitData", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WaitData = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ForcelyAwaken", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForcelyAwaken = bool(v != 0)
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckGcPeer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CheckGcPeer == nil {
				m.CheckGcPeer = &CheckGcPeer{}
			}
			if err := m.CheckGcPeer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlushMemtable", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FlushMemtable == nil {
				m.FlushMemtable = &FlushMemtable{}
			}
			if err := m.FlushMemtable.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AvailabilityContext", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AvailabilityContext == nil {
				m.AvailabilityContext = &AvailabilityContext{}
			}
			if err := m.AvailabilityContext.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RefreshBuckets", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RefreshBuckets == nil {
				m.RefreshBuckets = &RefreshBuckets{}
			}
			if err := m.RefreshBuckets.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SnapGenPrecheckPassed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SnapGenPrecheckPassed = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipRaftServerpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthRaftServerpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipRaftServerpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRaftServerpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRaftServerpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthRaftServerpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupRaftServerpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthRaftServerpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthRaftServerpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRaftServerpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupRaftServerpb = fmt.Errorf("proto: unexpected end of group")
)
