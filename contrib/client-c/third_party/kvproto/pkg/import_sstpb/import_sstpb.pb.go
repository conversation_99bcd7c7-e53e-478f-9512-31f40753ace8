// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: import_sstpb.proto

package import_sstpb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	brpb "github.com/pingcap/kvproto/pkg/brpb"
	encryptionpb "github.com/pingcap/kvproto/pkg/encryptionpb"
	errorpb "github.com/pingcap/kvproto/pkg/errorpb"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type SwitchMode int32

const (
	SwitchMode_Normal SwitchMode = 0
	SwitchMode_Import SwitchMode = 1
)

var SwitchMode_name = map[int32]string{
	0: "Normal",
	1: "Import",
}

var SwitchMode_value = map[string]int32{
	"Normal": 0,
	"Import": 1,
}

func (x SwitchMode) String() string {
	return proto.EnumName(SwitchMode_name, int32(x))
}

func (SwitchMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{0}
}

type DownloadRequestType int32

const (
	// For the compatibility with old version of TiDBs
	DownloadRequestType_Legacy DownloadRequestType = 0
	// For the TiDBs with newer versions that support keyspace feature.
	DownloadRequestType_Keyspace DownloadRequestType = 1
)

var DownloadRequestType_name = map[int32]string{
	0: "Legacy",
	1: "Keyspace",
}

var DownloadRequestType_value = map[string]int32{
	"Legacy":   0,
	"Keyspace": 1,
}

func (x DownloadRequestType) String() string {
	return proto.EnumName(DownloadRequestType_name, int32(x))
}

func (DownloadRequestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{1}
}

type Pair_OP int32

const (
	Pair_Put    Pair_OP = 0
	Pair_Delete Pair_OP = 1
)

var Pair_OP_name = map[int32]string{
	0: "Put",
	1: "Delete",
}

var Pair_OP_value = map[string]int32{
	"Put":    0,
	"Delete": 1,
}

func (x Pair_OP) String() string {
	return proto.EnumName(Pair_OP_name, int32(x))
}

func (Pair_OP) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{21, 0}
}

type SuspendImportRPCRequest struct {
	// whether to suspend new imports.
	ShouldSuspendImports bool `protobuf:"varint,1,opt,name=should_suspend_imports,json=shouldSuspendImports,proto3" json:"should_suspend_imports,omitempty"`
	// the duration of import service suspension
	// when should_deny_imports is false,
	// this won't take effect.
	DurationInSecs uint64 `protobuf:"varint,2,opt,name=duration_in_secs,json=durationInSecs,proto3" json:"duration_in_secs,omitempty"`
	// The identifier for the caller.
	Caller string `protobuf:"bytes,3,opt,name=caller,proto3" json:"caller,omitempty"`
}

func (m *SuspendImportRPCRequest) Reset()         { *m = SuspendImportRPCRequest{} }
func (m *SuspendImportRPCRequest) String() string { return proto.CompactTextString(m) }
func (*SuspendImportRPCRequest) ProtoMessage()    {}
func (*SuspendImportRPCRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{0}
}
func (m *SuspendImportRPCRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SuspendImportRPCRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SuspendImportRPCRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SuspendImportRPCRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuspendImportRPCRequest.Merge(m, src)
}
func (m *SuspendImportRPCRequest) XXX_Size() int {
	return m.Size()
}
func (m *SuspendImportRPCRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SuspendImportRPCRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SuspendImportRPCRequest proto.InternalMessageInfo

func (m *SuspendImportRPCRequest) GetShouldSuspendImports() bool {
	if m != nil {
		return m.ShouldSuspendImports
	}
	return false
}

func (m *SuspendImportRPCRequest) GetDurationInSecs() uint64 {
	if m != nil {
		return m.DurationInSecs
	}
	return 0
}

func (m *SuspendImportRPCRequest) GetCaller() string {
	if m != nil {
		return m.Caller
	}
	return ""
}

type SuspendImportRPCResponse struct {
	// The last state before this RPC.
	AlreadySuspended bool `protobuf:"varint,1,opt,name=already_suspended,json=alreadySuspended,proto3" json:"already_suspended,omitempty"`
}

func (m *SuspendImportRPCResponse) Reset()         { *m = SuspendImportRPCResponse{} }
func (m *SuspendImportRPCResponse) String() string { return proto.CompactTextString(m) }
func (*SuspendImportRPCResponse) ProtoMessage()    {}
func (*SuspendImportRPCResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{1}
}
func (m *SuspendImportRPCResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SuspendImportRPCResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SuspendImportRPCResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SuspendImportRPCResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SuspendImportRPCResponse.Merge(m, src)
}
func (m *SuspendImportRPCResponse) XXX_Size() int {
	return m.Size()
}
func (m *SuspendImportRPCResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SuspendImportRPCResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SuspendImportRPCResponse proto.InternalMessageInfo

func (m *SuspendImportRPCResponse) GetAlreadySuspended() bool {
	if m != nil {
		return m.AlreadySuspended
	}
	return false
}

type SwitchModeRequest struct {
	Mode   SwitchMode `protobuf:"varint,1,opt,name=mode,proto3,enum=import_sstpb.SwitchMode" json:"mode,omitempty"`
	Ranges []*Range   `protobuf:"bytes,2,rep,name=ranges,proto3" json:"ranges,omitempty"`
}

func (m *SwitchModeRequest) Reset()         { *m = SwitchModeRequest{} }
func (m *SwitchModeRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchModeRequest) ProtoMessage()    {}
func (*SwitchModeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{2}
}
func (m *SwitchModeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SwitchModeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SwitchModeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SwitchModeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchModeRequest.Merge(m, src)
}
func (m *SwitchModeRequest) XXX_Size() int {
	return m.Size()
}
func (m *SwitchModeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchModeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchModeRequest proto.InternalMessageInfo

func (m *SwitchModeRequest) GetMode() SwitchMode {
	if m != nil {
		return m.Mode
	}
	return SwitchMode_Normal
}

func (m *SwitchModeRequest) GetRanges() []*Range {
	if m != nil {
		return m.Ranges
	}
	return nil
}

type SwitchModeResponse struct {
}

func (m *SwitchModeResponse) Reset()         { *m = SwitchModeResponse{} }
func (m *SwitchModeResponse) String() string { return proto.CompactTextString(m) }
func (*SwitchModeResponse) ProtoMessage()    {}
func (*SwitchModeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{3}
}
func (m *SwitchModeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SwitchModeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SwitchModeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SwitchModeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchModeResponse.Merge(m, src)
}
func (m *SwitchModeResponse) XXX_Size() int {
	return m.Size()
}
func (m *SwitchModeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchModeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchModeResponse proto.InternalMessageInfo

type GetModeRequest struct {
}

func (m *GetModeRequest) Reset()         { *m = GetModeRequest{} }
func (m *GetModeRequest) String() string { return proto.CompactTextString(m) }
func (*GetModeRequest) ProtoMessage()    {}
func (*GetModeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{4}
}
func (m *GetModeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetModeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetModeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetModeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModeRequest.Merge(m, src)
}
func (m *GetModeRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetModeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetModeRequest proto.InternalMessageInfo

type GetModeResponse struct {
	Mode SwitchMode `protobuf:"varint,1,opt,name=mode,proto3,enum=import_sstpb.SwitchMode" json:"mode,omitempty"`
}

func (m *GetModeResponse) Reset()         { *m = GetModeResponse{} }
func (m *GetModeResponse) String() string { return proto.CompactTextString(m) }
func (*GetModeResponse) ProtoMessage()    {}
func (*GetModeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{5}
}
func (m *GetModeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetModeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetModeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetModeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModeResponse.Merge(m, src)
}
func (m *GetModeResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetModeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetModeResponse proto.InternalMessageInfo

func (m *GetModeResponse) GetMode() SwitchMode {
	if m != nil {
		return m.Mode
	}
	return SwitchMode_Normal
}

type Range struct {
	Start []byte `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End   []byte `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (m *Range) Reset()         { *m = Range{} }
func (m *Range) String() string { return proto.CompactTextString(m) }
func (*Range) ProtoMessage()    {}
func (*Range) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{6}
}
func (m *Range) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Range) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Range.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Range) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Range.Merge(m, src)
}
func (m *Range) XXX_Size() int {
	return m.Size()
}
func (m *Range) XXX_DiscardUnknown() {
	xxx_messageInfo_Range.DiscardUnknown(m)
}

var xxx_messageInfo_Range proto.InternalMessageInfo

func (m *Range) GetStart() []byte {
	if m != nil {
		return m.Start
	}
	return nil
}

func (m *Range) GetEnd() []byte {
	if m != nil {
		return m.End
	}
	return nil
}

type SSTMeta struct {
	Uuid            []byte              `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Range           *Range              `protobuf:"bytes,2,opt,name=range,proto3" json:"range,omitempty"`
	Crc32           uint32              `protobuf:"varint,3,opt,name=crc32,proto3" json:"crc32,omitempty"`
	Length          uint64              `protobuf:"varint,4,opt,name=length,proto3" json:"length,omitempty"`
	CfName          string              `protobuf:"bytes,5,opt,name=cf_name,json=cfName,proto3" json:"cf_name,omitempty"`
	RegionId        uint64              `protobuf:"varint,6,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	RegionEpoch     *metapb.RegionEpoch `protobuf:"bytes,7,opt,name=region_epoch,json=regionEpoch,proto3" json:"region_epoch,omitempty"`
	EndKeyExclusive bool                `protobuf:"varint,8,opt,name=end_key_exclusive,json=endKeyExclusive,proto3" json:"end_key_exclusive,omitempty"`
	// total_kvs and total_bytes is equivalent to PD's approximate_keys and approximate_size
	// set these values can save time from tikv upload keys and size to PD through Heartbeat.
	TotalKvs   uint64 `protobuf:"varint,9,opt,name=total_kvs,json=totalKvs,proto3" json:"total_kvs,omitempty"`
	TotalBytes uint64 `protobuf:"varint,10,opt,name=total_bytes,json=totalBytes,proto3" json:"total_bytes,omitempty"`
	// API version implies the encode of the key and value.
	ApiVersion kvrpcpb.APIVersion `protobuf:"varint,11,opt,name=api_version,json=apiVersion,proto3,enum=kvrpcpb.APIVersion" json:"api_version,omitempty"`
	// cipher_iv is used to encrypt/decrypt sst
	CipherIv []byte `protobuf:"bytes,12,opt,name=cipher_iv,json=cipherIv,proto3" json:"cipher_iv,omitempty"`
}

func (m *SSTMeta) Reset()         { *m = SSTMeta{} }
func (m *SSTMeta) String() string { return proto.CompactTextString(m) }
func (*SSTMeta) ProtoMessage()    {}
func (*SSTMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{7}
}
func (m *SSTMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SSTMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SSTMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SSTMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SSTMeta.Merge(m, src)
}
func (m *SSTMeta) XXX_Size() int {
	return m.Size()
}
func (m *SSTMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_SSTMeta.DiscardUnknown(m)
}

var xxx_messageInfo_SSTMeta proto.InternalMessageInfo

func (m *SSTMeta) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

func (m *SSTMeta) GetRange() *Range {
	if m != nil {
		return m.Range
	}
	return nil
}

func (m *SSTMeta) GetCrc32() uint32 {
	if m != nil {
		return m.Crc32
	}
	return 0
}

func (m *SSTMeta) GetLength() uint64 {
	if m != nil {
		return m.Length
	}
	return 0
}

func (m *SSTMeta) GetCfName() string {
	if m != nil {
		return m.CfName
	}
	return ""
}

func (m *SSTMeta) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *SSTMeta) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *SSTMeta) GetEndKeyExclusive() bool {
	if m != nil {
		return m.EndKeyExclusive
	}
	return false
}

func (m *SSTMeta) GetTotalKvs() uint64 {
	if m != nil {
		return m.TotalKvs
	}
	return 0
}

func (m *SSTMeta) GetTotalBytes() uint64 {
	if m != nil {
		return m.TotalBytes
	}
	return 0
}

func (m *SSTMeta) GetApiVersion() kvrpcpb.APIVersion {
	if m != nil {
		return m.ApiVersion
	}
	return kvrpcpb.APIVersion_V1
}

func (m *SSTMeta) GetCipherIv() []byte {
	if m != nil {
		return m.CipherIv
	}
	return nil
}

// A rewrite rule is applied on the *encoded* keys (the internal storage
// representation).
type RewriteRule struct {
	OldKeyPrefix []byte `protobuf:"bytes,1,opt,name=old_key_prefix,json=oldKeyPrefix,proto3" json:"old_key_prefix,omitempty"`
	NewKeyPrefix []byte `protobuf:"bytes,2,opt,name=new_key_prefix,json=newKeyPrefix,proto3" json:"new_key_prefix,omitempty"`
	// (Optional) Rewrite all keys in the range to use this timestamp.
	NewTimestamp uint64 `protobuf:"varint,3,opt,name=new_timestamp,json=newTimestamp,proto3" json:"new_timestamp,omitempty"`
	// (Optional) Skip keys with timestamps greater than this during download, useful for compacted SST backups.
	IgnoreAfterTimestamp uint64 `protobuf:"varint,4,opt,name=ignore_after_timestamp,json=ignoreAfterTimestamp,proto3" json:"ignore_after_timestamp,omitempty"`
	// (Optional) Skip write CF keys with timestamps less than this during download. Default CF keys are preserved.
	IgnoreBeforeTimestamp uint64 `protobuf:"varint,5,opt,name=ignore_before_timestamp,json=ignoreBeforeTimestamp,proto3" json:"ignore_before_timestamp,omitempty"`
}

func (m *RewriteRule) Reset()         { *m = RewriteRule{} }
func (m *RewriteRule) String() string { return proto.CompactTextString(m) }
func (*RewriteRule) ProtoMessage()    {}
func (*RewriteRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{8}
}
func (m *RewriteRule) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RewriteRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RewriteRule.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RewriteRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RewriteRule.Merge(m, src)
}
func (m *RewriteRule) XXX_Size() int {
	return m.Size()
}
func (m *RewriteRule) XXX_DiscardUnknown() {
	xxx_messageInfo_RewriteRule.DiscardUnknown(m)
}

var xxx_messageInfo_RewriteRule proto.InternalMessageInfo

func (m *RewriteRule) GetOldKeyPrefix() []byte {
	if m != nil {
		return m.OldKeyPrefix
	}
	return nil
}

func (m *RewriteRule) GetNewKeyPrefix() []byte {
	if m != nil {
		return m.NewKeyPrefix
	}
	return nil
}

func (m *RewriteRule) GetNewTimestamp() uint64 {
	if m != nil {
		return m.NewTimestamp
	}
	return 0
}

func (m *RewriteRule) GetIgnoreAfterTimestamp() uint64 {
	if m != nil {
		return m.IgnoreAfterTimestamp
	}
	return 0
}

func (m *RewriteRule) GetIgnoreBeforeTimestamp() uint64 {
	if m != nil {
		return m.IgnoreBeforeTimestamp
	}
	return 0
}

type UploadRequest struct {
	// Types that are valid to be assigned to Chunk:
	//	*UploadRequest_Meta
	//	*UploadRequest_Data
	Chunk isUploadRequest_Chunk `protobuf_oneof:"chunk"`
}

func (m *UploadRequest) Reset()         { *m = UploadRequest{} }
func (m *UploadRequest) String() string { return proto.CompactTextString(m) }
func (*UploadRequest) ProtoMessage()    {}
func (*UploadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{9}
}
func (m *UploadRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UploadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UploadRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UploadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadRequest.Merge(m, src)
}
func (m *UploadRequest) XXX_Size() int {
	return m.Size()
}
func (m *UploadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UploadRequest proto.InternalMessageInfo

type isUploadRequest_Chunk interface {
	isUploadRequest_Chunk()
	MarshalTo([]byte) (int, error)
	Size() int
}

type UploadRequest_Meta struct {
	Meta *SSTMeta `protobuf:"bytes,1,opt,name=meta,proto3,oneof" json:"meta,omitempty"`
}
type UploadRequest_Data struct {
	Data []byte `protobuf:"bytes,2,opt,name=data,proto3,oneof" json:"data,omitempty"`
}

func (*UploadRequest_Meta) isUploadRequest_Chunk() {}
func (*UploadRequest_Data) isUploadRequest_Chunk() {}

func (m *UploadRequest) GetChunk() isUploadRequest_Chunk {
	if m != nil {
		return m.Chunk
	}
	return nil
}

func (m *UploadRequest) GetMeta() *SSTMeta {
	if x, ok := m.GetChunk().(*UploadRequest_Meta); ok {
		return x.Meta
	}
	return nil
}

func (m *UploadRequest) GetData() []byte {
	if x, ok := m.GetChunk().(*UploadRequest_Data); ok {
		return x.Data
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*UploadRequest) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*UploadRequest_Meta)(nil),
		(*UploadRequest_Data)(nil),
	}
}

type UploadResponse struct {
}

func (m *UploadResponse) Reset()         { *m = UploadResponse{} }
func (m *UploadResponse) String() string { return proto.CompactTextString(m) }
func (*UploadResponse) ProtoMessage()    {}
func (*UploadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{10}
}
func (m *UploadResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UploadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UploadResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UploadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UploadResponse.Merge(m, src)
}
func (m *UploadResponse) XXX_Size() int {
	return m.Size()
}
func (m *UploadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UploadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UploadResponse proto.InternalMessageInfo

type IngestRequest struct {
	Context *kvrpcpb.Context `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	Sst     *SSTMeta         `protobuf:"bytes,2,opt,name=sst,proto3" json:"sst,omitempty"`
}

func (m *IngestRequest) Reset()         { *m = IngestRequest{} }
func (m *IngestRequest) String() string { return proto.CompactTextString(m) }
func (*IngestRequest) ProtoMessage()    {}
func (*IngestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{11}
}
func (m *IngestRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IngestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IngestRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IngestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IngestRequest.Merge(m, src)
}
func (m *IngestRequest) XXX_Size() int {
	return m.Size()
}
func (m *IngestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IngestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IngestRequest proto.InternalMessageInfo

func (m *IngestRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *IngestRequest) GetSst() *SSTMeta {
	if m != nil {
		return m.Sst
	}
	return nil
}

type MultiIngestRequest struct {
	Context *kvrpcpb.Context `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	Ssts    []*SSTMeta       `protobuf:"bytes,2,rep,name=ssts,proto3" json:"ssts,omitempty"`
}

func (m *MultiIngestRequest) Reset()         { *m = MultiIngestRequest{} }
func (m *MultiIngestRequest) String() string { return proto.CompactTextString(m) }
func (*MultiIngestRequest) ProtoMessage()    {}
func (*MultiIngestRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{12}
}
func (m *MultiIngestRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MultiIngestRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MultiIngestRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MultiIngestRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiIngestRequest.Merge(m, src)
}
func (m *MultiIngestRequest) XXX_Size() int {
	return m.Size()
}
func (m *MultiIngestRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiIngestRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MultiIngestRequest proto.InternalMessageInfo

func (m *MultiIngestRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *MultiIngestRequest) GetSsts() []*SSTMeta {
	if m != nil {
		return m.Ssts
	}
	return nil
}

type IngestResponse struct {
	Error *errorpb.Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *IngestResponse) Reset()         { *m = IngestResponse{} }
func (m *IngestResponse) String() string { return proto.CompactTextString(m) }
func (*IngestResponse) ProtoMessage()    {}
func (*IngestResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{13}
}
func (m *IngestResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IngestResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IngestResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IngestResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IngestResponse.Merge(m, src)
}
func (m *IngestResponse) XXX_Size() int {
	return m.Size()
}
func (m *IngestResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IngestResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IngestResponse proto.InternalMessageInfo

func (m *IngestResponse) GetError() *errorpb.Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type CompactRequest struct {
	// Compact files in the range and above the output level.
	// Compact all files if the range is not specified.
	// Compact all files to the bottommost level if the output level is -1.
	Range       *Range           `protobuf:"bytes,1,opt,name=range,proto3" json:"range,omitempty"`
	OutputLevel int32            `protobuf:"varint,2,opt,name=output_level,json=outputLevel,proto3" json:"output_level,omitempty"`
	Context     *kvrpcpb.Context `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
}

func (m *CompactRequest) Reset()         { *m = CompactRequest{} }
func (m *CompactRequest) String() string { return proto.CompactTextString(m) }
func (*CompactRequest) ProtoMessage()    {}
func (*CompactRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{14}
}
func (m *CompactRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CompactRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactRequest.Merge(m, src)
}
func (m *CompactRequest) XXX_Size() int {
	return m.Size()
}
func (m *CompactRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CompactRequest proto.InternalMessageInfo

func (m *CompactRequest) GetRange() *Range {
	if m != nil {
		return m.Range
	}
	return nil
}

func (m *CompactRequest) GetOutputLevel() int32 {
	if m != nil {
		return m.OutputLevel
	}
	return 0
}

func (m *CompactRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

type CompactResponse struct {
}

func (m *CompactResponse) Reset()         { *m = CompactResponse{} }
func (m *CompactResponse) String() string { return proto.CompactTextString(m) }
func (*CompactResponse) ProtoMessage()    {}
func (*CompactResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{15}
}
func (m *CompactResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CompactResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactResponse.Merge(m, src)
}
func (m *CompactResponse) XXX_Size() int {
	return m.Size()
}
func (m *CompactResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CompactResponse proto.InternalMessageInfo

type DownloadRequest struct {
	// Map represents the map of <name, SSTMeta>.
	// We'll generate all SSTMeta into one SST File.
	Ssts map[string]*SSTMeta `protobuf:"bytes,1,rep,name=ssts,proto3" json:"ssts,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// resolved_ts is used to merge related SST Files.
	ResolvedTs uint64 `protobuf:"varint,3,opt,name=resolved_ts,json=resolvedTs,proto3" json:"resolved_ts,omitempty"`
	// The SST meta used to identify the downloaded file.
	// Must be the same among all nodes in the same Raft group.
	// Note: the "crc32" and "cf_name" fields are ignored in this request,
	// and the "range" field represents the closed key range after rewrite
	// (as origin keys in encoded representation).
	Sst SSTMeta `protobuf:"bytes,2,opt,name=sst,proto3" json:"sst"`
	// The file name of the SST file.
	Name string `protobuf:"bytes,9,opt,name=name,proto3" json:"name,omitempty"`
	// Performs a key prefix rewrite after downloading the SST file.
	// All keys in the SST will be rewritten as:
	//
	//  new_key = new_key_prefix + old_key[len(old_key_prefix)..]
	//
	// When used for TiDB, rewriting the prefix changes the table ID. Please
	// note that key-rewrite is applied on the origin keys in encoded
	// representation (the SST itself should still use data keys in encoded
	// representation).
	//
	// You need to ensure that the keys before and after rewriting are in the
	// same order, otherwise the RPC request will fail.
	RewriteRule        RewriteRule          `protobuf:"bytes,13,opt,name=rewrite_rule,json=rewriteRule,proto3" json:"rewrite_rule"`
	SortedRewriteRules []*RewriteRule       `protobuf:"bytes,20,rep,name=sorted_rewrite_rules,json=sortedRewriteRules,proto3" json:"sorted_rewrite_rules,omitempty"`
	StorageBackend     *brpb.StorageBackend `protobuf:"bytes,14,opt,name=storage_backend,json=storageBackend,proto3" json:"storage_backend,omitempty"`
	// The identity for the stroage backend.
	// When this field presents, the storage would be cached.
	// If there is a cached storage, TiKV would use it driectly.
	StorageCacheId string `protobuf:"bytes,17,opt,name=storage_cache_id,json=storageCacheId,proto3" json:"storage_cache_id,omitempty"`
	IsRawKv        bool   `protobuf:"varint,15,opt,name=is_raw_kv,json=isRawKv,proto3" json:"is_raw_kv,omitempty"`
	// cipher_info is used to decrypt sst when download sst
	CipherInfo *brpb.CipherInfo `protobuf:"bytes,16,opt,name=cipher_info,json=cipherInfo,proto3" json:"cipher_info,omitempty"`
	// The type of the download request.
	RequestType DownloadRequestType `protobuf:"varint,18,opt,name=request_type,json=requestType,proto3,enum=import_sstpb.DownloadRequestType" json:"request_type,omitempty"`
	Context     *kvrpcpb.Context    `protobuf:"bytes,19,opt,name=context,proto3" json:"context,omitempty"`
}

func (m *DownloadRequest) Reset()         { *m = DownloadRequest{} }
func (m *DownloadRequest) String() string { return proto.CompactTextString(m) }
func (*DownloadRequest) ProtoMessage()    {}
func (*DownloadRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{16}
}
func (m *DownloadRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DownloadRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DownloadRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DownloadRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownloadRequest.Merge(m, src)
}
func (m *DownloadRequest) XXX_Size() int {
	return m.Size()
}
func (m *DownloadRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DownloadRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DownloadRequest proto.InternalMessageInfo

func (m *DownloadRequest) GetSsts() map[string]*SSTMeta {
	if m != nil {
		return m.Ssts
	}
	return nil
}

func (m *DownloadRequest) GetResolvedTs() uint64 {
	if m != nil {
		return m.ResolvedTs
	}
	return 0
}

func (m *DownloadRequest) GetSst() SSTMeta {
	if m != nil {
		return m.Sst
	}
	return SSTMeta{}
}

func (m *DownloadRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DownloadRequest) GetRewriteRule() RewriteRule {
	if m != nil {
		return m.RewriteRule
	}
	return RewriteRule{}
}

func (m *DownloadRequest) GetSortedRewriteRules() []*RewriteRule {
	if m != nil {
		return m.SortedRewriteRules
	}
	return nil
}

func (m *DownloadRequest) GetStorageBackend() *brpb.StorageBackend {
	if m != nil {
		return m.StorageBackend
	}
	return nil
}

func (m *DownloadRequest) GetStorageCacheId() string {
	if m != nil {
		return m.StorageCacheId
	}
	return ""
}

func (m *DownloadRequest) GetIsRawKv() bool {
	if m != nil {
		return m.IsRawKv
	}
	return false
}

func (m *DownloadRequest) GetCipherInfo() *brpb.CipherInfo {
	if m != nil {
		return m.CipherInfo
	}
	return nil
}

func (m *DownloadRequest) GetRequestType() DownloadRequestType {
	if m != nil {
		return m.RequestType
	}
	return DownloadRequestType_Legacy
}

func (m *DownloadRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

// For now it is just used for distinguishing the error of the request with the error
// of gRPC, add more concrete types if it is necessary later.
type Error struct {
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	// We meet some internal errors of the store.
	StoreError *errorpb.Error `protobuf:"bytes,2,opt,name=store_error,json=storeError,proto3" json:"store_error,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{17}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *Error) GetStoreError() *errorpb.Error {
	if m != nil {
		return m.StoreError
	}
	return nil
}

type DownloadResponse struct {
	// The actual key range (after rewrite) of the downloaded SST. The range is
	// inclusive in both ends.
	Range Range `protobuf:"bytes,1,opt,name=range,proto3" json:"range"`
	// Whether the SST is empty. An empty SST is prohibited in TiKV, do not
	// ingest if this field is true.
	// (Deprecated, should be replaced by checking `length == 0` in the future)
	IsEmpty bool   `protobuf:"varint,2,opt,name=is_empty,json=isEmpty,proto3" json:"is_empty,omitempty"`
	Error   *Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	// The CRC32 checksum of the rewritten SST file (implementation can return
	// zero, indicating the CRC32 was not calculated).
	Crc32 uint32 `protobuf:"varint,4,opt,name=crc32,proto3" json:"crc32,omitempty"`
	// The actual length of the rewritten SST file.
	Length uint64 `protobuf:"varint,5,opt,name=length,proto3" json:"length,omitempty"`
	// This field only return when file-copy backup enabled.
	// Because it will merge many SST files in a download request.
	Ssts []*SSTMeta `protobuf:"bytes,6,rep,name=ssts,proto3" json:"ssts,omitempty"`
}

func (m *DownloadResponse) Reset()         { *m = DownloadResponse{} }
func (m *DownloadResponse) String() string { return proto.CompactTextString(m) }
func (*DownloadResponse) ProtoMessage()    {}
func (*DownloadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{18}
}
func (m *DownloadResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DownloadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DownloadResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DownloadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownloadResponse.Merge(m, src)
}
func (m *DownloadResponse) XXX_Size() int {
	return m.Size()
}
func (m *DownloadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DownloadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DownloadResponse proto.InternalMessageInfo

func (m *DownloadResponse) GetRange() Range {
	if m != nil {
		return m.Range
	}
	return Range{}
}

func (m *DownloadResponse) GetIsEmpty() bool {
	if m != nil {
		return m.IsEmpty
	}
	return false
}

func (m *DownloadResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *DownloadResponse) GetCrc32() uint32 {
	if m != nil {
		return m.Crc32
	}
	return 0
}

func (m *DownloadResponse) GetLength() uint64 {
	if m != nil {
		return m.Length
	}
	return 0
}

func (m *DownloadResponse) GetSsts() []*SSTMeta {
	if m != nil {
		return m.Ssts
	}
	return nil
}

type SetDownloadSpeedLimitRequest struct {
	// The download speed limit (bytes/second). Set to 0 for unlimited speed.
	SpeedLimit uint64 `protobuf:"varint,1,opt,name=speed_limit,json=speedLimit,proto3" json:"speed_limit,omitempty"`
}

func (m *SetDownloadSpeedLimitRequest) Reset()         { *m = SetDownloadSpeedLimitRequest{} }
func (m *SetDownloadSpeedLimitRequest) String() string { return proto.CompactTextString(m) }
func (*SetDownloadSpeedLimitRequest) ProtoMessage()    {}
func (*SetDownloadSpeedLimitRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{19}
}
func (m *SetDownloadSpeedLimitRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SetDownloadSpeedLimitRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SetDownloadSpeedLimitRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SetDownloadSpeedLimitRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDownloadSpeedLimitRequest.Merge(m, src)
}
func (m *SetDownloadSpeedLimitRequest) XXX_Size() int {
	return m.Size()
}
func (m *SetDownloadSpeedLimitRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDownloadSpeedLimitRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetDownloadSpeedLimitRequest proto.InternalMessageInfo

func (m *SetDownloadSpeedLimitRequest) GetSpeedLimit() uint64 {
	if m != nil {
		return m.SpeedLimit
	}
	return 0
}

type SetDownloadSpeedLimitResponse struct {
}

func (m *SetDownloadSpeedLimitResponse) Reset()         { *m = SetDownloadSpeedLimitResponse{} }
func (m *SetDownloadSpeedLimitResponse) String() string { return proto.CompactTextString(m) }
func (*SetDownloadSpeedLimitResponse) ProtoMessage()    {}
func (*SetDownloadSpeedLimitResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{20}
}
func (m *SetDownloadSpeedLimitResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SetDownloadSpeedLimitResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SetDownloadSpeedLimitResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SetDownloadSpeedLimitResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDownloadSpeedLimitResponse.Merge(m, src)
}
func (m *SetDownloadSpeedLimitResponse) XXX_Size() int {
	return m.Size()
}
func (m *SetDownloadSpeedLimitResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDownloadSpeedLimitResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetDownloadSpeedLimitResponse proto.InternalMessageInfo

type Pair struct {
	Key   []byte  `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value []byte  `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Op    Pair_OP `protobuf:"varint,3,opt,name=op,proto3,enum=import_sstpb.Pair_OP" json:"op,omitempty"`
}

func (m *Pair) Reset()         { *m = Pair{} }
func (m *Pair) String() string { return proto.CompactTextString(m) }
func (*Pair) ProtoMessage()    {}
func (*Pair) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{21}
}
func (m *Pair) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Pair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Pair.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Pair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Pair.Merge(m, src)
}
func (m *Pair) XXX_Size() int {
	return m.Size()
}
func (m *Pair) XXX_DiscardUnknown() {
	xxx_messageInfo_Pair.DiscardUnknown(m)
}

var xxx_messageInfo_Pair proto.InternalMessageInfo

func (m *Pair) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *Pair) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *Pair) GetOp() Pair_OP {
	if m != nil {
		return m.Op
	}
	return Pair_Put
}

type WriteBatch struct {
	CommitTs uint64  `protobuf:"varint,1,opt,name=commit_ts,json=commitTs,proto3" json:"commit_ts,omitempty"`
	Pairs    []*Pair `protobuf:"bytes,2,rep,name=pairs,proto3" json:"pairs,omitempty"`
}

func (m *WriteBatch) Reset()         { *m = WriteBatch{} }
func (m *WriteBatch) String() string { return proto.CompactTextString(m) }
func (*WriteBatch) ProtoMessage()    {}
func (*WriteBatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{22}
}
func (m *WriteBatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteBatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteBatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteBatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteBatch.Merge(m, src)
}
func (m *WriteBatch) XXX_Size() int {
	return m.Size()
}
func (m *WriteBatch) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteBatch.DiscardUnknown(m)
}

var xxx_messageInfo_WriteBatch proto.InternalMessageInfo

func (m *WriteBatch) GetCommitTs() uint64 {
	if m != nil {
		return m.CommitTs
	}
	return 0
}

func (m *WriteBatch) GetPairs() []*Pair {
	if m != nil {
		return m.Pairs
	}
	return nil
}

type WriteRequest struct {
	// Types that are valid to be assigned to Chunk:
	//	*WriteRequest_Meta
	//	*WriteRequest_Batch
	Chunk   isWriteRequest_Chunk `protobuf_oneof:"chunk"`
	Context *kvrpcpb.Context     `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
}

func (m *WriteRequest) Reset()         { *m = WriteRequest{} }
func (m *WriteRequest) String() string { return proto.CompactTextString(m) }
func (*WriteRequest) ProtoMessage()    {}
func (*WriteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{23}
}
func (m *WriteRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteRequest.Merge(m, src)
}
func (m *WriteRequest) XXX_Size() int {
	return m.Size()
}
func (m *WriteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WriteRequest proto.InternalMessageInfo

type isWriteRequest_Chunk interface {
	isWriteRequest_Chunk()
	MarshalTo([]byte) (int, error)
	Size() int
}

type WriteRequest_Meta struct {
	Meta *SSTMeta `protobuf:"bytes,1,opt,name=meta,proto3,oneof" json:"meta,omitempty"`
}
type WriteRequest_Batch struct {
	Batch *WriteBatch `protobuf:"bytes,2,opt,name=batch,proto3,oneof" json:"batch,omitempty"`
}

func (*WriteRequest_Meta) isWriteRequest_Chunk()  {}
func (*WriteRequest_Batch) isWriteRequest_Chunk() {}

func (m *WriteRequest) GetChunk() isWriteRequest_Chunk {
	if m != nil {
		return m.Chunk
	}
	return nil
}

func (m *WriteRequest) GetMeta() *SSTMeta {
	if x, ok := m.GetChunk().(*WriteRequest_Meta); ok {
		return x.Meta
	}
	return nil
}

func (m *WriteRequest) GetBatch() *WriteBatch {
	if x, ok := m.GetChunk().(*WriteRequest_Batch); ok {
		return x.Batch
	}
	return nil
}

func (m *WriteRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*WriteRequest) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*WriteRequest_Meta)(nil),
		(*WriteRequest_Batch)(nil),
	}
}

type WriteResponse struct {
	Error *Error     `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Metas []*SSTMeta `protobuf:"bytes,2,rep,name=metas,proto3" json:"metas,omitempty"`
}

func (m *WriteResponse) Reset()         { *m = WriteResponse{} }
func (m *WriteResponse) String() string { return proto.CompactTextString(m) }
func (*WriteResponse) ProtoMessage()    {}
func (*WriteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{24}
}
func (m *WriteResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteResponse.Merge(m, src)
}
func (m *WriteResponse) XXX_Size() int {
	return m.Size()
}
func (m *WriteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WriteResponse proto.InternalMessageInfo

func (m *WriteResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *WriteResponse) GetMetas() []*SSTMeta {
	if m != nil {
		return m.Metas
	}
	return nil
}

type RawWriteBatch struct {
	Ttl   uint64  `protobuf:"varint,1,opt,name=ttl,proto3" json:"ttl,omitempty"`
	Pairs []*Pair `protobuf:"bytes,2,rep,name=pairs,proto3" json:"pairs,omitempty"`
	// To be compatible with the key encoding of API V2.
	// This field should be generated from the client instead of the server,
	// since the message will be send to all the replicas of a region.
	// Otherwise, the underlying data generated by the server would be inconsistent which is hard to scale
	// for other features like MVCC over RawKV.
	Ts uint64 `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
}

func (m *RawWriteBatch) Reset()         { *m = RawWriteBatch{} }
func (m *RawWriteBatch) String() string { return proto.CompactTextString(m) }
func (*RawWriteBatch) ProtoMessage()    {}
func (*RawWriteBatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{25}
}
func (m *RawWriteBatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RawWriteBatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RawWriteBatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RawWriteBatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RawWriteBatch.Merge(m, src)
}
func (m *RawWriteBatch) XXX_Size() int {
	return m.Size()
}
func (m *RawWriteBatch) XXX_DiscardUnknown() {
	xxx_messageInfo_RawWriteBatch.DiscardUnknown(m)
}

var xxx_messageInfo_RawWriteBatch proto.InternalMessageInfo

func (m *RawWriteBatch) GetTtl() uint64 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

func (m *RawWriteBatch) GetPairs() []*Pair {
	if m != nil {
		return m.Pairs
	}
	return nil
}

func (m *RawWriteBatch) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type RawWriteRequest struct {
	// Types that are valid to be assigned to Chunk:
	//	*RawWriteRequest_Meta
	//	*RawWriteRequest_Batch
	Chunk   isRawWriteRequest_Chunk `protobuf_oneof:"chunk"`
	Context *kvrpcpb.Context        `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
}

func (m *RawWriteRequest) Reset()         { *m = RawWriteRequest{} }
func (m *RawWriteRequest) String() string { return proto.CompactTextString(m) }
func (*RawWriteRequest) ProtoMessage()    {}
func (*RawWriteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{26}
}
func (m *RawWriteRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RawWriteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RawWriteRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RawWriteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RawWriteRequest.Merge(m, src)
}
func (m *RawWriteRequest) XXX_Size() int {
	return m.Size()
}
func (m *RawWriteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RawWriteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RawWriteRequest proto.InternalMessageInfo

type isRawWriteRequest_Chunk interface {
	isRawWriteRequest_Chunk()
	MarshalTo([]byte) (int, error)
	Size() int
}

type RawWriteRequest_Meta struct {
	Meta *SSTMeta `protobuf:"bytes,1,opt,name=meta,proto3,oneof" json:"meta,omitempty"`
}
type RawWriteRequest_Batch struct {
	Batch *RawWriteBatch `protobuf:"bytes,2,opt,name=batch,proto3,oneof" json:"batch,omitempty"`
}

func (*RawWriteRequest_Meta) isRawWriteRequest_Chunk()  {}
func (*RawWriteRequest_Batch) isRawWriteRequest_Chunk() {}

func (m *RawWriteRequest) GetChunk() isRawWriteRequest_Chunk {
	if m != nil {
		return m.Chunk
	}
	return nil
}

func (m *RawWriteRequest) GetMeta() *SSTMeta {
	if x, ok := m.GetChunk().(*RawWriteRequest_Meta); ok {
		return x.Meta
	}
	return nil
}

func (m *RawWriteRequest) GetBatch() *RawWriteBatch {
	if x, ok := m.GetChunk().(*RawWriteRequest_Batch); ok {
		return x.Batch
	}
	return nil
}

func (m *RawWriteRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*RawWriteRequest) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*RawWriteRequest_Meta)(nil),
		(*RawWriteRequest_Batch)(nil),
	}
}

type RawWriteResponse struct {
	Error *Error     `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Metas []*SSTMeta `protobuf:"bytes,2,rep,name=metas,proto3" json:"metas,omitempty"`
}

func (m *RawWriteResponse) Reset()         { *m = RawWriteResponse{} }
func (m *RawWriteResponse) String() string { return proto.CompactTextString(m) }
func (*RawWriteResponse) ProtoMessage()    {}
func (*RawWriteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{27}
}
func (m *RawWriteResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RawWriteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RawWriteResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RawWriteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RawWriteResponse.Merge(m, src)
}
func (m *RawWriteResponse) XXX_Size() int {
	return m.Size()
}
func (m *RawWriteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RawWriteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RawWriteResponse proto.InternalMessageInfo

func (m *RawWriteResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *RawWriteResponse) GetMetas() []*SSTMeta {
	if m != nil {
		return m.Metas
	}
	return nil
}

type DuplicateDetectRequest struct {
	Context  *kvrpcpb.Context `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	StartKey []byte           `protobuf:"bytes,2,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey   []byte           `protobuf:"bytes,3,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	// Return only the keys found by scanning, not their values.
	KeyOnly bool `protobuf:"varint,4,opt,name=key_only,json=keyOnly,proto3" json:"key_only,omitempty"`
	// We only check the data whose timestamp is larger than `min_commit_ts`. `min_commit_ts` is exclueded.
	MinCommitTs uint64 `protobuf:"varint,5,opt,name=min_commit_ts,json=minCommitTs,proto3" json:"min_commit_ts,omitempty"`
}

func (m *DuplicateDetectRequest) Reset()         { *m = DuplicateDetectRequest{} }
func (m *DuplicateDetectRequest) String() string { return proto.CompactTextString(m) }
func (*DuplicateDetectRequest) ProtoMessage()    {}
func (*DuplicateDetectRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{28}
}
func (m *DuplicateDetectRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DuplicateDetectRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DuplicateDetectRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DuplicateDetectRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DuplicateDetectRequest.Merge(m, src)
}
func (m *DuplicateDetectRequest) XXX_Size() int {
	return m.Size()
}
func (m *DuplicateDetectRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DuplicateDetectRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DuplicateDetectRequest proto.InternalMessageInfo

func (m *DuplicateDetectRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *DuplicateDetectRequest) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *DuplicateDetectRequest) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (m *DuplicateDetectRequest) GetKeyOnly() bool {
	if m != nil {
		return m.KeyOnly
	}
	return false
}

func (m *DuplicateDetectRequest) GetMinCommitTs() uint64 {
	if m != nil {
		return m.MinCommitTs
	}
	return 0
}

type KvPair struct {
	Key      []byte `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value    []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	CommitTs uint64 `protobuf:"varint,3,opt,name=commit_ts,json=commitTs,proto3" json:"commit_ts,omitempty"`
}

func (m *KvPair) Reset()         { *m = KvPair{} }
func (m *KvPair) String() string { return proto.CompactTextString(m) }
func (*KvPair) ProtoMessage()    {}
func (*KvPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{29}
}
func (m *KvPair) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KvPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KvPair.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KvPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KvPair.Merge(m, src)
}
func (m *KvPair) XXX_Size() int {
	return m.Size()
}
func (m *KvPair) XXX_DiscardUnknown() {
	xxx_messageInfo_KvPair.DiscardUnknown(m)
}

var xxx_messageInfo_KvPair proto.InternalMessageInfo

func (m *KvPair) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *KvPair) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *KvPair) GetCommitTs() uint64 {
	if m != nil {
		return m.CommitTs
	}
	return 0
}

type DuplicateDetectResponse struct {
	RegionError *errorpb.Error `protobuf:"bytes,1,opt,name=region_error,json=regionError,proto3" json:"region_error,omitempty"`
	KeyError    *Error         `protobuf:"bytes,2,opt,name=key_error,json=keyError,proto3" json:"key_error,omitempty"`
	// The these keys will be in asc order (but commit time is in desc order),
	//  and the content is just like following:
	// [
	//   {key: "key1", value: "value11", commit_ts: 1005},
	//   {key: "key1", value: "value12", commit_ts: 1004},
	//   {key: "key1", value: "value13", commit_ts: 1001},
	//   {key: "key2", value: "value21", commit_ts: 1004},
	//   {key: "key2", value: "value22", commit_ts: 1002},
	//   ...
	// ]
	Pairs []*KvPair `protobuf:"bytes,3,rep,name=pairs,proto3" json:"pairs,omitempty"`
}

func (m *DuplicateDetectResponse) Reset()         { *m = DuplicateDetectResponse{} }
func (m *DuplicateDetectResponse) String() string { return proto.CompactTextString(m) }
func (*DuplicateDetectResponse) ProtoMessage()    {}
func (*DuplicateDetectResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{30}
}
func (m *DuplicateDetectResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DuplicateDetectResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DuplicateDetectResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DuplicateDetectResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DuplicateDetectResponse.Merge(m, src)
}
func (m *DuplicateDetectResponse) XXX_Size() int {
	return m.Size()
}
func (m *DuplicateDetectResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DuplicateDetectResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DuplicateDetectResponse proto.InternalMessageInfo

func (m *DuplicateDetectResponse) GetRegionError() *errorpb.Error {
	if m != nil {
		return m.RegionError
	}
	return nil
}

func (m *DuplicateDetectResponse) GetKeyError() *Error {
	if m != nil {
		return m.KeyError
	}
	return nil
}

func (m *DuplicateDetectResponse) GetPairs() []*KvPair {
	if m != nil {
		return m.Pairs
	}
	return nil
}

type KVMeta struct {
	// The file name of the KV file.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// file offset, sometimes only need to get a part of data from the merged file
	RangeOffset uint64 `protobuf:"varint,11,opt,name=range_offset,json=rangeOffset,proto3" json:"range_offset,omitempty"`
	// file length for check.
	Length uint64 `protobuf:"varint,2,opt,name=length,proto3" json:"length,omitempty"`
	// range length of the merged file, if it exists.
	RangeLength uint64 `protobuf:"varint,12,opt,name=range_length,json=rangeLength,proto3" json:"range_length,omitempty"`
	// tell us which cf should apply. WRITE_CF or DEFAULT_CF e.g.
	Cf string `protobuf:"bytes,3,opt,name=cf,proto3" json:"cf,omitempty"`
	// is_delete represents whether we should delete the kv in tikv.
	// it may not be too much delete file. only rollBack operation will generate delete kv file.
	IsDelete bool `protobuf:"varint,4,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	// the key ts space being smaller than start_ts can be filter.
	StartTs uint64 `protobuf:"varint,10,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	// the key ts space large than restore_ts can be filter.
	RestoreTs uint64 `protobuf:"varint,5,opt,name=restore_ts,json=restoreTs,proto3" json:"restore_ts,omitempty"`
	StartKey  []byte `protobuf:"bytes,6,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey    []byte `protobuf:"bytes,7,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	// used for checksum when download kv file.
	Sha256 []byte `protobuf:"bytes,8,opt,name=sha256,proto3" json:"sha256,omitempty"`
	// the key ts space less than start_snapshot_ts can be filter.
	// Deprecated: this field 'start_snapshot_ts' is replaced by the field 'start_ts'.
	StartSnapshotTs uint64 `protobuf:"varint,9,opt,name=start_snapshot_ts,json=startSnapshotTs,proto3" json:"start_snapshot_ts,omitempty"`
	// the compression type for the file.
	CompressionType brpb.CompressionType `protobuf:"varint,13,opt,name=compression_type,json=compressionType,proto3,enum=backup.CompressionType" json:"compression_type,omitempty"`
	// encryption information of the kv file, not set if encryption is not enabled.
	FileEncryptionInfo *encryptionpb.FileEncryptionInfo `protobuf:"bytes,14,opt,name=file_encryption_info,json=fileEncryptionInfo,proto3" json:"file_encryption_info,omitempty"`
}

func (m *KVMeta) Reset()         { *m = KVMeta{} }
func (m *KVMeta) String() string { return proto.CompactTextString(m) }
func (*KVMeta) ProtoMessage()    {}
func (*KVMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{31}
}
func (m *KVMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KVMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KVMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KVMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KVMeta.Merge(m, src)
}
func (m *KVMeta) XXX_Size() int {
	return m.Size()
}
func (m *KVMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_KVMeta.DiscardUnknown(m)
}

var xxx_messageInfo_KVMeta proto.InternalMessageInfo

func (m *KVMeta) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *KVMeta) GetRangeOffset() uint64 {
	if m != nil {
		return m.RangeOffset
	}
	return 0
}

func (m *KVMeta) GetLength() uint64 {
	if m != nil {
		return m.Length
	}
	return 0
}

func (m *KVMeta) GetRangeLength() uint64 {
	if m != nil {
		return m.RangeLength
	}
	return 0
}

func (m *KVMeta) GetCf() string {
	if m != nil {
		return m.Cf
	}
	return ""
}

func (m *KVMeta) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *KVMeta) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *KVMeta) GetRestoreTs() uint64 {
	if m != nil {
		return m.RestoreTs
	}
	return 0
}

func (m *KVMeta) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *KVMeta) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (m *KVMeta) GetSha256() []byte {
	if m != nil {
		return m.Sha256
	}
	return nil
}

func (m *KVMeta) GetStartSnapshotTs() uint64 {
	if m != nil {
		return m.StartSnapshotTs
	}
	return 0
}

func (m *KVMeta) GetCompressionType() brpb.CompressionType {
	if m != nil {
		return m.CompressionType
	}
	return brpb.CompressionType_UNKNOWN
}

func (m *KVMeta) GetFileEncryptionInfo() *encryptionpb.FileEncryptionInfo {
	if m != nil {
		return m.FileEncryptionInfo
	}
	return nil
}

type ApplyRequest struct {
	// The meta of the KV file.
	Meta  *KVMeta   `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	Metas []*KVMeta `protobuf:"bytes,12,rep,name=metas,proto3" json:"metas,omitempty"`
	// Performs a key prefix rewrite after downloading the file.
	// All keys in the files will be rewritten as:
	//
	//  new_key = new_key_prefix + old_key[len(old_key_prefix)..]
	//
	// When used for TiDB, rewriting the prefix changes the table ID. Please
	// note that key-rewrite is applied on the origin keys in encoded
	// representation.
	//
	// You need to ensure that the keys before and after rewriting are in the
	// same order, otherwise the RPC request will fail.
	RewriteRule  RewriteRule    `protobuf:"bytes,2,opt,name=rewrite_rule,json=rewriteRule,proto3" json:"rewrite_rule"`
	RewriteRules []*RewriteRule `protobuf:"bytes,13,rep,name=rewrite_rules,json=rewriteRules,proto3" json:"rewrite_rules,omitempty"`
	// The identity for the stroage backend.
	// When this field presents, the storage would be cached.
	// If there is a cached storage, TiKV would use it driectly.
	StorageCacheId string               `protobuf:"bytes,5,opt,name=storage_cache_id,json=storageCacheId,proto3" json:"storage_cache_id,omitempty"`
	StorageBackend *brpb.StorageBackend `protobuf:"bytes,3,opt,name=storage_backend,json=storageBackend,proto3" json:"storage_backend,omitempty"`
	// context represents region info and it used to build raft commands.
	Context *kvrpcpb.Context `protobuf:"bytes,4,opt,name=context,proto3" json:"context,omitempty"`
	// plaintext data key to decrypt kv file if configured during log backup.
	CipherInfo *brpb.CipherInfo `protobuf:"bytes,11,opt,name=cipher_info,json=cipherInfo,proto3" json:"cipher_info,omitempty"`
	// master keys config used to decrypt data keys in restore if configured during log backup.
	MasterKeys []*encryptionpb.MasterKey `protobuf:"bytes,14,rep,name=master_keys,json=masterKeys,proto3" json:"master_keys,omitempty"`
}

func (m *ApplyRequest) Reset()         { *m = ApplyRequest{} }
func (m *ApplyRequest) String() string { return proto.CompactTextString(m) }
func (*ApplyRequest) ProtoMessage()    {}
func (*ApplyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{32}
}
func (m *ApplyRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ApplyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ApplyRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ApplyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyRequest.Merge(m, src)
}
func (m *ApplyRequest) XXX_Size() int {
	return m.Size()
}
func (m *ApplyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyRequest proto.InternalMessageInfo

func (m *ApplyRequest) GetMeta() *KVMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

func (m *ApplyRequest) GetMetas() []*KVMeta {
	if m != nil {
		return m.Metas
	}
	return nil
}

func (m *ApplyRequest) GetRewriteRule() RewriteRule {
	if m != nil {
		return m.RewriteRule
	}
	return RewriteRule{}
}

func (m *ApplyRequest) GetRewriteRules() []*RewriteRule {
	if m != nil {
		return m.RewriteRules
	}
	return nil
}

func (m *ApplyRequest) GetStorageCacheId() string {
	if m != nil {
		return m.StorageCacheId
	}
	return ""
}

func (m *ApplyRequest) GetStorageBackend() *brpb.StorageBackend {
	if m != nil {
		return m.StorageBackend
	}
	return nil
}

func (m *ApplyRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *ApplyRequest) GetCipherInfo() *brpb.CipherInfo {
	if m != nil {
		return m.CipherInfo
	}
	return nil
}

func (m *ApplyRequest) GetMasterKeys() []*encryptionpb.MasterKey {
	if m != nil {
		return m.MasterKeys
	}
	return nil
}

type ApplyResponse struct {
	// The actual key range (after rewrite) of the downloaded file. The range is
	// inclusive in both ends.
	Range Range  `protobuf:"bytes,1,opt,name=range,proto3" json:"range"`
	Error *Error `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *ApplyResponse) Reset()         { *m = ApplyResponse{} }
func (m *ApplyResponse) String() string { return proto.CompactTextString(m) }
func (*ApplyResponse) ProtoMessage()    {}
func (*ApplyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{33}
}
func (m *ApplyResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ApplyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ApplyResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ApplyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyResponse.Merge(m, src)
}
func (m *ApplyResponse) XXX_Size() int {
	return m.Size()
}
func (m *ApplyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyResponse proto.InternalMessageInfo

func (m *ApplyResponse) GetRange() Range {
	if m != nil {
		return m.Range
	}
	return Range{}
}

func (m *ApplyResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type ClearRequest struct {
	// clear files in import directory with given prefix.
	Prefix string `protobuf:"bytes,1,opt,name=prefix,proto3" json:"prefix,omitempty"`
}

func (m *ClearRequest) Reset()         { *m = ClearRequest{} }
func (m *ClearRequest) String() string { return proto.CompactTextString(m) }
func (*ClearRequest) ProtoMessage()    {}
func (*ClearRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{34}
}
func (m *ClearRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ClearRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ClearRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ClearRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearRequest.Merge(m, src)
}
func (m *ClearRequest) XXX_Size() int {
	return m.Size()
}
func (m *ClearRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ClearRequest proto.InternalMessageInfo

func (m *ClearRequest) GetPrefix() string {
	if m != nil {
		return m.Prefix
	}
	return ""
}

type ClearResponse struct {
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *ClearResponse) Reset()         { *m = ClearResponse{} }
func (m *ClearResponse) String() string { return proto.CompactTextString(m) }
func (*ClearResponse) ProtoMessage()    {}
func (*ClearResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_4d076b7935cfab6b, []int{35}
}
func (m *ClearResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ClearResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ClearResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ClearResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearResponse.Merge(m, src)
}
func (m *ClearResponse) XXX_Size() int {
	return m.Size()
}
func (m *ClearResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ClearResponse proto.InternalMessageInfo

func (m *ClearResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func init() {
	proto.RegisterEnum("import_sstpb.SwitchMode", SwitchMode_name, SwitchMode_value)
	proto.RegisterEnum("import_sstpb.DownloadRequestType", DownloadRequestType_name, DownloadRequestType_value)
	proto.RegisterEnum("import_sstpb.Pair_OP", Pair_OP_name, Pair_OP_value)
	proto.RegisterType((*SuspendImportRPCRequest)(nil), "import_sstpb.SuspendImportRPCRequest")
	proto.RegisterType((*SuspendImportRPCResponse)(nil), "import_sstpb.SuspendImportRPCResponse")
	proto.RegisterType((*SwitchModeRequest)(nil), "import_sstpb.SwitchModeRequest")
	proto.RegisterType((*SwitchModeResponse)(nil), "import_sstpb.SwitchModeResponse")
	proto.RegisterType((*GetModeRequest)(nil), "import_sstpb.GetModeRequest")
	proto.RegisterType((*GetModeResponse)(nil), "import_sstpb.GetModeResponse")
	proto.RegisterType((*Range)(nil), "import_sstpb.Range")
	proto.RegisterType((*SSTMeta)(nil), "import_sstpb.SSTMeta")
	proto.RegisterType((*RewriteRule)(nil), "import_sstpb.RewriteRule")
	proto.RegisterType((*UploadRequest)(nil), "import_sstpb.UploadRequest")
	proto.RegisterType((*UploadResponse)(nil), "import_sstpb.UploadResponse")
	proto.RegisterType((*IngestRequest)(nil), "import_sstpb.IngestRequest")
	proto.RegisterType((*MultiIngestRequest)(nil), "import_sstpb.MultiIngestRequest")
	proto.RegisterType((*IngestResponse)(nil), "import_sstpb.IngestResponse")
	proto.RegisterType((*CompactRequest)(nil), "import_sstpb.CompactRequest")
	proto.RegisterType((*CompactResponse)(nil), "import_sstpb.CompactResponse")
	proto.RegisterType((*DownloadRequest)(nil), "import_sstpb.DownloadRequest")
	proto.RegisterMapType((map[string]*SSTMeta)(nil), "import_sstpb.DownloadRequest.SstsEntry")
	proto.RegisterType((*Error)(nil), "import_sstpb.Error")
	proto.RegisterType((*DownloadResponse)(nil), "import_sstpb.DownloadResponse")
	proto.RegisterType((*SetDownloadSpeedLimitRequest)(nil), "import_sstpb.SetDownloadSpeedLimitRequest")
	proto.RegisterType((*SetDownloadSpeedLimitResponse)(nil), "import_sstpb.SetDownloadSpeedLimitResponse")
	proto.RegisterType((*Pair)(nil), "import_sstpb.Pair")
	proto.RegisterType((*WriteBatch)(nil), "import_sstpb.WriteBatch")
	proto.RegisterType((*WriteRequest)(nil), "import_sstpb.WriteRequest")
	proto.RegisterType((*WriteResponse)(nil), "import_sstpb.WriteResponse")
	proto.RegisterType((*RawWriteBatch)(nil), "import_sstpb.RawWriteBatch")
	proto.RegisterType((*RawWriteRequest)(nil), "import_sstpb.RawWriteRequest")
	proto.RegisterType((*RawWriteResponse)(nil), "import_sstpb.RawWriteResponse")
	proto.RegisterType((*DuplicateDetectRequest)(nil), "import_sstpb.DuplicateDetectRequest")
	proto.RegisterType((*KvPair)(nil), "import_sstpb.KvPair")
	proto.RegisterType((*DuplicateDetectResponse)(nil), "import_sstpb.DuplicateDetectResponse")
	proto.RegisterType((*KVMeta)(nil), "import_sstpb.KVMeta")
	proto.RegisterType((*ApplyRequest)(nil), "import_sstpb.ApplyRequest")
	proto.RegisterType((*ApplyResponse)(nil), "import_sstpb.ApplyResponse")
	proto.RegisterType((*ClearRequest)(nil), "import_sstpb.ClearRequest")
	proto.RegisterType((*ClearResponse)(nil), "import_sstpb.ClearResponse")
}

func init() { proto.RegisterFile("import_sstpb.proto", fileDescriptor_4d076b7935cfab6b) }

var fileDescriptor_4d076b7935cfab6b = []byte{
	// 2345 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0xcd, 0x72, 0xdb, 0xc8,
	0xf1, 0x27, 0xf8, 0xcd, 0xe6, 0x87, 0xe8, 0xb1, 0x2c, 0xd1, 0xb4, 0x2d, 0xdb, 0xf8, 0x7b, 0x77,
	0x65, 0xe9, 0x1f, 0xd9, 0x91, 0x37, 0xae, 0xad, 0x4d, 0x55, 0x5c, 0xd6, 0x47, 0x6c, 0x45, 0x96,
	0xad, 0x80, 0x8a, 0x73, 0xc8, 0x01, 0x05, 0x81, 0x43, 0x09, 0x21, 0x08, 0x20, 0x98, 0x21, 0x65,
	0xbe, 0x42, 0x4e, 0xc9, 0x2d, 0xd7, 0xe4, 0xb4, 0x95, 0x37, 0xc8, 0x1b, 0xec, 0x21, 0x87, 0x3d,
	0xee, 0x29, 0x95, 0xb2, 0xaf, 0xa9, 0x5c, 0xf2, 0x00, 0x49, 0x4d, 0xcf, 0x80, 0x04, 0x20, 0x4a,
	0xb4, 0x55, 0xc9, 0x89, 0x98, 0xee, 0x9e, 0x99, 0x9e, 0xfe, 0xfc, 0xcd, 0x10, 0x88, 0x33, 0x08,
	0xfc, 0x90, 0x9b, 0x8c, 0xf1, 0xe0, 0x78, 0x23, 0x08, 0x7d, 0xee, 0x93, 0x5a, 0x9c, 0xd6, 0xae,
	0x0d, 0x28, 0xb7, 0x22, 0x5e, 0xbb, 0x4e, 0xc3, 0xd0, 0x0f, 0xa7, 0xc3, 0xfe, 0x28, 0x0c, 0xec,
	0xc9, 0x10, 0x8e, 0xa7, 0x2c, 0x42, 0x3d, 0x3b, 0x1c, 0x07, 0xdc, 0xf1, 0xbd, 0x09, 0x6d, 0xf1,
	0xc4, 0x3f, 0xf1, 0xf1, 0xf3, 0x91, 0xf8, 0x52, 0xd4, 0x85, 0x70, 0xc8, 0x38, 0x7e, 0x4a, 0x82,
	0xfe, 0x7b, 0x0d, 0x96, 0x3b, 0x43, 0x16, 0x50, 0xaf, 0xbb, 0x87, 0xaa, 0x18, 0x87, 0xdb, 0x06,
	0xfd, 0xcd, 0x90, 0x32, 0x4e, 0xbe, 0x84, 0x25, 0x76, 0xea, 0x0f, 0xdd, 0xae, 0xc9, 0xa4, 0x84,
	0x29, 0xb5, 0x65, 0x2d, 0xed, 0x9e, 0xb6, 0x5a, 0x36, 0x16, 0x25, 0x37, 0x31, 0x9d, 0x91, 0x55,
	0x68, 0x76, 0x87, 0xa1, 0x25, 0x94, 0x31, 0x1d, 0xcf, 0x64, 0xd4, 0x66, 0xad, 0xec, 0x3d, 0x6d,
	0x35, 0x6f, 0x34, 0x22, 0xfa, 0x9e, 0xd7, 0xa1, 0x36, 0x23, 0x4b, 0x50, 0xb4, 0x2d, 0xd7, 0xa5,
	0x61, 0x2b, 0x77, 0x4f, 0x5b, 0xad, 0x18, 0x6a, 0xa4, 0xbf, 0x80, 0xd6, 0x79, 0x95, 0x58, 0xe0,
	0x7b, 0x8c, 0x92, 0x75, 0xb8, 0x66, 0xb9, 0x21, 0xb5, 0xba, 0xe3, 0x48, 0x29, 0xda, 0x55, 0xea,
	0x34, 0x15, 0xa3, 0x13, 0xd1, 0x75, 0x0f, 0xae, 0x75, 0xce, 0x1c, 0x6e, 0x9f, 0x1e, 0xf8, 0x5d,
	0x1a, 0x9d, 0xea, 0xff, 0x21, 0x3f, 0xf0, 0xbb, 0x14, 0x27, 0x35, 0x36, 0x5b, 0x1b, 0x09, 0xaf,
	0xc4, 0xc4, 0x51, 0x8a, 0xac, 0x43, 0x31, 0xb4, 0xbc, 0x13, 0x2a, 0xce, 0x90, 0x5b, 0xad, 0x6e,
	0x5e, 0x4f, 0xca, 0x1b, 0x82, 0x67, 0x28, 0x11, 0x7d, 0x11, 0x48, 0x7c, 0x3f, 0xa9, 0xb2, 0xde,
	0x84, 0xc6, 0x0b, 0xca, 0x63, 0x2a, 0xe8, 0xcf, 0x60, 0x61, 0x42, 0x51, 0xe7, 0xfa, 0x24, 0xad,
	0xf4, 0x47, 0x50, 0xc0, 0x9d, 0xc9, 0x22, 0x14, 0x18, 0xb7, 0x42, 0x8e, 0xf3, 0x6a, 0x86, 0x1c,
	0x90, 0x26, 0xe4, 0xa8, 0xd7, 0x45, 0xab, 0xd7, 0x0c, 0xf1, 0xa9, 0xff, 0x31, 0x07, 0xa5, 0x4e,
	0xe7, 0xe8, 0x80, 0x72, 0x8b, 0x10, 0xc8, 0x0f, 0x87, 0x4e, 0x57, 0x4d, 0xc1, 0x6f, 0xf2, 0x10,
	0x0a, 0x78, 0x06, 0x9c, 0x73, 0xc1, 0x29, 0xa5, 0x84, 0xd8, 0xd2, 0x0e, 0xed, 0x27, 0x9b, 0xe8,
	0xb4, 0xba, 0x21, 0x07, 0xc2, 0x97, 0x2e, 0xf5, 0x4e, 0xf8, 0x69, 0x2b, 0x8f, 0xbe, 0x56, 0x23,
	0xb2, 0x0c, 0x25, 0xbb, 0x67, 0x7a, 0xd6, 0x80, 0xb6, 0x0a, 0xca, 0xc9, 0xbd, 0xd7, 0xd6, 0x80,
	0x92, 0x5b, 0x50, 0x09, 0xe9, 0x09, 0x06, 0x49, 0xb7, 0x55, 0xc4, 0x39, 0x65, 0x49, 0xd8, 0xeb,
	0x92, 0xa7, 0x50, 0x53, 0x4c, 0x1a, 0xf8, 0xf6, 0x69, 0xab, 0xa4, 0xb4, 0x52, 0xf9, 0x61, 0x20,
	0x6f, 0x57, 0xb0, 0x8c, 0x6a, 0x38, 0x1d, 0x90, 0x35, 0xb8, 0x26, 0xc2, 0xb4, 0x4f, 0xc7, 0x26,
	0x7d, 0x67, 0xbb, 0x43, 0xe6, 0x8c, 0x68, 0xab, 0x8c, 0xd1, 0xb1, 0x40, 0xbd, 0xee, 0x3e, 0x1d,
	0xef, 0x46, 0x64, 0xa1, 0x00, 0xf7, 0xb9, 0xe5, 0x9a, 0xfd, 0x11, 0x6b, 0x55, 0xa4, 0x02, 0x48,
	0xd8, 0x1f, 0x31, 0x72, 0x17, 0xaa, 0x92, 0x79, 0x3c, 0xe6, 0x94, 0xb5, 0x00, 0xd9, 0x80, 0xa4,
	0x2d, 0x41, 0x21, 0x5f, 0x42, 0xd5, 0x0a, 0x1c, 0x73, 0x44, 0x43, 0xe6, 0xf8, 0x5e, 0xab, 0x8a,
	0x6e, 0xbb, 0xbe, 0x11, 0xe5, 0xe8, 0xf3, 0xc3, 0xbd, 0xb7, 0x92, 0x65, 0x80, 0x15, 0x38, 0xea,
	0x5b, 0xec, 0x69, 0x3b, 0xc1, 0x29, 0x0d, 0x4d, 0x67, 0xd4, 0xaa, 0xa1, 0xfd, 0xcb, 0x92, 0xb0,
	0x37, 0xd2, 0xff, 0xa1, 0x41, 0xd5, 0xa0, 0x67, 0xa1, 0xc3, 0xa9, 0x31, 0x74, 0x29, 0x79, 0x00,
	0x0d, 0xdf, 0x95, 0x87, 0x09, 0x42, 0xda, 0x73, 0xde, 0x29, 0x8f, 0xd5, 0x7c, 0x57, 0x9c, 0xe4,
	0x10, 0x69, 0x42, 0xca, 0xa3, 0x67, 0x71, 0x29, 0xe9, 0xf6, 0x9a, 0x47, 0xcf, 0xa6, 0x52, 0xff,
	0x07, 0x75, 0x21, 0xc5, 0x9d, 0x01, 0x65, 0xdc, 0x1a, 0x04, 0xe8, 0xbc, 0x3c, 0x0a, 0x1d, 0x45,
	0x34, 0x91, 0xef, 0xce, 0x89, 0xe7, 0x87, 0xd4, 0xb4, 0x7a, 0x9c, 0x86, 0x31, 0x69, 0xe9, 0xd3,
	0x45, 0xc9, 0x7d, 0x2e, 0x98, 0xd3, 0x59, 0x4f, 0x61, 0x59, 0xcd, 0x3a, 0xa6, 0x3d, 0xf1, 0x33,
	0x9d, 0x56, 0xc0, 0x69, 0x37, 0x24, 0x7b, 0x0b, 0xb9, 0x93, 0x79, 0xfa, 0xaf, 0xa0, 0xfe, 0x8b,
	0xc0, 0xf5, 0xad, 0x6e, 0x94, 0x98, 0xeb, 0x90, 0x17, 0xfe, 0xc5, 0x53, 0x56, 0x37, 0x6f, 0xa4,
	0x52, 0x40, 0x06, 0xef, 0xcb, 0x8c, 0x81, 0x42, 0x64, 0x11, 0xf2, 0x5d, 0x8b, 0x5b, 0xf2, 0xb0,
	0x82, 0x2a, 0x46, 0x5b, 0x25, 0x28, 0xd8, 0xa7, 0x43, 0xaf, 0x2f, 0x72, 0x2e, 0x5a, 0x5c, 0x65,
	0x61, 0x17, 0xea, 0x7b, 0x22, 0x49, 0x79, 0xb4, 0xdd, 0x1a, 0x94, 0x6c, 0xdf, 0xe3, 0xf4, 0x1d,
	0x57, 0x3b, 0x36, 0x27, 0xde, 0xdb, 0x96, 0x74, 0x23, 0x12, 0x20, 0x5f, 0x40, 0x8e, 0x31, 0xae,
	0x92, 0x63, 0xb6, 0x66, 0x86, 0x90, 0xd0, 0xfb, 0x40, 0x0e, 0x86, 0x2e, 0x77, 0xae, 0xbe, 0xd5,
	0x43, 0xc8, 0x33, 0xc6, 0xa3, 0x72, 0x73, 0xc1, 0x5e, 0x28, 0xa2, 0x3f, 0x85, 0x46, 0xb4, 0x8f,
	0xaa, 0x22, 0x0f, 0xa0, 0x80, 0x4d, 0x43, 0x6d, 0xd3, 0xd8, 0x88, 0x5a, 0xc8, 0xae, 0xf8, 0x35,
	0x24, 0x53, 0xff, 0xad, 0x06, 0x8d, 0x6d, 0x7f, 0x10, 0x58, 0xf6, 0x44, 0xc3, 0x49, 0xfe, 0x6b,
	0x73, 0xf3, 0xff, 0x3e, 0xd4, 0xfc, 0x21, 0x0f, 0x86, 0xdc, 0x74, 0xe9, 0x88, 0xba, 0x68, 0x94,
	0x82, 0x51, 0x95, 0xb4, 0x57, 0x82, 0x14, 0x3f, 0x6f, 0x6e, 0xce, 0x79, 0xf5, 0x6b, 0xb0, 0x30,
	0xd1, 0x45, 0xb9, 0xea, 0xaf, 0x05, 0x58, 0xd8, 0xf1, 0xcf, 0xbc, 0x78, 0x70, 0xfc, 0x58, 0x99,
	0x45, 0x43, 0xb3, 0x7c, 0x91, 0xd4, 0x2f, 0x25, 0xbc, 0xd1, 0x61, 0x9c, 0xed, 0x7a, 0x3c, 0x1c,
	0x4b, 0x43, 0x89, 0x6c, 0x0e, 0x29, 0xf3, 0xdd, 0x11, 0xed, 0x9a, 0x9c, 0xa9, 0xd8, 0x87, 0x88,
	0x74, 0xc4, 0xc8, 0x0f, 0xe6, 0xfb, 0x77, 0x2b, 0xff, 0xed, 0xdf, 0xee, 0x66, 0xd0, 0xcb, 0xa2,
	0x82, 0x62, 0x45, 0xab, 0x60, 0x45, 0xc3, 0x6f, 0xb2, 0x25, 0x4a, 0x16, 0x26, 0xaf, 0x19, 0x0e,
	0x5d, 0xda, 0xaa, 0xe3, 0x5a, 0x37, 0x53, 0x86, 0x9c, 0xa6, 0xb7, 0x5a, 0xaf, 0x1a, 0xc6, 0x32,
	0x7e, 0x1f, 0x16, 0x99, 0x1f, 0x72, 0xda, 0x35, 0xe3, 0x4b, 0xb1, 0xd6, 0x22, 0x1e, 0xfa, 0xe2,
	0xb5, 0x0c, 0x22, 0xa7, 0xc5, 0x48, 0x8c, 0x3c, 0x83, 0x05, 0xc6, 0xfd, 0xd0, 0x3a, 0xa1, 0xe6,
	0xb1, 0x65, 0xf7, 0x45, 0x43, 0x68, 0xa0, 0x4e, 0x4b, 0x1b, 0x62, 0x3c, 0x0c, 0x36, 0x3a, 0x92,
	0xbd, 0x25, 0xb9, 0x46, 0x83, 0x25, 0xc6, 0xa2, 0x91, 0x47, 0x0b, 0xd8, 0x96, 0x7d, 0x4a, 0x45,
	0xa1, 0xbe, 0x86, 0x27, 0x8e, 0x24, 0xb7, 0x05, 0x79, 0xaf, 0x4b, 0xda, 0x50, 0x71, 0x98, 0x19,
	0x5a, 0x67, 0x66, 0x7f, 0xd4, 0x5a, 0xc0, 0x72, 0x5b, 0x72, 0x98, 0x61, 0x9d, 0xed, 0x8f, 0xc8,
	0x13, 0xa8, 0x46, 0x25, 0xcf, 0xeb, 0xf9, 0xad, 0x26, 0xaa, 0x40, 0x22, 0x15, 0xb6, 0x65, 0xf1,
	0xf3, 0x7a, 0xbe, 0x01, 0xf6, 0xe4, 0x9b, 0xec, 0x08, 0x63, 0xa2, 0x2f, 0x4d, 0x3e, 0x0e, 0x68,
	0x8b, 0x60, 0x79, 0xbd, 0x7f, 0xa9, 0xd7, 0x8f, 0xc6, 0x01, 0x15, 0xe6, 0x9c, 0x0c, 0xe2, 0x61,
	0x78, 0x7d, 0x4e, 0x18, 0xb6, 0x5f, 0x43, 0x65, 0x12, 0x35, 0xa2, 0x7f, 0xf6, 0xe9, 0x18, 0x73,
	0xa1, 0x62, 0x88, 0x4f, 0xb2, 0x0e, 0x85, 0x91, 0xe5, 0x0e, 0xe9, 0xe5, 0x25, 0x40, 0xca, 0x7c,
	0x9d, 0xfd, 0x4a, 0xfb, 0x59, 0xbe, 0x5c, 0x6e, 0x56, 0x8c, 0xdc, 0x30, 0x74, 0x75, 0x03, 0x0a,
	0x98, 0x7e, 0xa4, 0x05, 0xa5, 0x01, 0x65, 0xcc, 0x52, 0x69, 0x56, 0x31, 0xa2, 0x21, 0x79, 0x04,
	0x55, 0x61, 0x52, 0x6a, 0xca, 0xec, 0xcd, 0xce, 0xcc, 0x5e, 0x40, 0x11, 0xfc, 0xd6, 0xdf, 0x6b,
	0xd0, 0x9c, 0x9e, 0x5f, 0x65, 0xff, 0xa3, 0xf9, 0x49, 0xac, 0xa2, 0x4e, 0xa5, 0xf2, 0x4d, 0x28,
	0x3b, 0xcc, 0xa4, 0x83, 0x80, 0x8f, 0x71, 0x4f, 0x74, 0xdb, 0xae, 0x18, 0x8a, 0x82, 0x20, 0x75,
	0xc9, 0xcd, 0x5a, 0x2b, 0x5e, 0x4e, 0xa6, 0x80, 0x20, 0x3f, 0x1b, 0x10, 0x14, 0x12, 0x80, 0x20,
	0xaa, 0x6f, 0xc5, 0xf9, 0xf5, 0xed, 0x19, 0xdc, 0xee, 0x50, 0x1e, 0x1d, 0xb3, 0x13, 0x50, 0xda,
	0x7d, 0xe5, 0x0c, 0x9c, 0x49, 0xd1, 0xba, 0x0b, 0x55, 0x26, 0x88, 0xa6, 0x2b, 0xa8, 0x78, 0xea,
	0xbc, 0x01, 0x6c, 0x22, 0xa7, 0xdf, 0x85, 0x3b, 0x17, 0x2c, 0xa0, 0x2a, 0x4d, 0x00, 0xf9, 0x43,
	0xcb, 0x09, 0xe3, 0x0e, 0xaf, 0x49, 0x87, 0x2f, 0xc6, 0x1d, 0x5e, 0x53, 0x9e, 0x25, 0x9f, 0x41,
	0xd6, 0x97, 0xbd, 0xb3, 0x91, 0x56, 0x5d, 0xac, 0xb3, 0xf1, 0xe6, 0xd0, 0xc8, 0xfa, 0x81, 0x7e,
	0x13, 0xb2, 0x6f, 0x0e, 0x49, 0x09, 0x72, 0x87, 0x43, 0xde, 0xcc, 0x10, 0x80, 0xe2, 0x0e, 0x75,
	0x29, 0xa7, 0x4d, 0x4d, 0xef, 0x00, 0xfc, 0x52, 0xe4, 0xe8, 0x96, 0xc5, 0xed, 0x53, 0xc4, 0x03,
	0xfe, 0x60, 0xe0, 0x70, 0x53, 0x81, 0xea, 0xbc, 0x51, 0x96, 0x84, 0x23, 0x01, 0xa4, 0x0b, 0x81,
	0xe5, 0x84, 0x51, 0x2b, 0x20, 0xe7, 0xf7, 0x33, 0xa4, 0x80, 0xfe, 0x27, 0x0d, 0x6a, 0xb8, 0xea,
	0x95, 0x5a, 0xe9, 0x63, 0x28, 0x1c, 0x0b, 0x6d, 0x54, 0xd8, 0xa5, 0xb0, 0xe7, 0x54, 0xdb, 0x97,
	0x19, 0x43, 0x0a, 0x7e, 0x4a, 0x7d, 0x9f, 0xb6, 0xe4, 0x13, 0xa8, 0x2b, 0x1d, 0x55, 0xb8, 0x3e,
	0x4c, 0x36, 0xab, 0xcb, 0x42, 0x6c, 0x1d, 0x0a, 0x42, 0xd5, 0x39, 0x5d, 0x51, 0xca, 0x08, 0x60,
	0x61, 0x58, 0x67, 0x31, 0x2b, 0x37, 0x21, 0xc7, 0xb9, 0xab, 0xec, 0x2b, 0x3e, 0x3f, 0xde, 0xb4,
	0xa4, 0x01, 0xd9, 0x49, 0xc7, 0xc8, 0x72, 0xa6, 0x7f, 0xa3, 0xc1, 0x42, 0xb4, 0xfa, 0x95, 0xac,
	0xfd, 0x24, 0x69, 0xed, 0x5b, 0xe9, 0x24, 0x3d, 0xfb, 0xaf, 0x19, 0xfc, 0xd7, 0xd0, 0x9c, 0x6a,
	0xfa, 0x3f, 0xb6, 0xf9, 0x5f, 0x34, 0x58, 0xda, 0x19, 0x06, 0xae, 0x63, 0x5b, 0x9c, 0xee, 0x50,
	0x4e, 0xed, 0x2b, 0x81, 0x9f, 0x5b, 0x50, 0xc1, 0x1b, 0x8c, 0x80, 0xb3, 0x2a, 0xf3, 0xca, 0x48,
	0xd8, 0xa7, 0x63, 0x71, 0x95, 0x50, 0xe0, 0x1e, 0x8d, 0x50, 0x33, 0x8a, 0x12, 0xd2, 0x8b, 0x32,
	0x26, 0xe0, 0xaf, 0xef, 0xb9, 0x63, 0xac, 0x41, 0x65, 0xa3, 0xd4, 0xa7, 0xe3, 0x37, 0x9e, 0x3b,
	0x26, 0x3a, 0xd4, 0x07, 0x8e, 0x67, 0x4e, 0x93, 0x4c, 0x16, 0xa3, 0xea, 0xc0, 0xf1, 0xb6, 0x55,
	0x9e, 0xe9, 0x07, 0x50, 0xdc, 0x1f, 0x7d, 0x52, 0x19, 0x48, 0xa4, 0x6d, 0x2e, 0x99, 0xb6, 0xfa,
	0x9f, 0x35, 0x58, 0x3e, 0x67, 0x0a, 0x65, 0xfe, 0x1f, 0x4e, 0xef, 0x35, 0x97, 0xc0, 0xb4, 0xe8,
	0x4a, 0x83, 0x6e, 0x78, 0x0c, 0x15, 0xbc, 0xce, 0xc4, 0x1a, 0xc3, 0x4c, 0xaf, 0x09, 0x13, 0xc8,
	0x19, 0x6b, 0x51, 0x70, 0xe7, 0xd0, 0x71, 0x8b, 0x49, 0x69, 0x79, 0xd4, 0xa8, 0x72, 0xfc, 0x2b,
	0x07, 0xc5, 0xfd, 0xb7, 0xd1, 0xb5, 0x10, 0x41, 0x8d, 0x16, 0x03, 0x35, 0xf7, 0xa1, 0x86, 0x9d,
	0xc2, 0xf4, 0x7b, 0x3d, 0x46, 0x39, 0x5e, 0x73, 0xf2, 0x46, 0x15, 0x69, 0x6f, 0x90, 0x14, 0xab,
	0xf3, 0xd9, 0x44, 0x9d, 0x9f, 0x4c, 0x55, 0xdc, 0x5a, 0x6c, 0xea, 0x2b, 0x29, 0xd2, 0x80, 0xac,
	0xdd, 0x53, 0x77, 0xff, 0xac, 0xdd, 0x13, 0x66, 0x75, 0x98, 0xd9, 0xc5, 0x52, 0xa9, 0x1c, 0x59,
	0x76, 0x98, 0x2c, 0x9d, 0xc2, 0xc9, 0x32, 0x34, 0x78, 0x74, 0x1d, 0x2b, 0xe1, 0xf8, 0x88, 0x91,
	0x3b, 0x20, 0xb0, 0x1c, 0xf6, 0xcf, 0x89, 0x87, 0x2b, 0x8a, 0x72, 0xc4, 0x92, 0x41, 0x55, 0xbc,
	0x38, 0xa8, 0x4a, 0x89, 0xa0, 0x5a, 0x82, 0x22, 0x3b, 0xb5, 0x36, 0x7f, 0xf4, 0x14, 0xef, 0x8f,
	0x35, 0x43, 0x8d, 0xc4, 0x15, 0x53, 0xae, 0xc6, 0x3c, 0x2b, 0x60, 0xa7, 0x3e, 0x2a, 0x24, 0xaf,
	0x8f, 0x0b, 0xc8, 0xe8, 0x28, 0xfa, 0x11, 0x23, 0x5b, 0xd0, 0xb4, 0xfd, 0x41, 0x10, 0x52, 0x26,
	0x6e, 0x7f, 0x12, 0xca, 0xd4, 0xb1, 0x79, 0x2c, 0x4f, 0x00, 0xd0, 0x94, 0x8f, 0x00, 0x66, 0xc1,
	0x4e, 0x12, 0x88, 0x01, 0x8b, 0x3d, 0xc7, 0xa5, 0xe6, 0xf4, 0x89, 0x47, 0x02, 0x29, 0x89, 0xe5,
	0xee, 0x6d, 0x24, 0x9e, 0x7e, 0x7e, 0xea, 0xb8, 0x74, 0x77, 0x42, 0x40, 0x58, 0x45, 0x7a, 0xe7,
	0x68, 0xfa, 0x3f, 0x73, 0x50, 0x7b, 0x1e, 0x04, 0xee, 0x38, 0xca, 0xd1, 0xd5, 0x44, 0x05, 0x4b,
	0x47, 0xcc, 0x5b, 0xd9, 0x93, 0xb1, 0x7c, 0xad, 0x45, 0x55, 0xa1, 0x36, 0x33, 0xb8, 0xde, 0xc6,
	0x8a, 0xc2, 0x39, 0x48, 0x9c, 0xbd, 0x02, 0x24, 0xfe, 0x09, 0xd4, 0x93, 0x58, 0xb8, 0x3e, 0x0f,
	0x0b, 0xd7, 0xc2, 0x38, 0x0a, 0x9e, 0x05, 0x62, 0x0b, 0x33, 0x41, 0xec, 0x0c, 0xbc, 0x9c, 0xfb,
	0x24, 0xbc, 0x1c, 0x2b, 0x74, 0xf9, 0x79, 0x85, 0x2e, 0x85, 0x8a, 0xab, 0x1f, 0x85, 0x8a, 0xbf,
	0x82, 0xea, 0xc0, 0x62, 0xe2, 0x66, 0xde, 0xa7, 0x63, 0xd6, 0x6a, 0xa0, 0x25, 0x96, 0x93, 0x11,
	0x70, 0x80, 0x02, 0xfb, 0x74, 0x6c, 0xc0, 0x20, 0xfa, 0x64, 0x7a, 0x1f, 0xea, 0xca, 0xdf, 0x57,
	0x85, 0x8a, 0x93, 0xc6, 0x91, 0x9d, 0xd7, 0x38, 0xf4, 0xcf, 0xa1, 0xb6, 0xed, 0x52, 0x2b, 0x8c,
	0x82, 0x6b, 0x09, 0x8a, 0xb1, 0xf7, 0x8b, 0x8a, 0xa1, 0x46, 0xfa, 0xd7, 0x50, 0x57, 0x72, 0x9f,
	0xdc, 0x9c, 0xd6, 0x1e, 0x00, 0x4c, 0x1f, 0xc5, 0x04, 0xc0, 0x7a, 0xed, 0x87, 0x03, 0xcb, 0x95,
	0x60, 0x4b, 0xbe, 0x1a, 0x36, 0xb5, 0xb5, 0x47, 0x70, 0x7d, 0xc6, 0x25, 0x41, 0x88, 0xbc, 0xa2,
	0x27, 0x96, 0x3d, 0x6e, 0x66, 0x48, 0x0d, 0xca, 0xc2, 0x42, 0x81, 0x65, 0xd3, 0xa6, 0xb6, 0xf9,
	0xef, 0x32, 0x54, 0xe4, 0xec, 0x4e, 0xe7, 0x88, 0xfc, 0x3c, 0xb1, 0xc9, 0xdd, 0x0b, 0xdf, 0xe4,
	0xe4, 0xd2, 0xed, 0x7b, 0x17, 0x0b, 0x28, 0xb8, 0x99, 0x21, 0x2f, 0xa1, 0xa4, 0x5e, 0xfe, 0xc8,
	0xed, 0xa4, 0x78, 0xf2, 0x89, 0xb0, 0x7d, 0xe7, 0x02, 0xee, 0x64, 0xa5, 0x17, 0x50, 0x94, 0x2f,
	0x1c, 0x24, 0x05, 0x21, 0x12, 0x8f, 0x2a, 0xed, 0xdb, 0xb3, 0x99, 0xd1, 0x32, 0xab, 0x1a, 0xd9,
	0x85, 0xa2, 0x7c, 0x45, 0x48, 0x2f, 0x94, 0x78, 0xc3, 0x48, 0x2f, 0x94, 0x7c, 0x78, 0x90, 0x27,
	0x53, 0xf7, 0xf8, 0xf4, 0xc9, 0x92, 0x4f, 0x0d, 0xe9, 0x93, 0xa5, 0x2f, 0xff, 0x19, 0x12, 0xc2,
	0x8d, 0x99, 0xa8, 0x9d, 0xac, 0xa5, 0x0c, 0x7c, 0xc9, 0xdd, 0xa0, 0xbd, 0xfe, 0x51, 0xb2, 0x93,
	0x3d, 0xf7, 0xa1, 0x1c, 0xf1, 0xc9, 0x9d, 0x4b, 0xaf, 0x99, 0xed, 0x95, 0x8b, 0xd8, 0x93, 0xc5,
	0x76, 0xa0, 0x80, 0xa8, 0x8b, 0xb4, 0x67, 0x40, 0xe9, 0x68, 0x99, 0x5b, 0x33, 0x79, 0x31, 0xbf,
	0x1c, 0x40, 0x39, 0x82, 0x6f, 0x69, 0x95, 0x52, 0x00, 0x34, 0xad, 0x52, 0x1a, 0xf5, 0xe1, 0x72,
	0x6f, 0xa0, 0x1a, 0x7b, 0x99, 0x22, 0xa9, 0x60, 0x3d, 0xff, 0x68, 0x35, 0xd7, 0xe1, 0xc7, 0xb0,
	0x90, 0x82, 0x39, 0xe4, 0x41, 0xca, 0x34, 0x33, 0x01, 0x61, 0xfb, 0xb3, 0x39, 0x52, 0xd1, 0x0e,
	0x8f, 0x35, 0xb2, 0x05, 0x05, 0xac, 0x5b, 0x69, 0x4b, 0xc6, 0x9b, 0x57, 0xda, 0x92, 0x89, 0x42,
	0x87, 0x89, 0x02, 0x58, 0x66, 0x44, 0x6f, 0x64, 0xe9, 0x85, 0xe2, 0x85, 0x2a, 0xbd, 0x50, 0xa2,
	0x38, 0xe9, 0x19, 0x62, 0x43, 0x33, 0xfd, 0xb7, 0x04, 0x49, 0x9d, 0xe5, 0x82, 0x7f, 0x52, 0xda,
	0x9f, 0xcf, 0x13, 0x8b, 0x36, 0xd9, 0xda, 0xfc, 0xfe, 0x9b, 0xb2, 0xf6, 0xed, 0xfb, 0x15, 0xed,
	0xbb, 0xf7, 0x2b, 0xda, 0xdf, 0xdf, 0xaf, 0x68, 0xbf, 0xfb, 0xb0, 0x92, 0xf9, 0xc3, 0x87, 0x95,
	0xcc, 0x77, 0x1f, 0x56, 0x32, 0xdf, 0x7f, 0x58, 0xc9, 0x40, 0xd3, 0x0f, 0x4f, 0x36, 0xb8, 0xd3,
	0x1f, 0x6d, 0xf4, 0x47, 0xf8, 0x1f, 0xce, 0x71, 0x11, 0x7f, 0x9e, 0xfc, 0x27, 0x00, 0x00, 0xff,
	0xff, 0xad, 0x9b, 0x4c, 0x26, 0x61, 0x1a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ImportSSTClient is the client API for ImportSST service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ImportSSTClient interface {
	// Switch to normal/import mode.
	SwitchMode(ctx context.Context, in *SwitchModeRequest, opts ...grpc.CallOption) (*SwitchModeResponse, error)
	// Get import mode(normal/import).
	GetMode(ctx context.Context, in *GetModeRequest, opts ...grpc.CallOption) (*GetModeResponse, error)
	// Upload an SST file to a server.
	Upload(ctx context.Context, opts ...grpc.CallOption) (ImportSST_UploadClient, error)
	// Ingest an uploaded SST file to a region.
	Ingest(ctx context.Context, in *IngestRequest, opts ...grpc.CallOption) (*IngestResponse, error)
	// Compact the specific range for better performance.
	Compact(ctx context.Context, in *CompactRequest, opts ...grpc.CallOption) (*CompactResponse, error)
	SetDownloadSpeedLimit(ctx context.Context, in *SetDownloadSpeedLimitRequest, opts ...grpc.CallOption) (*SetDownloadSpeedLimitResponse, error)
	// Download an SST file from an external storage, and performs key-rewrite
	// after downloading.
	Download(ctx context.Context, in *DownloadRequest, opts ...grpc.CallOption) (*DownloadResponse, error)
	// Open a write stream to generate sst files
	Write(ctx context.Context, opts ...grpc.CallOption) (ImportSST_WriteClient, error)
	RawWrite(ctx context.Context, opts ...grpc.CallOption) (ImportSST_RawWriteClient, error)
	// Ingest Multiple files in one request
	MultiIngest(ctx context.Context, in *MultiIngestRequest, opts ...grpc.CallOption) (*IngestResponse, error)
	// Collect duplicate data from TiKV.
	DuplicateDetect(ctx context.Context, in *DuplicateDetectRequest, opts ...grpc.CallOption) (ImportSST_DuplicateDetectClient, error)
	// Apply download & apply increment kv files to TiKV.
	Apply(ctx context.Context, in *ApplyRequest, opts ...grpc.CallOption) (*ApplyResponse, error)
	// ClearFiles clear applied file after restore succeed.
	ClearFiles(ctx context.Context, in *ClearRequest, opts ...grpc.CallOption) (*ClearResponse, error)
	// Suspend ingest for data listeners don't support catching import data.
	SuspendImportRPC(ctx context.Context, in *SuspendImportRPCRequest, opts ...grpc.CallOption) (*SuspendImportRPCResponse, error)
}

type importSSTClient struct {
	cc *grpc.ClientConn
}

func NewImportSSTClient(cc *grpc.ClientConn) ImportSSTClient {
	return &importSSTClient{cc}
}

func (c *importSSTClient) SwitchMode(ctx context.Context, in *SwitchModeRequest, opts ...grpc.CallOption) (*SwitchModeResponse, error) {
	out := new(SwitchModeResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/SwitchMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) GetMode(ctx context.Context, in *GetModeRequest, opts ...grpc.CallOption) (*GetModeResponse, error) {
	out := new(GetModeResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/GetMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) Upload(ctx context.Context, opts ...grpc.CallOption) (ImportSST_UploadClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ImportSST_serviceDesc.Streams[0], "/import_sstpb.ImportSST/Upload", opts...)
	if err != nil {
		return nil, err
	}
	x := &importSSTUploadClient{stream}
	return x, nil
}

type ImportSST_UploadClient interface {
	Send(*UploadRequest) error
	CloseAndRecv() (*UploadResponse, error)
	grpc.ClientStream
}

type importSSTUploadClient struct {
	grpc.ClientStream
}

func (x *importSSTUploadClient) Send(m *UploadRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *importSSTUploadClient) CloseAndRecv() (*UploadResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(UploadResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *importSSTClient) Ingest(ctx context.Context, in *IngestRequest, opts ...grpc.CallOption) (*IngestResponse, error) {
	out := new(IngestResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/Ingest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) Compact(ctx context.Context, in *CompactRequest, opts ...grpc.CallOption) (*CompactResponse, error) {
	out := new(CompactResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/Compact", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) SetDownloadSpeedLimit(ctx context.Context, in *SetDownloadSpeedLimitRequest, opts ...grpc.CallOption) (*SetDownloadSpeedLimitResponse, error) {
	out := new(SetDownloadSpeedLimitResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/SetDownloadSpeedLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) Download(ctx context.Context, in *DownloadRequest, opts ...grpc.CallOption) (*DownloadResponse, error) {
	out := new(DownloadResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/Download", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) Write(ctx context.Context, opts ...grpc.CallOption) (ImportSST_WriteClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ImportSST_serviceDesc.Streams[1], "/import_sstpb.ImportSST/Write", opts...)
	if err != nil {
		return nil, err
	}
	x := &importSSTWriteClient{stream}
	return x, nil
}

type ImportSST_WriteClient interface {
	Send(*WriteRequest) error
	CloseAndRecv() (*WriteResponse, error)
	grpc.ClientStream
}

type importSSTWriteClient struct {
	grpc.ClientStream
}

func (x *importSSTWriteClient) Send(m *WriteRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *importSSTWriteClient) CloseAndRecv() (*WriteResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(WriteResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *importSSTClient) RawWrite(ctx context.Context, opts ...grpc.CallOption) (ImportSST_RawWriteClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ImportSST_serviceDesc.Streams[2], "/import_sstpb.ImportSST/RawWrite", opts...)
	if err != nil {
		return nil, err
	}
	x := &importSSTRawWriteClient{stream}
	return x, nil
}

type ImportSST_RawWriteClient interface {
	Send(*RawWriteRequest) error
	CloseAndRecv() (*RawWriteResponse, error)
	grpc.ClientStream
}

type importSSTRawWriteClient struct {
	grpc.ClientStream
}

func (x *importSSTRawWriteClient) Send(m *RawWriteRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *importSSTRawWriteClient) CloseAndRecv() (*RawWriteResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(RawWriteResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *importSSTClient) MultiIngest(ctx context.Context, in *MultiIngestRequest, opts ...grpc.CallOption) (*IngestResponse, error) {
	out := new(IngestResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/MultiIngest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) DuplicateDetect(ctx context.Context, in *DuplicateDetectRequest, opts ...grpc.CallOption) (ImportSST_DuplicateDetectClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ImportSST_serviceDesc.Streams[3], "/import_sstpb.ImportSST/DuplicateDetect", opts...)
	if err != nil {
		return nil, err
	}
	x := &importSSTDuplicateDetectClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ImportSST_DuplicateDetectClient interface {
	Recv() (*DuplicateDetectResponse, error)
	grpc.ClientStream
}

type importSSTDuplicateDetectClient struct {
	grpc.ClientStream
}

func (x *importSSTDuplicateDetectClient) Recv() (*DuplicateDetectResponse, error) {
	m := new(DuplicateDetectResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *importSSTClient) Apply(ctx context.Context, in *ApplyRequest, opts ...grpc.CallOption) (*ApplyResponse, error) {
	out := new(ApplyResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/Apply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) ClearFiles(ctx context.Context, in *ClearRequest, opts ...grpc.CallOption) (*ClearResponse, error) {
	out := new(ClearResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/ClearFiles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importSSTClient) SuspendImportRPC(ctx context.Context, in *SuspendImportRPCRequest, opts ...grpc.CallOption) (*SuspendImportRPCResponse, error) {
	out := new(SuspendImportRPCResponse)
	err := c.cc.Invoke(ctx, "/import_sstpb.ImportSST/SuspendImportRPC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ImportSSTServer is the server API for ImportSST service.
type ImportSSTServer interface {
	// Switch to normal/import mode.
	SwitchMode(context.Context, *SwitchModeRequest) (*SwitchModeResponse, error)
	// Get import mode(normal/import).
	GetMode(context.Context, *GetModeRequest) (*GetModeResponse, error)
	// Upload an SST file to a server.
	Upload(ImportSST_UploadServer) error
	// Ingest an uploaded SST file to a region.
	Ingest(context.Context, *IngestRequest) (*IngestResponse, error)
	// Compact the specific range for better performance.
	Compact(context.Context, *CompactRequest) (*CompactResponse, error)
	SetDownloadSpeedLimit(context.Context, *SetDownloadSpeedLimitRequest) (*SetDownloadSpeedLimitResponse, error)
	// Download an SST file from an external storage, and performs key-rewrite
	// after downloading.
	Download(context.Context, *DownloadRequest) (*DownloadResponse, error)
	// Open a write stream to generate sst files
	Write(ImportSST_WriteServer) error
	RawWrite(ImportSST_RawWriteServer) error
	// Ingest Multiple files in one request
	MultiIngest(context.Context, *MultiIngestRequest) (*IngestResponse, error)
	// Collect duplicate data from TiKV.
	DuplicateDetect(*DuplicateDetectRequest, ImportSST_DuplicateDetectServer) error
	// Apply download & apply increment kv files to TiKV.
	Apply(context.Context, *ApplyRequest) (*ApplyResponse, error)
	// ClearFiles clear applied file after restore succeed.
	ClearFiles(context.Context, *ClearRequest) (*ClearResponse, error)
	// Suspend ingest for data listeners don't support catching import data.
	SuspendImportRPC(context.Context, *SuspendImportRPCRequest) (*SuspendImportRPCResponse, error)
}

// UnimplementedImportSSTServer can be embedded to have forward compatible implementations.
type UnimplementedImportSSTServer struct {
}

func (*UnimplementedImportSSTServer) SwitchMode(ctx context.Context, req *SwitchModeRequest) (*SwitchModeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SwitchMode not implemented")
}
func (*UnimplementedImportSSTServer) GetMode(ctx context.Context, req *GetModeRequest) (*GetModeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMode not implemented")
}
func (*UnimplementedImportSSTServer) Upload(srv ImportSST_UploadServer) error {
	return status.Errorf(codes.Unimplemented, "method Upload not implemented")
}
func (*UnimplementedImportSSTServer) Ingest(ctx context.Context, req *IngestRequest) (*IngestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ingest not implemented")
}
func (*UnimplementedImportSSTServer) Compact(ctx context.Context, req *CompactRequest) (*CompactResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Compact not implemented")
}
func (*UnimplementedImportSSTServer) SetDownloadSpeedLimit(ctx context.Context, req *SetDownloadSpeedLimitRequest) (*SetDownloadSpeedLimitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetDownloadSpeedLimit not implemented")
}
func (*UnimplementedImportSSTServer) Download(ctx context.Context, req *DownloadRequest) (*DownloadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Download not implemented")
}
func (*UnimplementedImportSSTServer) Write(srv ImportSST_WriteServer) error {
	return status.Errorf(codes.Unimplemented, "method Write not implemented")
}
func (*UnimplementedImportSSTServer) RawWrite(srv ImportSST_RawWriteServer) error {
	return status.Errorf(codes.Unimplemented, "method RawWrite not implemented")
}
func (*UnimplementedImportSSTServer) MultiIngest(ctx context.Context, req *MultiIngestRequest) (*IngestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MultiIngest not implemented")
}
func (*UnimplementedImportSSTServer) DuplicateDetect(req *DuplicateDetectRequest, srv ImportSST_DuplicateDetectServer) error {
	return status.Errorf(codes.Unimplemented, "method DuplicateDetect not implemented")
}
func (*UnimplementedImportSSTServer) Apply(ctx context.Context, req *ApplyRequest) (*ApplyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Apply not implemented")
}
func (*UnimplementedImportSSTServer) ClearFiles(ctx context.Context, req *ClearRequest) (*ClearResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearFiles not implemented")
}
func (*UnimplementedImportSSTServer) SuspendImportRPC(ctx context.Context, req *SuspendImportRPCRequest) (*SuspendImportRPCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SuspendImportRPC not implemented")
}

func RegisterImportSSTServer(s *grpc.Server, srv ImportSSTServer) {
	s.RegisterService(&_ImportSST_serviceDesc, srv)
}

func _ImportSST_SwitchMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchModeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).SwitchMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/SwitchMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).SwitchMode(ctx, req.(*SwitchModeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_GetMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).GetMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/GetMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).GetMode(ctx, req.(*GetModeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_Upload_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ImportSSTServer).Upload(&importSSTUploadServer{stream})
}

type ImportSST_UploadServer interface {
	SendAndClose(*UploadResponse) error
	Recv() (*UploadRequest, error)
	grpc.ServerStream
}

type importSSTUploadServer struct {
	grpc.ServerStream
}

func (x *importSSTUploadServer) SendAndClose(m *UploadResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *importSSTUploadServer) Recv() (*UploadRequest, error) {
	m := new(UploadRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ImportSST_Ingest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IngestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).Ingest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/Ingest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).Ingest(ctx, req.(*IngestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_Compact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).Compact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/Compact",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).Compact(ctx, req.(*CompactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_SetDownloadSpeedLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDownloadSpeedLimitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).SetDownloadSpeedLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/SetDownloadSpeedLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).SetDownloadSpeedLimit(ctx, req.(*SetDownloadSpeedLimitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_Download_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).Download(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/Download",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).Download(ctx, req.(*DownloadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_Write_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ImportSSTServer).Write(&importSSTWriteServer{stream})
}

type ImportSST_WriteServer interface {
	SendAndClose(*WriteResponse) error
	Recv() (*WriteRequest, error)
	grpc.ServerStream
}

type importSSTWriteServer struct {
	grpc.ServerStream
}

func (x *importSSTWriteServer) SendAndClose(m *WriteResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *importSSTWriteServer) Recv() (*WriteRequest, error) {
	m := new(WriteRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ImportSST_RawWrite_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ImportSSTServer).RawWrite(&importSSTRawWriteServer{stream})
}

type ImportSST_RawWriteServer interface {
	SendAndClose(*RawWriteResponse) error
	Recv() (*RawWriteRequest, error)
	grpc.ServerStream
}

type importSSTRawWriteServer struct {
	grpc.ServerStream
}

func (x *importSSTRawWriteServer) SendAndClose(m *RawWriteResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *importSSTRawWriteServer) Recv() (*RawWriteRequest, error) {
	m := new(RawWriteRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ImportSST_MultiIngest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MultiIngestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).MultiIngest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/MultiIngest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).MultiIngest(ctx, req.(*MultiIngestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_DuplicateDetect_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DuplicateDetectRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ImportSSTServer).DuplicateDetect(m, &importSSTDuplicateDetectServer{stream})
}

type ImportSST_DuplicateDetectServer interface {
	Send(*DuplicateDetectResponse) error
	grpc.ServerStream
}

type importSSTDuplicateDetectServer struct {
	grpc.ServerStream
}

func (x *importSSTDuplicateDetectServer) Send(m *DuplicateDetectResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _ImportSST_Apply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).Apply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/Apply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).Apply(ctx, req.(*ApplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_ClearFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).ClearFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/ClearFiles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).ClearFiles(ctx, req.(*ClearRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportSST_SuspendImportRPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SuspendImportRPCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportSSTServer).SuspendImportRPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_sstpb.ImportSST/SuspendImportRPC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportSSTServer).SuspendImportRPC(ctx, req.(*SuspendImportRPCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ImportSST_serviceDesc = grpc.ServiceDesc{
	ServiceName: "import_sstpb.ImportSST",
	HandlerType: (*ImportSSTServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SwitchMode",
			Handler:    _ImportSST_SwitchMode_Handler,
		},
		{
			MethodName: "GetMode",
			Handler:    _ImportSST_GetMode_Handler,
		},
		{
			MethodName: "Ingest",
			Handler:    _ImportSST_Ingest_Handler,
		},
		{
			MethodName: "Compact",
			Handler:    _ImportSST_Compact_Handler,
		},
		{
			MethodName: "SetDownloadSpeedLimit",
			Handler:    _ImportSST_SetDownloadSpeedLimit_Handler,
		},
		{
			MethodName: "Download",
			Handler:    _ImportSST_Download_Handler,
		},
		{
			MethodName: "MultiIngest",
			Handler:    _ImportSST_MultiIngest_Handler,
		},
		{
			MethodName: "Apply",
			Handler:    _ImportSST_Apply_Handler,
		},
		{
			MethodName: "ClearFiles",
			Handler:    _ImportSST_ClearFiles_Handler,
		},
		{
			MethodName: "SuspendImportRPC",
			Handler:    _ImportSST_SuspendImportRPC_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Upload",
			Handler:       _ImportSST_Upload_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "Write",
			Handler:       _ImportSST_Write_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "RawWrite",
			Handler:       _ImportSST_RawWrite_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "DuplicateDetect",
			Handler:       _ImportSST_DuplicateDetect_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "import_sstpb.proto",
}

func (m *SuspendImportRPCRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SuspendImportRPCRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SuspendImportRPCRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Caller) > 0 {
		i -= len(m.Caller)
		copy(dAtA[i:], m.Caller)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Caller)))
		i--
		dAtA[i] = 0x1a
	}
	if m.DurationInSecs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.DurationInSecs))
		i--
		dAtA[i] = 0x10
	}
	if m.ShouldSuspendImports {
		i--
		if m.ShouldSuspendImports {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SuspendImportRPCResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SuspendImportRPCResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SuspendImportRPCResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AlreadySuspended {
		i--
		if m.AlreadySuspended {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SwitchModeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SwitchModeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SwitchModeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Mode != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Mode))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SwitchModeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SwitchModeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SwitchModeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetModeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetModeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetModeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetModeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetModeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetModeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Mode != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Mode))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Range) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Range) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Range) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.End) > 0 {
		i -= len(m.End)
		copy(dAtA[i:], m.End)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.End)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Start) > 0 {
		i -= len(m.Start)
		copy(dAtA[i:], m.Start)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Start)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SSTMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SSTMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SSTMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CipherIv) > 0 {
		i -= len(m.CipherIv)
		copy(dAtA[i:], m.CipherIv)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.CipherIv)))
		i--
		dAtA[i] = 0x62
	}
	if m.ApiVersion != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.ApiVersion))
		i--
		dAtA[i] = 0x58
	}
	if m.TotalBytes != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.TotalBytes))
		i--
		dAtA[i] = 0x50
	}
	if m.TotalKvs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.TotalKvs))
		i--
		dAtA[i] = 0x48
	}
	if m.EndKeyExclusive {
		i--
		if m.EndKeyExclusive {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x40
	}
	if m.RegionEpoch != nil {
		{
			size, err := m.RegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.RegionId != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x30
	}
	if len(m.CfName) > 0 {
		i -= len(m.CfName)
		copy(dAtA[i:], m.CfName)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.CfName)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Length != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Length))
		i--
		dAtA[i] = 0x20
	}
	if m.Crc32 != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Crc32))
		i--
		dAtA[i] = 0x18
	}
	if m.Range != nil {
		{
			size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RewriteRule) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RewriteRule) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RewriteRule) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IgnoreBeforeTimestamp != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.IgnoreBeforeTimestamp))
		i--
		dAtA[i] = 0x28
	}
	if m.IgnoreAfterTimestamp != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.IgnoreAfterTimestamp))
		i--
		dAtA[i] = 0x20
	}
	if m.NewTimestamp != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.NewTimestamp))
		i--
		dAtA[i] = 0x18
	}
	if len(m.NewKeyPrefix) > 0 {
		i -= len(m.NewKeyPrefix)
		copy(dAtA[i:], m.NewKeyPrefix)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.NewKeyPrefix)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.OldKeyPrefix) > 0 {
		i -= len(m.OldKeyPrefix)
		copy(dAtA[i:], m.OldKeyPrefix)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.OldKeyPrefix)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UploadRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UploadRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Chunk != nil {
		{
			size := m.Chunk.Size()
			i -= size
			if _, err := m.Chunk.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *UploadRequest_Meta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UploadRequest_Meta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *UploadRequest_Data) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UploadRequest_Data) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Data != nil {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *UploadResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UploadResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *IngestRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IngestRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IngestRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Sst != nil {
		{
			size, err := m.Sst.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MultiIngestRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MultiIngestRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MultiIngestRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ssts) > 0 {
		for iNdEx := len(m.Ssts) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ssts[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *IngestResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IngestResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IngestResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CompactRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CompactRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.OutputLevel != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.OutputLevel))
		i--
		dAtA[i] = 0x10
	}
	if m.Range != nil {
		{
			size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CompactResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CompactResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *DownloadRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownloadRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DownloadRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SortedRewriteRules) > 0 {
		for iNdEx := len(m.SortedRewriteRules) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.SortedRewriteRules[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xa2
		}
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if m.RequestType != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.RequestType))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x90
	}
	if len(m.StorageCacheId) > 0 {
		i -= len(m.StorageCacheId)
		copy(dAtA[i:], m.StorageCacheId)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.StorageCacheId)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if m.CipherInfo != nil {
		{
			size, err := m.CipherInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if m.IsRawKv {
		i--
		if m.IsRawKv {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x78
	}
	if m.StorageBackend != nil {
		{
			size, err := m.StorageBackend.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	{
		size, err := m.RewriteRule.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintImportSstpb(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x6a
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x4a
	}
	if m.ResolvedTs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.ResolvedTs))
		i--
		dAtA[i] = 0x18
	}
	{
		size, err := m.Sst.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintImportSstpb(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if len(m.Ssts) > 0 {
		for k := range m.Ssts {
			v := m.Ssts[k]
			baseI := i
			if v != nil {
				{
					size, err := v.MarshalToSizedBuffer(dAtA[:i])
					if err != nil {
						return 0, err
					}
					i -= size
					i = encodeVarintImportSstpb(dAtA, i, uint64(size))
				}
				i--
				dAtA[i] = 0x12
			}
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintImportSstpb(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintImportSstpb(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StoreError != nil {
		{
			size, err := m.StoreError.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DownloadResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DownloadResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DownloadResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ssts) > 0 {
		for iNdEx := len(m.Ssts) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ssts[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if m.Length != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Length))
		i--
		dAtA[i] = 0x28
	}
	if m.Crc32 != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Crc32))
		i--
		dAtA[i] = 0x20
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.IsEmpty {
		i--
		if m.IsEmpty {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	{
		size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintImportSstpb(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *SetDownloadSpeedLimitRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetDownloadSpeedLimitRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SetDownloadSpeedLimitRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SpeedLimit != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.SpeedLimit))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SetDownloadSpeedLimitResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetDownloadSpeedLimitResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SetDownloadSpeedLimitResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *Pair) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Pair) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Pair) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Op != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Op))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WriteBatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteBatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteBatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Pairs) > 0 {
		for iNdEx := len(m.Pairs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Pairs[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.CommitTs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.CommitTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *WriteRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Chunk != nil {
		{
			size := m.Chunk.Size()
			i -= size
			if _, err := m.Chunk.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *WriteRequest_Meta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteRequest_Meta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *WriteRequest_Batch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteRequest_Batch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Batch != nil {
		{
			size, err := m.Batch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *WriteResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Metas) > 0 {
		for iNdEx := len(m.Metas) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Metas[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RawWriteBatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RawWriteBatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RawWriteBatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Ts != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Ts))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Pairs) > 0 {
		for iNdEx := len(m.Pairs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Pairs[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Ttl != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Ttl))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RawWriteRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RawWriteRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RawWriteRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Chunk != nil {
		{
			size := m.Chunk.Size()
			i -= size
			if _, err := m.Chunk.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *RawWriteRequest_Meta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RawWriteRequest_Meta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *RawWriteRequest_Batch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RawWriteRequest_Batch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Batch != nil {
		{
			size, err := m.Batch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *RawWriteResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RawWriteResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RawWriteResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Metas) > 0 {
		for iNdEx := len(m.Metas) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Metas[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DuplicateDetectRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DuplicateDetectRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DuplicateDetectRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MinCommitTs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.MinCommitTs))
		i--
		dAtA[i] = 0x28
	}
	if m.KeyOnly {
		i--
		if m.KeyOnly {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x12
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *KvPair) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KvPair) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KvPair) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CommitTs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.CommitTs))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DuplicateDetectResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DuplicateDetectResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DuplicateDetectResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Pairs) > 0 {
		for iNdEx := len(m.Pairs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Pairs[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.KeyError != nil {
		{
			size, err := m.KeyError.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.RegionError != nil {
		{
			size, err := m.RegionError.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *KVMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KVMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KVMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.FileEncryptionInfo != nil {
		{
			size, err := m.FileEncryptionInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x72
	}
	if m.CompressionType != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.CompressionType))
		i--
		dAtA[i] = 0x68
	}
	if m.RangeLength != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.RangeLength))
		i--
		dAtA[i] = 0x60
	}
	if m.RangeOffset != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.RangeOffset))
		i--
		dAtA[i] = 0x58
	}
	if m.StartTs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x50
	}
	if m.StartSnapshotTs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.StartSnapshotTs))
		i--
		dAtA[i] = 0x48
	}
	if len(m.Sha256) > 0 {
		i -= len(m.Sha256)
		copy(dAtA[i:], m.Sha256)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Sha256)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x32
	}
	if m.RestoreTs != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.RestoreTs))
		i--
		dAtA[i] = 0x28
	}
	if m.IsDelete {
		i--
		if m.IsDelete {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if len(m.Cf) > 0 {
		i -= len(m.Cf)
		copy(dAtA[i:], m.Cf)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Cf)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Length != 0 {
		i = encodeVarintImportSstpb(dAtA, i, uint64(m.Length))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ApplyRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ApplyRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.MasterKeys) > 0 {
		for iNdEx := len(m.MasterKeys) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MasterKeys[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x72
		}
	}
	if len(m.RewriteRules) > 0 {
		for iNdEx := len(m.RewriteRules) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RewriteRules[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x6a
		}
	}
	if len(m.Metas) > 0 {
		for iNdEx := len(m.Metas) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Metas[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportSstpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x62
		}
	}
	if m.CipherInfo != nil {
		{
			size, err := m.CipherInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if len(m.StorageCacheId) > 0 {
		i -= len(m.StorageCacheId)
		copy(dAtA[i:], m.StorageCacheId)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.StorageCacheId)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.StorageBackend != nil {
		{
			size, err := m.StorageBackend.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.RewriteRule.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintImportSstpb(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ApplyResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ApplyResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ApplyResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	{
		size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintImportSstpb(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *ClearRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ClearRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Prefix) > 0 {
		i -= len(m.Prefix)
		copy(dAtA[i:], m.Prefix)
		i = encodeVarintImportSstpb(dAtA, i, uint64(len(m.Prefix)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ClearResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ClearResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportSstpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintImportSstpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovImportSstpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *SuspendImportRPCRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShouldSuspendImports {
		n += 2
	}
	if m.DurationInSecs != 0 {
		n += 1 + sovImportSstpb(uint64(m.DurationInSecs))
	}
	l = len(m.Caller)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *SuspendImportRPCResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.AlreadySuspended {
		n += 2
	}
	return n
}

func (m *SwitchModeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Mode != 0 {
		n += 1 + sovImportSstpb(uint64(m.Mode))
	}
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *SwitchModeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetModeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetModeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Mode != 0 {
		n += 1 + sovImportSstpb(uint64(m.Mode))
	}
	return n
}

func (m *Range) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Start)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.End)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *SSTMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.Range != nil {
		l = m.Range.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.Crc32 != 0 {
		n += 1 + sovImportSstpb(uint64(m.Crc32))
	}
	if m.Length != 0 {
		n += 1 + sovImportSstpb(uint64(m.Length))
	}
	l = len(m.CfName)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.RegionId != 0 {
		n += 1 + sovImportSstpb(uint64(m.RegionId))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.EndKeyExclusive {
		n += 2
	}
	if m.TotalKvs != 0 {
		n += 1 + sovImportSstpb(uint64(m.TotalKvs))
	}
	if m.TotalBytes != 0 {
		n += 1 + sovImportSstpb(uint64(m.TotalBytes))
	}
	if m.ApiVersion != 0 {
		n += 1 + sovImportSstpb(uint64(m.ApiVersion))
	}
	l = len(m.CipherIv)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *RewriteRule) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.OldKeyPrefix)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.NewKeyPrefix)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.NewTimestamp != 0 {
		n += 1 + sovImportSstpb(uint64(m.NewTimestamp))
	}
	if m.IgnoreAfterTimestamp != 0 {
		n += 1 + sovImportSstpb(uint64(m.IgnoreAfterTimestamp))
	}
	if m.IgnoreBeforeTimestamp != 0 {
		n += 1 + sovImportSstpb(uint64(m.IgnoreBeforeTimestamp))
	}
	return n
}

func (m *UploadRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Chunk != nil {
		n += m.Chunk.Size()
	}
	return n
}

func (m *UploadRequest_Meta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}
func (m *UploadRequest_Data) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}
func (m *UploadResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *IngestRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.Sst != nil {
		l = m.Sst.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *MultiIngestRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if len(m.Ssts) > 0 {
		for _, e := range m.Ssts {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *IngestResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *CompactRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Range != nil {
		l = m.Range.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.OutputLevel != 0 {
		n += 1 + sovImportSstpb(uint64(m.OutputLevel))
	}
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *CompactResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *DownloadRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Ssts) > 0 {
		for k, v := range m.Ssts {
			_ = k
			_ = v
			l = 0
			if v != nil {
				l = v.Size()
				l += 1 + sovImportSstpb(uint64(l))
			}
			mapEntrySize := 1 + len(k) + sovImportSstpb(uint64(len(k))) + l
			n += mapEntrySize + 1 + sovImportSstpb(uint64(mapEntrySize))
		}
	}
	l = m.Sst.Size()
	n += 1 + l + sovImportSstpb(uint64(l))
	if m.ResolvedTs != 0 {
		n += 1 + sovImportSstpb(uint64(m.ResolvedTs))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = m.RewriteRule.Size()
	n += 1 + l + sovImportSstpb(uint64(l))
	if m.StorageBackend != nil {
		l = m.StorageBackend.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.IsRawKv {
		n += 2
	}
	if m.CipherInfo != nil {
		l = m.CipherInfo.Size()
		n += 2 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.StorageCacheId)
	if l > 0 {
		n += 2 + l + sovImportSstpb(uint64(l))
	}
	if m.RequestType != 0 {
		n += 2 + sovImportSstpb(uint64(m.RequestType))
	}
	if m.Context != nil {
		l = m.Context.Size()
		n += 2 + l + sovImportSstpb(uint64(l))
	}
	if len(m.SortedRewriteRules) > 0 {
		for _, e := range m.SortedRewriteRules {
			l = e.Size()
			n += 2 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.StoreError != nil {
		l = m.StoreError.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *DownloadResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Range.Size()
	n += 1 + l + sovImportSstpb(uint64(l))
	if m.IsEmpty {
		n += 2
	}
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.Crc32 != 0 {
		n += 1 + sovImportSstpb(uint64(m.Crc32))
	}
	if m.Length != 0 {
		n += 1 + sovImportSstpb(uint64(m.Length))
	}
	if len(m.Ssts) > 0 {
		for _, e := range m.Ssts {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *SetDownloadSpeedLimitRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SpeedLimit != 0 {
		n += 1 + sovImportSstpb(uint64(m.SpeedLimit))
	}
	return n
}

func (m *SetDownloadSpeedLimitResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *Pair) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.Op != 0 {
		n += 1 + sovImportSstpb(uint64(m.Op))
	}
	return n
}

func (m *WriteBatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CommitTs != 0 {
		n += 1 + sovImportSstpb(uint64(m.CommitTs))
	}
	if len(m.Pairs) > 0 {
		for _, e := range m.Pairs {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *WriteRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Chunk != nil {
		n += m.Chunk.Size()
	}
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *WriteRequest_Meta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}
func (m *WriteRequest_Batch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Batch != nil {
		l = m.Batch.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}
func (m *WriteResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if len(m.Metas) > 0 {
		for _, e := range m.Metas {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *RawWriteBatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Ttl != 0 {
		n += 1 + sovImportSstpb(uint64(m.Ttl))
	}
	if len(m.Pairs) > 0 {
		for _, e := range m.Pairs {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	if m.Ts != 0 {
		n += 1 + sovImportSstpb(uint64(m.Ts))
	}
	return n
}

func (m *RawWriteRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Chunk != nil {
		n += m.Chunk.Size()
	}
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *RawWriteRequest_Meta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}
func (m *RawWriteRequest_Batch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Batch != nil {
		l = m.Batch.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}
func (m *RawWriteResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if len(m.Metas) > 0 {
		for _, e := range m.Metas {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *DuplicateDetectRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.KeyOnly {
		n += 2
	}
	if m.MinCommitTs != 0 {
		n += 1 + sovImportSstpb(uint64(m.MinCommitTs))
	}
	return n
}

func (m *KvPair) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.CommitTs != 0 {
		n += 1 + sovImportSstpb(uint64(m.CommitTs))
	}
	return n
}

func (m *DuplicateDetectResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionError != nil {
		l = m.RegionError.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.KeyError != nil {
		l = m.KeyError.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if len(m.Pairs) > 0 {
		for _, e := range m.Pairs {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *KVMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.Length != 0 {
		n += 1 + sovImportSstpb(uint64(m.Length))
	}
	l = len(m.Cf)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.IsDelete {
		n += 2
	}
	if m.RestoreTs != 0 {
		n += 1 + sovImportSstpb(uint64(m.RestoreTs))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.Sha256)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.StartSnapshotTs != 0 {
		n += 1 + sovImportSstpb(uint64(m.StartSnapshotTs))
	}
	if m.StartTs != 0 {
		n += 1 + sovImportSstpb(uint64(m.StartTs))
	}
	if m.RangeOffset != 0 {
		n += 1 + sovImportSstpb(uint64(m.RangeOffset))
	}
	if m.RangeLength != 0 {
		n += 1 + sovImportSstpb(uint64(m.RangeLength))
	}
	if m.CompressionType != 0 {
		n += 1 + sovImportSstpb(uint64(m.CompressionType))
	}
	if m.FileEncryptionInfo != nil {
		l = m.FileEncryptionInfo.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *ApplyRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = m.RewriteRule.Size()
	n += 1 + l + sovImportSstpb(uint64(l))
	if m.StorageBackend != nil {
		l = m.StorageBackend.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	l = len(m.StorageCacheId)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if m.CipherInfo != nil {
		l = m.CipherInfo.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	if len(m.Metas) > 0 {
		for _, e := range m.Metas {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	if len(m.RewriteRules) > 0 {
		for _, e := range m.RewriteRules {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	if len(m.MasterKeys) > 0 {
		for _, e := range m.MasterKeys {
			l = e.Size()
			n += 1 + l + sovImportSstpb(uint64(l))
		}
	}
	return n
}

func (m *ApplyResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Range.Size()
	n += 1 + l + sovImportSstpb(uint64(l))
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *ClearRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Prefix)
	if l > 0 {
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func (m *ClearResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovImportSstpb(uint64(l))
	}
	return n
}

func sovImportSstpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozImportSstpb(x uint64) (n int) {
	return sovImportSstpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SuspendImportRPCRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SuspendImportRPCRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SuspendImportRPCRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShouldSuspendImports", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShouldSuspendImports = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationInSecs", wireType)
			}
			m.DurationInSecs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DurationInSecs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Caller", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Caller = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SuspendImportRPCResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SuspendImportRPCResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SuspendImportRPCResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AlreadySuspended", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AlreadySuspended = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SwitchModeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SwitchModeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SwitchModeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mode", wireType)
			}
			m.Mode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Mode |= SwitchMode(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, &Range{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SwitchModeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SwitchModeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SwitchModeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetModeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetModeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetModeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetModeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetModeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetModeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mode", wireType)
			}
			m.Mode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Mode |= SwitchMode(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Range) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Range: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Range: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Start = append(m.Start[:0], dAtA[iNdEx:postIndex]...)
			if m.Start == nil {
				m.Start = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.End = append(m.End[:0], dAtA[iNdEx:postIndex]...)
			if m.End == nil {
				m.End = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SSTMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SSTMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SSTMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Range == nil {
				m.Range = &Range{}
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Crc32", wireType)
			}
			m.Crc32 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Crc32 |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Length", wireType)
			}
			m.Length = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Length |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CfName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CfName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKeyExclusive", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.EndKeyExclusive = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalKvs", wireType)
			}
			m.TotalKvs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalKvs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalBytes", wireType)
			}
			m.TotalBytes = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalBytes |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiVersion", wireType)
			}
			m.ApiVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApiVersion |= kvrpcpb.APIVersion(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CipherIv", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CipherIv = append(m.CipherIv[:0], dAtA[iNdEx:postIndex]...)
			if m.CipherIv == nil {
				m.CipherIv = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RewriteRule) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RewriteRule: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RewriteRule: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OldKeyPrefix", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OldKeyPrefix = append(m.OldKeyPrefix[:0], dAtA[iNdEx:postIndex]...)
			if m.OldKeyPrefix == nil {
				m.OldKeyPrefix = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewKeyPrefix", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.NewKeyPrefix = append(m.NewKeyPrefix[:0], dAtA[iNdEx:postIndex]...)
			if m.NewKeyPrefix == nil {
				m.NewKeyPrefix = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewTimestamp", wireType)
			}
			m.NewTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewTimestamp |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IgnoreAfterTimestamp", wireType)
			}
			m.IgnoreAfterTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IgnoreAfterTimestamp |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IgnoreBeforeTimestamp", wireType)
			}
			m.IgnoreBeforeTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IgnoreBeforeTimestamp |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UploadRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UploadRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &SSTMeta{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &UploadRequest_Meta{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := make([]byte, postIndex-iNdEx)
			copy(v, dAtA[iNdEx:postIndex])
			m.Chunk = &UploadRequest_Data{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UploadResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UploadResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IngestRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IngestRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IngestRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Sst == nil {
				m.Sst = &SSTMeta{}
			}
			if err := m.Sst.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MultiIngestRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MultiIngestRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MultiIngestRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ssts", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ssts = append(m.Ssts, &SSTMeta{})
			if err := m.Ssts[len(m.Ssts)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IngestResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IngestResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IngestResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &errorpb.Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Range == nil {
				m.Range = &Range{}
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OutputLevel", wireType)
			}
			m.OutputLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OutputLevel |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownloadRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DownloadRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DownloadRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ssts", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Ssts == nil {
				m.Ssts = make(map[string]*SSTMeta)
			}
			var mapkey string
			var mapvalue *SSTMeta
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowImportSstpb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowImportSstpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthImportSstpb
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthImportSstpb
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var mapmsglen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowImportSstpb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						mapmsglen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if mapmsglen < 0 {
						return ErrInvalidLengthImportSstpb
					}
					postmsgIndex := iNdEx + mapmsglen
					if postmsgIndex < 0 {
						return ErrInvalidLengthImportSstpb
					}
					if postmsgIndex > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = &SSTMeta{}
					if err := mapvalue.Unmarshal(dAtA[iNdEx:postmsgIndex]); err != nil {
						return err
					}
					iNdEx = postmsgIndex
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipImportSstpb(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthImportSstpb
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Ssts[mapkey] = mapvalue
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sst", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Sst.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResolvedTs", wireType)
			}
			m.ResolvedTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResolvedTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RewriteRule", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.RewriteRule.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorageBackend", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StorageBackend == nil {
				m.StorageBackend = &brpb.StorageBackend{}
			}
			if err := m.StorageBackend.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 15:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsRawKv", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsRawKv = bool(v != 0)
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CipherInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CipherInfo == nil {
				m.CipherInfo = &brpb.CipherInfo{}
			}
			if err := m.CipherInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorageCacheId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StorageCacheId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestType", wireType)
			}
			m.RequestType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestType |= DownloadRequestType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SortedRewriteRules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SortedRewriteRules = append(m.SortedRewriteRules, &RewriteRule{})
			if err := m.SortedRewriteRules[len(m.SortedRewriteRules)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StoreError == nil {
				m.StoreError = &errorpb.Error{}
			}
			if err := m.StoreError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DownloadResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DownloadResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DownloadResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsEmpty", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsEmpty = bool(v != 0)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Crc32", wireType)
			}
			m.Crc32 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Crc32 |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Length", wireType)
			}
			m.Length = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Length |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ssts", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ssts = append(m.Ssts, &SSTMeta{})
			if err := m.Ssts[len(m.Ssts)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetDownloadSpeedLimitRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetDownloadSpeedLimitRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetDownloadSpeedLimitRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpeedLimit", wireType)
			}
			m.SpeedLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpeedLimit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetDownloadSpeedLimitResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetDownloadSpeedLimitResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetDownloadSpeedLimitResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Pair) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Pair: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Pair: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Op", wireType)
			}
			m.Op = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Op |= Pair_OP(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteBatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteBatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteBatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitTs", wireType)
			}
			m.CommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pairs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pairs = append(m.Pairs, &Pair{})
			if err := m.Pairs[len(m.Pairs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &SSTMeta{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &WriteRequest_Meta{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Batch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &WriteBatch{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &WriteRequest_Batch{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metas", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metas = append(m.Metas, &SSTMeta{})
			if err := m.Metas[len(m.Metas)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RawWriteBatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RawWriteBatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RawWriteBatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pairs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pairs = append(m.Pairs, &Pair{})
			if err := m.Pairs[len(m.Pairs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RawWriteRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RawWriteRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RawWriteRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &SSTMeta{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &RawWriteRequest_Meta{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Batch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &RawWriteBatch{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &RawWriteRequest_Batch{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RawWriteResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RawWriteResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RawWriteResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metas", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metas = append(m.Metas, &SSTMeta{})
			if err := m.Metas[len(m.Metas)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DuplicateDetectRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DuplicateDetectRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DuplicateDetectRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyOnly", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.KeyOnly = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinCommitTs", wireType)
			}
			m.MinCommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MinCommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KvPair) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KvPair: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KvPair: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitTs", wireType)
			}
			m.CommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DuplicateDetectResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DuplicateDetectResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DuplicateDetectResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionError == nil {
				m.RegionError = &errorpb.Error{}
			}
			if err := m.RegionError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyError == nil {
				m.KeyError = &Error{}
			}
			if err := m.KeyError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pairs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pairs = append(m.Pairs, &KvPair{})
			if err := m.Pairs[len(m.Pairs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KVMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KVMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KVMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Length", wireType)
			}
			m.Length = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Length |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cf", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cf = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsDelete", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDelete = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RestoreTs", wireType)
			}
			m.RestoreTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RestoreTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sha256", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sha256 = append(m.Sha256[:0], dAtA[iNdEx:postIndex]...)
			if m.Sha256 == nil {
				m.Sha256 = []byte{}
			}
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartSnapshotTs", wireType)
			}
			m.StartSnapshotTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartSnapshotTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeOffset", wireType)
			}
			m.RangeOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RangeOffset |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RangeLength", wireType)
			}
			m.RangeLength = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RangeLength |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CompressionType", wireType)
			}
			m.CompressionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CompressionType |= brpb.CompressionType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileEncryptionInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.FileEncryptionInfo == nil {
				m.FileEncryptionInfo = &encryptionpb.FileEncryptionInfo{}
			}
			if err := m.FileEncryptionInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ApplyRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ApplyRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &KVMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RewriteRule", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.RewriteRule.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorageBackend", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StorageBackend == nil {
				m.StorageBackend = &brpb.StorageBackend{}
			}
			if err := m.StorageBackend.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorageCacheId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StorageCacheId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CipherInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.CipherInfo == nil {
				m.CipherInfo = &brpb.CipherInfo{}
			}
			if err := m.CipherInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metas", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metas = append(m.Metas, &KVMeta{})
			if err := m.Metas[len(m.Metas)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RewriteRules", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RewriteRules = append(m.RewriteRules, &RewriteRule{})
			if err := m.RewriteRules[len(m.RewriteRules)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MasterKeys", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MasterKeys = append(m.MasterKeys, &encryptionpb.MasterKey{})
			if err := m.MasterKeys[len(m.MasterKeys)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ApplyResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ApplyResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ApplyResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ClearRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ClearRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Prefix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Prefix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ClearResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ClearResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportSstpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportSstpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportSstpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipImportSstpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowImportSstpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowImportSstpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthImportSstpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupImportSstpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthImportSstpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthImportSstpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowImportSstpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupImportSstpb = fmt.Errorf("proto: unexpected end of group")
)
