// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: mpp.proto

package mpp

import (
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	coprocessor "github.com/pingcap/kvproto/pkg/coprocessor"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// TaskMeta contains meta of a mpp plan, including query's ts and task address.
type TaskMeta struct {
	StartTs                uint64             `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	TaskId                 int64              `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	PartitionId            int64              `protobuf:"varint,3,opt,name=partition_id,json=partitionId,proto3" json:"partition_id,omitempty"`
	Address                string             `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	GatherId               uint64             `protobuf:"varint,5,opt,name=gather_id,json=gatherId,proto3" json:"gather_id,omitempty"`
	QueryTs                uint64             `protobuf:"varint,6,opt,name=query_ts,json=queryTs,proto3" json:"query_ts,omitempty"`
	LocalQueryId           uint64             `protobuf:"varint,7,opt,name=local_query_id,json=localQueryId,proto3" json:"local_query_id,omitempty"`
	ServerId               uint64             `protobuf:"varint,8,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
	MppVersion             int64              `protobuf:"varint,9,opt,name=mpp_version,json=mppVersion,proto3" json:"mpp_version,omitempty"`
	KeyspaceId             uint32             `protobuf:"varint,10,opt,name=keyspace_id,json=keyspaceId,proto3" json:"keyspace_id,omitempty"`
	CoordinatorAddress     string             `protobuf:"bytes,11,opt,name=coordinator_address,json=coordinatorAddress,proto3" json:"coordinator_address,omitempty"`
	ReportExecutionSummary bool               `protobuf:"varint,12,opt,name=report_execution_summary,json=reportExecutionSummary,proto3" json:"report_execution_summary,omitempty"`
	ApiVersion             kvrpcpb.APIVersion `protobuf:"varint,16,opt,name=api_version,json=apiVersion,proto3,enum=kvrpcpb.APIVersion" json:"api_version,omitempty"`
	ResourceGroupName      string             `protobuf:"bytes,17,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	ConnectionId           uint64             `protobuf:"varint,18,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
	ConnectionAlias        string             `protobuf:"bytes,19,opt,name=connection_alias,json=connectionAlias,proto3" json:"connection_alias,omitempty"`
}

func (m *TaskMeta) Reset()         { *m = TaskMeta{} }
func (m *TaskMeta) String() string { return proto.CompactTextString(m) }
func (*TaskMeta) ProtoMessage()    {}
func (*TaskMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{0}
}
func (m *TaskMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TaskMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TaskMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TaskMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskMeta.Merge(m, src)
}
func (m *TaskMeta) XXX_Size() int {
	return m.Size()
}
func (m *TaskMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskMeta.DiscardUnknown(m)
}

var xxx_messageInfo_TaskMeta proto.InternalMessageInfo

func (m *TaskMeta) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *TaskMeta) GetTaskId() int64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *TaskMeta) GetPartitionId() int64 {
	if m != nil {
		return m.PartitionId
	}
	return 0
}

func (m *TaskMeta) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *TaskMeta) GetGatherId() uint64 {
	if m != nil {
		return m.GatherId
	}
	return 0
}

func (m *TaskMeta) GetQueryTs() uint64 {
	if m != nil {
		return m.QueryTs
	}
	return 0
}

func (m *TaskMeta) GetLocalQueryId() uint64 {
	if m != nil {
		return m.LocalQueryId
	}
	return 0
}

func (m *TaskMeta) GetServerId() uint64 {
	if m != nil {
		return m.ServerId
	}
	return 0
}

func (m *TaskMeta) GetMppVersion() int64 {
	if m != nil {
		return m.MppVersion
	}
	return 0
}

func (m *TaskMeta) GetKeyspaceId() uint32 {
	if m != nil {
		return m.KeyspaceId
	}
	return 0
}

func (m *TaskMeta) GetCoordinatorAddress() string {
	if m != nil {
		return m.CoordinatorAddress
	}
	return ""
}

func (m *TaskMeta) GetReportExecutionSummary() bool {
	if m != nil {
		return m.ReportExecutionSummary
	}
	return false
}

func (m *TaskMeta) GetApiVersion() kvrpcpb.APIVersion {
	if m != nil {
		return m.ApiVersion
	}
	return kvrpcpb.APIVersion_V1
}

func (m *TaskMeta) GetResourceGroupName() string {
	if m != nil {
		return m.ResourceGroupName
	}
	return ""
}

func (m *TaskMeta) GetConnectionId() uint64 {
	if m != nil {
		return m.ConnectionId
	}
	return 0
}

func (m *TaskMeta) GetConnectionAlias() string {
	if m != nil {
		return m.ConnectionAlias
	}
	return ""
}

type IsAliveRequest struct {
}

func (m *IsAliveRequest) Reset()         { *m = IsAliveRequest{} }
func (m *IsAliveRequest) String() string { return proto.CompactTextString(m) }
func (*IsAliveRequest) ProtoMessage()    {}
func (*IsAliveRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{1}
}
func (m *IsAliveRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IsAliveRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IsAliveRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IsAliveRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsAliveRequest.Merge(m, src)
}
func (m *IsAliveRequest) XXX_Size() int {
	return m.Size()
}
func (m *IsAliveRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IsAliveRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IsAliveRequest proto.InternalMessageInfo

type IsAliveResponse struct {
	Available  bool  `protobuf:"varint,1,opt,name=available,proto3" json:"available,omitempty"`
	MppVersion int64 `protobuf:"varint,2,opt,name=mpp_version,json=mppVersion,proto3" json:"mpp_version,omitempty"`
}

func (m *IsAliveResponse) Reset()         { *m = IsAliveResponse{} }
func (m *IsAliveResponse) String() string { return proto.CompactTextString(m) }
func (*IsAliveResponse) ProtoMessage()    {}
func (*IsAliveResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{2}
}
func (m *IsAliveResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IsAliveResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IsAliveResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IsAliveResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsAliveResponse.Merge(m, src)
}
func (m *IsAliveResponse) XXX_Size() int {
	return m.Size()
}
func (m *IsAliveResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IsAliveResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IsAliveResponse proto.InternalMessageInfo

func (m *IsAliveResponse) GetAvailable() bool {
	if m != nil {
		return m.Available
	}
	return false
}

func (m *IsAliveResponse) GetMppVersion() int64 {
	if m != nil {
		return m.MppVersion
	}
	return 0
}

// Dipsatch the task request to different tiflash servers.
type DispatchTaskRequest struct {
	Meta        *TaskMeta                 `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	EncodedPlan []byte                    `protobuf:"bytes,2,opt,name=encoded_plan,json=encodedPlan,proto3" json:"encoded_plan,omitempty"`
	Timeout     int64                     `protobuf:"varint,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	Regions     []*coprocessor.RegionInfo `protobuf:"bytes,4,rep,name=regions,proto3" json:"regions,omitempty"`
	// If this task contains table scan, we still need their region info.
	SchemaVer int64 `protobuf:"varint,5,opt,name=schema_ver,json=schemaVer,proto3" json:"schema_ver,omitempty"`
	// Used for partition table scan
	TableRegions []*coprocessor.TableRegions `protobuf:"bytes,6,rep,name=table_regions,json=tableRegions,proto3" json:"table_regions,omitempty"`
}

func (m *DispatchTaskRequest) Reset()         { *m = DispatchTaskRequest{} }
func (m *DispatchTaskRequest) String() string { return proto.CompactTextString(m) }
func (*DispatchTaskRequest) ProtoMessage()    {}
func (*DispatchTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{3}
}
func (m *DispatchTaskRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DispatchTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DispatchTaskRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DispatchTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DispatchTaskRequest.Merge(m, src)
}
func (m *DispatchTaskRequest) XXX_Size() int {
	return m.Size()
}
func (m *DispatchTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DispatchTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DispatchTaskRequest proto.InternalMessageInfo

func (m *DispatchTaskRequest) GetMeta() *TaskMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

func (m *DispatchTaskRequest) GetEncodedPlan() []byte {
	if m != nil {
		return m.EncodedPlan
	}
	return nil
}

func (m *DispatchTaskRequest) GetTimeout() int64 {
	if m != nil {
		return m.Timeout
	}
	return 0
}

func (m *DispatchTaskRequest) GetRegions() []*coprocessor.RegionInfo {
	if m != nil {
		return m.Regions
	}
	return nil
}

func (m *DispatchTaskRequest) GetSchemaVer() int64 {
	if m != nil {
		return m.SchemaVer
	}
	return 0
}

func (m *DispatchTaskRequest) GetTableRegions() []*coprocessor.TableRegions {
	if m != nil {
		return m.TableRegions
	}
	return nil
}

// Get response of DispatchTaskRequest.
type DispatchTaskResponse struct {
	Error        *Error           `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	RetryRegions []*metapb.Region `protobuf:"bytes,2,rep,name=retry_regions,json=retryRegions,proto3" json:"retry_regions,omitempty"`
}

func (m *DispatchTaskResponse) Reset()         { *m = DispatchTaskResponse{} }
func (m *DispatchTaskResponse) String() string { return proto.CompactTextString(m) }
func (*DispatchTaskResponse) ProtoMessage()    {}
func (*DispatchTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{4}
}
func (m *DispatchTaskResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DispatchTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DispatchTaskResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DispatchTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DispatchTaskResponse.Merge(m, src)
}
func (m *DispatchTaskResponse) XXX_Size() int {
	return m.Size()
}
func (m *DispatchTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DispatchTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DispatchTaskResponse proto.InternalMessageInfo

func (m *DispatchTaskResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *DispatchTaskResponse) GetRetryRegions() []*metapb.Region {
	if m != nil {
		return m.RetryRegions
	}
	return nil
}

// CancelTaskRequest closes the execution of a task.
type CancelTaskRequest struct {
	Meta  *TaskMeta `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	Error *Error    `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *CancelTaskRequest) Reset()         { *m = CancelTaskRequest{} }
func (m *CancelTaskRequest) String() string { return proto.CompactTextString(m) }
func (*CancelTaskRequest) ProtoMessage()    {}
func (*CancelTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{5}
}
func (m *CancelTaskRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CancelTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CancelTaskRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CancelTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelTaskRequest.Merge(m, src)
}
func (m *CancelTaskRequest) XXX_Size() int {
	return m.Size()
}
func (m *CancelTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelTaskRequest proto.InternalMessageInfo

func (m *CancelTaskRequest) GetMeta() *TaskMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

func (m *CancelTaskRequest) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type CancelTaskResponse struct {
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *CancelTaskResponse) Reset()         { *m = CancelTaskResponse{} }
func (m *CancelTaskResponse) String() string { return proto.CompactTextString(m) }
func (*CancelTaskResponse) ProtoMessage()    {}
func (*CancelTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{6}
}
func (m *CancelTaskResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CancelTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CancelTaskResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CancelTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelTaskResponse.Merge(m, src)
}
func (m *CancelTaskResponse) XXX_Size() int {
	return m.Size()
}
func (m *CancelTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelTaskResponse proto.InternalMessageInfo

func (m *CancelTaskResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

// ReportTaskStatus reports the execution status of a task.
// when TiFlash reports status to TiDB, ReportTaskStatusRequest serialize tipb.TiFlashExecutionInfo into data;
type ReportTaskStatusRequest struct {
	Meta  *TaskMeta `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	Data  []byte    `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Error *Error    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *ReportTaskStatusRequest) Reset()         { *m = ReportTaskStatusRequest{} }
func (m *ReportTaskStatusRequest) String() string { return proto.CompactTextString(m) }
func (*ReportTaskStatusRequest) ProtoMessage()    {}
func (*ReportTaskStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{7}
}
func (m *ReportTaskStatusRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReportTaskStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReportTaskStatusRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReportTaskStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTaskStatusRequest.Merge(m, src)
}
func (m *ReportTaskStatusRequest) XXX_Size() int {
	return m.Size()
}
func (m *ReportTaskStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTaskStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTaskStatusRequest proto.InternalMessageInfo

func (m *ReportTaskStatusRequest) GetMeta() *TaskMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

func (m *ReportTaskStatusRequest) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ReportTaskStatusRequest) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type ReportTaskStatusResponse struct {
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *ReportTaskStatusResponse) Reset()         { *m = ReportTaskStatusResponse{} }
func (m *ReportTaskStatusResponse) String() string { return proto.CompactTextString(m) }
func (*ReportTaskStatusResponse) ProtoMessage()    {}
func (*ReportTaskStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{8}
}
func (m *ReportTaskStatusResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReportTaskStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReportTaskStatusResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReportTaskStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportTaskStatusResponse.Merge(m, src)
}
func (m *ReportTaskStatusResponse) XXX_Size() int {
	return m.Size()
}
func (m *ReportTaskStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportTaskStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportTaskStatusResponse proto.InternalMessageInfo

func (m *ReportTaskStatusResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

// build connection between different tasks. Data is sent by the tasks that are closer to the data sources.
type EstablishMPPConnectionRequest struct {
	SenderMeta   *TaskMeta `protobuf:"bytes,1,opt,name=sender_meta,json=senderMeta,proto3" json:"sender_meta,omitempty"`
	ReceiverMeta *TaskMeta `protobuf:"bytes,2,opt,name=receiver_meta,json=receiverMeta,proto3" json:"receiver_meta,omitempty"`
}

func (m *EstablishMPPConnectionRequest) Reset()         { *m = EstablishMPPConnectionRequest{} }
func (m *EstablishMPPConnectionRequest) String() string { return proto.CompactTextString(m) }
func (*EstablishMPPConnectionRequest) ProtoMessage()    {}
func (*EstablishMPPConnectionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{9}
}
func (m *EstablishMPPConnectionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EstablishMPPConnectionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EstablishMPPConnectionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EstablishMPPConnectionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EstablishMPPConnectionRequest.Merge(m, src)
}
func (m *EstablishMPPConnectionRequest) XXX_Size() int {
	return m.Size()
}
func (m *EstablishMPPConnectionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EstablishMPPConnectionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EstablishMPPConnectionRequest proto.InternalMessageInfo

func (m *EstablishMPPConnectionRequest) GetSenderMeta() *TaskMeta {
	if m != nil {
		return m.SenderMeta
	}
	return nil
}

func (m *EstablishMPPConnectionRequest) GetReceiverMeta() *TaskMeta {
	if m != nil {
		return m.ReceiverMeta
	}
	return nil
}

// when TiFlash sends data to TiDB, Data packets wrap tipb.SelectResponse, i.e., serialize tipb.SelectResponse into data;
// when TiFlash sends data to TiFlash, data blocks are serialized into chunks, and the execution_summaries in tipb.SelectResponse are serialized into data only for the last packet.
type MPPDataPacket struct {
	Data      []byte   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Error     *Error   `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Chunks    [][]byte `protobuf:"bytes,3,rep,name=chunks,proto3" json:"chunks,omitempty"`
	StreamIds []uint64 `protobuf:"varint,4,rep,packed,name=stream_ids,json=streamIds,proto3" json:"stream_ids,omitempty"`
	Version   int64    `protobuf:"varint,5,opt,name=version,proto3" json:"version,omitempty"`
}

func (m *MPPDataPacket) Reset()         { *m = MPPDataPacket{} }
func (m *MPPDataPacket) String() string { return proto.CompactTextString(m) }
func (*MPPDataPacket) ProtoMessage()    {}
func (*MPPDataPacket) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{10}
}
func (m *MPPDataPacket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MPPDataPacket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MPPDataPacket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MPPDataPacket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MPPDataPacket.Merge(m, src)
}
func (m *MPPDataPacket) XXX_Size() int {
	return m.Size()
}
func (m *MPPDataPacket) XXX_DiscardUnknown() {
	xxx_messageInfo_MPPDataPacket.DiscardUnknown(m)
}

var xxx_messageInfo_MPPDataPacket proto.InternalMessageInfo

func (m *MPPDataPacket) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *MPPDataPacket) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *MPPDataPacket) GetChunks() [][]byte {
	if m != nil {
		return m.Chunks
	}
	return nil
}

func (m *MPPDataPacket) GetStreamIds() []uint64 {
	if m != nil {
		return m.StreamIds
	}
	return nil
}

func (m *MPPDataPacket) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type Error struct {
	Code       int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg        string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	MppVersion int64  `protobuf:"varint,3,opt,name=mpp_version,json=mppVersion,proto3" json:"mpp_version,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_819623c7fa76fc55, []int{11}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *Error) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *Error) GetMppVersion() int64 {
	if m != nil {
		return m.MppVersion
	}
	return 0
}

func init() {
	proto.RegisterType((*TaskMeta)(nil), "mpp.TaskMeta")
	proto.RegisterType((*IsAliveRequest)(nil), "mpp.IsAliveRequest")
	proto.RegisterType((*IsAliveResponse)(nil), "mpp.IsAliveResponse")
	proto.RegisterType((*DispatchTaskRequest)(nil), "mpp.DispatchTaskRequest")
	proto.RegisterType((*DispatchTaskResponse)(nil), "mpp.DispatchTaskResponse")
	proto.RegisterType((*CancelTaskRequest)(nil), "mpp.CancelTaskRequest")
	proto.RegisterType((*CancelTaskResponse)(nil), "mpp.CancelTaskResponse")
	proto.RegisterType((*ReportTaskStatusRequest)(nil), "mpp.ReportTaskStatusRequest")
	proto.RegisterType((*ReportTaskStatusResponse)(nil), "mpp.ReportTaskStatusResponse")
	proto.RegisterType((*EstablishMPPConnectionRequest)(nil), "mpp.EstablishMPPConnectionRequest")
	proto.RegisterType((*MPPDataPacket)(nil), "mpp.MPPDataPacket")
	proto.RegisterType((*Error)(nil), "mpp.Error")
}

func init() { proto.RegisterFile("mpp.proto", fileDescriptor_819623c7fa76fc55) }

var fileDescriptor_819623c7fa76fc55 = []byte{
	// 933 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x55, 0x4d, 0x6f, 0xdb, 0x46,
	0x13, 0x36, 0x45, 0x59, 0x1f, 0x23, 0xc9, 0x96, 0x57, 0x41, 0xcc, 0xe4, 0x7d, 0xa3, 0x2a, 0x6a,
	0x0f, 0xea, 0x45, 0x41, 0x95, 0xa2, 0xe8, 0xa1, 0x28, 0xe0, 0x26, 0x46, 0xc1, 0x83, 0x03, 0x75,
	0x63, 0x04, 0xbd, 0x11, 0x6b, 0x72, 0x2a, 0x11, 0x12, 0xb9, 0x9b, 0xdd, 0xa5, 0x50, 0x9f, 0x7b,
	0xe8, 0xb5, 0xa7, 0xa2, 0x3f, 0xa1, 0x3f, 0xa5, 0xc7, 0x1c, 0xd3, 0x5b, 0x61, 0xff, 0x91, 0x62,
	0x77, 0x49, 0x59, 0x71, 0x63, 0x20, 0x39, 0x69, 0xe6, 0x99, 0xd9, 0x99, 0x67, 0xbe, 0x28, 0x68,
	0x67, 0x42, 0x4c, 0x85, 0xe4, 0x9a, 0x13, 0x3f, 0x13, 0xe2, 0xe1, 0x51, 0xcc, 0x85, 0xe4, 0x31,
	0x2a, 0xc5, 0xa5, 0xc3, 0x1f, 0x76, 0x33, 0xd4, 0x4c, 0x5c, 0x94, 0x5a, 0x6f, 0xb5, 0x91, 0x22,
	0xde, 0xaa, 0xf7, 0x16, 0x7c, 0xc1, 0xad, 0xf8, 0xc4, 0x48, 0x25, 0x7a, 0x28, 0x0b, 0xa5, 0xad,
	0xe8, 0x80, 0xf1, 0xdf, 0x75, 0x68, 0x9d, 0x33, 0xb5, 0x3a, 0x43, 0xcd, 0xc8, 0x03, 0x68, 0x29,
	0xcd, 0xa4, 0x8e, 0xb4, 0x0a, 0xbc, 0x91, 0x37, 0xa9, 0xd3, 0xa6, 0xd5, 0xcf, 0x15, 0x39, 0x86,
	0xa6, 0x66, 0x6a, 0x15, 0xa5, 0x49, 0x50, 0x1b, 0x79, 0x13, 0x9f, 0x36, 0x8c, 0x1a, 0x26, 0xe4,
	0x31, 0x74, 0x05, 0x93, 0x3a, 0xd5, 0x29, 0xcf, 0x8d, 0xd5, 0xb7, 0xd6, 0xce, 0x16, 0x0b, 0x13,
	0x12, 0x40, 0x93, 0x25, 0x89, 0x44, 0xa5, 0x82, 0xfa, 0xc8, 0x9b, 0xb4, 0x69, 0xa5, 0x92, 0xff,
	0x41, 0x7b, 0xc1, 0xf4, 0x12, 0xa5, 0x79, 0xb9, 0x6f, 0x33, 0xb6, 0x1c, 0x10, 0x26, 0x86, 0xcd,
	0xeb, 0x02, 0xe5, 0xa5, 0x61, 0xd3, 0x70, 0x6c, 0xac, 0x7e, 0xae, 0xc8, 0x67, 0x70, 0xb0, 0xe6,
	0x31, 0x5b, 0x47, 0xce, 0x21, 0x4d, 0x82, 0xa6, 0x75, 0xe8, 0x5a, 0xf4, 0x07, 0x03, 0x86, 0x89,
	0x89, 0xae, 0x50, 0x6e, 0x5c, 0xf4, 0x96, 0x8b, 0xee, 0x80, 0x30, 0x21, 0x9f, 0x40, 0x27, 0x13,
	0x22, 0xda, 0xa0, 0x54, 0x29, 0xcf, 0x83, 0xb6, 0xa5, 0x0d, 0x99, 0x10, 0xaf, 0x1c, 0x62, 0x1c,
	0x56, 0x78, 0xa9, 0x04, 0x8b, 0xd1, 0xbc, 0x87, 0x91, 0x37, 0xe9, 0x51, 0xa8, 0xa0, 0x30, 0x21,
	0x4f, 0x60, 0x10, 0x73, 0x2e, 0x93, 0x34, 0x67, 0x9a, 0xcb, 0xa8, 0x2a, 0xb1, 0x63, 0x4b, 0x24,
	0x3b, 0xa6, 0x93, 0xb2, 0xda, 0xaf, 0x21, 0x90, 0x28, 0xb8, 0xd4, 0x11, 0xfe, 0x8c, 0x71, 0x61,
	0x3b, 0xa6, 0x8a, 0x2c, 0x63, 0xf2, 0x32, 0xe8, 0x8e, 0xbc, 0x49, 0x8b, 0xde, 0x77, 0xf6, 0xd3,
	0xca, 0xfc, 0xd2, 0x59, 0xc9, 0x97, 0xd0, 0x61, 0x22, 0xdd, 0x92, 0xed, 0x8f, 0xbc, 0xc9, 0xc1,
	0x6c, 0x30, 0xad, 0x26, 0x7e, 0x32, 0x0f, 0x4b, 0xd6, 0x14, 0x98, 0x48, 0xab, 0x0a, 0xa6, 0x30,
	0x90, 0xa8, 0x78, 0x21, 0x63, 0x8c, 0x16, 0x92, 0x17, 0x22, 0xca, 0x59, 0x86, 0xc1, 0x91, 0x25,
	0x78, 0x54, 0x99, 0xbe, 0x37, 0x96, 0x17, 0x2c, 0x43, 0xf2, 0x29, 0xf4, 0x62, 0x9e, 0xe7, 0x18,
	0x57, 0xb3, 0x24, 0xae, 0xa9, 0x37, 0x60, 0x98, 0x90, 0xcf, 0xa1, 0xbf, 0xe3, 0xc4, 0xd6, 0x29,
	0x53, 0xc1, 0xc0, 0x46, 0x3c, 0xbc, 0xc1, 0x4f, 0x0c, 0x3c, 0xee, 0xc3, 0x41, 0xa8, 0x4e, 0xd6,
	0xe9, 0x06, 0x29, 0xbe, 0x2e, 0x50, 0xe9, 0xf1, 0x1c, 0x0e, 0xb7, 0x88, 0x12, 0x3c, 0x57, 0x48,
	0xfe, 0x0f, 0x6d, 0xb6, 0x61, 0xe9, 0x9a, 0x5d, 0xac, 0xd1, 0x2e, 0x5d, 0x8b, 0xde, 0x00, 0xb7,
	0xa7, 0x54, 0xbb, 0x3d, 0xa5, 0xf1, 0xaf, 0x35, 0x18, 0x3c, 0x4f, 0x95, 0x60, 0x3a, 0x5e, 0x9a,
	0x3d, 0x2e, 0x33, 0x91, 0xc7, 0x50, 0x37, 0xd7, 0x61, 0x23, 0x76, 0x66, 0xbd, 0xa9, 0xb9, 0xa6,
	0x6a, 0xcf, 0xa9, 0x35, 0x99, 0xcd, 0xc5, 0x3c, 0xe6, 0x09, 0x26, 0x91, 0x58, 0x33, 0x17, 0xbc,
	0x4b, 0x3b, 0x25, 0x36, 0x5f, 0xb3, 0xdc, 0x6c, 0xae, 0x4e, 0x33, 0xe4, 0x85, 0x2e, 0xf7, 0xba,
	0x52, 0xc9, 0x17, 0xd0, 0x94, 0xb8, 0x48, 0x79, 0x6e, 0x76, 0xda, 0x9f, 0x74, 0x66, 0xc7, 0xd3,
	0xdd, 0x03, 0xa5, 0xd6, 0x16, 0xe6, 0x3f, 0x71, 0x5a, 0xf9, 0x91, 0x47, 0x00, 0x2a, 0x5e, 0x62,
	0xc6, 0x4c, 0x39, 0x76, 0xdb, 0x7d, 0xda, 0x76, 0xc8, 0x2b, 0x94, 0xe4, 0x5b, 0xe8, 0x69, 0x53,
	0x73, 0x54, 0xc5, 0x6d, 0xd8, 0xb8, 0x0f, 0xde, 0x89, 0x7b, 0x6e, 0x3c, 0x5c, 0x70, 0x45, 0xbb,
	0x7a, 0x47, 0x1b, 0x67, 0x70, 0xef, 0xdd, 0x46, 0x94, 0x0d, 0x1e, 0xc1, 0x3e, 0x4a, 0xc9, 0x65,
	0xd9, 0x0a, 0xb0, 0xad, 0x38, 0x35, 0x08, 0x75, 0x06, 0xf2, 0x14, 0x7a, 0x12, 0xb5, 0xbc, 0xdc,
	0x66, 0xae, 0xd9, 0xcc, 0x07, 0xd3, 0xf2, 0xfb, 0xe2, 0x32, 0xd0, 0xae, 0x75, 0xaa, 0xd2, 0xfd,
	0x08, 0x47, 0xcf, 0x58, 0x1e, 0xe3, 0xfa, 0x23, 0xbb, 0xbe, 0xa5, 0x53, 0xbb, 0x83, 0xce, 0xf8,
	0x2b, 0x20, 0xbb, 0x91, 0x3f, 0xb4, 0x8c, 0xb1, 0x84, 0x63, 0x6a, 0xcf, 0xc7, 0xbc, 0x7b, 0xa9,
	0x99, 0x2e, 0xd4, 0x47, 0xf0, 0x22, 0x50, 0x4f, 0x98, 0x66, 0xe5, 0x16, 0x58, 0xf9, 0x26, 0xa7,
	0x7f, 0x57, 0xce, 0x6f, 0x20, 0xf8, 0x6f, 0xce, 0x0f, 0x66, 0xfc, 0x8b, 0x07, 0x8f, 0x4e, 0x95,
	0x99, 0x62, 0xaa, 0x96, 0x67, 0xf3, 0xf9, 0xb3, 0xed, 0x01, 0x55, 0xc4, 0xa7, 0xd0, 0x51, 0x98,
	0x27, 0x28, 0xa3, 0xbb, 0xf9, 0x83, 0xf3, 0xb0, 0x5f, 0xf0, 0x99, 0x19, 0x65, 0x8c, 0xe9, 0xa6,
	0x7a, 0x51, 0x7b, 0xdf, 0x8b, 0x6e, 0xe5, 0x63, 0xb4, 0xf1, 0xef, 0x1e, 0xf4, 0xce, 0xe6, 0xf3,
	0xe7, 0x4c, 0xb3, 0x39, 0x8b, 0x57, 0xa8, 0xb7, 0xbd, 0xf0, 0xde, 0xd7, 0x8b, 0xbb, 0xe6, 0x46,
	0xee, 0x43, 0x23, 0x5e, 0x16, 0xf9, 0x4a, 0x05, 0xfe, 0xc8, 0x9f, 0x74, 0x69, 0xa9, 0xd9, 0xbd,
	0xd7, 0x12, 0x59, 0x16, 0xa5, 0x89, 0xbb, 0x96, 0x3a, 0x6d, 0x3b, 0x24, 0x4c, 0x94, 0xb9, 0xb1,
	0xea, 0xbc, 0xdd, 0x4d, 0x54, 0xea, 0xf8, 0x05, 0xec, 0xdb, 0x04, 0x86, 0x8f, 0xb9, 0x49, 0xcb,
	0x67, 0x9f, 0x5a, 0x99, 0xf4, 0xc1, 0xcf, 0xd4, 0xc2, 0xb2, 0x69, 0x53, 0x23, 0xde, 0xfe, 0x56,
	0xf8, 0xb7, 0xbf, 0x15, 0xdf, 0xcd, 0xde, 0xfe, 0xd9, 0xf2, 0xfe, 0xba, 0x1a, 0x7a, 0x6f, 0xae,
	0x86, 0xde, 0x3f, 0x57, 0x43, 0xef, 0xb7, 0xeb, 0xe1, 0xde, 0x1f, 0xd7, 0xc3, 0xbd, 0x37, 0xd7,
	0xc3, 0xbd, 0xb7, 0xd7, 0xc3, 0x3d, 0xe8, 0x73, 0xb9, 0x98, 0xea, 0x74, 0xb5, 0x99, 0xae, 0x36,
	0xf6, 0xff, 0xf1, 0xa2, 0x61, 0x7f, 0x9e, 0xfe, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xe0, 0x05, 0x46,
	0x9b, 0x8f, 0x07, 0x00, 0x00,
}

func (m *TaskMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TaskMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TaskMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ConnectionAlias) > 0 {
		i -= len(m.ConnectionAlias)
		copy(dAtA[i:], m.ConnectionAlias)
		i = encodeVarintMpp(dAtA, i, uint64(len(m.ConnectionAlias)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if m.ConnectionId != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.ConnectionId))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x90
	}
	if len(m.ResourceGroupName) > 0 {
		i -= len(m.ResourceGroupName)
		copy(dAtA[i:], m.ResourceGroupName)
		i = encodeVarintMpp(dAtA, i, uint64(len(m.ResourceGroupName)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if m.ApiVersion != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.ApiVersion))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if m.ReportExecutionSummary {
		i--
		if m.ReportExecutionSummary {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x60
	}
	if len(m.CoordinatorAddress) > 0 {
		i -= len(m.CoordinatorAddress)
		copy(dAtA[i:], m.CoordinatorAddress)
		i = encodeVarintMpp(dAtA, i, uint64(len(m.CoordinatorAddress)))
		i--
		dAtA[i] = 0x5a
	}
	if m.KeyspaceId != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.KeyspaceId))
		i--
		dAtA[i] = 0x50
	}
	if m.MppVersion != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.MppVersion))
		i--
		dAtA[i] = 0x48
	}
	if m.ServerId != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.ServerId))
		i--
		dAtA[i] = 0x40
	}
	if m.LocalQueryId != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.LocalQueryId))
		i--
		dAtA[i] = 0x38
	}
	if m.QueryTs != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.QueryTs))
		i--
		dAtA[i] = 0x30
	}
	if m.GatherId != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.GatherId))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Address) > 0 {
		i -= len(m.Address)
		copy(dAtA[i:], m.Address)
		i = encodeVarintMpp(dAtA, i, uint64(len(m.Address)))
		i--
		dAtA[i] = 0x22
	}
	if m.PartitionId != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.PartitionId))
		i--
		dAtA[i] = 0x18
	}
	if m.TaskId != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.TaskId))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTs != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *IsAliveRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IsAliveRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IsAliveRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *IsAliveResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IsAliveResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IsAliveResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MppVersion != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.MppVersion))
		i--
		dAtA[i] = 0x10
	}
	if m.Available {
		i--
		if m.Available {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DispatchTaskRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DispatchTaskRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DispatchTaskRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableRegions) > 0 {
		for iNdEx := len(m.TableRegions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TableRegions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintMpp(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if m.SchemaVer != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.SchemaVer))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Regions) > 0 {
		for iNdEx := len(m.Regions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Regions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintMpp(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if m.Timeout != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.Timeout))
		i--
		dAtA[i] = 0x18
	}
	if len(m.EncodedPlan) > 0 {
		i -= len(m.EncodedPlan)
		copy(dAtA[i:], m.EncodedPlan)
		i = encodeVarintMpp(dAtA, i, uint64(len(m.EncodedPlan)))
		i--
		dAtA[i] = 0x12
	}
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DispatchTaskResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DispatchTaskResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DispatchTaskResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RetryRegions) > 0 {
		for iNdEx := len(m.RetryRegions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RetryRegions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintMpp(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CancelTaskRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelTaskRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CancelTaskRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CancelTaskResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelTaskResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CancelTaskResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReportTaskStatusRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportTaskStatusRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReportTaskStatusRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintMpp(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x12
	}
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReportTaskStatusResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportTaskStatusResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReportTaskStatusResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *EstablishMPPConnectionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EstablishMPPConnectionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EstablishMPPConnectionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ReceiverMeta != nil {
		{
			size, err := m.ReceiverMeta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.SenderMeta != nil {
		{
			size, err := m.SenderMeta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MPPDataPacket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MPPDataPacket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MPPDataPacket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Version != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x28
	}
	if len(m.StreamIds) > 0 {
		dAtA12 := make([]byte, len(m.StreamIds)*10)
		var j11 int
		for _, num := range m.StreamIds {
			for num >= 1<<7 {
				dAtA12[j11] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j11++
			}
			dAtA12[j11] = uint8(num)
			j11++
		}
		i -= j11
		copy(dAtA[i:], dAtA12[:j11])
		i = encodeVarintMpp(dAtA, i, uint64(j11))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Chunks) > 0 {
		for iNdEx := len(m.Chunks) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Chunks[iNdEx])
			copy(dAtA[i:], m.Chunks[iNdEx])
			i = encodeVarintMpp(dAtA, i, uint64(len(m.Chunks[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMpp(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintMpp(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MppVersion != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.MppVersion))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintMpp(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintMpp(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintMpp(dAtA []byte, offset int, v uint64) int {
	offset -= sovMpp(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *TaskMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTs != 0 {
		n += 1 + sovMpp(uint64(m.StartTs))
	}
	if m.TaskId != 0 {
		n += 1 + sovMpp(uint64(m.TaskId))
	}
	if m.PartitionId != 0 {
		n += 1 + sovMpp(uint64(m.PartitionId))
	}
	l = len(m.Address)
	if l > 0 {
		n += 1 + l + sovMpp(uint64(l))
	}
	if m.GatherId != 0 {
		n += 1 + sovMpp(uint64(m.GatherId))
	}
	if m.QueryTs != 0 {
		n += 1 + sovMpp(uint64(m.QueryTs))
	}
	if m.LocalQueryId != 0 {
		n += 1 + sovMpp(uint64(m.LocalQueryId))
	}
	if m.ServerId != 0 {
		n += 1 + sovMpp(uint64(m.ServerId))
	}
	if m.MppVersion != 0 {
		n += 1 + sovMpp(uint64(m.MppVersion))
	}
	if m.KeyspaceId != 0 {
		n += 1 + sovMpp(uint64(m.KeyspaceId))
	}
	l = len(m.CoordinatorAddress)
	if l > 0 {
		n += 1 + l + sovMpp(uint64(l))
	}
	if m.ReportExecutionSummary {
		n += 2
	}
	if m.ApiVersion != 0 {
		n += 2 + sovMpp(uint64(m.ApiVersion))
	}
	l = len(m.ResourceGroupName)
	if l > 0 {
		n += 2 + l + sovMpp(uint64(l))
	}
	if m.ConnectionId != 0 {
		n += 2 + sovMpp(uint64(m.ConnectionId))
	}
	l = len(m.ConnectionAlias)
	if l > 0 {
		n += 2 + l + sovMpp(uint64(l))
	}
	return n
}

func (m *IsAliveRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *IsAliveResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Available {
		n += 2
	}
	if m.MppVersion != 0 {
		n += 1 + sovMpp(uint64(m.MppVersion))
	}
	return n
}

func (m *DispatchTaskRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	l = len(m.EncodedPlan)
	if l > 0 {
		n += 1 + l + sovMpp(uint64(l))
	}
	if m.Timeout != 0 {
		n += 1 + sovMpp(uint64(m.Timeout))
	}
	if len(m.Regions) > 0 {
		for _, e := range m.Regions {
			l = e.Size()
			n += 1 + l + sovMpp(uint64(l))
		}
	}
	if m.SchemaVer != 0 {
		n += 1 + sovMpp(uint64(m.SchemaVer))
	}
	if len(m.TableRegions) > 0 {
		for _, e := range m.TableRegions {
			l = e.Size()
			n += 1 + l + sovMpp(uint64(l))
		}
	}
	return n
}

func (m *DispatchTaskResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	if len(m.RetryRegions) > 0 {
		for _, e := range m.RetryRegions {
			l = e.Size()
			n += 1 + l + sovMpp(uint64(l))
		}
	}
	return n
}

func (m *CancelTaskRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	return n
}

func (m *CancelTaskResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	return n
}

func (m *ReportTaskStatusRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovMpp(uint64(l))
	}
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	return n
}

func (m *ReportTaskStatusResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	return n
}

func (m *EstablishMPPConnectionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SenderMeta != nil {
		l = m.SenderMeta.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	if m.ReceiverMeta != nil {
		l = m.ReceiverMeta.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	return n
}

func (m *MPPDataPacket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovMpp(uint64(l))
	}
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovMpp(uint64(l))
	}
	if len(m.Chunks) > 0 {
		for _, b := range m.Chunks {
			l = len(b)
			n += 1 + l + sovMpp(uint64(l))
		}
	}
	if len(m.StreamIds) > 0 {
		l = 0
		for _, e := range m.StreamIds {
			l += sovMpp(uint64(e))
		}
		n += 1 + sovMpp(uint64(l)) + l
	}
	if m.Version != 0 {
		n += 1 + sovMpp(uint64(m.Version))
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovMpp(uint64(m.Code))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovMpp(uint64(l))
	}
	if m.MppVersion != 0 {
		n += 1 + sovMpp(uint64(m.MppVersion))
	}
	return n
}

func sovMpp(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozMpp(x uint64) (n int) {
	return sovMpp(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *TaskMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TaskMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TaskMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			m.TaskId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PartitionId", wireType)
			}
			m.PartitionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PartitionId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Address = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GatherId", wireType)
			}
			m.GatherId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GatherId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueryTs", wireType)
			}
			m.QueryTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QueryTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LocalQueryId", wireType)
			}
			m.LocalQueryId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LocalQueryId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServerId", wireType)
			}
			m.ServerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MppVersion", wireType)
			}
			m.MppVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MppVersion |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyspaceId", wireType)
			}
			m.KeyspaceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeyspaceId |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CoordinatorAddress", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CoordinatorAddress = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReportExecutionSummary", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReportExecutionSummary = bool(v != 0)
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiVersion", wireType)
			}
			m.ApiVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApiVersion |= kvrpcpb.APIVersion(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResourceGroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResourceGroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			m.ConnectionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConnectionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionAlias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionAlias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IsAliveRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IsAliveRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IsAliveRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IsAliveResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IsAliveResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IsAliveResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Available", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Available = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MppVersion", wireType)
			}
			m.MppVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MppVersion |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DispatchTaskRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DispatchTaskRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DispatchTaskRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &TaskMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EncodedPlan", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EncodedPlan = append(m.EncodedPlan[:0], dAtA[iNdEx:postIndex]...)
			if m.EncodedPlan == nil {
				m.EncodedPlan = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timeout", wireType)
			}
			m.Timeout = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timeout |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Regions = append(m.Regions, &coprocessor.RegionInfo{})
			if err := m.Regions[len(m.Regions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SchemaVer", wireType)
			}
			m.SchemaVer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SchemaVer |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableRegions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableRegions = append(m.TableRegions, &coprocessor.TableRegions{})
			if err := m.TableRegions[len(m.TableRegions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DispatchTaskResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DispatchTaskResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DispatchTaskResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RetryRegions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RetryRegions = append(m.RetryRegions, &metapb.Region{})
			if err := m.RetryRegions[len(m.RetryRegions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelTaskRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CancelTaskRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CancelTaskRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &TaskMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelTaskResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CancelTaskResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CancelTaskResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportTaskStatusRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReportTaskStatusRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReportTaskStatusRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &TaskMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportTaskStatusResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReportTaskStatusResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReportTaskStatusResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EstablishMPPConnectionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EstablishMPPConnectionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EstablishMPPConnectionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SenderMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SenderMeta == nil {
				m.SenderMeta = &TaskMeta{}
			}
			if err := m.SenderMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReceiverMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReceiverMeta == nil {
				m.ReceiverMeta = &TaskMeta{}
			}
			if err := m.ReceiverMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MPPDataPacket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MPPDataPacket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MPPDataPacket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunks = append(m.Chunks, make([]byte, postIndex-iNdEx))
			copy(m.Chunks[len(m.Chunks)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMpp
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StreamIds = append(m.StreamIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMpp
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMpp
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthMpp
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StreamIds) == 0 {
					m.StreamIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMpp
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StreamIds = append(m.StreamIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StreamIds", wireType)
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMpp
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMpp
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MppVersion", wireType)
			}
			m.MppVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MppVersion |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMpp(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMpp
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipMpp(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMpp
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMpp
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthMpp
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupMpp
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthMpp
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthMpp        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMpp          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupMpp = fmt.Errorf("proto: unexpected end of group")
)
