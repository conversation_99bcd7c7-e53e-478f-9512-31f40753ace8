// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: deadlock.proto

package deadlock

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DeadlockRequestType int32

const (
	DeadlockRequestType_Detect DeadlockRequestType = 0
	// CleanUpWaitFor cleans a single entry the transaction is waiting.
	DeadlockRequestType_CleanUpWaitFor DeadlockRequestType = 1
	// CleanUp cleans all entries the transaction is waiting.
	DeadlockRequestType_CleanUp DeadlockRequestType = 2
)

var DeadlockRequestType_name = map[int32]string{
	0: "Detect",
	1: "CleanUpWaitFor",
	2: "CleanUp",
}

var DeadlockRequestType_value = map[string]int32{
	"Detect":         0,
	"CleanUpWaitFor": 1,
	"CleanUp":        2,
}

func (x DeadlockRequestType) String() string {
	return proto.EnumName(DeadlockRequestType_name, int32(x))
}

func (DeadlockRequestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_1995e4345c820a37, []int{0}
}

type WaitForEntriesRequest struct {
}

func (m *WaitForEntriesRequest) Reset()         { *m = WaitForEntriesRequest{} }
func (m *WaitForEntriesRequest) String() string { return proto.CompactTextString(m) }
func (*WaitForEntriesRequest) ProtoMessage()    {}
func (*WaitForEntriesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1995e4345c820a37, []int{0}
}
func (m *WaitForEntriesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WaitForEntriesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WaitForEntriesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WaitForEntriesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WaitForEntriesRequest.Merge(m, src)
}
func (m *WaitForEntriesRequest) XXX_Size() int {
	return m.Size()
}
func (m *WaitForEntriesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WaitForEntriesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WaitForEntriesRequest proto.InternalMessageInfo

type WaitForEntriesResponse struct {
	Entries []WaitForEntry `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries"`
}

func (m *WaitForEntriesResponse) Reset()         { *m = WaitForEntriesResponse{} }
func (m *WaitForEntriesResponse) String() string { return proto.CompactTextString(m) }
func (*WaitForEntriesResponse) ProtoMessage()    {}
func (*WaitForEntriesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1995e4345c820a37, []int{1}
}
func (m *WaitForEntriesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WaitForEntriesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WaitForEntriesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WaitForEntriesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WaitForEntriesResponse.Merge(m, src)
}
func (m *WaitForEntriesResponse) XXX_Size() int {
	return m.Size()
}
func (m *WaitForEntriesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WaitForEntriesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WaitForEntriesResponse proto.InternalMessageInfo

func (m *WaitForEntriesResponse) GetEntries() []WaitForEntry {
	if m != nil {
		return m.Entries
	}
	return nil
}

type WaitForEntry struct {
	// The transaction id that is waiting.
	Txn uint64 `protobuf:"varint,1,opt,name=txn,proto3" json:"txn,omitempty"`
	// The transaction id that is being waited for.
	WaitForTxn uint64 `protobuf:"varint,2,opt,name=wait_for_txn,json=waitForTxn,proto3" json:"wait_for_txn,omitempty"`
	// The hash value of the key is being waited for.
	KeyHash uint64 `protobuf:"varint,3,opt,name=key_hash,json=keyHash,proto3" json:"key_hash,omitempty"`
	// The key the current txn is trying to lock.
	Key []byte `protobuf:"bytes,4,opt,name=key,proto3" json:"key,omitempty"`
	// The tag came from the lock request's context.
	ResourceGroupTag []byte `protobuf:"bytes,5,opt,name=resource_group_tag,json=resourceGroupTag,proto3" json:"resource_group_tag,omitempty"`
	// Milliseconds it has been waits.
	WaitTime uint64 `protobuf:"varint,6,opt,name=wait_time,json=waitTime,proto3" json:"wait_time,omitempty"`
}

func (m *WaitForEntry) Reset()         { *m = WaitForEntry{} }
func (m *WaitForEntry) String() string { return proto.CompactTextString(m) }
func (*WaitForEntry) ProtoMessage()    {}
func (*WaitForEntry) Descriptor() ([]byte, []int) {
	return fileDescriptor_1995e4345c820a37, []int{2}
}
func (m *WaitForEntry) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WaitForEntry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WaitForEntry.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WaitForEntry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WaitForEntry.Merge(m, src)
}
func (m *WaitForEntry) XXX_Size() int {
	return m.Size()
}
func (m *WaitForEntry) XXX_DiscardUnknown() {
	xxx_messageInfo_WaitForEntry.DiscardUnknown(m)
}

var xxx_messageInfo_WaitForEntry proto.InternalMessageInfo

func (m *WaitForEntry) GetTxn() uint64 {
	if m != nil {
		return m.Txn
	}
	return 0
}

func (m *WaitForEntry) GetWaitForTxn() uint64 {
	if m != nil {
		return m.WaitForTxn
	}
	return 0
}

func (m *WaitForEntry) GetKeyHash() uint64 {
	if m != nil {
		return m.KeyHash
	}
	return 0
}

func (m *WaitForEntry) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *WaitForEntry) GetResourceGroupTag() []byte {
	if m != nil {
		return m.ResourceGroupTag
	}
	return nil
}

func (m *WaitForEntry) GetWaitTime() uint64 {
	if m != nil {
		return m.WaitTime
	}
	return 0
}

type ReplaceLockByKeyItem struct {
	KeyHash   uint64 `protobuf:"varint,1,opt,name=key_hash,json=keyHash,proto3" json:"key_hash,omitempty"`
	Key       []byte `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	OldLockTs uint64 `protobuf:"varint,3,opt,name=old_lock_ts,json=oldLockTs,proto3" json:"old_lock_ts,omitempty"`
	NewLockTs uint64 `protobuf:"varint,4,opt,name=new_lock_ts,json=newLockTs,proto3" json:"new_lock_ts,omitempty"`
}

func (m *ReplaceLockByKeyItem) Reset()         { *m = ReplaceLockByKeyItem{} }
func (m *ReplaceLockByKeyItem) String() string { return proto.CompactTextString(m) }
func (*ReplaceLockByKeyItem) ProtoMessage()    {}
func (*ReplaceLockByKeyItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_1995e4345c820a37, []int{3}
}
func (m *ReplaceLockByKeyItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReplaceLockByKeyItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReplaceLockByKeyItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReplaceLockByKeyItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplaceLockByKeyItem.Merge(m, src)
}
func (m *ReplaceLockByKeyItem) XXX_Size() int {
	return m.Size()
}
func (m *ReplaceLockByKeyItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplaceLockByKeyItem.DiscardUnknown(m)
}

var xxx_messageInfo_ReplaceLockByKeyItem proto.InternalMessageInfo

func (m *ReplaceLockByKeyItem) GetKeyHash() uint64 {
	if m != nil {
		return m.KeyHash
	}
	return 0
}

func (m *ReplaceLockByKeyItem) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *ReplaceLockByKeyItem) GetOldLockTs() uint64 {
	if m != nil {
		return m.OldLockTs
	}
	return 0
}

func (m *ReplaceLockByKeyItem) GetNewLockTs() uint64 {
	if m != nil {
		return m.NewLockTs
	}
	return 0
}

type ReplaceLocksByKeysRequest struct {
	Items []*ReplaceLockByKeyItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (m *ReplaceLocksByKeysRequest) Reset()         { *m = ReplaceLocksByKeysRequest{} }
func (m *ReplaceLocksByKeysRequest) String() string { return proto.CompactTextString(m) }
func (*ReplaceLocksByKeysRequest) ProtoMessage()    {}
func (*ReplaceLocksByKeysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1995e4345c820a37, []int{4}
}
func (m *ReplaceLocksByKeysRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReplaceLocksByKeysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReplaceLocksByKeysRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReplaceLocksByKeysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplaceLocksByKeysRequest.Merge(m, src)
}
func (m *ReplaceLocksByKeysRequest) XXX_Size() int {
	return m.Size()
}
func (m *ReplaceLocksByKeysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplaceLocksByKeysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReplaceLocksByKeysRequest proto.InternalMessageInfo

func (m *ReplaceLocksByKeysRequest) GetItems() []*ReplaceLockByKeyItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type DeadlockRequest struct {
	Tp                 DeadlockRequestType        `protobuf:"varint,1,opt,name=tp,proto3,enum=deadlock.DeadlockRequestType" json:"tp,omitempty"`
	Entry              WaitForEntry               `protobuf:"bytes,2,opt,name=entry,proto3" json:"entry"`
	ReplaceLocksByKeys *ReplaceLocksByKeysRequest `protobuf:"bytes,3,opt,name=replace_locks_by_keys,json=replaceLocksByKeys,proto3" json:"replace_locks_by_keys,omitempty"`
}

func (m *DeadlockRequest) Reset()         { *m = DeadlockRequest{} }
func (m *DeadlockRequest) String() string { return proto.CompactTextString(m) }
func (*DeadlockRequest) ProtoMessage()    {}
func (*DeadlockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1995e4345c820a37, []int{5}
}
func (m *DeadlockRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeadlockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeadlockRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeadlockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeadlockRequest.Merge(m, src)
}
func (m *DeadlockRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeadlockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeadlockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeadlockRequest proto.InternalMessageInfo

func (m *DeadlockRequest) GetTp() DeadlockRequestType {
	if m != nil {
		return m.Tp
	}
	return DeadlockRequestType_Detect
}

func (m *DeadlockRequest) GetEntry() WaitForEntry {
	if m != nil {
		return m.Entry
	}
	return WaitForEntry{}
}

func (m *DeadlockRequest) GetReplaceLocksByKeys() *ReplaceLocksByKeysRequest {
	if m != nil {
		return m.ReplaceLocksByKeys
	}
	return nil
}

type DeadlockResponse struct {
	// The same entry sent by DeadlockRequest, identifies the sender.
	Entry WaitForEntry `protobuf:"bytes,1,opt,name=entry,proto3" json:"entry"`
	// The key hash of the lock that is hold by the waiting transaction. The hash of the `deadlock_key` field.
	DeadlockKeyHash uint64 `protobuf:"varint,2,opt,name=deadlock_key_hash,json=deadlockKeyHash,proto3" json:"deadlock_key_hash,omitempty"`
	// The other entries of the dead lock circle. The current entry is in `entry` field and  not
	// included in this field.
	WaitChain []*WaitForEntry `protobuf:"bytes,3,rep,name=wait_chain,json=waitChain,proto3" json:"wait_chain,omitempty"`
	// The key of the lock that is hold by the waiting transaction.
	DeadlockKey []byte `protobuf:"bytes,4,opt,name=deadlock_key,json=deadlockKey,proto3" json:"deadlock_key,omitempty"`
}

func (m *DeadlockResponse) Reset()         { *m = DeadlockResponse{} }
func (m *DeadlockResponse) String() string { return proto.CompactTextString(m) }
func (*DeadlockResponse) ProtoMessage()    {}
func (*DeadlockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1995e4345c820a37, []int{6}
}
func (m *DeadlockResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeadlockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeadlockResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeadlockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeadlockResponse.Merge(m, src)
}
func (m *DeadlockResponse) XXX_Size() int {
	return m.Size()
}
func (m *DeadlockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeadlockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeadlockResponse proto.InternalMessageInfo

func (m *DeadlockResponse) GetEntry() WaitForEntry {
	if m != nil {
		return m.Entry
	}
	return WaitForEntry{}
}

func (m *DeadlockResponse) GetDeadlockKeyHash() uint64 {
	if m != nil {
		return m.DeadlockKeyHash
	}
	return 0
}

func (m *DeadlockResponse) GetWaitChain() []*WaitForEntry {
	if m != nil {
		return m.WaitChain
	}
	return nil
}

func (m *DeadlockResponse) GetDeadlockKey() []byte {
	if m != nil {
		return m.DeadlockKey
	}
	return nil
}

func init() {
	proto.RegisterEnum("deadlock.DeadlockRequestType", DeadlockRequestType_name, DeadlockRequestType_value)
	proto.RegisterType((*WaitForEntriesRequest)(nil), "deadlock.WaitForEntriesRequest")
	proto.RegisterType((*WaitForEntriesResponse)(nil), "deadlock.WaitForEntriesResponse")
	proto.RegisterType((*WaitForEntry)(nil), "deadlock.WaitForEntry")
	proto.RegisterType((*ReplaceLockByKeyItem)(nil), "deadlock.ReplaceLockByKeyItem")
	proto.RegisterType((*ReplaceLocksByKeysRequest)(nil), "deadlock.ReplaceLocksByKeysRequest")
	proto.RegisterType((*DeadlockRequest)(nil), "deadlock.DeadlockRequest")
	proto.RegisterType((*DeadlockResponse)(nil), "deadlock.DeadlockResponse")
}

func init() { proto.RegisterFile("deadlock.proto", fileDescriptor_1995e4345c820a37) }

var fileDescriptor_1995e4345c820a37 = []byte{
	// 615 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x54, 0xcb, 0x6e, 0xd3, 0x40,
	0x14, 0xf5, 0x24, 0x69, 0x9b, 0xde, 0x44, 0x6d, 0x3a, 0xb4, 0xc5, 0x0d, 0xc2, 0x0d, 0x66, 0x13,
	0x55, 0x50, 0x50, 0x78, 0x7c, 0x40, 0xda, 0x52, 0x50, 0x59, 0x80, 0x15, 0x1e, 0x3b, 0xcb, 0x4d,
	0x2e, 0x89, 0x95, 0xc4, 0x63, 0x3c, 0x13, 0xa5, 0x5e, 0xf3, 0x03, 0x2c, 0xf9, 0x04, 0x24, 0xfe,
	0x80, 0x2f, 0xe8, 0x82, 0x45, 0x97, 0x5d, 0x21, 0xd4, 0xfc, 0x08, 0x9a, 0x19, 0x3b, 0x49, 0x8b,
	0x0b, 0xec, 0xae, 0xef, 0x39, 0x3e, 0x73, 0xee, 0x63, 0x06, 0x56, 0x3a, 0xe8, 0x75, 0x06, 0xac,
	0xdd, 0xdf, 0x0d, 0x23, 0x26, 0x18, 0x2d, 0xa6, 0xdf, 0xd5, 0xf5, 0x2e, 0xeb, 0x32, 0x95, 0x7c,
	0x20, 0x23, 0x8d, 0x57, 0x57, 0xa3, 0x11, 0x17, 0x2a, 0xd4, 0x09, 0xfb, 0x26, 0x6c, 0xbc, 0xf3,
	0x7c, 0xf1, 0x8c, 0x45, 0x07, 0x81, 0x88, 0x7c, 0xe4, 0x0e, 0x7e, 0x1c, 0x21, 0x17, 0xf6, 0x2b,
	0xd8, 0xbc, 0x0a, 0xf0, 0x90, 0x05, 0x1c, 0xe9, 0x53, 0x58, 0x42, 0x9d, 0x32, 0x49, 0x2d, 0x5f,
	0x2f, 0x35, 0x36, 0x77, 0xa7, 0x2e, 0xe6, 0x7e, 0x89, 0x9b, 0x85, 0xd3, 0x9f, 0xdb, 0x86, 0x93,
	0x92, 0xed, 0xef, 0x04, 0xca, 0xf3, 0x38, 0xad, 0x40, 0x5e, 0x9c, 0x04, 0x26, 0xa9, 0x91, 0x7a,
	0xc1, 0x91, 0x21, 0xad, 0x41, 0x79, 0xec, 0xf9, 0xc2, 0xfd, 0xc0, 0x22, 0x57, 0x42, 0x39, 0x05,
	0xc1, 0x58, 0xff, 0xd5, 0x3a, 0x09, 0xe8, 0x16, 0x14, 0xfb, 0x18, 0xbb, 0x3d, 0x8f, 0xf7, 0xcc,
	0xbc, 0x42, 0x97, 0xfa, 0x18, 0x3f, 0xf7, 0x78, 0x4f, 0xca, 0xf5, 0x31, 0x36, 0x0b, 0x35, 0x52,
	0x2f, 0x3b, 0x32, 0xa4, 0xf7, 0x80, 0x46, 0xc8, 0xd9, 0x28, 0x6a, 0xa3, 0xdb, 0x8d, 0xd8, 0x28,
	0x74, 0x85, 0xd7, 0x35, 0x17, 0x14, 0xa1, 0x92, 0x22, 0x87, 0x12, 0x68, 0x79, 0x5d, 0x7a, 0x0b,
	0x96, 0xd5, 0xe1, 0xc2, 0x1f, 0xa2, 0xb9, 0xa8, 0xb4, 0x8b, 0x32, 0xd1, 0xf2, 0x87, 0x68, 0x7f,
	0x22, 0xb0, 0xee, 0x60, 0x38, 0xf0, 0xda, 0xf8, 0x92, 0xb5, 0xfb, 0xcd, 0xf8, 0x08, 0xe3, 0x17,
	0x02, 0x87, 0x97, 0x0c, 0x91, 0x4c, 0x43, 0xb9, 0x99, 0x21, 0x0b, 0x4a, 0x6c, 0xd0, 0x71, 0x65,
	0xab, 0x5c, 0xc1, 0x93, 0x02, 0x96, 0xd9, 0xa0, 0x23, 0x35, 0x5b, 0x5c, 0xe2, 0x01, 0x8e, 0xa7,
	0x78, 0x41, 0xe3, 0x01, 0x8e, 0x35, 0x6e, 0xbf, 0x86, 0xad, 0x39, 0x13, 0x5c, 0xb9, 0x48, 0x27,
	0x46, 0x1f, 0xc3, 0x82, 0x2f, 0x70, 0x98, 0x4e, 0xc5, 0x9a, 0x4d, 0x25, 0xcb, 0xb8, 0xa3, 0xc9,
	0xf6, 0x0f, 0x02, 0xab, 0xfb, 0x09, 0x31, 0x55, 0xba, 0x0f, 0x39, 0x11, 0xaa, 0x6a, 0x56, 0x1a,
	0xb7, 0x67, 0x32, 0x57, 0x68, 0xad, 0x38, 0x44, 0x27, 0x27, 0x42, 0xda, 0x80, 0x05, 0x39, 0x63,
	0x5d, 0xe9, 0xbf, 0xd6, 0x41, 0x53, 0xe9, 0x5b, 0xd8, 0x88, 0xb4, 0x2b, 0x55, 0x2d, 0x77, 0x8f,
	0x63, 0xb7, 0x8f, 0xb1, 0xee, 0x49, 0xa9, 0x71, 0x37, 0xd3, 0xfc, 0xe5, 0x82, 0x1d, 0x1a, 0xfd,
	0x01, 0xc9, 0x72, 0x2a, 0x33, 0x9f, 0xc9, 0xc6, 0x4e, 0x0d, 0x92, 0xff, 0x37, 0xb8, 0x03, 0x6b,
	0x29, 0xcb, 0x9d, 0x0e, 0x58, 0xef, 0xe3, 0x6a, 0x0a, 0x1c, 0x25, 0x83, 0x7e, 0x02, 0x6a, 0x45,
	0xdd, 0x76, 0xcf, 0xf3, 0x03, 0x33, 0xff, 0xb7, 0x4b, 0xe1, 0xa8, 0x1d, 0xdb, 0x93, 0x44, 0x7a,
	0x07, 0xca, 0xf3, 0x47, 0x24, 0x9b, 0x5b, 0x9a, 0x53, 0xdf, 0x69, 0xc2, 0x8d, 0x8c, 0xae, 0x53,
	0x80, 0xc5, 0x7d, 0x14, 0xd8, 0x16, 0x15, 0x83, 0x52, 0x58, 0xd9, 0x1b, 0xa0, 0x17, 0xbc, 0x09,
	0x93, 0x73, 0x2a, 0x84, 0x96, 0x60, 0x29, 0xc9, 0x55, 0x72, 0x8d, 0x6f, 0x04, 0x8a, 0xa9, 0x08,
	0x7d, 0x0f, 0x6b, 0x87, 0x28, 0x2e, 0xdf, 0x6c, 0xba, 0x9d, 0xe9, 0x75, 0xf6, 0x18, 0x54, 0x6b,
	0xd7, 0x13, 0x74, 0x8b, 0x6d, 0x83, 0x1e, 0xa4, 0x9e, 0xe8, 0xd6, 0xb5, 0x2b, 0x53, 0xad, 0x66,
	0x41, 0xa9, 0x44, 0x9d, 0x3c, 0x24, 0xcd, 0xda, 0xf9, 0xd7, 0x22, 0x39, 0xbd, 0xb0, 0xc8, 0xd9,
	0x85, 0x45, 0x7e, 0x5d, 0x58, 0xe4, 0xf3, 0xc4, 0x32, 0xbe, 0x4c, 0x2c, 0xe3, 0x6c, 0x62, 0x19,
	0xe7, 0x13, 0xcb, 0x38, 0x5e, 0x54, 0x2f, 0xd7, 0xa3, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0x60,
	0x42, 0x9b, 0xa6, 0xfc, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DeadlockClient is the client API for Deadlock service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DeadlockClient interface {
	// Get local wait for entries, should be handle by every node.
	// The owner should sent this request to all members to build the complete wait for graph.
	GetWaitForEntries(ctx context.Context, in *WaitForEntriesRequest, opts ...grpc.CallOption) (*WaitForEntriesResponse, error)
	// Detect should only sent to the owner. only be handled by the owner.
	// The DeadlockResponse is sent back only if there is deadlock detected.
	// CleanUpWaitFor and CleanUp doesn't return responses.
	Detect(ctx context.Context, opts ...grpc.CallOption) (Deadlock_DetectClient, error)
}

type deadlockClient struct {
	cc *grpc.ClientConn
}

func NewDeadlockClient(cc *grpc.ClientConn) DeadlockClient {
	return &deadlockClient{cc}
}

func (c *deadlockClient) GetWaitForEntries(ctx context.Context, in *WaitForEntriesRequest, opts ...grpc.CallOption) (*WaitForEntriesResponse, error) {
	out := new(WaitForEntriesResponse)
	err := c.cc.Invoke(ctx, "/deadlock.Deadlock/GetWaitForEntries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deadlockClient) Detect(ctx context.Context, opts ...grpc.CallOption) (Deadlock_DetectClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Deadlock_serviceDesc.Streams[0], "/deadlock.Deadlock/Detect", opts...)
	if err != nil {
		return nil, err
	}
	x := &deadlockDetectClient{stream}
	return x, nil
}

type Deadlock_DetectClient interface {
	Send(*DeadlockRequest) error
	Recv() (*DeadlockResponse, error)
	grpc.ClientStream
}

type deadlockDetectClient struct {
	grpc.ClientStream
}

func (x *deadlockDetectClient) Send(m *DeadlockRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *deadlockDetectClient) Recv() (*DeadlockResponse, error) {
	m := new(DeadlockResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// DeadlockServer is the server API for Deadlock service.
type DeadlockServer interface {
	// Get local wait for entries, should be handle by every node.
	// The owner should sent this request to all members to build the complete wait for graph.
	GetWaitForEntries(context.Context, *WaitForEntriesRequest) (*WaitForEntriesResponse, error)
	// Detect should only sent to the owner. only be handled by the owner.
	// The DeadlockResponse is sent back only if there is deadlock detected.
	// CleanUpWaitFor and CleanUp doesn't return responses.
	Detect(Deadlock_DetectServer) error
}

// UnimplementedDeadlockServer can be embedded to have forward compatible implementations.
type UnimplementedDeadlockServer struct {
}

func (*UnimplementedDeadlockServer) GetWaitForEntries(ctx context.Context, req *WaitForEntriesRequest) (*WaitForEntriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWaitForEntries not implemented")
}
func (*UnimplementedDeadlockServer) Detect(srv Deadlock_DetectServer) error {
	return status.Errorf(codes.Unimplemented, "method Detect not implemented")
}

func RegisterDeadlockServer(s *grpc.Server, srv DeadlockServer) {
	s.RegisterService(&_Deadlock_serviceDesc, srv)
}

func _Deadlock_GetWaitForEntries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WaitForEntriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeadlockServer).GetWaitForEntries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/deadlock.Deadlock/GetWaitForEntries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeadlockServer).GetWaitForEntries(ctx, req.(*WaitForEntriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Deadlock_Detect_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(DeadlockServer).Detect(&deadlockDetectServer{stream})
}

type Deadlock_DetectServer interface {
	Send(*DeadlockResponse) error
	Recv() (*DeadlockRequest, error)
	grpc.ServerStream
}

type deadlockDetectServer struct {
	grpc.ServerStream
}

func (x *deadlockDetectServer) Send(m *DeadlockResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *deadlockDetectServer) Recv() (*DeadlockRequest, error) {
	m := new(DeadlockRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _Deadlock_serviceDesc = grpc.ServiceDesc{
	ServiceName: "deadlock.Deadlock",
	HandlerType: (*DeadlockServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWaitForEntries",
			Handler:    _Deadlock_GetWaitForEntries_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Detect",
			Handler:       _Deadlock_Detect_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "deadlock.proto",
}

func (m *WaitForEntriesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WaitForEntriesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WaitForEntriesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *WaitForEntriesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WaitForEntriesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WaitForEntriesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Entries) > 0 {
		for iNdEx := len(m.Entries) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Entries[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDeadlock(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *WaitForEntry) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WaitForEntry) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WaitForEntry) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.WaitTime != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.WaitTime))
		i--
		dAtA[i] = 0x30
	}
	if len(m.ResourceGroupTag) > 0 {
		i -= len(m.ResourceGroupTag)
		copy(dAtA[i:], m.ResourceGroupTag)
		i = encodeVarintDeadlock(dAtA, i, uint64(len(m.ResourceGroupTag)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintDeadlock(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x22
	}
	if m.KeyHash != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.KeyHash))
		i--
		dAtA[i] = 0x18
	}
	if m.WaitForTxn != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.WaitForTxn))
		i--
		dAtA[i] = 0x10
	}
	if m.Txn != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.Txn))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ReplaceLockByKeyItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReplaceLockByKeyItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReplaceLockByKeyItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.NewLockTs != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.NewLockTs))
		i--
		dAtA[i] = 0x20
	}
	if m.OldLockTs != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.OldLockTs))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintDeadlock(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x12
	}
	if m.KeyHash != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.KeyHash))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ReplaceLocksByKeysRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReplaceLocksByKeysRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReplaceLocksByKeysRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDeadlock(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DeadlockRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeadlockRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeadlockRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ReplaceLocksByKeys != nil {
		{
			size, err := m.ReplaceLocksByKeys.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDeadlock(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.Entry.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintDeadlock(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if m.Tp != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.Tp))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DeadlockResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeadlockResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeadlockResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DeadlockKey) > 0 {
		i -= len(m.DeadlockKey)
		copy(dAtA[i:], m.DeadlockKey)
		i = encodeVarintDeadlock(dAtA, i, uint64(len(m.DeadlockKey)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.WaitChain) > 0 {
		for iNdEx := len(m.WaitChain) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.WaitChain[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDeadlock(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.DeadlockKeyHash != 0 {
		i = encodeVarintDeadlock(dAtA, i, uint64(m.DeadlockKeyHash))
		i--
		dAtA[i] = 0x10
	}
	{
		size, err := m.Entry.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintDeadlock(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func encodeVarintDeadlock(dAtA []byte, offset int, v uint64) int {
	offset -= sovDeadlock(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *WaitForEntriesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *WaitForEntriesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Entries) > 0 {
		for _, e := range m.Entries {
			l = e.Size()
			n += 1 + l + sovDeadlock(uint64(l))
		}
	}
	return n
}

func (m *WaitForEntry) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Txn != 0 {
		n += 1 + sovDeadlock(uint64(m.Txn))
	}
	if m.WaitForTxn != 0 {
		n += 1 + sovDeadlock(uint64(m.WaitForTxn))
	}
	if m.KeyHash != 0 {
		n += 1 + sovDeadlock(uint64(m.KeyHash))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovDeadlock(uint64(l))
	}
	l = len(m.ResourceGroupTag)
	if l > 0 {
		n += 1 + l + sovDeadlock(uint64(l))
	}
	if m.WaitTime != 0 {
		n += 1 + sovDeadlock(uint64(m.WaitTime))
	}
	return n
}

func (m *ReplaceLockByKeyItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.KeyHash != 0 {
		n += 1 + sovDeadlock(uint64(m.KeyHash))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovDeadlock(uint64(l))
	}
	if m.OldLockTs != 0 {
		n += 1 + sovDeadlock(uint64(m.OldLockTs))
	}
	if m.NewLockTs != 0 {
		n += 1 + sovDeadlock(uint64(m.NewLockTs))
	}
	return n
}

func (m *ReplaceLocksByKeysRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovDeadlock(uint64(l))
		}
	}
	return n
}

func (m *DeadlockRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Tp != 0 {
		n += 1 + sovDeadlock(uint64(m.Tp))
	}
	l = m.Entry.Size()
	n += 1 + l + sovDeadlock(uint64(l))
	if m.ReplaceLocksByKeys != nil {
		l = m.ReplaceLocksByKeys.Size()
		n += 1 + l + sovDeadlock(uint64(l))
	}
	return n
}

func (m *DeadlockResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Entry.Size()
	n += 1 + l + sovDeadlock(uint64(l))
	if m.DeadlockKeyHash != 0 {
		n += 1 + sovDeadlock(uint64(m.DeadlockKeyHash))
	}
	if len(m.WaitChain) > 0 {
		for _, e := range m.WaitChain {
			l = e.Size()
			n += 1 + l + sovDeadlock(uint64(l))
		}
	}
	l = len(m.DeadlockKey)
	if l > 0 {
		n += 1 + l + sovDeadlock(uint64(l))
	}
	return n
}

func sovDeadlock(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDeadlock(x uint64) (n int) {
	return sovDeadlock(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *WaitForEntriesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDeadlock
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WaitForEntriesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WaitForEntriesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDeadlock(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDeadlock
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WaitForEntriesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDeadlock
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WaitForEntriesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WaitForEntriesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entries", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Entries = append(m.Entries, WaitForEntry{})
			if err := m.Entries[len(m.Entries)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDeadlock(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDeadlock
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WaitForEntry) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDeadlock
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WaitForEntry: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WaitForEntry: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Txn", wireType)
			}
			m.Txn = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Txn |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitForTxn", wireType)
			}
			m.WaitForTxn = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WaitForTxn |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyHash", wireType)
			}
			m.KeyHash = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeyHash |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResourceGroupTag", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResourceGroupTag = append(m.ResourceGroupTag[:0], dAtA[iNdEx:postIndex]...)
			if m.ResourceGroupTag == nil {
				m.ResourceGroupTag = []byte{}
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitTime", wireType)
			}
			m.WaitTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WaitTime |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDeadlock(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDeadlock
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReplaceLockByKeyItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDeadlock
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReplaceLockByKeyItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReplaceLockByKeyItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyHash", wireType)
			}
			m.KeyHash = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeyHash |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OldLockTs", wireType)
			}
			m.OldLockTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OldLockTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewLockTs", wireType)
			}
			m.NewLockTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewLockTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDeadlock(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDeadlock
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReplaceLocksByKeysRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDeadlock
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReplaceLocksByKeysRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReplaceLocksByKeysRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, &ReplaceLockByKeyItem{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDeadlock(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDeadlock
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeadlockRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDeadlock
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeadlockRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeadlockRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tp", wireType)
			}
			m.Tp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tp |= DeadlockRequestType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entry", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Entry.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReplaceLocksByKeys", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReplaceLocksByKeys == nil {
				m.ReplaceLocksByKeys = &ReplaceLocksByKeysRequest{}
			}
			if err := m.ReplaceLocksByKeys.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDeadlock(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDeadlock
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeadlockResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDeadlock
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeadlockResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeadlockResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Entry", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Entry.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeadlockKeyHash", wireType)
			}
			m.DeadlockKeyHash = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeadlockKeyHash |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitChain", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WaitChain = append(m.WaitChain, &WaitForEntry{})
			if err := m.WaitChain[len(m.WaitChain)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeadlockKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDeadlock
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDeadlock
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeadlockKey = append(m.DeadlockKey[:0], dAtA[iNdEx:postIndex]...)
			if m.DeadlockKey == nil {
				m.DeadlockKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDeadlock(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDeadlock
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDeadlock(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDeadlock
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDeadlock
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDeadlock
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDeadlock
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDeadlock
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDeadlock        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDeadlock          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDeadlock = fmt.Errorf("proto: unexpected end of group")
)
