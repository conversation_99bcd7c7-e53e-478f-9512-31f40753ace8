// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: resource_manager.proto

package resource_manager

import (
	"context"
	encoding_binary "encoding/binary"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type RequestUnitType int32

const (
	RequestUnitType_RU RequestUnitType = 0
)

var RequestUnitType_name = map[int32]string{
	0: "RU",
}

var RequestUnitType_value = map[string]int32{
	"RU": 0,
}

func (x RequestUnitType) String() string {
	return proto.EnumName(RequestUnitType_name, int32(x))
}

func (RequestUnitType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{0}
}

type RawResourceType int32

const (
	RawResourceType_CPU         RawResourceType = 0
	RawResourceType_IOReadFlow  RawResourceType = 1
	RawResourceType_IOWriteFlow RawResourceType = 2
)

var RawResourceType_name = map[int32]string{
	0: "CPU",
	1: "IOReadFlow",
	2: "IOWriteFlow",
}

var RawResourceType_value = map[string]int32{
	"CPU":         0,
	"IOReadFlow":  1,
	"IOWriteFlow": 2,
}

func (x RawResourceType) String() string {
	return proto.EnumName(RawResourceType_name, int32(x))
}

func (RawResourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{1}
}

type GroupMode int32

const (
	GroupMode_Unknown GroupMode = 0
	GroupMode_RUMode  GroupMode = 1
	GroupMode_RawMode GroupMode = 2
)

var GroupMode_name = map[int32]string{
	0: "Unknown",
	1: "RUMode",
	2: "RawMode",
}

var GroupMode_value = map[string]int32{
	"Unknown": 0,
	"RUMode":  1,
	"RawMode": 2,
}

func (x GroupMode) String() string {
	return proto.EnumName(GroupMode_name, int32(x))
}

func (GroupMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{2}
}

type RunawayAction int32

const (
	RunawayAction_NoneAction  RunawayAction = 0
	RunawayAction_DryRun      RunawayAction = 1
	RunawayAction_CoolDown    RunawayAction = 2
	RunawayAction_Kill        RunawayAction = 3
	RunawayAction_SwitchGroup RunawayAction = 4
)

var RunawayAction_name = map[int32]string{
	0: "NoneAction",
	1: "DryRun",
	2: "CoolDown",
	3: "Kill",
	4: "SwitchGroup",
}

var RunawayAction_value = map[string]int32{
	"NoneAction":  0,
	"DryRun":      1,
	"CoolDown":    2,
	"Kill":        3,
	"SwitchGroup": 4,
}

func (x RunawayAction) String() string {
	return proto.EnumName(RunawayAction_name, int32(x))
}

func (RunawayAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{3}
}

type RunawayWatchType int32

const (
	RunawayWatchType_NoneWatch RunawayWatchType = 0
	RunawayWatchType_Exact     RunawayWatchType = 1
	RunawayWatchType_Similar   RunawayWatchType = 2
	RunawayWatchType_Plan      RunawayWatchType = 3
)

var RunawayWatchType_name = map[int32]string{
	0: "NoneWatch",
	1: "Exact",
	2: "Similar",
	3: "Plan",
}

var RunawayWatchType_value = map[string]int32{
	"NoneWatch": 0,
	"Exact":     1,
	"Similar":   2,
	"Plan":      3,
}

func (x RunawayWatchType) String() string {
	return proto.EnumName(RunawayWatchType_name, int32(x))
}

func (RunawayWatchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{4}
}

// KeyspaceIDValue is a wrapper for the value of keyspace ID.
// Because the 0 value is a valid keyspace ID, we need to use a wrapper to distinguish it from the null keyspace ID.
type KeyspaceIDValue struct {
	Value uint32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *KeyspaceIDValue) Reset()         { *m = KeyspaceIDValue{} }
func (m *KeyspaceIDValue) String() string { return proto.CompactTextString(m) }
func (*KeyspaceIDValue) ProtoMessage()    {}
func (*KeyspaceIDValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{0}
}
func (m *KeyspaceIDValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeyspaceIDValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeyspaceIDValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KeyspaceIDValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyspaceIDValue.Merge(m, src)
}
func (m *KeyspaceIDValue) XXX_Size() int {
	return m.Size()
}
func (m *KeyspaceIDValue) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyspaceIDValue.DiscardUnknown(m)
}

var xxx_messageInfo_KeyspaceIDValue proto.InternalMessageInfo

func (m *KeyspaceIDValue) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

type ListResourceGroupsRequest struct {
	WithRuStats bool `protobuf:"varint,1,opt,name=with_ru_stats,json=withRuStats,proto3" json:"with_ru_stats,omitempty"`
	// There're two cases for this field:
	//   - If the keyspace ID is not set, it means this may be a message from an older version.
	//     To maintain compatibility, we will treat it as a null keyspace ID, which is uint32.Max.
	//   - If the keyspace ID is set to a valid value, the listed resource groups will be filtered
	//     by the given keyspace ID.
	KeyspaceId *KeyspaceIDValue `protobuf:"bytes,2,opt,name=keyspace_id,json=keyspaceId,proto3" json:"keyspace_id,omitempty"`
}

func (m *ListResourceGroupsRequest) Reset()         { *m = ListResourceGroupsRequest{} }
func (m *ListResourceGroupsRequest) String() string { return proto.CompactTextString(m) }
func (*ListResourceGroupsRequest) ProtoMessage()    {}
func (*ListResourceGroupsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{1}
}
func (m *ListResourceGroupsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListResourceGroupsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListResourceGroupsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListResourceGroupsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListResourceGroupsRequest.Merge(m, src)
}
func (m *ListResourceGroupsRequest) XXX_Size() int {
	return m.Size()
}
func (m *ListResourceGroupsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListResourceGroupsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListResourceGroupsRequest proto.InternalMessageInfo

func (m *ListResourceGroupsRequest) GetWithRuStats() bool {
	if m != nil {
		return m.WithRuStats
	}
	return false
}

func (m *ListResourceGroupsRequest) GetKeyspaceId() *KeyspaceIDValue {
	if m != nil {
		return m.KeyspaceId
	}
	return nil
}

type ListResourceGroupsResponse struct {
	Error  *Error           `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Groups []*ResourceGroup `protobuf:"bytes,2,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (m *ListResourceGroupsResponse) Reset()         { *m = ListResourceGroupsResponse{} }
func (m *ListResourceGroupsResponse) String() string { return proto.CompactTextString(m) }
func (*ListResourceGroupsResponse) ProtoMessage()    {}
func (*ListResourceGroupsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{2}
}
func (m *ListResourceGroupsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ListResourceGroupsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ListResourceGroupsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ListResourceGroupsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListResourceGroupsResponse.Merge(m, src)
}
func (m *ListResourceGroupsResponse) XXX_Size() int {
	return m.Size()
}
func (m *ListResourceGroupsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListResourceGroupsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListResourceGroupsResponse proto.InternalMessageInfo

func (m *ListResourceGroupsResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *ListResourceGroupsResponse) GetGroups() []*ResourceGroup {
	if m != nil {
		return m.Groups
	}
	return nil
}

type GetResourceGroupRequest struct {
	ResourceGroupName string `protobuf:"bytes,1,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	WithRuStats       bool   `protobuf:"varint,2,opt,name=with_ru_stats,json=withRuStats,proto3" json:"with_ru_stats,omitempty"`
	// There're two cases for this field:
	//   - If the keyspace ID is not set, it means this may be a message from an older version.
	//     To maintain compatibility, we will treat it as a null keyspace ID, which is uint32.Max.
	//   - If the keyspace ID is set to a valid value, it will try to get the resource group within
	//     the given keyspace ID.
	KeyspaceId *KeyspaceIDValue `protobuf:"bytes,3,opt,name=keyspace_id,json=keyspaceId,proto3" json:"keyspace_id,omitempty"`
}

func (m *GetResourceGroupRequest) Reset()         { *m = GetResourceGroupRequest{} }
func (m *GetResourceGroupRequest) String() string { return proto.CompactTextString(m) }
func (*GetResourceGroupRequest) ProtoMessage()    {}
func (*GetResourceGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{3}
}
func (m *GetResourceGroupRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetResourceGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetResourceGroupRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetResourceGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceGroupRequest.Merge(m, src)
}
func (m *GetResourceGroupRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetResourceGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceGroupRequest proto.InternalMessageInfo

func (m *GetResourceGroupRequest) GetResourceGroupName() string {
	if m != nil {
		return m.ResourceGroupName
	}
	return ""
}

func (m *GetResourceGroupRequest) GetWithRuStats() bool {
	if m != nil {
		return m.WithRuStats
	}
	return false
}

func (m *GetResourceGroupRequest) GetKeyspaceId() *KeyspaceIDValue {
	if m != nil {
		return m.KeyspaceId
	}
	return nil
}

type GetResourceGroupResponse struct {
	Error *Error         `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Group *ResourceGroup `protobuf:"bytes,2,opt,name=group,proto3" json:"group,omitempty"`
}

func (m *GetResourceGroupResponse) Reset()         { *m = GetResourceGroupResponse{} }
func (m *GetResourceGroupResponse) String() string { return proto.CompactTextString(m) }
func (*GetResourceGroupResponse) ProtoMessage()    {}
func (*GetResourceGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{4}
}
func (m *GetResourceGroupResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetResourceGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetResourceGroupResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetResourceGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetResourceGroupResponse.Merge(m, src)
}
func (m *GetResourceGroupResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetResourceGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetResourceGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetResourceGroupResponse proto.InternalMessageInfo

func (m *GetResourceGroupResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *GetResourceGroupResponse) GetGroup() *ResourceGroup {
	if m != nil {
		return m.Group
	}
	return nil
}

type DeleteResourceGroupRequest struct {
	ResourceGroupName string `protobuf:"bytes,1,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// There're two cases for this field:
	//   - If the keyspace ID is not set, it means this may be a message from an older version.
	//     To maintain compatibility, we will treat it as a null keyspace ID, which is uint32.Max.
	//   - If the keyspace ID is set to a valid value, it will try to delete the resource group within
	//     the given keyspace ID.
	KeyspaceId *KeyspaceIDValue `protobuf:"bytes,2,opt,name=keyspace_id,json=keyspaceId,proto3" json:"keyspace_id,omitempty"`
}

func (m *DeleteResourceGroupRequest) Reset()         { *m = DeleteResourceGroupRequest{} }
func (m *DeleteResourceGroupRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteResourceGroupRequest) ProtoMessage()    {}
func (*DeleteResourceGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{5}
}
func (m *DeleteResourceGroupRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteResourceGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteResourceGroupRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteResourceGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResourceGroupRequest.Merge(m, src)
}
func (m *DeleteResourceGroupRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteResourceGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResourceGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResourceGroupRequest proto.InternalMessageInfo

func (m *DeleteResourceGroupRequest) GetResourceGroupName() string {
	if m != nil {
		return m.ResourceGroupName
	}
	return ""
}

func (m *DeleteResourceGroupRequest) GetKeyspaceId() *KeyspaceIDValue {
	if m != nil {
		return m.KeyspaceId
	}
	return nil
}

type DeleteResourceGroupResponse struct {
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Body  string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (m *DeleteResourceGroupResponse) Reset()         { *m = DeleteResourceGroupResponse{} }
func (m *DeleteResourceGroupResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteResourceGroupResponse) ProtoMessage()    {}
func (*DeleteResourceGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{6}
}
func (m *DeleteResourceGroupResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteResourceGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteResourceGroupResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteResourceGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResourceGroupResponse.Merge(m, src)
}
func (m *DeleteResourceGroupResponse) XXX_Size() int {
	return m.Size()
}
func (m *DeleteResourceGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResourceGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResourceGroupResponse proto.InternalMessageInfo

func (m *DeleteResourceGroupResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *DeleteResourceGroupResponse) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

type PutResourceGroupRequest struct {
	Group *ResourceGroup `protobuf:"bytes,1,opt,name=group,proto3" json:"group,omitempty"`
}

func (m *PutResourceGroupRequest) Reset()         { *m = PutResourceGroupRequest{} }
func (m *PutResourceGroupRequest) String() string { return proto.CompactTextString(m) }
func (*PutResourceGroupRequest) ProtoMessage()    {}
func (*PutResourceGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{7}
}
func (m *PutResourceGroupRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutResourceGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutResourceGroupRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PutResourceGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutResourceGroupRequest.Merge(m, src)
}
func (m *PutResourceGroupRequest) XXX_Size() int {
	return m.Size()
}
func (m *PutResourceGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PutResourceGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PutResourceGroupRequest proto.InternalMessageInfo

func (m *PutResourceGroupRequest) GetGroup() *ResourceGroup {
	if m != nil {
		return m.Group
	}
	return nil
}

type PutResourceGroupResponse struct {
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Body  string `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
}

func (m *PutResourceGroupResponse) Reset()         { *m = PutResourceGroupResponse{} }
func (m *PutResourceGroupResponse) String() string { return proto.CompactTextString(m) }
func (*PutResourceGroupResponse) ProtoMessage()    {}
func (*PutResourceGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{8}
}
func (m *PutResourceGroupResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PutResourceGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PutResourceGroupResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PutResourceGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PutResourceGroupResponse.Merge(m, src)
}
func (m *PutResourceGroupResponse) XXX_Size() int {
	return m.Size()
}
func (m *PutResourceGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PutResourceGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PutResourceGroupResponse proto.InternalMessageInfo

func (m *PutResourceGroupResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *PutResourceGroupResponse) GetBody() string {
	if m != nil {
		return m.Body
	}
	return ""
}

type TokenBucketsRequest struct {
	Requests              []*TokenBucketRequest `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	TargetRequestPeriodMs uint64                `protobuf:"varint,2,opt,name=target_request_period_ms,json=targetRequestPeriodMs,proto3" json:"target_request_period_ms,omitempty"`
	ClientUniqueId        uint64                `protobuf:"varint,3,opt,name=client_unique_id,json=clientUniqueId,proto3" json:"client_unique_id,omitempty"`
}

func (m *TokenBucketsRequest) Reset()         { *m = TokenBucketsRequest{} }
func (m *TokenBucketsRequest) String() string { return proto.CompactTextString(m) }
func (*TokenBucketsRequest) ProtoMessage()    {}
func (*TokenBucketsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{9}
}
func (m *TokenBucketsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TokenBucketsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TokenBucketsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TokenBucketsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenBucketsRequest.Merge(m, src)
}
func (m *TokenBucketsRequest) XXX_Size() int {
	return m.Size()
}
func (m *TokenBucketsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenBucketsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TokenBucketsRequest proto.InternalMessageInfo

func (m *TokenBucketsRequest) GetRequests() []*TokenBucketRequest {
	if m != nil {
		return m.Requests
	}
	return nil
}

func (m *TokenBucketsRequest) GetTargetRequestPeriodMs() uint64 {
	if m != nil {
		return m.TargetRequestPeriodMs
	}
	return 0
}

func (m *TokenBucketsRequest) GetClientUniqueId() uint64 {
	if m != nil {
		return m.ClientUniqueId
	}
	return 0
}

type TokenBucketRequest struct {
	ResourceGroupName string `protobuf:"bytes,1,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Types that are valid to be assigned to Request:
	//	*TokenBucketRequest_RuItems
	//	*TokenBucketRequest_RawResourceItems
	Request isTokenBucketRequest_Request `protobuf_oneof:"request"`
	// Aggregate statistics in group level.
	ConsumptionSinceLastRequest *Consumption `protobuf:"bytes,4,opt,name=consumption_since_last_request,json=consumptionSinceLastRequest,proto3" json:"consumption_since_last_request,omitempty"`
	// label background request.
	IsBackground bool `protobuf:"varint,5,opt,name=is_background,json=isBackground,proto3" json:"is_background,omitempty"`
	IsTiflash    bool `protobuf:"varint,6,opt,name=is_tiflash,json=isTiflash,proto3" json:"is_tiflash,omitempty"`
	// There're two cases for this field:
	//   - If the keyspace ID is not set, it means this may be a message from an older version.
	//     To maintain compatibility, we will treat it as a null keyspace ID, which is uint32.Max.
	//   - If the keyspace ID is set to a valid value, it will try to request the token bucket from
	//     the resource group within the given keyspace ID.
	KeyspaceId *KeyspaceIDValue `protobuf:"bytes,7,opt,name=keyspace_id,json=keyspaceId,proto3" json:"keyspace_id,omitempty"`
}

func (m *TokenBucketRequest) Reset()         { *m = TokenBucketRequest{} }
func (m *TokenBucketRequest) String() string { return proto.CompactTextString(m) }
func (*TokenBucketRequest) ProtoMessage()    {}
func (*TokenBucketRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{10}
}
func (m *TokenBucketRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TokenBucketRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TokenBucketRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TokenBucketRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenBucketRequest.Merge(m, src)
}
func (m *TokenBucketRequest) XXX_Size() int {
	return m.Size()
}
func (m *TokenBucketRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenBucketRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TokenBucketRequest proto.InternalMessageInfo

type isTokenBucketRequest_Request interface {
	isTokenBucketRequest_Request()
	MarshalTo([]byte) (int, error)
	Size() int
}

type TokenBucketRequest_RuItems struct {
	RuItems *TokenBucketRequest_RequestRU `protobuf:"bytes,2,opt,name=ru_items,json=ruItems,proto3,oneof" json:"ru_items,omitempty"`
}
type TokenBucketRequest_RawResourceItems struct {
	RawResourceItems *TokenBucketRequest_RequestRawResource `protobuf:"bytes,3,opt,name=raw_resource_items,json=rawResourceItems,proto3,oneof" json:"raw_resource_items,omitempty"`
}

func (*TokenBucketRequest_RuItems) isTokenBucketRequest_Request()          {}
func (*TokenBucketRequest_RawResourceItems) isTokenBucketRequest_Request() {}

func (m *TokenBucketRequest) GetRequest() isTokenBucketRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (m *TokenBucketRequest) GetResourceGroupName() string {
	if m != nil {
		return m.ResourceGroupName
	}
	return ""
}

func (m *TokenBucketRequest) GetRuItems() *TokenBucketRequest_RequestRU {
	if x, ok := m.GetRequest().(*TokenBucketRequest_RuItems); ok {
		return x.RuItems
	}
	return nil
}

func (m *TokenBucketRequest) GetRawResourceItems() *TokenBucketRequest_RequestRawResource {
	if x, ok := m.GetRequest().(*TokenBucketRequest_RawResourceItems); ok {
		return x.RawResourceItems
	}
	return nil
}

func (m *TokenBucketRequest) GetConsumptionSinceLastRequest() *Consumption {
	if m != nil {
		return m.ConsumptionSinceLastRequest
	}
	return nil
}

func (m *TokenBucketRequest) GetIsBackground() bool {
	if m != nil {
		return m.IsBackground
	}
	return false
}

func (m *TokenBucketRequest) GetIsTiflash() bool {
	if m != nil {
		return m.IsTiflash
	}
	return false
}

func (m *TokenBucketRequest) GetKeyspaceId() *KeyspaceIDValue {
	if m != nil {
		return m.KeyspaceId
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*TokenBucketRequest) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*TokenBucketRequest_RuItems)(nil),
		(*TokenBucketRequest_RawResourceItems)(nil),
	}
}

type TokenBucketRequest_RequestRU struct {
	RequestRU []*RequestUnitItem `protobuf:"bytes,1,rep,name=request_r_u,json=requestRU,proto3" json:"request_r_u,omitempty"`
}

func (m *TokenBucketRequest_RequestRU) Reset()         { *m = TokenBucketRequest_RequestRU{} }
func (m *TokenBucketRequest_RequestRU) String() string { return proto.CompactTextString(m) }
func (*TokenBucketRequest_RequestRU) ProtoMessage()    {}
func (*TokenBucketRequest_RequestRU) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{10, 0}
}
func (m *TokenBucketRequest_RequestRU) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TokenBucketRequest_RequestRU) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TokenBucketRequest_RequestRU.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TokenBucketRequest_RequestRU) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenBucketRequest_RequestRU.Merge(m, src)
}
func (m *TokenBucketRequest_RequestRU) XXX_Size() int {
	return m.Size()
}
func (m *TokenBucketRequest_RequestRU) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenBucketRequest_RequestRU.DiscardUnknown(m)
}

var xxx_messageInfo_TokenBucketRequest_RequestRU proto.InternalMessageInfo

func (m *TokenBucketRequest_RequestRU) GetRequestRU() []*RequestUnitItem {
	if m != nil {
		return m.RequestRU
	}
	return nil
}

type TokenBucketRequest_RequestRawResource struct {
	RequestRawResource []*RawResourceItem `protobuf:"bytes,1,rep,name=request_raw_resource,json=requestRawResource,proto3" json:"request_raw_resource,omitempty"`
}

func (m *TokenBucketRequest_RequestRawResource) Reset()         { *m = TokenBucketRequest_RequestRawResource{} }
func (m *TokenBucketRequest_RequestRawResource) String() string { return proto.CompactTextString(m) }
func (*TokenBucketRequest_RequestRawResource) ProtoMessage()    {}
func (*TokenBucketRequest_RequestRawResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{10, 1}
}
func (m *TokenBucketRequest_RequestRawResource) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TokenBucketRequest_RequestRawResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TokenBucketRequest_RequestRawResource.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TokenBucketRequest_RequestRawResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenBucketRequest_RequestRawResource.Merge(m, src)
}
func (m *TokenBucketRequest_RequestRawResource) XXX_Size() int {
	return m.Size()
}
func (m *TokenBucketRequest_RequestRawResource) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenBucketRequest_RequestRawResource.DiscardUnknown(m)
}

var xxx_messageInfo_TokenBucketRequest_RequestRawResource proto.InternalMessageInfo

func (m *TokenBucketRequest_RequestRawResource) GetRequestRawResource() []*RawResourceItem {
	if m != nil {
		return m.RequestRawResource
	}
	return nil
}

type TokenBucketsResponse struct {
	Error     *Error                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Responses []*TokenBucketResponse `protobuf:"bytes,2,rep,name=responses,proto3" json:"responses,omitempty"`
}

func (m *TokenBucketsResponse) Reset()         { *m = TokenBucketsResponse{} }
func (m *TokenBucketsResponse) String() string { return proto.CompactTextString(m) }
func (*TokenBucketsResponse) ProtoMessage()    {}
func (*TokenBucketsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{11}
}
func (m *TokenBucketsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TokenBucketsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TokenBucketsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TokenBucketsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenBucketsResponse.Merge(m, src)
}
func (m *TokenBucketsResponse) XXX_Size() int {
	return m.Size()
}
func (m *TokenBucketsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenBucketsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TokenBucketsResponse proto.InternalMessageInfo

func (m *TokenBucketsResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *TokenBucketsResponse) GetResponses() []*TokenBucketResponse {
	if m != nil {
		return m.Responses
	}
	return nil
}

type TokenBucketResponse struct {
	ResourceGroupName string `protobuf:"bytes,1,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// RU mode
	GrantedRUTokens []*GrantedRUTokenBucket `protobuf:"bytes,2,rep,name=granted_r_u_tokens,json=grantedRUTokens,proto3" json:"granted_r_u_tokens,omitempty"`
	// Raw mode
	GrantedResourceTokens []*GrantedRawResourceTokenBucket `protobuf:"bytes,3,rep,name=granted_resource_tokens,json=grantedResourceTokens,proto3" json:"granted_resource_tokens,omitempty"`
	// There're two cases for this field:
	//   - If the keyspace ID is not set, it means this may be a message from an older version,
	//     which can be safely ignored to keep compatibility.
	//   - If the keyspace ID is set to a valid value, it means this response is from the resource
	//     group within this keyspace ID.
	KeyspaceId *KeyspaceIDValue `protobuf:"bytes,4,opt,name=keyspace_id,json=keyspaceId,proto3" json:"keyspace_id,omitempty"`
}

func (m *TokenBucketResponse) Reset()         { *m = TokenBucketResponse{} }
func (m *TokenBucketResponse) String() string { return proto.CompactTextString(m) }
func (*TokenBucketResponse) ProtoMessage()    {}
func (*TokenBucketResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{12}
}
func (m *TokenBucketResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TokenBucketResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TokenBucketResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TokenBucketResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenBucketResponse.Merge(m, src)
}
func (m *TokenBucketResponse) XXX_Size() int {
	return m.Size()
}
func (m *TokenBucketResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenBucketResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TokenBucketResponse proto.InternalMessageInfo

func (m *TokenBucketResponse) GetResourceGroupName() string {
	if m != nil {
		return m.ResourceGroupName
	}
	return ""
}

func (m *TokenBucketResponse) GetGrantedRUTokens() []*GrantedRUTokenBucket {
	if m != nil {
		return m.GrantedRUTokens
	}
	return nil
}

func (m *TokenBucketResponse) GetGrantedResourceTokens() []*GrantedRawResourceTokenBucket {
	if m != nil {
		return m.GrantedResourceTokens
	}
	return nil
}

func (m *TokenBucketResponse) GetKeyspaceId() *KeyspaceIDValue {
	if m != nil {
		return m.KeyspaceId
	}
	return nil
}

type GrantedRUTokenBucket struct {
	Type          RequestUnitType `protobuf:"varint,1,opt,name=type,proto3,enum=resource_manager.RequestUnitType" json:"type,omitempty"`
	GrantedTokens *TokenBucket    `protobuf:"bytes,2,opt,name=granted_tokens,json=grantedTokens,proto3" json:"granted_tokens,omitempty"`
	TrickleTimeMs int64           `protobuf:"varint,3,opt,name=trickle_time_ms,json=trickleTimeMs,proto3" json:"trickle_time_ms,omitempty"`
}

func (m *GrantedRUTokenBucket) Reset()         { *m = GrantedRUTokenBucket{} }
func (m *GrantedRUTokenBucket) String() string { return proto.CompactTextString(m) }
func (*GrantedRUTokenBucket) ProtoMessage()    {}
func (*GrantedRUTokenBucket) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{13}
}
func (m *GrantedRUTokenBucket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GrantedRUTokenBucket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GrantedRUTokenBucket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GrantedRUTokenBucket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantedRUTokenBucket.Merge(m, src)
}
func (m *GrantedRUTokenBucket) XXX_Size() int {
	return m.Size()
}
func (m *GrantedRUTokenBucket) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantedRUTokenBucket.DiscardUnknown(m)
}

var xxx_messageInfo_GrantedRUTokenBucket proto.InternalMessageInfo

func (m *GrantedRUTokenBucket) GetType() RequestUnitType {
	if m != nil {
		return m.Type
	}
	return RequestUnitType_RU
}

func (m *GrantedRUTokenBucket) GetGrantedTokens() *TokenBucket {
	if m != nil {
		return m.GrantedTokens
	}
	return nil
}

func (m *GrantedRUTokenBucket) GetTrickleTimeMs() int64 {
	if m != nil {
		return m.TrickleTimeMs
	}
	return 0
}

type GrantedRawResourceTokenBucket struct {
	Type          RawResourceType `protobuf:"varint,1,opt,name=type,proto3,enum=resource_manager.RawResourceType" json:"type,omitempty"`
	GrantedTokens *TokenBucket    `protobuf:"bytes,2,opt,name=granted_tokens,json=grantedTokens,proto3" json:"granted_tokens,omitempty"`
	TrickleTimeMs int64           `protobuf:"varint,3,opt,name=trickle_time_ms,json=trickleTimeMs,proto3" json:"trickle_time_ms,omitempty"`
}

func (m *GrantedRawResourceTokenBucket) Reset()         { *m = GrantedRawResourceTokenBucket{} }
func (m *GrantedRawResourceTokenBucket) String() string { return proto.CompactTextString(m) }
func (*GrantedRawResourceTokenBucket) ProtoMessage()    {}
func (*GrantedRawResourceTokenBucket) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{14}
}
func (m *GrantedRawResourceTokenBucket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GrantedRawResourceTokenBucket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GrantedRawResourceTokenBucket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GrantedRawResourceTokenBucket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrantedRawResourceTokenBucket.Merge(m, src)
}
func (m *GrantedRawResourceTokenBucket) XXX_Size() int {
	return m.Size()
}
func (m *GrantedRawResourceTokenBucket) XXX_DiscardUnknown() {
	xxx_messageInfo_GrantedRawResourceTokenBucket.DiscardUnknown(m)
}

var xxx_messageInfo_GrantedRawResourceTokenBucket proto.InternalMessageInfo

func (m *GrantedRawResourceTokenBucket) GetType() RawResourceType {
	if m != nil {
		return m.Type
	}
	return RawResourceType_CPU
}

func (m *GrantedRawResourceTokenBucket) GetGrantedTokens() *TokenBucket {
	if m != nil {
		return m.GrantedTokens
	}
	return nil
}

func (m *GrantedRawResourceTokenBucket) GetTrickleTimeMs() int64 {
	if m != nil {
		return m.TrickleTimeMs
	}
	return 0
}

type Consumption struct {
	RRU               float64 `protobuf:"fixed64,1,opt,name=r_r_u,json=rRU,proto3" json:"r_r_u,omitempty"`
	WRU               float64 `protobuf:"fixed64,2,opt,name=w_r_u,json=wRU,proto3" json:"w_r_u,omitempty"`
	ReadBytes         float64 `protobuf:"fixed64,3,opt,name=read_bytes,json=readBytes,proto3" json:"read_bytes,omitempty"`
	WriteBytes        float64 `protobuf:"fixed64,4,opt,name=write_bytes,json=writeBytes,proto3" json:"write_bytes,omitempty"`
	TotalCpuTimeMs    float64 `protobuf:"fixed64,5,opt,name=total_cpu_time_ms,json=totalCpuTimeMs,proto3" json:"total_cpu_time_ms,omitempty"`
	SqlLayerCpuTimeMs float64 `protobuf:"fixed64,6,opt,name=sql_layer_cpu_time_ms,json=sqlLayerCpuTimeMs,proto3" json:"sql_layer_cpu_time_ms,omitempty"`
	KvReadRpcCount    float64 `protobuf:"fixed64,7,opt,name=kv_read_rpc_count,json=kvReadRpcCount,proto3" json:"kv_read_rpc_count,omitempty"`
	KvWriteRpcCount   float64 `protobuf:"fixed64,8,opt,name=kv_write_rpc_count,json=kvWriteRpcCount,proto3" json:"kv_write_rpc_count,omitempty"`
}

func (m *Consumption) Reset()         { *m = Consumption{} }
func (m *Consumption) String() string { return proto.CompactTextString(m) }
func (*Consumption) ProtoMessage()    {}
func (*Consumption) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{15}
}
func (m *Consumption) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Consumption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Consumption.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Consumption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Consumption.Merge(m, src)
}
func (m *Consumption) XXX_Size() int {
	return m.Size()
}
func (m *Consumption) XXX_DiscardUnknown() {
	xxx_messageInfo_Consumption.DiscardUnknown(m)
}

var xxx_messageInfo_Consumption proto.InternalMessageInfo

func (m *Consumption) GetRRU() float64 {
	if m != nil {
		return m.RRU
	}
	return 0
}

func (m *Consumption) GetWRU() float64 {
	if m != nil {
		return m.WRU
	}
	return 0
}

func (m *Consumption) GetReadBytes() float64 {
	if m != nil {
		return m.ReadBytes
	}
	return 0
}

func (m *Consumption) GetWriteBytes() float64 {
	if m != nil {
		return m.WriteBytes
	}
	return 0
}

func (m *Consumption) GetTotalCpuTimeMs() float64 {
	if m != nil {
		return m.TotalCpuTimeMs
	}
	return 0
}

func (m *Consumption) GetSqlLayerCpuTimeMs() float64 {
	if m != nil {
		return m.SqlLayerCpuTimeMs
	}
	return 0
}

func (m *Consumption) GetKvReadRpcCount() float64 {
	if m != nil {
		return m.KvReadRpcCount
	}
	return 0
}

func (m *Consumption) GetKvWriteRpcCount() float64 {
	if m != nil {
		return m.KvWriteRpcCount
	}
	return 0
}

type RequestUnitItem struct {
	Type  RequestUnitType `protobuf:"varint,1,opt,name=type,proto3,enum=resource_manager.RequestUnitType" json:"type,omitempty"`
	Value float64         `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *RequestUnitItem) Reset()         { *m = RequestUnitItem{} }
func (m *RequestUnitItem) String() string { return proto.CompactTextString(m) }
func (*RequestUnitItem) ProtoMessage()    {}
func (*RequestUnitItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{16}
}
func (m *RequestUnitItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RequestUnitItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RequestUnitItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RequestUnitItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RequestUnitItem.Merge(m, src)
}
func (m *RequestUnitItem) XXX_Size() int {
	return m.Size()
}
func (m *RequestUnitItem) XXX_DiscardUnknown() {
	xxx_messageInfo_RequestUnitItem.DiscardUnknown(m)
}

var xxx_messageInfo_RequestUnitItem proto.InternalMessageInfo

func (m *RequestUnitItem) GetType() RequestUnitType {
	if m != nil {
		return m.Type
	}
	return RequestUnitType_RU
}

func (m *RequestUnitItem) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

type RawResourceItem struct {
	Type  RawResourceType `protobuf:"varint,1,opt,name=type,proto3,enum=resource_manager.RawResourceType" json:"type,omitempty"`
	Value float64         `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *RawResourceItem) Reset()         { *m = RawResourceItem{} }
func (m *RawResourceItem) String() string { return proto.CompactTextString(m) }
func (*RawResourceItem) ProtoMessage()    {}
func (*RawResourceItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{17}
}
func (m *RawResourceItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RawResourceItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RawResourceItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RawResourceItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RawResourceItem.Merge(m, src)
}
func (m *RawResourceItem) XXX_Size() int {
	return m.Size()
}
func (m *RawResourceItem) XXX_DiscardUnknown() {
	xxx_messageInfo_RawResourceItem.DiscardUnknown(m)
}

var xxx_messageInfo_RawResourceItem proto.InternalMessageInfo

func (m *RawResourceItem) GetType() RawResourceType {
	if m != nil {
		return m.Type
	}
	return RawResourceType_CPU
}

func (m *RawResourceItem) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

// ResourceGroup the settings definitions.
type ResourceGroup struct {
	Name string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Mode GroupMode `protobuf:"varint,2,opt,name=mode,proto3,enum=resource_manager.GroupMode" json:"mode,omitempty"`
	// Used in RU mode, group settings with WRU/RRU etc resource abstract unit.
	RUSettings *GroupRequestUnitSettings `protobuf:"bytes,3,opt,name=r_u_settings,json=rUSettings,proto3" json:"r_u_settings,omitempty"`
	// Used in Raw mode, group settings with CPU/IO etc resource unit.
	RawResourceSettings *GroupRawResourceSettings `protobuf:"bytes,4,opt,name=raw_resource_settings,json=rawResourceSettings,proto3" json:"raw_resource_settings,omitempty"`
	// The task scheduling priority
	Priority uint32 `protobuf:"varint,5,opt,name=priority,proto3" json:"priority,omitempty"`
	// Runaway queries settings
	RunawaySettings *RunawaySettings `protobuf:"bytes,6,opt,name=runaway_settings,json=runawaySettings,proto3" json:"runaway_settings,omitempty"`
	// Background task control settings.
	BackgroundSettings *BackgroundSettings `protobuf:"bytes,7,opt,name=background_settings,json=backgroundSettings,proto3" json:"background_settings,omitempty"`
	// RU consumption statistics.
	RUStats *Consumption `protobuf:"bytes,8,opt,name=RUStats,proto3" json:"RUStats,omitempty"`
	// The keyspace ID that the resource group belongs to.
	// There're two cases for this field:
	//   - If the keyspace ID is not set, it means this may be a message from an older version.
	//     To maintain compatibility, we will treat it as a null keyspace ID, which is uint32.Max.
	//   - If the keyspace ID is set to a valid value, it will directly be used.
	KeyspaceId *KeyspaceIDValue `protobuf:"bytes,9,opt,name=keyspace_id,json=keyspaceId,proto3" json:"keyspace_id,omitempty"`
}

func (m *ResourceGroup) Reset()         { *m = ResourceGroup{} }
func (m *ResourceGroup) String() string { return proto.CompactTextString(m) }
func (*ResourceGroup) ProtoMessage()    {}
func (*ResourceGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{18}
}
func (m *ResourceGroup) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResourceGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResourceGroup.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResourceGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResourceGroup.Merge(m, src)
}
func (m *ResourceGroup) XXX_Size() int {
	return m.Size()
}
func (m *ResourceGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_ResourceGroup.DiscardUnknown(m)
}

var xxx_messageInfo_ResourceGroup proto.InternalMessageInfo

func (m *ResourceGroup) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ResourceGroup) GetMode() GroupMode {
	if m != nil {
		return m.Mode
	}
	return GroupMode_Unknown
}

func (m *ResourceGroup) GetRUSettings() *GroupRequestUnitSettings {
	if m != nil {
		return m.RUSettings
	}
	return nil
}

func (m *ResourceGroup) GetRawResourceSettings() *GroupRawResourceSettings {
	if m != nil {
		return m.RawResourceSettings
	}
	return nil
}

func (m *ResourceGroup) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *ResourceGroup) GetRunawaySettings() *RunawaySettings {
	if m != nil {
		return m.RunawaySettings
	}
	return nil
}

func (m *ResourceGroup) GetBackgroundSettings() *BackgroundSettings {
	if m != nil {
		return m.BackgroundSettings
	}
	return nil
}

func (m *ResourceGroup) GetRUStats() *Consumption {
	if m != nil {
		return m.RUStats
	}
	return nil
}

func (m *ResourceGroup) GetKeyspaceId() *KeyspaceIDValue {
	if m != nil {
		return m.KeyspaceId
	}
	return nil
}

type GroupRequestUnitSettings struct {
	RU *TokenBucket `protobuf:"bytes,1,opt,name=r_u,json=rU,proto3" json:"r_u,omitempty"`
}

func (m *GroupRequestUnitSettings) Reset()         { *m = GroupRequestUnitSettings{} }
func (m *GroupRequestUnitSettings) String() string { return proto.CompactTextString(m) }
func (*GroupRequestUnitSettings) ProtoMessage()    {}
func (*GroupRequestUnitSettings) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{19}
}
func (m *GroupRequestUnitSettings) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GroupRequestUnitSettings) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GroupRequestUnitSettings.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GroupRequestUnitSettings) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupRequestUnitSettings.Merge(m, src)
}
func (m *GroupRequestUnitSettings) XXX_Size() int {
	return m.Size()
}
func (m *GroupRequestUnitSettings) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupRequestUnitSettings.DiscardUnknown(m)
}

var xxx_messageInfo_GroupRequestUnitSettings proto.InternalMessageInfo

func (m *GroupRequestUnitSettings) GetRU() *TokenBucket {
	if m != nil {
		return m.RU
	}
	return nil
}

type GroupRawResourceSettings struct {
	Cpu     *TokenBucket `protobuf:"bytes,1,opt,name=cpu,proto3" json:"cpu,omitempty"`
	IoRead  *TokenBucket `protobuf:"bytes,2,opt,name=io_read,json=ioRead,proto3" json:"io_read,omitempty"`
	IoWrite *TokenBucket `protobuf:"bytes,3,opt,name=io_write,json=ioWrite,proto3" json:"io_write,omitempty"`
}

func (m *GroupRawResourceSettings) Reset()         { *m = GroupRawResourceSettings{} }
func (m *GroupRawResourceSettings) String() string { return proto.CompactTextString(m) }
func (*GroupRawResourceSettings) ProtoMessage()    {}
func (*GroupRawResourceSettings) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{20}
}
func (m *GroupRawResourceSettings) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GroupRawResourceSettings) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GroupRawResourceSettings.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GroupRawResourceSettings) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupRawResourceSettings.Merge(m, src)
}
func (m *GroupRawResourceSettings) XXX_Size() int {
	return m.Size()
}
func (m *GroupRawResourceSettings) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupRawResourceSettings.DiscardUnknown(m)
}

var xxx_messageInfo_GroupRawResourceSettings proto.InternalMessageInfo

func (m *GroupRawResourceSettings) GetCpu() *TokenBucket {
	if m != nil {
		return m.Cpu
	}
	return nil
}

func (m *GroupRawResourceSettings) GetIoRead() *TokenBucket {
	if m != nil {
		return m.IoRead
	}
	return nil
}

func (m *GroupRawResourceSettings) GetIoWrite() *TokenBucket {
	if m != nil {
		return m.IoWrite
	}
	return nil
}

type TokenBucket struct {
	Settings *TokenLimitSettings `protobuf:"bytes,1,opt,name=settings,proto3" json:"settings,omitempty"`
	// Once used to reconfigure, the tokens is delta tokens.
	Tokens float64 `protobuf:"fixed64,2,opt,name=tokens,proto3" json:"tokens,omitempty"`
}

func (m *TokenBucket) Reset()         { *m = TokenBucket{} }
func (m *TokenBucket) String() string { return proto.CompactTextString(m) }
func (*TokenBucket) ProtoMessage()    {}
func (*TokenBucket) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{21}
}
func (m *TokenBucket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TokenBucket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TokenBucket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TokenBucket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenBucket.Merge(m, src)
}
func (m *TokenBucket) XXX_Size() int {
	return m.Size()
}
func (m *TokenBucket) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenBucket.DiscardUnknown(m)
}

var xxx_messageInfo_TokenBucket proto.InternalMessageInfo

func (m *TokenBucket) GetSettings() *TokenLimitSettings {
	if m != nil {
		return m.Settings
	}
	return nil
}

func (m *TokenBucket) GetTokens() float64 {
	if m != nil {
		return m.Tokens
	}
	return 0
}

type TokenLimitSettings struct {
	FillRate   uint64  `protobuf:"varint,1,opt,name=fill_rate,json=fillRate,proto3" json:"fill_rate,omitempty"`
	BurstLimit int64   `protobuf:"varint,2,opt,name=burst_limit,json=burstLimit,proto3" json:"burst_limit,omitempty"`
	MaxTokens  float64 `protobuf:"fixed64,3,opt,name=max_tokens,json=maxTokens,proto3" json:"max_tokens,omitempty"`
}

func (m *TokenLimitSettings) Reset()         { *m = TokenLimitSettings{} }
func (m *TokenLimitSettings) String() string { return proto.CompactTextString(m) }
func (*TokenLimitSettings) ProtoMessage()    {}
func (*TokenLimitSettings) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{22}
}
func (m *TokenLimitSettings) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TokenLimitSettings) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TokenLimitSettings.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TokenLimitSettings) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenLimitSettings.Merge(m, src)
}
func (m *TokenLimitSettings) XXX_Size() int {
	return m.Size()
}
func (m *TokenLimitSettings) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenLimitSettings.DiscardUnknown(m)
}

var xxx_messageInfo_TokenLimitSettings proto.InternalMessageInfo

func (m *TokenLimitSettings) GetFillRate() uint64 {
	if m != nil {
		return m.FillRate
	}
	return 0
}

func (m *TokenLimitSettings) GetBurstLimit() int64 {
	if m != nil {
		return m.BurstLimit
	}
	return 0
}

func (m *TokenLimitSettings) GetMaxTokens() float64 {
	if m != nil {
		return m.MaxTokens
	}
	return 0
}

type Error struct {
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{23}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type RunawayRule struct {
	ExecElapsedTimeMs uint64 `protobuf:"varint,1,opt,name=exec_elapsed_time_ms,json=execElapsedTimeMs,proto3" json:"exec_elapsed_time_ms,omitempty"`
	ProcessedKeys     int64  `protobuf:"varint,2,opt,name=processed_keys,json=processedKeys,proto3" json:"processed_keys,omitempty"`
	RequestUnit       int64  `protobuf:"varint,3,opt,name=request_unit,json=requestUnit,proto3" json:"request_unit,omitempty"`
}

func (m *RunawayRule) Reset()         { *m = RunawayRule{} }
func (m *RunawayRule) String() string { return proto.CompactTextString(m) }
func (*RunawayRule) ProtoMessage()    {}
func (*RunawayRule) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{24}
}
func (m *RunawayRule) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RunawayRule) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RunawayRule.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RunawayRule) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunawayRule.Merge(m, src)
}
func (m *RunawayRule) XXX_Size() int {
	return m.Size()
}
func (m *RunawayRule) XXX_DiscardUnknown() {
	xxx_messageInfo_RunawayRule.DiscardUnknown(m)
}

var xxx_messageInfo_RunawayRule proto.InternalMessageInfo

func (m *RunawayRule) GetExecElapsedTimeMs() uint64 {
	if m != nil {
		return m.ExecElapsedTimeMs
	}
	return 0
}

func (m *RunawayRule) GetProcessedKeys() int64 {
	if m != nil {
		return m.ProcessedKeys
	}
	return 0
}

func (m *RunawayRule) GetRequestUnit() int64 {
	if m != nil {
		return m.RequestUnit
	}
	return 0
}

type RunawayWatch struct {
	// how long would the watch last
	LastingDurationMs int64            `protobuf:"varint,1,opt,name=lasting_duration_ms,json=lastingDurationMs,proto3" json:"lasting_duration_ms,omitempty"`
	Type              RunawayWatchType `protobuf:"varint,2,opt,name=type,proto3,enum=resource_manager.RunawayWatchType" json:"type,omitempty"`
}

func (m *RunawayWatch) Reset()         { *m = RunawayWatch{} }
func (m *RunawayWatch) String() string { return proto.CompactTextString(m) }
func (*RunawayWatch) ProtoMessage()    {}
func (*RunawayWatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{25}
}
func (m *RunawayWatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RunawayWatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RunawayWatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RunawayWatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunawayWatch.Merge(m, src)
}
func (m *RunawayWatch) XXX_Size() int {
	return m.Size()
}
func (m *RunawayWatch) XXX_DiscardUnknown() {
	xxx_messageInfo_RunawayWatch.DiscardUnknown(m)
}

var xxx_messageInfo_RunawayWatch proto.InternalMessageInfo

func (m *RunawayWatch) GetLastingDurationMs() int64 {
	if m != nil {
		return m.LastingDurationMs
	}
	return 0
}

func (m *RunawayWatch) GetType() RunawayWatchType {
	if m != nil {
		return m.Type
	}
	return RunawayWatchType_NoneWatch
}

type RunawaySettings struct {
	Rule   *RunawayRule  `protobuf:"bytes,1,opt,name=rule,proto3" json:"rule,omitempty"`
	Action RunawayAction `protobuf:"varint,2,opt,name=action,proto3,enum=resource_manager.RunawayAction" json:"action,omitempty"`
	Watch  *RunawayWatch `protobuf:"bytes,3,opt,name=watch,proto3" json:"watch,omitempty"`
	// When the runaway action is `SwitchGroup`,
	// this field will be used to indicate which group to switch.
	SwitchGroupName string `protobuf:"bytes,4,opt,name=switch_group_name,json=switchGroupName,proto3" json:"switch_group_name,omitempty"`
}

func (m *RunawaySettings) Reset()         { *m = RunawaySettings{} }
func (m *RunawaySettings) String() string { return proto.CompactTextString(m) }
func (*RunawaySettings) ProtoMessage()    {}
func (*RunawaySettings) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{26}
}
func (m *RunawaySettings) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RunawaySettings) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RunawaySettings.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RunawaySettings) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunawaySettings.Merge(m, src)
}
func (m *RunawaySettings) XXX_Size() int {
	return m.Size()
}
func (m *RunawaySettings) XXX_DiscardUnknown() {
	xxx_messageInfo_RunawaySettings.DiscardUnknown(m)
}

var xxx_messageInfo_RunawaySettings proto.InternalMessageInfo

func (m *RunawaySettings) GetRule() *RunawayRule {
	if m != nil {
		return m.Rule
	}
	return nil
}

func (m *RunawaySettings) GetAction() RunawayAction {
	if m != nil {
		return m.Action
	}
	return RunawayAction_NoneAction
}

func (m *RunawaySettings) GetWatch() *RunawayWatch {
	if m != nil {
		return m.Watch
	}
	return nil
}

func (m *RunawaySettings) GetSwitchGroupName() string {
	if m != nil {
		return m.SwitchGroupName
	}
	return ""
}

type BackgroundSettings struct {
	// background task types.
	JobTypes []string `protobuf:"bytes,1,rep,name=job_types,json=jobTypes,proto3" json:"job_types,omitempty"`
	// the percentage limit of total resource(cpu/io) that background tasks can use.
	UtilizationLimit uint64 `protobuf:"varint,2,opt,name=utilization_limit,json=utilizationLimit,proto3" json:"utilization_limit,omitempty"`
}

func (m *BackgroundSettings) Reset()         { *m = BackgroundSettings{} }
func (m *BackgroundSettings) String() string { return proto.CompactTextString(m) }
func (*BackgroundSettings) ProtoMessage()    {}
func (*BackgroundSettings) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{27}
}
func (m *BackgroundSettings) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BackgroundSettings) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BackgroundSettings.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BackgroundSettings) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackgroundSettings.Merge(m, src)
}
func (m *BackgroundSettings) XXX_Size() int {
	return m.Size()
}
func (m *BackgroundSettings) XXX_DiscardUnknown() {
	xxx_messageInfo_BackgroundSettings.DiscardUnknown(m)
}

var xxx_messageInfo_BackgroundSettings proto.InternalMessageInfo

func (m *BackgroundSettings) GetJobTypes() []string {
	if m != nil {
		return m.JobTypes
	}
	return nil
}

func (m *BackgroundSettings) GetUtilizationLimit() uint64 {
	if m != nil {
		return m.UtilizationLimit
	}
	return 0
}

type Participant struct {
	// name is the unique name of the resource manager participant.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// id is the unique id of the resource manager participant.
	Id uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// listen_urls is the serivce endpoint list in the url format.
	// listen_urls[0] is primary service endpoint.
	ListenUrls []string `protobuf:"bytes,3,rep,name=listen_urls,json=listenUrls,proto3" json:"listen_urls,omitempty"`
}

func (m *Participant) Reset()         { *m = Participant{} }
func (m *Participant) String() string { return proto.CompactTextString(m) }
func (*Participant) ProtoMessage()    {}
func (*Participant) Descriptor() ([]byte, []int) {
	return fileDescriptor_7048dd9233ee965d, []int{28}
}
func (m *Participant) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Participant) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Participant.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Participant) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Participant.Merge(m, src)
}
func (m *Participant) XXX_Size() int {
	return m.Size()
}
func (m *Participant) XXX_DiscardUnknown() {
	xxx_messageInfo_Participant.DiscardUnknown(m)
}

var xxx_messageInfo_Participant proto.InternalMessageInfo

func (m *Participant) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Participant) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Participant) GetListenUrls() []string {
	if m != nil {
		return m.ListenUrls
	}
	return nil
}

func init() {
	proto.RegisterEnum("resource_manager.RequestUnitType", RequestUnitType_name, RequestUnitType_value)
	proto.RegisterEnum("resource_manager.RawResourceType", RawResourceType_name, RawResourceType_value)
	proto.RegisterEnum("resource_manager.GroupMode", GroupMode_name, GroupMode_value)
	proto.RegisterEnum("resource_manager.RunawayAction", RunawayAction_name, RunawayAction_value)
	proto.RegisterEnum("resource_manager.RunawayWatchType", RunawayWatchType_name, RunawayWatchType_value)
	proto.RegisterType((*KeyspaceIDValue)(nil), "resource_manager.KeyspaceIDValue")
	proto.RegisterType((*ListResourceGroupsRequest)(nil), "resource_manager.ListResourceGroupsRequest")
	proto.RegisterType((*ListResourceGroupsResponse)(nil), "resource_manager.ListResourceGroupsResponse")
	proto.RegisterType((*GetResourceGroupRequest)(nil), "resource_manager.GetResourceGroupRequest")
	proto.RegisterType((*GetResourceGroupResponse)(nil), "resource_manager.GetResourceGroupResponse")
	proto.RegisterType((*DeleteResourceGroupRequest)(nil), "resource_manager.DeleteResourceGroupRequest")
	proto.RegisterType((*DeleteResourceGroupResponse)(nil), "resource_manager.DeleteResourceGroupResponse")
	proto.RegisterType((*PutResourceGroupRequest)(nil), "resource_manager.PutResourceGroupRequest")
	proto.RegisterType((*PutResourceGroupResponse)(nil), "resource_manager.PutResourceGroupResponse")
	proto.RegisterType((*TokenBucketsRequest)(nil), "resource_manager.TokenBucketsRequest")
	proto.RegisterType((*TokenBucketRequest)(nil), "resource_manager.TokenBucketRequest")
	proto.RegisterType((*TokenBucketRequest_RequestRU)(nil), "resource_manager.TokenBucketRequest.RequestRU")
	proto.RegisterType((*TokenBucketRequest_RequestRawResource)(nil), "resource_manager.TokenBucketRequest.RequestRawResource")
	proto.RegisterType((*TokenBucketsResponse)(nil), "resource_manager.TokenBucketsResponse")
	proto.RegisterType((*TokenBucketResponse)(nil), "resource_manager.TokenBucketResponse")
	proto.RegisterType((*GrantedRUTokenBucket)(nil), "resource_manager.GrantedRUTokenBucket")
	proto.RegisterType((*GrantedRawResourceTokenBucket)(nil), "resource_manager.GrantedRawResourceTokenBucket")
	proto.RegisterType((*Consumption)(nil), "resource_manager.Consumption")
	proto.RegisterType((*RequestUnitItem)(nil), "resource_manager.RequestUnitItem")
	proto.RegisterType((*RawResourceItem)(nil), "resource_manager.RawResourceItem")
	proto.RegisterType((*ResourceGroup)(nil), "resource_manager.ResourceGroup")
	proto.RegisterType((*GroupRequestUnitSettings)(nil), "resource_manager.GroupRequestUnitSettings")
	proto.RegisterType((*GroupRawResourceSettings)(nil), "resource_manager.GroupRawResourceSettings")
	proto.RegisterType((*TokenBucket)(nil), "resource_manager.TokenBucket")
	proto.RegisterType((*TokenLimitSettings)(nil), "resource_manager.TokenLimitSettings")
	proto.RegisterType((*Error)(nil), "resource_manager.Error")
	proto.RegisterType((*RunawayRule)(nil), "resource_manager.RunawayRule")
	proto.RegisterType((*RunawayWatch)(nil), "resource_manager.RunawayWatch")
	proto.RegisterType((*RunawaySettings)(nil), "resource_manager.RunawaySettings")
	proto.RegisterType((*BackgroundSettings)(nil), "resource_manager.BackgroundSettings")
	proto.RegisterType((*Participant)(nil), "resource_manager.Participant")
}

func init() { proto.RegisterFile("resource_manager.proto", fileDescriptor_7048dd9233ee965d) }

var fileDescriptor_7048dd9233ee965d = []byte{
	// 1957 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0x4f, 0x73, 0x1b, 0x49,
	0x15, 0xd7, 0xe8, 0x9f, 0xa5, 0x27, 0xcb, 0x92, 0xdb, 0xce, 0x46, 0xab, 0x54, 0x9c, 0x64, 0x60,
	0x83, 0xe3, 0xb0, 0xce, 0xae, 0x61, 0xd7, 0x54, 0x71, 0xd9, 0xd8, 0x0e, 0x59, 0x13, 0x3b, 0xeb,
	0x6a, 0x5b, 0xec, 0x89, 0x0c, 0xed, 0x51, 0x47, 0xe9, 0x68, 0x34, 0x23, 0xf7, 0xf4, 0x58, 0x11,
	0x27, 0xaa, 0xe0, 0x40, 0x71, 0xda, 0x23, 0x1f, 0x81, 0xa2, 0x38, 0xc0, 0x15, 0x8a, 0x2a, 0x0e,
	0x54, 0xc1, 0x71, 0x8f, 0x7b, 0x83, 0x4a, 0xbe, 0x08, 0xd5, 0x7f, 0x66, 0x34, 0xb2, 0x24, 0xdb,
	0x71, 0xa5, 0xd8, 0xdb, 0xcc, 0xeb, 0xf7, 0x7e, 0xef, 0xcd, 0xeb, 0xf7, 0x7e, 0xfd, 0xa6, 0xe1,
	0x3d, 0x4e, 0xc3, 0x20, 0xe2, 0x2e, 0x75, 0x7a, 0xc4, 0x27, 0x1d, 0xca, 0xd7, 0xfb, 0x3c, 0x10,
	0x01, 0xaa, 0x9f, 0x95, 0x37, 0x97, 0x3b, 0x41, 0x27, 0x50, 0x8b, 0x0f, 0xe4, 0x93, 0xd6, 0x6b,
	0xd6, 0x78, 0x14, 0x0a, 0xf5, 0xa8, 0x05, 0xf6, 0xf7, 0xa0, 0xf6, 0x84, 0x0e, 0xc3, 0x3e, 0x71,
	0xe9, 0xee, 0xce, 0xcf, 0x88, 0x17, 0x51, 0xb4, 0x0c, 0x85, 0x53, 0xf9, 0xd0, 0xb0, 0x6e, 0x5b,
	0xab, 0x55, 0xac, 0x5f, 0xec, 0x5f, 0x5b, 0xf0, 0xfe, 0x1e, 0x0b, 0x05, 0x36, 0x8e, 0x1e, 0xf3,
	0x20, 0xea, 0x87, 0x98, 0x9e, 0x44, 0x34, 0x14, 0xc8, 0x86, 0xea, 0x80, 0x89, 0x17, 0x0e, 0x8f,
	0x9c, 0x50, 0x10, 0x11, 0x2a, 0xdb, 0x12, 0xae, 0x48, 0x21, 0x8e, 0x0e, 0xa5, 0x08, 0x6d, 0x41,
	0xa5, 0x6b, 0x5c, 0x39, 0xac, 0xdd, 0xc8, 0xde, 0xb6, 0x56, 0x2b, 0x1b, 0x77, 0xd6, 0x27, 0xbe,
	0xe8, 0x4c, 0x3c, 0x18, 0x62, 0xab, 0xdd, 0xb6, 0xfd, 0x1b, 0x0b, 0x9a, 0xd3, 0xa2, 0x08, 0xfb,
	0x81, 0x1f, 0x52, 0xf4, 0x21, 0x14, 0x28, 0xe7, 0x01, 0x57, 0xee, 0x2b, 0x1b, 0xd7, 0x27, 0xc1,
	0x1f, 0xc9, 0x65, 0xac, 0xb5, 0xd0, 0x26, 0x14, 0x3b, 0x0a, 0xa0, 0x91, 0xbd, 0x9d, 0x5b, 0xad,
	0x6c, 0xdc, 0x9a, 0xd4, 0x1f, 0x73, 0x84, 0x8d, 0xba, 0xfd, 0x17, 0x0b, 0xae, 0x3f, 0xa6, 0xe3,
	0x51, 0xc4, 0xa9, 0x58, 0x87, 0xa5, 0x04, 0x45, 0xa9, 0x3b, 0x3e, 0xe9, 0xe9, 0x64, 0x96, 0xf1,
	0x22, 0x4f, 0x9b, 0x3c, 0x25, 0x3d, 0x3a, 0x99, 0xba, 0xec, 0x85, 0xa9, 0xcb, 0x5d, 0x25, 0x75,
	0xbf, 0xb2, 0xa0, 0x31, 0x19, 0xf3, 0xd5, 0x12, 0xf7, 0x09, 0x14, 0xd4, 0xa7, 0x99, 0x4d, 0xbc,
	0x30, 0x6f, 0x5a, 0xdb, 0xfe, 0xca, 0x82, 0xe6, 0x0e, 0xf5, 0xa8, 0xa0, 0xef, 0x24, 0x73, 0xef,
	0xa2, 0xa0, 0x7e, 0x01, 0x37, 0xa6, 0x46, 0x74, 0xb5, 0xbc, 0x20, 0xc8, 0x1f, 0x07, 0xed, 0xa1,
	0x0a, 0xa5, 0x8c, 0xd5, 0xb3, 0x7d, 0x00, 0xd7, 0x0f, 0xa2, 0xe9, 0xa5, 0x92, 0xa4, 0xd1, 0x7a,
	0xab, 0x34, 0xfe, 0x1c, 0x1a, 0x93, 0x88, 0xef, 0x2e, 0xe0, 0xbf, 0x5a, 0xb0, 0x74, 0x14, 0x74,
	0xa9, 0xbf, 0x15, 0xb9, 0x5d, 0x2a, 0x92, 0x1e, 0xff, 0x0c, 0x4a, 0x5c, 0x3f, 0xca, 0xf6, 0x96,
	0xfd, 0xf2, 0xdd, 0x49, 0xf4, 0x94, 0xa1, 0xb1, 0xc3, 0x89, 0x15, 0xda, 0x84, 0x86, 0x20, 0xbc,
	0x43, 0x85, 0x63, 0x44, 0x4e, 0x9f, 0x72, 0x16, 0xb4, 0x9d, 0x9e, 0xae, 0xfa, 0x3c, 0xbe, 0xa6,
	0xd7, 0x8d, 0xe9, 0x81, 0x5a, 0xdd, 0x0f, 0xd1, 0x2a, 0xd4, 0x5d, 0x8f, 0x51, 0x5f, 0x38, 0x91,
	0xcf, 0x4e, 0xa2, 0xa4, 0x09, 0xf2, 0x78, 0x41, 0xcb, 0x5b, 0x4a, 0xbc, 0xdb, 0xb6, 0xff, 0x54,
	0x00, 0x34, 0x19, 0xc3, 0x5b, 0x97, 0xd6, 0x13, 0x28, 0xf1, 0xc8, 0x61, 0x82, 0x9a, 0xc8, 0x2a,
	0x1b, 0xeb, 0x97, 0xf9, 0xd6, 0xf5, 0xf8, 0x9b, 0x5b, 0x9f, 0x67, 0xf0, 0x1c, 0x8f, 0x76, 0x25,
	0x00, 0xea, 0x00, 0xe2, 0x64, 0xe0, 0x24, 0xf6, 0x1a, 0x56, 0x37, 0xf1, 0xe6, 0x5b, 0xc1, 0x92,
	0x41, 0xbc, 0xeb, 0x9f, 0x67, 0x70, 0x9d, 0x8f, 0x5e, 0xb5, 0xa3, 0x63, 0x58, 0x71, 0x03, 0x3f,
	0x8c, 0x7a, 0x7d, 0xc1, 0x02, 0xdf, 0x09, 0x99, 0xef, 0x52, 0xc7, 0x23, 0x61, 0x92, 0xef, 0x46,
	0x5e, 0x39, 0xbd, 0x39, 0xe9, 0x74, 0x7b, 0x64, 0x87, 0x6f, 0xa4, 0x40, 0x0e, 0x25, 0xc6, 0x1e,
	0x09, 0x93, 0x4c, 0x7e, 0x07, 0xaa, 0x2c, 0x74, 0x8e, 0x89, 0xdb, 0x95, 0x79, 0xf4, 0xdb, 0x8d,
	0x82, 0xa2, 0xab, 0x79, 0x16, 0x6e, 0x25, 0x32, 0x74, 0x13, 0x80, 0x85, 0x8e, 0x60, 0xcf, 0x3d,
	0x12, 0xbe, 0x68, 0x14, 0x95, 0x46, 0x99, 0x85, 0x47, 0x5a, 0x70, 0xb6, 0x71, 0xe7, 0xae, 0xd0,
	0xb8, 0xcd, 0xa7, 0x50, 0x4e, 0x92, 0x8d, 0x1e, 0x42, 0x25, 0xae, 0x28, 0xee, 0x44, 0xa6, 0x3a,
	0xef, 0x4c, 0x6b, 0x27, 0xa5, 0xd4, 0xf2, 0x99, 0x90, 0x19, 0xc3, 0x65, 0x1e, 0x43, 0x34, 0x19,
	0xa0, 0xc9, 0x2c, 0xa3, 0x43, 0x58, 0x4e, 0x80, 0x53, 0x5b, 0x78, 0x8e, 0x87, 0xf1, 0x3d, 0xc1,
	0x88, 0x4f, 0x80, 0x6e, 0x95, 0x61, 0xce, 0x48, 0xed, 0xdf, 0x59, 0xb0, 0x3c, 0xde, 0x6b, 0x57,
	0xeb, 0xe3, 0x6d, 0x28, 0x73, 0x63, 0x1a, 0x1f, 0x66, 0x1f, 0x5c, 0x50, 0x59, 0x5a, 0x1b, 0x8f,
	0xec, 0xec, 0x7f, 0x65, 0xc7, 0x1a, 0x3f, 0x89, 0xe5, 0x6d, 0x9b, 0xe7, 0x10, 0x50, 0x87, 0x13,
	0x5f, 0xd0, 0xb6, 0xdc, 0x0d, 0x47, 0x48, 0xc8, 0x38, 0xaa, 0xbb, 0x93, 0x51, 0x3d, 0xd6, 0xba,
	0xb8, 0x95, 0xf6, 0x5d, 0xeb, 0x8c, 0x49, 0x65, 0x13, 0x5d, 0x4f, 0x40, 0x63, 0x04, 0x83, 0x9c,
	0x53, 0xc8, 0x0f, 0x66, 0x23, 0x8f, 0x72, 0x9f, 0x76, 0x71, 0x2d, 0x76, 0x91, 0x5e, 0x9b, 0x38,
	0x6b, 0xf3, 0x57, 0x39, 0x55, 0xfe, 0x66, 0xc1, 0xf2, 0xb4, 0xcf, 0x42, 0x9f, 0x40, 0x5e, 0x0c,
	0xfb, 0x3a, 0x77, 0x0b, 0x17, 0x54, 0xe8, 0xd1, 0xb0, 0x4f, 0xb1, 0x52, 0x47, 0x3b, 0xb0, 0x10,
	0x7f, 0x7c, 0x92, 0xcd, 0x19, 0x8d, 0x9c, 0xfe, 0xc2, 0xaa, 0x31, 0x32, 0x5f, 0x76, 0x17, 0x6a,
	0x82, 0x33, 0xb7, 0xeb, 0x51, 0x47, 0xb0, 0x1e, 0x75, 0x0c, 0x09, 0xe5, 0x70, 0xd5, 0x88, 0x8f,
	0x58, 0x8f, 0xee, 0x87, 0xf6, 0x3f, 0x2d, 0xb8, 0x79, 0x6e, 0xea, 0x2e, 0xf1, 0x19, 0x29, 0xbb,
	0x6f, 0xeb, 0x33, 0xfe, 0x98, 0x85, 0x4a, 0x8a, 0xd6, 0x10, 0x82, 0x02, 0x37, 0xf4, 0x60, 0xad,
	0x5a, 0x38, 0xc7, 0x71, 0x4b, 0xca, 0x06, 0x4a, 0x96, 0xd5, 0xb2, 0x01, 0x6e, 0x49, 0xf2, 0xe2,
	0x94, 0xb4, 0x9d, 0xe3, 0xa1, 0xa0, 0x1a, 0xda, 0x92, 0x5d, 0x42, 0xda, 0x5b, 0x52, 0x80, 0x6e,
	0x41, 0x65, 0xc0, 0x99, 0xa0, 0x66, 0x3d, 0xaf, 0xd6, 0x41, 0x89, 0xb4, 0xc2, 0x3d, 0x58, 0x14,
	0x81, 0x20, 0x9e, 0xe3, 0xf6, 0xa3, 0x24, 0xc2, 0x82, 0x52, 0x5b, 0x50, 0x0b, 0xdb, 0xfd, 0x48,
	0x87, 0x88, 0x3e, 0x82, 0x6b, 0xe1, 0x89, 0xe7, 0x78, 0x64, 0x48, 0xf9, 0x98, 0x7a, 0x51, 0xa9,
	0x2f, 0x86, 0x27, 0xde, 0x9e, 0x5c, 0x1b, 0x59, 0xdc, 0x83, 0xc5, 0xee, 0xa9, 0xa3, 0xe2, 0xe3,
	0x7d, 0xd7, 0x71, 0x83, 0xc8, 0x17, 0x8a, 0x40, 0x2d, 0xbc, 0xd0, 0x3d, 0xc5, 0x94, 0xb4, 0x71,
	0xdf, 0xdd, 0x96, 0x52, 0x74, 0x1f, 0x50, 0xf7, 0xd4, 0xd1, 0xb1, 0x8e, 0x74, 0x4b, 0x4a, 0xb7,
	0xd6, 0x3d, 0xfd, 0x52, 0x2e, 0xc4, 0xca, 0xf6, 0x33, 0xa8, 0x9d, 0x21, 0xc7, 0xab, 0xd6, 0x6a,
	0xf2, 0xfb, 0xa0, 0x53, 0x6a, 0x7e, 0x1f, 0x24, 0xfe, 0x38, 0x35, 0x5e, 0xb5, 0x88, 0xa6, 0xe3,
	0xff, 0x23, 0x0f, 0xd5, 0xb1, 0x89, 0x48, 0x8e, 0x36, 0x29, 0x9a, 0x52, 0xcf, 0xe8, 0x01, 0xe4,
	0x7b, 0x41, 0x5b, 0x9b, 0x2e, 0x6c, 0xdc, 0x98, 0xc6, 0x18, 0x41, 0xd4, 0xdf, 0x0f, 0xda, 0x14,
	0x2b, 0x45, 0xb4, 0x07, 0xf3, 0x92, 0xc2, 0x42, 0x2a, 0x04, 0xf3, 0x3b, 0xf1, 0xa1, 0xbd, 0x36,
	0xc3, 0x30, 0x95, 0x90, 0x43, 0x63, 0x81, 0x81, 0xb7, 0xe2, 0x67, 0xf4, 0x0c, 0xae, 0x8d, 0x0d,
	0x02, 0x09, 0x6c, 0xfe, 0x7c, 0xd8, 0x51, 0x1e, 0x12, 0xd8, 0x25, 0x3e, 0x29, 0x44, 0x4d, 0x28,
	0xf5, 0x39, 0x0b, 0x38, 0x13, 0x43, 0x55, 0x70, 0x55, 0x9c, 0xbc, 0xa3, 0x3d, 0xa8, 0xf3, 0xc8,
	0x27, 0x03, 0x32, 0x1c, 0xb9, 0x2d, 0xce, 0xe2, 0x36, 0xac, 0x35, 0x13, 0x6f, 0x35, 0x3e, 0x2e,
	0x40, 0x2d, 0x58, 0x1a, 0x8d, 0x00, 0x23, 0x40, 0x7d, 0x92, 0x4f, 0x19, 0x0b, 0x47, 0xb3, 0x41,
	0x82, 0x89, 0x8e, 0x27, 0x64, 0x68, 0x13, 0xe6, 0x70, 0x4b, 0xfd, 0xf2, 0xa8, 0x3a, 0xbd, 0x70,
	0x52, 0x89, 0xb5, 0xcf, 0x92, 0x76, 0xf9, 0x2a, 0xa4, 0xfd, 0x53, 0x68, 0xcc, 0xda, 0x45, 0xb4,
	0x0e, 0xb9, 0x98, 0x39, 0x2e, 0xa4, 0xab, 0x2c, 0x6f, 0xd9, 0x7f, 0xb7, 0x62, 0xb0, 0x29, 0xdb,
	0xf4, 0x00, 0x72, 0x6e, 0xff, 0x92, 0x60, 0x52, 0x13, 0x7d, 0x0a, 0x73, 0x2c, 0x50, 0x4d, 0x7f,
	0x39, 0xc2, 0x2c, 0xb2, 0x40, 0x32, 0x01, 0xfa, 0x11, 0x94, 0x58, 0xa0, 0x19, 0xc0, 0x54, 0xee,
	0x05, 0x86, 0x73, 0x2c, 0x50, 0xb4, 0x60, 0x77, 0xa0, 0x92, 0xe6, 0xfb, 0xcf, 0xa0, 0x94, 0xec,
	0xb1, 0x35, 0x6b, 0x8f, 0x95, 0xc1, 0x1e, 0xeb, 0xa5, 0x8a, 0x3f, 0xb1, 0x42, 0xef, 0x41, 0x31,
	0x45, 0xf9, 0x16, 0x36, 0x6f, 0xf6, 0x89, 0x19, 0xd7, 0xc7, 0xec, 0xd0, 0x0d, 0x28, 0x3f, 0x67,
	0x9e, 0xe7, 0x70, 0x22, 0x74, 0x03, 0xe7, 0x71, 0x49, 0x0a, 0x30, 0x11, 0x54, 0x12, 0xf0, 0x71,
	0xc4, 0x43, 0xe1, 0x78, 0xd2, 0x46, 0xe1, 0xe5, 0x30, 0x28, 0x91, 0x42, 0x91, 0x04, 0xde, 0x23,
	0xaf, 0x46, 0xd3, 0x81, 0x22, 0xf0, 0x1e, 0x79, 0xa5, 0xcf, 0x0f, 0xfb, 0x0e, 0x14, 0xd4, 0xec,
	0x84, 0x1a, 0x30, 0xd7, 0xa3, 0x61, 0x48, 0x3a, 0x31, 0x49, 0xc4, 0xaf, 0xf6, 0x6f, 0x2d, 0xa8,
	0x98, 0x1e, 0xc0, 0x91, 0x27, 0x79, 0x63, 0x99, 0xbe, 0xa2, 0xae, 0x43, 0x3d, 0xd2, 0x0f, 0xe5,
	0xe9, 0x65, 0x68, 0x5a, 0x87, 0xb6, 0x28, 0xd7, 0x1e, 0xe9, 0x25, 0x43, 0xd3, 0x1f, 0xc0, 0x42,
	0x9f, 0x07, 0x2e, 0x0d, 0xa5, 0xb6, 0xac, 0x31, 0x13, 0x66, 0x35, 0x91, 0xca, 0x4a, 0x44, 0x77,
	0x60, 0x3e, 0x1e, 0x2f, 0x23, 0x9f, 0x09, 0x73, 0x8e, 0xc5, 0xb3, 0xac, 0xac, 0x40, 0xfb, 0x14,
	0xe6, 0x4d, 0x24, 0x5f, 0x12, 0xe1, 0xbe, 0x90, 0xc3, 0x98, 0x9c, 0xe8, 0x99, 0xdf, 0x71, 0xda,
	0x11, 0x27, 0x6a, 0xd0, 0x37, 0x91, 0xe4, 0xf0, 0xa2, 0x59, 0xda, 0x31, 0x2b, 0xfb, 0x21, 0xfa,
	0xd4, 0xb0, 0xac, 0xa6, 0x3c, 0x7b, 0x66, 0xaf, 0x2b, 0xf4, 0x11, 0xcd, 0xda, 0xff, 0xb1, 0xa0,
	0x76, 0x86, 0x06, 0xd0, 0xc7, 0x90, 0xe7, 0x91, 0x47, 0x67, 0x57, 0x6e, 0x2a, 0x67, 0x58, 0xa9,
	0xa2, 0x4d, 0x28, 0x12, 0x57, 0x86, 0x62, 0x02, 0xb8, 0x35, 0xd3, 0xe8, 0xa1, 0x52, 0xc3, 0x46,
	0x1d, 0xfd, 0x10, 0x0a, 0x03, 0x19, 0x92, 0x29, 0xdc, 0x95, 0xf3, 0x03, 0xc7, 0x5a, 0x19, 0xad,
	0xc1, 0x62, 0x38, 0x60, 0xc2, 0x7d, 0x91, 0x1e, 0x54, 0xf3, 0x6a, 0x73, 0x6b, 0x7a, 0x21, 0x19,
	0x53, 0xed, 0x67, 0x80, 0x26, 0x69, 0x49, 0x96, 0xde, 0xcb, 0xe0, 0xd8, 0x91, 0x39, 0xd0, 0xbf,
	0xb9, 0x65, 0x5c, 0x7a, 0x19, 0x1c, 0xcb, 0xcc, 0x84, 0xe8, 0x3e, 0x2c, 0x46, 0x82, 0x79, 0xec,
	0x97, 0x3a, 0xef, 0xa3, 0x02, 0xcc, 0xe3, 0x7a, 0x6a, 0x41, 0x95, 0xa1, 0x8d, 0xa1, 0x72, 0x40,
	0xb8, 0x60, 0x2e, 0xeb, 0x13, 0x5f, 0x4c, 0x3d, 0x8f, 0x16, 0x20, 0x6b, 0x2e, 0x2e, 0xf2, 0x38,
	0xcb, 0xda, 0xb2, 0xb4, 0x3d, 0x16, 0x0a, 0xea, 0x3b, 0x11, 0xf7, 0xf4, 0x60, 0x5b, 0xc6, 0xa0,
	0x45, 0x2d, 0xee, 0x85, 0x6b, 0xef, 0x8f, 0x1d, 0xd3, 0x32, 0x28, 0x54, 0x84, 0x2c, 0x6e, 0xd5,
	0x33, 0x6b, 0x3f, 0x1e, 0x3b, 0x61, 0xd5, 0xd2, 0x1c, 0xe4, 0xb6, 0x0f, 0x5a, 0xf5, 0x0c, 0x5a,
	0x00, 0xd8, 0xfd, 0x42, 0x52, 0xc2, 0x4f, 0xbc, 0x60, 0x50, 0xb7, 0x50, 0x0d, 0x2a, 0xbb, 0x5f,
	0xa8, 0x4e, 0x57, 0x82, 0xec, 0xda, 0xc7, 0x50, 0x4e, 0x8e, 0x3e, 0x54, 0x81, 0xb9, 0x96, 0xdf,
	0xf5, 0x83, 0x81, 0x5f, 0xcf, 0x20, 0x80, 0x22, 0x6e, 0x49, 0x71, 0xdd, 0x92, 0x0b, 0x98, 0x0c,
	0xd4, 0x4b, 0x76, 0xed, 0x08, 0xaa, 0x63, 0x3b, 0x27, 0x9d, 0x3c, 0x0d, 0x7c, 0xaa, 0xdf, 0xb4,
	0xe5, 0x0e, 0x1f, 0xe2, 0xc8, 0xaf, 0x5b, 0x68, 0x1e, 0x4a, 0xdb, 0x41, 0xe0, 0xed, 0x48, 0xcc,
	0x2c, 0x2a, 0x41, 0xfe, 0x09, 0xf3, 0xbc, 0x7a, 0x4e, 0x06, 0x72, 0x38, 0xda, 0x96, 0x7a, 0x7e,
	0x6d, 0x1b, 0xea, 0x67, 0x0b, 0x12, 0x55, 0xa1, 0x2c, 0x81, 0x95, 0xa0, 0x9e, 0x41, 0x65, 0x28,
	0x3c, 0x7a, 0x45, 0x5c, 0xa1, 0x03, 0x3a, 0x64, 0x3d, 0xe6, 0x11, 0xae, 0x51, 0x0f, 0x3c, 0xe2,
	0xd7, 0x73, 0x1b, 0x7f, 0x2e, 0xc8, 0x34, 0xe9, 0x44, 0xec, 0xeb, 0x6a, 0x41, 0x27, 0x80, 0x26,
	0x2f, 0x0e, 0xd1, 0xfd, 0xc9, 0xb2, 0x9a, 0x79, 0xc9, 0xd9, 0xfc, 0xfe, 0xe5, 0x94, 0xf5, 0x5f,
	0x93, 0x9d, 0x41, 0x5d, 0xa8, 0x9f, 0xbd, 0x70, 0x43, 0xf7, 0xa6, 0x9c, 0xf1, 0xd3, 0x2f, 0x12,
	0x9b, 0x6b, 0x97, 0x51, 0x4d, 0x3b, 0x7b, 0xd8, 0x6e, 0x5f, 0xe8, 0x6c, 0xc6, 0x55, 0xd4, 0x34,
	0x67, 0xb3, 0xee, 0x98, 0xec, 0x0c, 0xf2, 0x61, 0x69, 0x3f, 0x68, 0xb3, 0xe7, 0xc3, 0xff, 0x93,
	0x3f, 0x01, 0x4b, 0x53, 0x6e, 0xe9, 0xd0, 0x94, 0x0d, 0x99, 0x7d, 0xbd, 0xd8, 0xfc, 0xf0, 0x92,
	0xda, 0x89, 0xd7, 0x97, 0xb0, 0xf4, 0xd0, 0x3d, 0x89, 0x18, 0x4f, 0xff, 0xfb, 0x84, 0xe8, 0xfc,
	0x1f, 0xeb, 0xa4, 0x5a, 0xee, 0x5e, 0xa4, 0x16, 0xfb, 0x59, 0xb5, 0x3e, 0xb2, 0xb6, 0x6e, 0x7f,
	0xf3, 0x87, 0x92, 0xf5, 0xef, 0xd7, 0x2b, 0xd6, 0xd7, 0xaf, 0x57, 0xac, 0xff, 0xbe, 0x5e, 0xb1,
	0xbe, 0x7a, 0xb3, 0x92, 0xf9, 0xfd, 0x9b, 0x95, 0xcc, 0xd7, 0x6f, 0x56, 0x32, 0xdf, 0xbc, 0x59,
	0xc9, 0x1c, 0x17, 0xd5, 0x85, 0xfd, 0x0f, 0xfe, 0x17, 0x00, 0x00, 0xff, 0xff, 0x90, 0xfb, 0xcd,
	0x87, 0x03, 0x18, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ResourceManagerClient is the client API for ResourceManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ResourceManagerClient interface {
	ListResourceGroups(ctx context.Context, in *ListResourceGroupsRequest, opts ...grpc.CallOption) (*ListResourceGroupsResponse, error)
	GetResourceGroup(ctx context.Context, in *GetResourceGroupRequest, opts ...grpc.CallOption) (*GetResourceGroupResponse, error)
	AddResourceGroup(ctx context.Context, in *PutResourceGroupRequest, opts ...grpc.CallOption) (*PutResourceGroupResponse, error)
	ModifyResourceGroup(ctx context.Context, in *PutResourceGroupRequest, opts ...grpc.CallOption) (*PutResourceGroupResponse, error)
	DeleteResourceGroup(ctx context.Context, in *DeleteResourceGroupRequest, opts ...grpc.CallOption) (*DeleteResourceGroupResponse, error)
	AcquireTokenBuckets(ctx context.Context, opts ...grpc.CallOption) (ResourceManager_AcquireTokenBucketsClient, error)
}

type resourceManagerClient struct {
	cc *grpc.ClientConn
}

func NewResourceManagerClient(cc *grpc.ClientConn) ResourceManagerClient {
	return &resourceManagerClient{cc}
}

func (c *resourceManagerClient) ListResourceGroups(ctx context.Context, in *ListResourceGroupsRequest, opts ...grpc.CallOption) (*ListResourceGroupsResponse, error) {
	out := new(ListResourceGroupsResponse)
	err := c.cc.Invoke(ctx, "/resource_manager.ResourceManager/ListResourceGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerClient) GetResourceGroup(ctx context.Context, in *GetResourceGroupRequest, opts ...grpc.CallOption) (*GetResourceGroupResponse, error) {
	out := new(GetResourceGroupResponse)
	err := c.cc.Invoke(ctx, "/resource_manager.ResourceManager/GetResourceGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerClient) AddResourceGroup(ctx context.Context, in *PutResourceGroupRequest, opts ...grpc.CallOption) (*PutResourceGroupResponse, error) {
	out := new(PutResourceGroupResponse)
	err := c.cc.Invoke(ctx, "/resource_manager.ResourceManager/AddResourceGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerClient) ModifyResourceGroup(ctx context.Context, in *PutResourceGroupRequest, opts ...grpc.CallOption) (*PutResourceGroupResponse, error) {
	out := new(PutResourceGroupResponse)
	err := c.cc.Invoke(ctx, "/resource_manager.ResourceManager/ModifyResourceGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerClient) DeleteResourceGroup(ctx context.Context, in *DeleteResourceGroupRequest, opts ...grpc.CallOption) (*DeleteResourceGroupResponse, error) {
	out := new(DeleteResourceGroupResponse)
	err := c.cc.Invoke(ctx, "/resource_manager.ResourceManager/DeleteResourceGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resourceManagerClient) AcquireTokenBuckets(ctx context.Context, opts ...grpc.CallOption) (ResourceManager_AcquireTokenBucketsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ResourceManager_serviceDesc.Streams[0], "/resource_manager.ResourceManager/AcquireTokenBuckets", opts...)
	if err != nil {
		return nil, err
	}
	x := &resourceManagerAcquireTokenBucketsClient{stream}
	return x, nil
}

type ResourceManager_AcquireTokenBucketsClient interface {
	Send(*TokenBucketsRequest) error
	Recv() (*TokenBucketsResponse, error)
	grpc.ClientStream
}

type resourceManagerAcquireTokenBucketsClient struct {
	grpc.ClientStream
}

func (x *resourceManagerAcquireTokenBucketsClient) Send(m *TokenBucketsRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *resourceManagerAcquireTokenBucketsClient) Recv() (*TokenBucketsResponse, error) {
	m := new(TokenBucketsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ResourceManagerServer is the server API for ResourceManager service.
type ResourceManagerServer interface {
	ListResourceGroups(context.Context, *ListResourceGroupsRequest) (*ListResourceGroupsResponse, error)
	GetResourceGroup(context.Context, *GetResourceGroupRequest) (*GetResourceGroupResponse, error)
	AddResourceGroup(context.Context, *PutResourceGroupRequest) (*PutResourceGroupResponse, error)
	ModifyResourceGroup(context.Context, *PutResourceGroupRequest) (*PutResourceGroupResponse, error)
	DeleteResourceGroup(context.Context, *DeleteResourceGroupRequest) (*DeleteResourceGroupResponse, error)
	AcquireTokenBuckets(ResourceManager_AcquireTokenBucketsServer) error
}

// UnimplementedResourceManagerServer can be embedded to have forward compatible implementations.
type UnimplementedResourceManagerServer struct {
}

func (*UnimplementedResourceManagerServer) ListResourceGroups(ctx context.Context, req *ListResourceGroupsRequest) (*ListResourceGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListResourceGroups not implemented")
}
func (*UnimplementedResourceManagerServer) GetResourceGroup(ctx context.Context, req *GetResourceGroupRequest) (*GetResourceGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResourceGroup not implemented")
}
func (*UnimplementedResourceManagerServer) AddResourceGroup(ctx context.Context, req *PutResourceGroupRequest) (*PutResourceGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddResourceGroup not implemented")
}
func (*UnimplementedResourceManagerServer) ModifyResourceGroup(ctx context.Context, req *PutResourceGroupRequest) (*PutResourceGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyResourceGroup not implemented")
}
func (*UnimplementedResourceManagerServer) DeleteResourceGroup(ctx context.Context, req *DeleteResourceGroupRequest) (*DeleteResourceGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteResourceGroup not implemented")
}
func (*UnimplementedResourceManagerServer) AcquireTokenBuckets(srv ResourceManager_AcquireTokenBucketsServer) error {
	return status.Errorf(codes.Unimplemented, "method AcquireTokenBuckets not implemented")
}

func RegisterResourceManagerServer(s *grpc.Server, srv ResourceManagerServer) {
	s.RegisterService(&_ResourceManager_serviceDesc, srv)
}

func _ResourceManager_ListResourceGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListResourceGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerServer).ListResourceGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/resource_manager.ResourceManager/ListResourceGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerServer).ListResourceGroups(ctx, req.(*ListResourceGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManager_GetResourceGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerServer).GetResourceGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/resource_manager.ResourceManager/GetResourceGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerServer).GetResourceGroup(ctx, req.(*GetResourceGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManager_AddResourceGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutResourceGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerServer).AddResourceGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/resource_manager.ResourceManager/AddResourceGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerServer).AddResourceGroup(ctx, req.(*PutResourceGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManager_ModifyResourceGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutResourceGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerServer).ModifyResourceGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/resource_manager.ResourceManager/ModifyResourceGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerServer).ModifyResourceGroup(ctx, req.(*PutResourceGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManager_DeleteResourceGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteResourceGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResourceManagerServer).DeleteResourceGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/resource_manager.ResourceManager/DeleteResourceGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResourceManagerServer).DeleteResourceGroup(ctx, req.(*DeleteResourceGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ResourceManager_AcquireTokenBuckets_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ResourceManagerServer).AcquireTokenBuckets(&resourceManagerAcquireTokenBucketsServer{stream})
}

type ResourceManager_AcquireTokenBucketsServer interface {
	Send(*TokenBucketsResponse) error
	Recv() (*TokenBucketsRequest, error)
	grpc.ServerStream
}

type resourceManagerAcquireTokenBucketsServer struct {
	grpc.ServerStream
}

func (x *resourceManagerAcquireTokenBucketsServer) Send(m *TokenBucketsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *resourceManagerAcquireTokenBucketsServer) Recv() (*TokenBucketsRequest, error) {
	m := new(TokenBucketsRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _ResourceManager_serviceDesc = grpc.ServiceDesc{
	ServiceName: "resource_manager.ResourceManager",
	HandlerType: (*ResourceManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListResourceGroups",
			Handler:    _ResourceManager_ListResourceGroups_Handler,
		},
		{
			MethodName: "GetResourceGroup",
			Handler:    _ResourceManager_GetResourceGroup_Handler,
		},
		{
			MethodName: "AddResourceGroup",
			Handler:    _ResourceManager_AddResourceGroup_Handler,
		},
		{
			MethodName: "ModifyResourceGroup",
			Handler:    _ResourceManager_ModifyResourceGroup_Handler,
		},
		{
			MethodName: "DeleteResourceGroup",
			Handler:    _ResourceManager_DeleteResourceGroup_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "AcquireTokenBuckets",
			Handler:       _ResourceManager_AcquireTokenBuckets_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "resource_manager.proto",
}

func (m *KeyspaceIDValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeyspaceIDValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KeyspaceIDValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Value != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Value))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ListResourceGroupsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListResourceGroupsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListResourceGroupsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.KeyspaceId != nil {
		{
			size, err := m.KeyspaceId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.WithRuStats {
		i--
		if m.WithRuStats {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ListResourceGroupsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ListResourceGroupsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ListResourceGroupsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Groups) > 0 {
		for iNdEx := len(m.Groups) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Groups[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintResourceManager(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetResourceGroupRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetResourceGroupRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetResourceGroupRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.KeyspaceId != nil {
		{
			size, err := m.KeyspaceId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.WithRuStats {
		i--
		if m.WithRuStats {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if len(m.ResourceGroupName) > 0 {
		i -= len(m.ResourceGroupName)
		copy(dAtA[i:], m.ResourceGroupName)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.ResourceGroupName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetResourceGroupResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetResourceGroupResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetResourceGroupResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Group != nil {
		{
			size, err := m.Group.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeleteResourceGroupRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteResourceGroupRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteResourceGroupRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.KeyspaceId != nil {
		{
			size, err := m.KeyspaceId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.ResourceGroupName) > 0 {
		i -= len(m.ResourceGroupName)
		copy(dAtA[i:], m.ResourceGroupName)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.ResourceGroupName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeleteResourceGroupResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteResourceGroupResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteResourceGroupResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x12
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PutResourceGroupRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutResourceGroupRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PutResourceGroupRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Group != nil {
		{
			size, err := m.Group.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PutResourceGroupResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PutResourceGroupResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PutResourceGroupResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x12
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TokenBucketsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenBucketsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucketsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ClientUniqueId != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.ClientUniqueId))
		i--
		dAtA[i] = 0x18
	}
	if m.TargetRequestPeriodMs != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.TargetRequestPeriodMs))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Requests) > 0 {
		for iNdEx := len(m.Requests) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Requests[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintResourceManager(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TokenBucketRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenBucketRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucketRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.KeyspaceId != nil {
		{
			size, err := m.KeyspaceId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.IsTiflash {
		i--
		if m.IsTiflash {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	if m.IsBackground {
		i--
		if m.IsBackground {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if m.ConsumptionSinceLastRequest != nil {
		{
			size, err := m.ConsumptionSinceLastRequest.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Request != nil {
		{
			size := m.Request.Size()
			i -= size
			if _, err := m.Request.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	if len(m.ResourceGroupName) > 0 {
		i -= len(m.ResourceGroupName)
		copy(dAtA[i:], m.ResourceGroupName)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.ResourceGroupName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TokenBucketRequest_RuItems) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucketRequest_RuItems) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RuItems != nil {
		{
			size, err := m.RuItems.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *TokenBucketRequest_RawResourceItems) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucketRequest_RawResourceItems) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.RawResourceItems != nil {
		{
			size, err := m.RawResourceItems.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *TokenBucketRequest_RequestRU) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenBucketRequest_RequestRU) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucketRequest_RequestRU) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RequestRU) > 0 {
		for iNdEx := len(m.RequestRU) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RequestRU[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintResourceManager(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TokenBucketRequest_RequestRawResource) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenBucketRequest_RequestRawResource) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucketRequest_RequestRawResource) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RequestRawResource) > 0 {
		for iNdEx := len(m.RequestRawResource) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RequestRawResource[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintResourceManager(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TokenBucketsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenBucketsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucketsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Responses) > 0 {
		for iNdEx := len(m.Responses) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Responses[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintResourceManager(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TokenBucketResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenBucketResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucketResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.KeyspaceId != nil {
		{
			size, err := m.KeyspaceId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.GrantedResourceTokens) > 0 {
		for iNdEx := len(m.GrantedResourceTokens) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.GrantedResourceTokens[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintResourceManager(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.GrantedRUTokens) > 0 {
		for iNdEx := len(m.GrantedRUTokens) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.GrantedRUTokens[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintResourceManager(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.ResourceGroupName) > 0 {
		i -= len(m.ResourceGroupName)
		copy(dAtA[i:], m.ResourceGroupName)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.ResourceGroupName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GrantedRUTokenBucket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrantedRUTokenBucket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GrantedRUTokenBucket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TrickleTimeMs != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.TrickleTimeMs))
		i--
		dAtA[i] = 0x18
	}
	if m.GrantedTokens != nil {
		{
			size, err := m.GrantedTokens.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Type != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GrantedRawResourceTokenBucket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrantedRawResourceTokenBucket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GrantedRawResourceTokenBucket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TrickleTimeMs != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.TrickleTimeMs))
		i--
		dAtA[i] = 0x18
	}
	if m.GrantedTokens != nil {
		{
			size, err := m.GrantedTokens.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Type != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Consumption) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Consumption) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Consumption) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.KvWriteRpcCount != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.KvWriteRpcCount))))
		i--
		dAtA[i] = 0x41
	}
	if m.KvReadRpcCount != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.KvReadRpcCount))))
		i--
		dAtA[i] = 0x39
	}
	if m.SqlLayerCpuTimeMs != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.SqlLayerCpuTimeMs))))
		i--
		dAtA[i] = 0x31
	}
	if m.TotalCpuTimeMs != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.TotalCpuTimeMs))))
		i--
		dAtA[i] = 0x29
	}
	if m.WriteBytes != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.WriteBytes))))
		i--
		dAtA[i] = 0x21
	}
	if m.ReadBytes != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.ReadBytes))))
		i--
		dAtA[i] = 0x19
	}
	if m.WRU != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.WRU))))
		i--
		dAtA[i] = 0x11
	}
	if m.RRU != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.RRU))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *RequestUnitItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RequestUnitItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RequestUnitItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Value != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Value))))
		i--
		dAtA[i] = 0x11
	}
	if m.Type != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RawResourceItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RawResourceItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RawResourceItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Value != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Value))))
		i--
		dAtA[i] = 0x11
	}
	if m.Type != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ResourceGroup) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResourceGroup) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResourceGroup) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.KeyspaceId != nil {
		{
			size, err := m.KeyspaceId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.RUStats != nil {
		{
			size, err := m.RUStats.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.BackgroundSettings != nil {
		{
			size, err := m.BackgroundSettings.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	if m.RunawaySettings != nil {
		{
			size, err := m.RunawaySettings.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Priority != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Priority))
		i--
		dAtA[i] = 0x28
	}
	if m.RawResourceSettings != nil {
		{
			size, err := m.RawResourceSettings.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.RUSettings != nil {
		{
			size, err := m.RUSettings.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Mode != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Mode))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GroupRequestUnitSettings) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupRequestUnitSettings) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GroupRequestUnitSettings) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RU != nil {
		{
			size, err := m.RU.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GroupRawResourceSettings) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupRawResourceSettings) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GroupRawResourceSettings) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IoWrite != nil {
		{
			size, err := m.IoWrite.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.IoRead != nil {
		{
			size, err := m.IoRead.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Cpu != nil {
		{
			size, err := m.Cpu.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TokenBucket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenBucket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenBucket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Tokens != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Tokens))))
		i--
		dAtA[i] = 0x11
	}
	if m.Settings != nil {
		{
			size, err := m.Settings.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TokenLimitSettings) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TokenLimitSettings) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TokenLimitSettings) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MaxTokens != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.MaxTokens))))
		i--
		dAtA[i] = 0x19
	}
	if m.BurstLimit != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.BurstLimit))
		i--
		dAtA[i] = 0x10
	}
	if m.FillRate != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.FillRate))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RunawayRule) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RunawayRule) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RunawayRule) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.RequestUnit != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.RequestUnit))
		i--
		dAtA[i] = 0x18
	}
	if m.ProcessedKeys != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.ProcessedKeys))
		i--
		dAtA[i] = 0x10
	}
	if m.ExecElapsedTimeMs != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.ExecElapsedTimeMs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RunawayWatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RunawayWatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RunawayWatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x10
	}
	if m.LastingDurationMs != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.LastingDurationMs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RunawaySettings) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RunawaySettings) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RunawaySettings) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SwitchGroupName) > 0 {
		i -= len(m.SwitchGroupName)
		copy(dAtA[i:], m.SwitchGroupName)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.SwitchGroupName)))
		i--
		dAtA[i] = 0x22
	}
	if m.Watch != nil {
		{
			size, err := m.Watch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Action != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Action))
		i--
		dAtA[i] = 0x10
	}
	if m.Rule != nil {
		{
			size, err := m.Rule.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintResourceManager(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *BackgroundSettings) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BackgroundSettings) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BackgroundSettings) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.UtilizationLimit != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.UtilizationLimit))
		i--
		dAtA[i] = 0x10
	}
	if len(m.JobTypes) > 0 {
		for iNdEx := len(m.JobTypes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.JobTypes[iNdEx])
			copy(dAtA[i:], m.JobTypes[iNdEx])
			i = encodeVarintResourceManager(dAtA, i, uint64(len(m.JobTypes[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Participant) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Participant) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Participant) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ListenUrls) > 0 {
		for iNdEx := len(m.ListenUrls) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ListenUrls[iNdEx])
			copy(dAtA[i:], m.ListenUrls[iNdEx])
			i = encodeVarintResourceManager(dAtA, i, uint64(len(m.ListenUrls[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Id != 0 {
		i = encodeVarintResourceManager(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintResourceManager(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintResourceManager(dAtA []byte, offset int, v uint64) int {
	offset -= sovResourceManager(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *KeyspaceIDValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Value != 0 {
		n += 1 + sovResourceManager(uint64(m.Value))
	}
	return n
}

func (m *ListResourceGroupsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.WithRuStats {
		n += 2
	}
	if m.KeyspaceId != nil {
		l = m.KeyspaceId.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *ListResourceGroupsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if len(m.Groups) > 0 {
		for _, e := range m.Groups {
			l = e.Size()
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	return n
}

func (m *GetResourceGroupRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ResourceGroupName)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.WithRuStats {
		n += 2
	}
	if m.KeyspaceId != nil {
		l = m.KeyspaceId.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *GetResourceGroupResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.Group != nil {
		l = m.Group.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *DeleteResourceGroupRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ResourceGroupName)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.KeyspaceId != nil {
		l = m.KeyspaceId.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *DeleteResourceGroupResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *PutResourceGroupRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Group != nil {
		l = m.Group.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *PutResourceGroupResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *TokenBucketsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Requests) > 0 {
		for _, e := range m.Requests {
			l = e.Size()
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	if m.TargetRequestPeriodMs != 0 {
		n += 1 + sovResourceManager(uint64(m.TargetRequestPeriodMs))
	}
	if m.ClientUniqueId != 0 {
		n += 1 + sovResourceManager(uint64(m.ClientUniqueId))
	}
	return n
}

func (m *TokenBucketRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ResourceGroupName)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.Request != nil {
		n += m.Request.Size()
	}
	if m.ConsumptionSinceLastRequest != nil {
		l = m.ConsumptionSinceLastRequest.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.IsBackground {
		n += 2
	}
	if m.IsTiflash {
		n += 2
	}
	if m.KeyspaceId != nil {
		l = m.KeyspaceId.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *TokenBucketRequest_RuItems) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RuItems != nil {
		l = m.RuItems.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}
func (m *TokenBucketRequest_RawResourceItems) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RawResourceItems != nil {
		l = m.RawResourceItems.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}
func (m *TokenBucketRequest_RequestRU) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.RequestRU) > 0 {
		for _, e := range m.RequestRU {
			l = e.Size()
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	return n
}

func (m *TokenBucketRequest_RequestRawResource) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.RequestRawResource) > 0 {
		for _, e := range m.RequestRawResource {
			l = e.Size()
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	return n
}

func (m *TokenBucketsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if len(m.Responses) > 0 {
		for _, e := range m.Responses {
			l = e.Size()
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	return n
}

func (m *TokenBucketResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ResourceGroupName)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if len(m.GrantedRUTokens) > 0 {
		for _, e := range m.GrantedRUTokens {
			l = e.Size()
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	if len(m.GrantedResourceTokens) > 0 {
		for _, e := range m.GrantedResourceTokens {
			l = e.Size()
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	if m.KeyspaceId != nil {
		l = m.KeyspaceId.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *GrantedRUTokenBucket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovResourceManager(uint64(m.Type))
	}
	if m.GrantedTokens != nil {
		l = m.GrantedTokens.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.TrickleTimeMs != 0 {
		n += 1 + sovResourceManager(uint64(m.TrickleTimeMs))
	}
	return n
}

func (m *GrantedRawResourceTokenBucket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovResourceManager(uint64(m.Type))
	}
	if m.GrantedTokens != nil {
		l = m.GrantedTokens.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.TrickleTimeMs != 0 {
		n += 1 + sovResourceManager(uint64(m.TrickleTimeMs))
	}
	return n
}

func (m *Consumption) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RRU != 0 {
		n += 9
	}
	if m.WRU != 0 {
		n += 9
	}
	if m.ReadBytes != 0 {
		n += 9
	}
	if m.WriteBytes != 0 {
		n += 9
	}
	if m.TotalCpuTimeMs != 0 {
		n += 9
	}
	if m.SqlLayerCpuTimeMs != 0 {
		n += 9
	}
	if m.KvReadRpcCount != 0 {
		n += 9
	}
	if m.KvWriteRpcCount != 0 {
		n += 9
	}
	return n
}

func (m *RequestUnitItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovResourceManager(uint64(m.Type))
	}
	if m.Value != 0 {
		n += 9
	}
	return n
}

func (m *RawResourceItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovResourceManager(uint64(m.Type))
	}
	if m.Value != 0 {
		n += 9
	}
	return n
}

func (m *ResourceGroup) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.Mode != 0 {
		n += 1 + sovResourceManager(uint64(m.Mode))
	}
	if m.RUSettings != nil {
		l = m.RUSettings.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.RawResourceSettings != nil {
		l = m.RawResourceSettings.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.Priority != 0 {
		n += 1 + sovResourceManager(uint64(m.Priority))
	}
	if m.RunawaySettings != nil {
		l = m.RunawaySettings.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.BackgroundSettings != nil {
		l = m.BackgroundSettings.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.RUStats != nil {
		l = m.RUStats.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.KeyspaceId != nil {
		l = m.KeyspaceId.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *GroupRequestUnitSettings) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RU != nil {
		l = m.RU.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *GroupRawResourceSettings) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Cpu != nil {
		l = m.Cpu.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.IoRead != nil {
		l = m.IoRead.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.IoWrite != nil {
		l = m.IoWrite.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *TokenBucket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Settings != nil {
		l = m.Settings.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.Tokens != 0 {
		n += 9
	}
	return n
}

func (m *TokenLimitSettings) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FillRate != 0 {
		n += 1 + sovResourceManager(uint64(m.FillRate))
	}
	if m.BurstLimit != 0 {
		n += 1 + sovResourceManager(uint64(m.BurstLimit))
	}
	if m.MaxTokens != 0 {
		n += 9
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *RunawayRule) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ExecElapsedTimeMs != 0 {
		n += 1 + sovResourceManager(uint64(m.ExecElapsedTimeMs))
	}
	if m.ProcessedKeys != 0 {
		n += 1 + sovResourceManager(uint64(m.ProcessedKeys))
	}
	if m.RequestUnit != 0 {
		n += 1 + sovResourceManager(uint64(m.RequestUnit))
	}
	return n
}

func (m *RunawayWatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.LastingDurationMs != 0 {
		n += 1 + sovResourceManager(uint64(m.LastingDurationMs))
	}
	if m.Type != 0 {
		n += 1 + sovResourceManager(uint64(m.Type))
	}
	return n
}

func (m *RunawaySettings) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Rule != nil {
		l = m.Rule.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.Action != 0 {
		n += 1 + sovResourceManager(uint64(m.Action))
	}
	if m.Watch != nil {
		l = m.Watch.Size()
		n += 1 + l + sovResourceManager(uint64(l))
	}
	l = len(m.SwitchGroupName)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	return n
}

func (m *BackgroundSettings) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.JobTypes) > 0 {
		for _, s := range m.JobTypes {
			l = len(s)
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	if m.UtilizationLimit != 0 {
		n += 1 + sovResourceManager(uint64(m.UtilizationLimit))
	}
	return n
}

func (m *Participant) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovResourceManager(uint64(l))
	}
	if m.Id != 0 {
		n += 1 + sovResourceManager(uint64(m.Id))
	}
	if len(m.ListenUrls) > 0 {
		for _, s := range m.ListenUrls {
			l = len(s)
			n += 1 + l + sovResourceManager(uint64(l))
		}
	}
	return n
}

func sovResourceManager(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozResourceManager(x uint64) (n int) {
	return sovResourceManager(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *KeyspaceIDValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeyspaceIDValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeyspaceIDValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			m.Value = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Value |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListResourceGroupsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListResourceGroupsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListResourceGroupsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WithRuStats", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithRuStats = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyspaceId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyspaceId == nil {
				m.KeyspaceId = &KeyspaceIDValue{}
			}
			if err := m.KeyspaceId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ListResourceGroupsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ListResourceGroupsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ListResourceGroupsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Groups", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Groups = append(m.Groups, &ResourceGroup{})
			if err := m.Groups[len(m.Groups)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetResourceGroupRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetResourceGroupRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetResourceGroupRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResourceGroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResourceGroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WithRuStats", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.WithRuStats = bool(v != 0)
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyspaceId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyspaceId == nil {
				m.KeyspaceId = &KeyspaceIDValue{}
			}
			if err := m.KeyspaceId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetResourceGroupResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetResourceGroupResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetResourceGroupResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Group == nil {
				m.Group = &ResourceGroup{}
			}
			if err := m.Group.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteResourceGroupRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteResourceGroupRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteResourceGroupRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResourceGroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResourceGroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyspaceId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyspaceId == nil {
				m.KeyspaceId = &KeyspaceIDValue{}
			}
			if err := m.KeyspaceId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteResourceGroupResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteResourceGroupResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteResourceGroupResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutResourceGroupRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutResourceGroupRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutResourceGroupRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Group == nil {
				m.Group = &ResourceGroup{}
			}
			if err := m.Group.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PutResourceGroupResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PutResourceGroupResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PutResourceGroupResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenBucketsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TokenBucketsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TokenBucketsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Requests", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Requests = append(m.Requests, &TokenBucketRequest{})
			if err := m.Requests[len(m.Requests)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TargetRequestPeriodMs", wireType)
			}
			m.TargetRequestPeriodMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetRequestPeriodMs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientUniqueId", wireType)
			}
			m.ClientUniqueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientUniqueId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenBucketRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TokenBucketRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TokenBucketRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResourceGroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResourceGroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RuItems", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &TokenBucketRequest_RequestRU{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Request = &TokenBucketRequest_RuItems{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawResourceItems", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &TokenBucketRequest_RequestRawResource{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Request = &TokenBucketRequest_RawResourceItems{v}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConsumptionSinceLastRequest", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ConsumptionSinceLastRequest == nil {
				m.ConsumptionSinceLastRequest = &Consumption{}
			}
			if err := m.ConsumptionSinceLastRequest.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsBackground", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBackground = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsTiflash", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsTiflash = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyspaceId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyspaceId == nil {
				m.KeyspaceId = &KeyspaceIDValue{}
			}
			if err := m.KeyspaceId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenBucketRequest_RequestRU) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RequestRU: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RequestRU: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestRU", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequestRU = append(m.RequestRU, &RequestUnitItem{})
			if err := m.RequestRU[len(m.RequestRU)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenBucketRequest_RequestRawResource) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RequestRawResource: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RequestRawResource: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestRawResource", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RequestRawResource = append(m.RequestRawResource, &RawResourceItem{})
			if err := m.RequestRawResource[len(m.RequestRawResource)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenBucketsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TokenBucketsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TokenBucketsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Responses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Responses = append(m.Responses, &TokenBucketResponse{})
			if err := m.Responses[len(m.Responses)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenBucketResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TokenBucketResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TokenBucketResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResourceGroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResourceGroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrantedRUTokens", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GrantedRUTokens = append(m.GrantedRUTokens, &GrantedRUTokenBucket{})
			if err := m.GrantedRUTokens[len(m.GrantedRUTokens)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrantedResourceTokens", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GrantedResourceTokens = append(m.GrantedResourceTokens, &GrantedRawResourceTokenBucket{})
			if err := m.GrantedResourceTokens[len(m.GrantedResourceTokens)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyspaceId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyspaceId == nil {
				m.KeyspaceId = &KeyspaceIDValue{}
			}
			if err := m.KeyspaceId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GrantedRUTokenBucket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GrantedRUTokenBucket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GrantedRUTokenBucket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= RequestUnitType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrantedTokens", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GrantedTokens == nil {
				m.GrantedTokens = &TokenBucket{}
			}
			if err := m.GrantedTokens.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrickleTimeMs", wireType)
			}
			m.TrickleTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrickleTimeMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GrantedRawResourceTokenBucket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GrantedRawResourceTokenBucket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GrantedRawResourceTokenBucket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= RawResourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrantedTokens", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GrantedTokens == nil {
				m.GrantedTokens = &TokenBucket{}
			}
			if err := m.GrantedTokens.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TrickleTimeMs", wireType)
			}
			m.TrickleTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TrickleTimeMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Consumption) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Consumption: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Consumption: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field RRU", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.RRU = float64(math.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field WRU", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.WRU = float64(math.Float64frombits(v))
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadBytes", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.ReadBytes = float64(math.Float64frombits(v))
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field WriteBytes", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.WriteBytes = float64(math.Float64frombits(v))
		case 5:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field TotalCpuTimeMs", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.TotalCpuTimeMs = float64(math.Float64frombits(v))
		case 6:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlLayerCpuTimeMs", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.SqlLayerCpuTimeMs = float64(math.Float64frombits(v))
		case 7:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvReadRpcCount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.KvReadRpcCount = float64(math.Float64frombits(v))
		case 8:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field KvWriteRpcCount", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.KvWriteRpcCount = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RequestUnitItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RequestUnitItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RequestUnitItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= RequestUnitType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RawResourceItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RawResourceItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RawResourceItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= RawResourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResourceGroup) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResourceGroup: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResourceGroup: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mode", wireType)
			}
			m.Mode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Mode |= GroupMode(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RUSettings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RUSettings == nil {
				m.RUSettings = &GroupRequestUnitSettings{}
			}
			if err := m.RUSettings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawResourceSettings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RawResourceSettings == nil {
				m.RawResourceSettings = &GroupRawResourceSettings{}
			}
			if err := m.RawResourceSettings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Priority", wireType)
			}
			m.Priority = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Priority |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RunawaySettings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RunawaySettings == nil {
				m.RunawaySettings = &RunawaySettings{}
			}
			if err := m.RunawaySettings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BackgroundSettings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.BackgroundSettings == nil {
				m.BackgroundSettings = &BackgroundSettings{}
			}
			if err := m.BackgroundSettings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RUStats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RUStats == nil {
				m.RUStats = &Consumption{}
			}
			if err := m.RUStats.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyspaceId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.KeyspaceId == nil {
				m.KeyspaceId = &KeyspaceIDValue{}
			}
			if err := m.KeyspaceId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupRequestUnitSettings) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GroupRequestUnitSettings: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GroupRequestUnitSettings: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RU", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RU == nil {
				m.RU = &TokenBucket{}
			}
			if err := m.RU.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupRawResourceSettings) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GroupRawResourceSettings: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GroupRawResourceSettings: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cpu", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Cpu == nil {
				m.Cpu = &TokenBucket{}
			}
			if err := m.Cpu.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IoRead", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.IoRead == nil {
				m.IoRead = &TokenBucket{}
			}
			if err := m.IoRead.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IoWrite", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.IoWrite == nil {
				m.IoWrite = &TokenBucket{}
			}
			if err := m.IoWrite.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenBucket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TokenBucket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TokenBucket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Settings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Settings == nil {
				m.Settings = &TokenLimitSettings{}
			}
			if err := m.Settings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tokens", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Tokens = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TokenLimitSettings) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TokenLimitSettings: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TokenLimitSettings: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FillRate", wireType)
			}
			m.FillRate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FillRate |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BurstLimit", wireType)
			}
			m.BurstLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BurstLimit |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxTokens", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.MaxTokens = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RunawayRule) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RunawayRule: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RunawayRule: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecElapsedTimeMs", wireType)
			}
			m.ExecElapsedTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecElapsedTimeMs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProcessedKeys", wireType)
			}
			m.ProcessedKeys = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProcessedKeys |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequestUnit", wireType)
			}
			m.RequestUnit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RequestUnit |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RunawayWatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RunawayWatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RunawayWatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastingDurationMs", wireType)
			}
			m.LastingDurationMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastingDurationMs |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= RunawayWatchType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RunawaySettings) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RunawaySettings: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RunawaySettings: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rule", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Rule == nil {
				m.Rule = &RunawayRule{}
			}
			if err := m.Rule.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Action", wireType)
			}
			m.Action = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Action |= RunawayAction(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Watch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Watch == nil {
				m.Watch = &RunawayWatch{}
			}
			if err := m.Watch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SwitchGroupName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SwitchGroupName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BackgroundSettings) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BackgroundSettings: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BackgroundSettings: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field JobTypes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.JobTypes = append(m.JobTypes, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UtilizationLimit", wireType)
			}
			m.UtilizationLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UtilizationLimit |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Participant) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Participant: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Participant: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ListenUrls", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthResourceManager
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthResourceManager
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ListenUrls = append(m.ListenUrls, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipResourceManager(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthResourceManager
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipResourceManager(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowResourceManager
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowResourceManager
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthResourceManager
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupResourceManager
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthResourceManager
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthResourceManager        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowResourceManager          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupResourceManager = fmt.Errorf("proto: unexpected end of group")
)
