// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: disaggregated.proto

package disaggregated

import (
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	coprocessor "github.com/pingcap/kvproto/pkg/coprocessor"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type S3LockResult struct {
	// Types that are valid to be assigned to Error:
	//	*S3LockResult_Success
	//	*S3LockResult_NotOwner
	//	*S3LockResult_Conflict
	Error isS3LockResult_Error `protobuf_oneof:"error"`
}

func (m *S3LockResult) Reset()         { *m = S3LockResult{} }
func (m *S3LockResult) String() string { return proto.CompactTextString(m) }
func (*S3LockResult) ProtoMessage()    {}
func (*S3LockResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{0}
}
func (m *S3LockResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *S3LockResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_S3LockResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *S3LockResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_S3LockResult.Merge(m, src)
}
func (m *S3LockResult) XXX_Size() int {
	return m.Size()
}
func (m *S3LockResult) XXX_DiscardUnknown() {
	xxx_messageInfo_S3LockResult.DiscardUnknown(m)
}

var xxx_messageInfo_S3LockResult proto.InternalMessageInfo

type isS3LockResult_Error interface {
	isS3LockResult_Error()
	MarshalTo([]byte) (int, error)
	Size() int
}

type S3LockResult_Success struct {
	Success *Success `protobuf:"bytes,1,opt,name=success,proto3,oneof" json:"success,omitempty"`
}
type S3LockResult_NotOwner struct {
	NotOwner *NotOwner `protobuf:"bytes,2,opt,name=not_owner,json=notOwner,proto3,oneof" json:"not_owner,omitempty"`
}
type S3LockResult_Conflict struct {
	Conflict *Conflict `protobuf:"bytes,3,opt,name=conflict,proto3,oneof" json:"conflict,omitempty"`
}

func (*S3LockResult_Success) isS3LockResult_Error()  {}
func (*S3LockResult_NotOwner) isS3LockResult_Error() {}
func (*S3LockResult_Conflict) isS3LockResult_Error() {}

func (m *S3LockResult) GetError() isS3LockResult_Error {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *S3LockResult) GetSuccess() *Success {
	if x, ok := m.GetError().(*S3LockResult_Success); ok {
		return x.Success
	}
	return nil
}

func (m *S3LockResult) GetNotOwner() *NotOwner {
	if x, ok := m.GetError().(*S3LockResult_NotOwner); ok {
		return x.NotOwner
	}
	return nil
}

func (m *S3LockResult) GetConflict() *Conflict {
	if x, ok := m.GetError().(*S3LockResult_Conflict); ok {
		return x.Conflict
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*S3LockResult) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*S3LockResult_Success)(nil),
		(*S3LockResult_NotOwner)(nil),
		(*S3LockResult_Conflict)(nil),
	}
}

type Success struct {
}

func (m *Success) Reset()         { *m = Success{} }
func (m *Success) String() string { return proto.CompactTextString(m) }
func (*Success) ProtoMessage()    {}
func (*Success) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{1}
}
func (m *Success) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Success) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Success.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Success) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Success.Merge(m, src)
}
func (m *Success) XXX_Size() int {
	return m.Size()
}
func (m *Success) XXX_DiscardUnknown() {
	xxx_messageInfo_Success.DiscardUnknown(m)
}

var xxx_messageInfo_Success proto.InternalMessageInfo

// Error caused by S3GC owner changed
// client should retry
type NotOwner struct {
}

func (m *NotOwner) Reset()         { *m = NotOwner{} }
func (m *NotOwner) String() string { return proto.CompactTextString(m) }
func (*NotOwner) ProtoMessage()    {}
func (*NotOwner) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{2}
}
func (m *NotOwner) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *NotOwner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_NotOwner.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *NotOwner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotOwner.Merge(m, src)
}
func (m *NotOwner) XXX_Size() int {
	return m.Size()
}
func (m *NotOwner) XXX_DiscardUnknown() {
	xxx_messageInfo_NotOwner.DiscardUnknown(m)
}

var xxx_messageInfo_NotOwner proto.InternalMessageInfo

// Error caused by concurrency conflict,
// request cancel
type Conflict struct {
	Reason string `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (m *Conflict) Reset()         { *m = Conflict{} }
func (m *Conflict) String() string { return proto.CompactTextString(m) }
func (*Conflict) ProtoMessage()    {}
func (*Conflict) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{3}
}
func (m *Conflict) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Conflict) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Conflict.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Conflict) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Conflict.Merge(m, src)
}
func (m *Conflict) XXX_Size() int {
	return m.Size()
}
func (m *Conflict) XXX_DiscardUnknown() {
	xxx_messageInfo_Conflict.DiscardUnknown(m)
}

var xxx_messageInfo_Conflict proto.InternalMessageInfo

func (m *Conflict) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type TryAddLockRequest struct {
	// The data file key to add lock
	DataFileKey []byte `protobuf:"bytes,1,opt,name=data_file_key,json=dataFileKey,proto3" json:"data_file_key,omitempty"`
	// The lock store id
	LockStoreId uint64 `protobuf:"varint,3,opt,name=lock_store_id,json=lockStoreId,proto3" json:"lock_store_id,omitempty"`
	// The upload sequence number of lock store
	LockSeq uint64 `protobuf:"varint,4,opt,name=lock_seq,json=lockSeq,proto3" json:"lock_seq,omitempty"`
}

func (m *TryAddLockRequest) Reset()         { *m = TryAddLockRequest{} }
func (m *TryAddLockRequest) String() string { return proto.CompactTextString(m) }
func (*TryAddLockRequest) ProtoMessage()    {}
func (*TryAddLockRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{4}
}
func (m *TryAddLockRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TryAddLockRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TryAddLockRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TryAddLockRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TryAddLockRequest.Merge(m, src)
}
func (m *TryAddLockRequest) XXX_Size() int {
	return m.Size()
}
func (m *TryAddLockRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TryAddLockRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TryAddLockRequest proto.InternalMessageInfo

func (m *TryAddLockRequest) GetDataFileKey() []byte {
	if m != nil {
		return m.DataFileKey
	}
	return nil
}

func (m *TryAddLockRequest) GetLockStoreId() uint64 {
	if m != nil {
		return m.LockStoreId
	}
	return 0
}

func (m *TryAddLockRequest) GetLockSeq() uint64 {
	if m != nil {
		return m.LockSeq
	}
	return 0
}

type TryAddLockResponse struct {
	Result *S3LockResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (m *TryAddLockResponse) Reset()         { *m = TryAddLockResponse{} }
func (m *TryAddLockResponse) String() string { return proto.CompactTextString(m) }
func (*TryAddLockResponse) ProtoMessage()    {}
func (*TryAddLockResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{5}
}
func (m *TryAddLockResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TryAddLockResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TryAddLockResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TryAddLockResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TryAddLockResponse.Merge(m, src)
}
func (m *TryAddLockResponse) XXX_Size() int {
	return m.Size()
}
func (m *TryAddLockResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TryAddLockResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TryAddLockResponse proto.InternalMessageInfo

func (m *TryAddLockResponse) GetResult() *S3LockResult {
	if m != nil {
		return m.Result
	}
	return nil
}

type TryMarkDeleteRequest struct {
	// The data file key to be marked as deleted
	DataFileKey []byte `protobuf:"bytes,1,opt,name=data_file_key,json=dataFileKey,proto3" json:"data_file_key,omitempty"`
}

func (m *TryMarkDeleteRequest) Reset()         { *m = TryMarkDeleteRequest{} }
func (m *TryMarkDeleteRequest) String() string { return proto.CompactTextString(m) }
func (*TryMarkDeleteRequest) ProtoMessage()    {}
func (*TryMarkDeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{6}
}
func (m *TryMarkDeleteRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TryMarkDeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TryMarkDeleteRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TryMarkDeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TryMarkDeleteRequest.Merge(m, src)
}
func (m *TryMarkDeleteRequest) XXX_Size() int {
	return m.Size()
}
func (m *TryMarkDeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TryMarkDeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TryMarkDeleteRequest proto.InternalMessageInfo

func (m *TryMarkDeleteRequest) GetDataFileKey() []byte {
	if m != nil {
		return m.DataFileKey
	}
	return nil
}

type TryMarkDeleteResponse struct {
	Result *S3LockResult `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (m *TryMarkDeleteResponse) Reset()         { *m = TryMarkDeleteResponse{} }
func (m *TryMarkDeleteResponse) String() string { return proto.CompactTextString(m) }
func (*TryMarkDeleteResponse) ProtoMessage()    {}
func (*TryMarkDeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{7}
}
func (m *TryMarkDeleteResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TryMarkDeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TryMarkDeleteResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TryMarkDeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TryMarkDeleteResponse.Merge(m, src)
}
func (m *TryMarkDeleteResponse) XXX_Size() int {
	return m.Size()
}
func (m *TryMarkDeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TryMarkDeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TryMarkDeleteResponse proto.InternalMessageInfo

func (m *TryMarkDeleteResponse) GetResult() *S3LockResult {
	if m != nil {
		return m.Result
	}
	return nil
}

type GetDisaggConfigRequest struct {
}

func (m *GetDisaggConfigRequest) Reset()         { *m = GetDisaggConfigRequest{} }
func (m *GetDisaggConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetDisaggConfigRequest) ProtoMessage()    {}
func (*GetDisaggConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{8}
}
func (m *GetDisaggConfigRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetDisaggConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetDisaggConfigRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetDisaggConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDisaggConfigRequest.Merge(m, src)
}
func (m *GetDisaggConfigRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetDisaggConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDisaggConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDisaggConfigRequest proto.InternalMessageInfo

type DisaggS3Config struct {
	Bucket   string `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	Root     string `protobuf:"bytes,2,opt,name=root,proto3" json:"root,omitempty"`
	Endpoint string `protobuf:"bytes,3,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
}

func (m *DisaggS3Config) Reset()         { *m = DisaggS3Config{} }
func (m *DisaggS3Config) String() string { return proto.CompactTextString(m) }
func (*DisaggS3Config) ProtoMessage()    {}
func (*DisaggS3Config) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{9}
}
func (m *DisaggS3Config) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DisaggS3Config) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DisaggS3Config.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DisaggS3Config) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisaggS3Config.Merge(m, src)
}
func (m *DisaggS3Config) XXX_Size() int {
	return m.Size()
}
func (m *DisaggS3Config) XXX_DiscardUnknown() {
	xxx_messageInfo_DisaggS3Config.DiscardUnknown(m)
}

var xxx_messageInfo_DisaggS3Config proto.InternalMessageInfo

func (m *DisaggS3Config) GetBucket() string {
	if m != nil {
		return m.Bucket
	}
	return ""
}

func (m *DisaggS3Config) GetRoot() string {
	if m != nil {
		return m.Root
	}
	return ""
}

func (m *DisaggS3Config) GetEndpoint() string {
	if m != nil {
		return m.Endpoint
	}
	return ""
}

type GetDisaggConfigResponse struct {
	S3Config *DisaggS3Config `protobuf:"bytes,1,opt,name=s3_config,json=s3Config,proto3" json:"s3_config,omitempty"`
}

func (m *GetDisaggConfigResponse) Reset()         { *m = GetDisaggConfigResponse{} }
func (m *GetDisaggConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetDisaggConfigResponse) ProtoMessage()    {}
func (*GetDisaggConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{10}
}
func (m *GetDisaggConfigResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetDisaggConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetDisaggConfigResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetDisaggConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDisaggConfigResponse.Merge(m, src)
}
func (m *GetDisaggConfigResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetDisaggConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDisaggConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDisaggConfigResponse proto.InternalMessageInfo

func (m *GetDisaggConfigResponse) GetS3Config() *DisaggS3Config {
	if m != nil {
		return m.S3Config
	}
	return nil
}

type DisaggTaskMeta struct {
	StartTs uint64 `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	// gather_id + query_ts + server_id + local_query_id to represent a global unique query.
	GatherId        int64              `protobuf:"varint,9,opt,name=gather_id,json=gatherId,proto3" json:"gather_id,omitempty"`
	QueryTs         uint64             `protobuf:"varint,2,opt,name=query_ts,json=queryTs,proto3" json:"query_ts,omitempty"`
	ServerId        uint64             `protobuf:"varint,3,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
	LocalQueryId    uint64             `protobuf:"varint,4,opt,name=local_query_id,json=localQueryId,proto3" json:"local_query_id,omitempty"`
	TaskId          int64              `protobuf:"varint,5,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	ExecutorId      string             `protobuf:"bytes,6,opt,name=executor_id,json=executorId,proto3" json:"executor_id,omitempty"`
	KeyspaceId      uint32             `protobuf:"varint,7,opt,name=keyspace_id,json=keyspaceId,proto3" json:"keyspace_id,omitempty"`
	ApiVersion      kvrpcpb.APIVersion `protobuf:"varint,8,opt,name=api_version,json=apiVersion,proto3,enum=kvrpcpb.APIVersion" json:"api_version,omitempty"`
	ConnectionId    uint64             `protobuf:"varint,10,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
	ConnectionAlias string             `protobuf:"bytes,11,opt,name=connection_alias,json=connectionAlias,proto3" json:"connection_alias,omitempty"`
}

func (m *DisaggTaskMeta) Reset()         { *m = DisaggTaskMeta{} }
func (m *DisaggTaskMeta) String() string { return proto.CompactTextString(m) }
func (*DisaggTaskMeta) ProtoMessage()    {}
func (*DisaggTaskMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{11}
}
func (m *DisaggTaskMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DisaggTaskMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DisaggTaskMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DisaggTaskMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisaggTaskMeta.Merge(m, src)
}
func (m *DisaggTaskMeta) XXX_Size() int {
	return m.Size()
}
func (m *DisaggTaskMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_DisaggTaskMeta.DiscardUnknown(m)
}

var xxx_messageInfo_DisaggTaskMeta proto.InternalMessageInfo

func (m *DisaggTaskMeta) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *DisaggTaskMeta) GetGatherId() int64 {
	if m != nil {
		return m.GatherId
	}
	return 0
}

func (m *DisaggTaskMeta) GetQueryTs() uint64 {
	if m != nil {
		return m.QueryTs
	}
	return 0
}

func (m *DisaggTaskMeta) GetServerId() uint64 {
	if m != nil {
		return m.ServerId
	}
	return 0
}

func (m *DisaggTaskMeta) GetLocalQueryId() uint64 {
	if m != nil {
		return m.LocalQueryId
	}
	return 0
}

func (m *DisaggTaskMeta) GetTaskId() int64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *DisaggTaskMeta) GetExecutorId() string {
	if m != nil {
		return m.ExecutorId
	}
	return ""
}

func (m *DisaggTaskMeta) GetKeyspaceId() uint32 {
	if m != nil {
		return m.KeyspaceId
	}
	return 0
}

func (m *DisaggTaskMeta) GetApiVersion() kvrpcpb.APIVersion {
	if m != nil {
		return m.ApiVersion
	}
	return kvrpcpb.APIVersion_V1
}

func (m *DisaggTaskMeta) GetConnectionId() uint64 {
	if m != nil {
		return m.ConnectionId
	}
	return 0
}

func (m *DisaggTaskMeta) GetConnectionAlias() string {
	if m != nil {
		return m.ConnectionAlias
	}
	return ""
}

type DisaggReadError struct {
	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (m *DisaggReadError) Reset()         { *m = DisaggReadError{} }
func (m *DisaggReadError) String() string { return proto.CompactTextString(m) }
func (*DisaggReadError) ProtoMessage()    {}
func (*DisaggReadError) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{12}
}
func (m *DisaggReadError) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DisaggReadError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DisaggReadError.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DisaggReadError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DisaggReadError.Merge(m, src)
}
func (m *DisaggReadError) XXX_Size() int {
	return m.Size()
}
func (m *DisaggReadError) XXX_DiscardUnknown() {
	xxx_messageInfo_DisaggReadError.DiscardUnknown(m)
}

var xxx_messageInfo_DisaggReadError proto.InternalMessageInfo

func (m *DisaggReadError) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *DisaggReadError) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type EstablishDisaggTaskError struct {
	// Types that are valid to be assigned to Errors:
	//	*EstablishDisaggTaskError_ErrorRegion
	//	*EstablishDisaggTaskError_ErrorLocked
	//	*EstablishDisaggTaskError_ErrorOther
	Errors isEstablishDisaggTaskError_Errors `protobuf_oneof:"errors"`
}

func (m *EstablishDisaggTaskError) Reset()         { *m = EstablishDisaggTaskError{} }
func (m *EstablishDisaggTaskError) String() string { return proto.CompactTextString(m) }
func (*EstablishDisaggTaskError) ProtoMessage()    {}
func (*EstablishDisaggTaskError) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{13}
}
func (m *EstablishDisaggTaskError) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EstablishDisaggTaskError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EstablishDisaggTaskError.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EstablishDisaggTaskError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EstablishDisaggTaskError.Merge(m, src)
}
func (m *EstablishDisaggTaskError) XXX_Size() int {
	return m.Size()
}
func (m *EstablishDisaggTaskError) XXX_DiscardUnknown() {
	xxx_messageInfo_EstablishDisaggTaskError.DiscardUnknown(m)
}

var xxx_messageInfo_EstablishDisaggTaskError proto.InternalMessageInfo

type isEstablishDisaggTaskError_Errors interface {
	isEstablishDisaggTaskError_Errors()
	MarshalTo([]byte) (int, error)
	Size() int
}

type EstablishDisaggTaskError_ErrorRegion struct {
	ErrorRegion *ErrorRegion `protobuf:"bytes,1,opt,name=error_region,json=errorRegion,proto3,oneof" json:"error_region,omitempty"`
}
type EstablishDisaggTaskError_ErrorLocked struct {
	ErrorLocked *ErrorLocked `protobuf:"bytes,2,opt,name=error_locked,json=errorLocked,proto3,oneof" json:"error_locked,omitempty"`
}
type EstablishDisaggTaskError_ErrorOther struct {
	ErrorOther *ErrorOther `protobuf:"bytes,99,opt,name=error_other,json=errorOther,proto3,oneof" json:"error_other,omitempty"`
}

func (*EstablishDisaggTaskError_ErrorRegion) isEstablishDisaggTaskError_Errors() {}
func (*EstablishDisaggTaskError_ErrorLocked) isEstablishDisaggTaskError_Errors() {}
func (*EstablishDisaggTaskError_ErrorOther) isEstablishDisaggTaskError_Errors()  {}

func (m *EstablishDisaggTaskError) GetErrors() isEstablishDisaggTaskError_Errors {
	if m != nil {
		return m.Errors
	}
	return nil
}

func (m *EstablishDisaggTaskError) GetErrorRegion() *ErrorRegion {
	if x, ok := m.GetErrors().(*EstablishDisaggTaskError_ErrorRegion); ok {
		return x.ErrorRegion
	}
	return nil
}

func (m *EstablishDisaggTaskError) GetErrorLocked() *ErrorLocked {
	if x, ok := m.GetErrors().(*EstablishDisaggTaskError_ErrorLocked); ok {
		return x.ErrorLocked
	}
	return nil
}

func (m *EstablishDisaggTaskError) GetErrorOther() *ErrorOther {
	if x, ok := m.GetErrors().(*EstablishDisaggTaskError_ErrorOther); ok {
		return x.ErrorOther
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*EstablishDisaggTaskError) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*EstablishDisaggTaskError_ErrorRegion)(nil),
		(*EstablishDisaggTaskError_ErrorLocked)(nil),
		(*EstablishDisaggTaskError_ErrorOther)(nil),
	}
}

type ErrorRegion struct {
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// The read node needs to update its region cache about these regions.
	RegionIds []uint64 `protobuf:"varint,2,rep,packed,name=region_ids,json=regionIds,proto3" json:"region_ids,omitempty"`
}

func (m *ErrorRegion) Reset()         { *m = ErrorRegion{} }
func (m *ErrorRegion) String() string { return proto.CompactTextString(m) }
func (*ErrorRegion) ProtoMessage()    {}
func (*ErrorRegion) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{14}
}
func (m *ErrorRegion) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ErrorRegion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ErrorRegion.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ErrorRegion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ErrorRegion.Merge(m, src)
}
func (m *ErrorRegion) XXX_Size() int {
	return m.Size()
}
func (m *ErrorRegion) XXX_DiscardUnknown() {
	xxx_messageInfo_ErrorRegion.DiscardUnknown(m)
}

var xxx_messageInfo_ErrorRegion proto.InternalMessageInfo

func (m *ErrorRegion) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ErrorRegion) GetRegionIds() []uint64 {
	if m != nil {
		return m.RegionIds
	}
	return nil
}

type ErrorLocked struct {
	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	// The read node needs to resolve these locks.
	Locked []*kvrpcpb.LockInfo `protobuf:"bytes,2,rep,name=locked,proto3" json:"locked,omitempty"`
}

func (m *ErrorLocked) Reset()         { *m = ErrorLocked{} }
func (m *ErrorLocked) String() string { return proto.CompactTextString(m) }
func (*ErrorLocked) ProtoMessage()    {}
func (*ErrorLocked) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{15}
}
func (m *ErrorLocked) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ErrorLocked) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ErrorLocked.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ErrorLocked) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ErrorLocked.Merge(m, src)
}
func (m *ErrorLocked) XXX_Size() int {
	return m.Size()
}
func (m *ErrorLocked) XXX_DiscardUnknown() {
	xxx_messageInfo_ErrorLocked.DiscardUnknown(m)
}

var xxx_messageInfo_ErrorLocked proto.InternalMessageInfo

func (m *ErrorLocked) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ErrorLocked) GetLocked() []*kvrpcpb.LockInfo {
	if m != nil {
		return m.Locked
	}
	return nil
}

type ErrorOther struct {
	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (m *ErrorOther) Reset()         { *m = ErrorOther{} }
func (m *ErrorOther) String() string { return proto.CompactTextString(m) }
func (*ErrorOther) ProtoMessage()    {}
func (*ErrorOther) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{16}
}
func (m *ErrorOther) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ErrorOther) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ErrorOther.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ErrorOther) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ErrorOther.Merge(m, src)
}
func (m *ErrorOther) XXX_Size() int {
	return m.Size()
}
func (m *ErrorOther) XXX_DiscardUnknown() {
	xxx_messageInfo_ErrorOther.DiscardUnknown(m)
}

var xxx_messageInfo_ErrorOther proto.InternalMessageInfo

func (m *ErrorOther) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ErrorOther) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type EstablishDisaggTaskRequest struct {
	Meta    *DisaggTaskMeta `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	Address string          `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// The write node needs to ensure that subsequent
	// FetchDisaggPagesRequest can be processed within timeout_s.
	// unit: seconds
	TimeoutS int64 `protobuf:"varint,3,opt,name=timeout_s,json=timeoutS,proto3" json:"timeout_s,omitempty"`
	// The key ranges, Region meta that read node need to execute TableScan
	Regions   []*coprocessor.RegionInfo `protobuf:"bytes,4,rep,name=regions,proto3" json:"regions,omitempty"`
	SchemaVer int64                     `protobuf:"varint,5,opt,name=schema_ver,json=schemaVer,proto3" json:"schema_ver,omitempty"`
	// Used for PartitionTableScan
	TableRegions []*coprocessor.TableRegions `protobuf:"bytes,6,rep,name=table_regions,json=tableRegions,proto3" json:"table_regions,omitempty"`
	// The encoded TableScan/PartitionTableScan + Selection.
	EncodedPlan []byte `protobuf:"bytes,7,opt,name=encoded_plan,json=encodedPlan,proto3" json:"encoded_plan,omitempty"`
}

func (m *EstablishDisaggTaskRequest) Reset()         { *m = EstablishDisaggTaskRequest{} }
func (m *EstablishDisaggTaskRequest) String() string { return proto.CompactTextString(m) }
func (*EstablishDisaggTaskRequest) ProtoMessage()    {}
func (*EstablishDisaggTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{17}
}
func (m *EstablishDisaggTaskRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EstablishDisaggTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EstablishDisaggTaskRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EstablishDisaggTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EstablishDisaggTaskRequest.Merge(m, src)
}
func (m *EstablishDisaggTaskRequest) XXX_Size() int {
	return m.Size()
}
func (m *EstablishDisaggTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EstablishDisaggTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EstablishDisaggTaskRequest proto.InternalMessageInfo

func (m *EstablishDisaggTaskRequest) GetMeta() *DisaggTaskMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

func (m *EstablishDisaggTaskRequest) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *EstablishDisaggTaskRequest) GetTimeoutS() int64 {
	if m != nil {
		return m.TimeoutS
	}
	return 0
}

func (m *EstablishDisaggTaskRequest) GetRegions() []*coprocessor.RegionInfo {
	if m != nil {
		return m.Regions
	}
	return nil
}

func (m *EstablishDisaggTaskRequest) GetSchemaVer() int64 {
	if m != nil {
		return m.SchemaVer
	}
	return 0
}

func (m *EstablishDisaggTaskRequest) GetTableRegions() []*coprocessor.TableRegions {
	if m != nil {
		return m.TableRegions
	}
	return nil
}

func (m *EstablishDisaggTaskRequest) GetEncodedPlan() []byte {
	if m != nil {
		return m.EncodedPlan
	}
	return nil
}

type EstablishDisaggTaskResponse struct {
	Error *EstablishDisaggTaskError `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	// Write node maintains a snapshot with a lease time.
	// Read node should read the delta pages
	// (ColumnFileInMemory and ColumnFileTiny)
	// along with this store_id and snapshot_id.
	StoreId    uint64          `protobuf:"varint,3,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	SnapshotId *DisaggTaskMeta `protobuf:"bytes,4,opt,name=snapshot_id,json=snapshotId,proto3" json:"snapshot_id,omitempty"`
	// Serialized disaggregated tasks (per physical table)
	Tables [][]byte `protobuf:"bytes,5,rep,name=tables,proto3" json:"tables,omitempty"`
}

func (m *EstablishDisaggTaskResponse) Reset()         { *m = EstablishDisaggTaskResponse{} }
func (m *EstablishDisaggTaskResponse) String() string { return proto.CompactTextString(m) }
func (*EstablishDisaggTaskResponse) ProtoMessage()    {}
func (*EstablishDisaggTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{18}
}
func (m *EstablishDisaggTaskResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EstablishDisaggTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EstablishDisaggTaskResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EstablishDisaggTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EstablishDisaggTaskResponse.Merge(m, src)
}
func (m *EstablishDisaggTaskResponse) XXX_Size() int {
	return m.Size()
}
func (m *EstablishDisaggTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EstablishDisaggTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EstablishDisaggTaskResponse proto.InternalMessageInfo

func (m *EstablishDisaggTaskResponse) GetError() *EstablishDisaggTaskError {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *EstablishDisaggTaskResponse) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *EstablishDisaggTaskResponse) GetSnapshotId() *DisaggTaskMeta {
	if m != nil {
		return m.SnapshotId
	}
	return nil
}

func (m *EstablishDisaggTaskResponse) GetTables() [][]byte {
	if m != nil {
		return m.Tables
	}
	return nil
}

type CancelDisaggTaskRequest struct {
	Meta *DisaggTaskMeta `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
}

func (m *CancelDisaggTaskRequest) Reset()         { *m = CancelDisaggTaskRequest{} }
func (m *CancelDisaggTaskRequest) String() string { return proto.CompactTextString(m) }
func (*CancelDisaggTaskRequest) ProtoMessage()    {}
func (*CancelDisaggTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{19}
}
func (m *CancelDisaggTaskRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CancelDisaggTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CancelDisaggTaskRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CancelDisaggTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelDisaggTaskRequest.Merge(m, src)
}
func (m *CancelDisaggTaskRequest) XXX_Size() int {
	return m.Size()
}
func (m *CancelDisaggTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelDisaggTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CancelDisaggTaskRequest proto.InternalMessageInfo

func (m *CancelDisaggTaskRequest) GetMeta() *DisaggTaskMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

type CancelDisaggTaskResponse struct {
}

func (m *CancelDisaggTaskResponse) Reset()         { *m = CancelDisaggTaskResponse{} }
func (m *CancelDisaggTaskResponse) String() string { return proto.CompactTextString(m) }
func (*CancelDisaggTaskResponse) ProtoMessage()    {}
func (*CancelDisaggTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{20}
}
func (m *CancelDisaggTaskResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CancelDisaggTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CancelDisaggTaskResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CancelDisaggTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelDisaggTaskResponse.Merge(m, src)
}
func (m *CancelDisaggTaskResponse) XXX_Size() int {
	return m.Size()
}
func (m *CancelDisaggTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelDisaggTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CancelDisaggTaskResponse proto.InternalMessageInfo

type FetchDisaggPagesRequest struct {
	// The snapshot id to fetch pages
	SnapshotId *DisaggTaskMeta `protobuf:"bytes,1,opt,name=snapshot_id,json=snapshotId,proto3" json:"snapshot_id,omitempty"`
	TableId    int64           `protobuf:"varint,2,opt,name=table_id,json=tableId,proto3" json:"table_id,omitempty"`
	SegmentId  uint64          `protobuf:"varint,3,opt,name=segment_id,json=segmentId,proto3" json:"segment_id,omitempty"`
	// It must be a subset of the delta pages ids returned
	// in EstablishDisaggTaskResponse.segments
	PageIds []uint64 `protobuf:"varint,4,rep,packed,name=page_ids,json=pageIds,proto3" json:"page_ids,omitempty"`
}

func (m *FetchDisaggPagesRequest) Reset()         { *m = FetchDisaggPagesRequest{} }
func (m *FetchDisaggPagesRequest) String() string { return proto.CompactTextString(m) }
func (*FetchDisaggPagesRequest) ProtoMessage()    {}
func (*FetchDisaggPagesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{21}
}
func (m *FetchDisaggPagesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FetchDisaggPagesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FetchDisaggPagesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FetchDisaggPagesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FetchDisaggPagesRequest.Merge(m, src)
}
func (m *FetchDisaggPagesRequest) XXX_Size() int {
	return m.Size()
}
func (m *FetchDisaggPagesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FetchDisaggPagesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FetchDisaggPagesRequest proto.InternalMessageInfo

func (m *FetchDisaggPagesRequest) GetSnapshotId() *DisaggTaskMeta {
	if m != nil {
		return m.SnapshotId
	}
	return nil
}

func (m *FetchDisaggPagesRequest) GetTableId() int64 {
	if m != nil {
		return m.TableId
	}
	return 0
}

func (m *FetchDisaggPagesRequest) GetSegmentId() uint64 {
	if m != nil {
		return m.SegmentId
	}
	return 0
}

func (m *FetchDisaggPagesRequest) GetPageIds() []uint64 {
	if m != nil {
		return m.PageIds
	}
	return nil
}

type PagesPacket struct {
	Error *DisaggReadError `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	// Serialized column file data
	// * ColumnFilePersisted alone with its schema, page data, field offsets
	Pages [][]byte `protobuf:"bytes,2,rep,name=pages,proto3" json:"pages,omitempty"`
	// * ColumnFileInMemory alone with its serialized block
	Chunks [][]byte `protobuf:"bytes,3,rep,name=chunks,proto3" json:"chunks,omitempty"`
	// Return tipb.SelectResponse.execution_summaries in the
	// last packet
	Summaries [][]byte `protobuf:"bytes,4,rep,name=summaries,proto3" json:"summaries,omitempty"`
}

func (m *PagesPacket) Reset()         { *m = PagesPacket{} }
func (m *PagesPacket) String() string { return proto.CompactTextString(m) }
func (*PagesPacket) ProtoMessage()    {}
func (*PagesPacket) Descriptor() ([]byte, []int) {
	return fileDescriptor_1026192e39a9f8dc, []int{22}
}
func (m *PagesPacket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PagesPacket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PagesPacket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PagesPacket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PagesPacket.Merge(m, src)
}
func (m *PagesPacket) XXX_Size() int {
	return m.Size()
}
func (m *PagesPacket) XXX_DiscardUnknown() {
	xxx_messageInfo_PagesPacket.DiscardUnknown(m)
}

var xxx_messageInfo_PagesPacket proto.InternalMessageInfo

func (m *PagesPacket) GetError() *DisaggReadError {
	if m != nil {
		return m.Error
	}
	return nil
}

func (m *PagesPacket) GetPages() [][]byte {
	if m != nil {
		return m.Pages
	}
	return nil
}

func (m *PagesPacket) GetChunks() [][]byte {
	if m != nil {
		return m.Chunks
	}
	return nil
}

func (m *PagesPacket) GetSummaries() [][]byte {
	if m != nil {
		return m.Summaries
	}
	return nil
}

func init() {
	proto.RegisterType((*S3LockResult)(nil), "disaggregated.S3LockResult")
	proto.RegisterType((*Success)(nil), "disaggregated.Success")
	proto.RegisterType((*NotOwner)(nil), "disaggregated.NotOwner")
	proto.RegisterType((*Conflict)(nil), "disaggregated.Conflict")
	proto.RegisterType((*TryAddLockRequest)(nil), "disaggregated.TryAddLockRequest")
	proto.RegisterType((*TryAddLockResponse)(nil), "disaggregated.TryAddLockResponse")
	proto.RegisterType((*TryMarkDeleteRequest)(nil), "disaggregated.TryMarkDeleteRequest")
	proto.RegisterType((*TryMarkDeleteResponse)(nil), "disaggregated.TryMarkDeleteResponse")
	proto.RegisterType((*GetDisaggConfigRequest)(nil), "disaggregated.GetDisaggConfigRequest")
	proto.RegisterType((*DisaggS3Config)(nil), "disaggregated.DisaggS3Config")
	proto.RegisterType((*GetDisaggConfigResponse)(nil), "disaggregated.GetDisaggConfigResponse")
	proto.RegisterType((*DisaggTaskMeta)(nil), "disaggregated.DisaggTaskMeta")
	proto.RegisterType((*DisaggReadError)(nil), "disaggregated.DisaggReadError")
	proto.RegisterType((*EstablishDisaggTaskError)(nil), "disaggregated.EstablishDisaggTaskError")
	proto.RegisterType((*ErrorRegion)(nil), "disaggregated.ErrorRegion")
	proto.RegisterType((*ErrorLocked)(nil), "disaggregated.ErrorLocked")
	proto.RegisterType((*ErrorOther)(nil), "disaggregated.ErrorOther")
	proto.RegisterType((*EstablishDisaggTaskRequest)(nil), "disaggregated.EstablishDisaggTaskRequest")
	proto.RegisterType((*EstablishDisaggTaskResponse)(nil), "disaggregated.EstablishDisaggTaskResponse")
	proto.RegisterType((*CancelDisaggTaskRequest)(nil), "disaggregated.CancelDisaggTaskRequest")
	proto.RegisterType((*CancelDisaggTaskResponse)(nil), "disaggregated.CancelDisaggTaskResponse")
	proto.RegisterType((*FetchDisaggPagesRequest)(nil), "disaggregated.FetchDisaggPagesRequest")
	proto.RegisterType((*PagesPacket)(nil), "disaggregated.PagesPacket")
}

func init() { proto.RegisterFile("disaggregated.proto", fileDescriptor_1026192e39a9f8dc) }

var fileDescriptor_1026192e39a9f8dc = []byte{
	// 1223 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xcb, 0x72, 0x1b, 0x45,
	0x14, 0xd5, 0x44, 0xb2, 0x34, 0xba, 0x23, 0xe7, 0xd1, 0x09, 0xf1, 0xd8, 0x21, 0xc2, 0x0c, 0x54,
	0xa1, 0x6c, 0x4c, 0x45, 0x0e, 0x50, 0x95, 0x82, 0x50, 0x79, 0x62, 0x81, 0x43, 0x4c, 0xdb, 0xa4,
	0xd8, 0x4d, 0xb5, 0x67, 0x6e, 0xc6, 0x53, 0x1a, 0x4d, 0xcb, 0xdd, 0x2d, 0x83, 0xbf, 0x80, 0x25,
	0x2c, 0xf9, 0x04, 0x56, 0x7c, 0x00, 0xfc, 0x00, 0x3b, 0xb2, 0xcc, 0x92, 0x8a, 0xf9, 0x10, 0xaa,
	0x1f, 0x23, 0xd9, 0xb2, 0x79, 0xa4, 0x60, 0xa5, 0xbe, 0xa7, 0xef, 0xb9, 0x73, 0xdf, 0x2d, 0xb8,
	0x9c, 0xe6, 0x92, 0x65, 0x99, 0xc0, 0x8c, 0x29, 0x4c, 0xd7, 0xc6, 0x82, 0x2b, 0x4e, 0x16, 0x4f,
	0x80, 0x2b, 0x97, 0x12, 0x3e, 0x16, 0x3c, 0x41, 0x29, 0xb9, 0xb0, 0x1a, 0x2b, 0x8b, 0xc3, 0x03,
	0x31, 0x4e, 0xc6, 0xbb, 0x4e, 0xbc, 0x92, 0xf1, 0x8c, 0x9b, 0xe3, 0xbb, 0xfa, 0xe4, 0xd0, 0x0b,
	0x62, 0x22, 0x95, 0x39, 0x5a, 0x20, 0xfa, 0xd9, 0x83, 0xce, 0xf6, 0xfa, 0x26, 0x4f, 0x86, 0x14,
	0xe5, 0xa4, 0x50, 0xa4, 0x0f, 0x2d, 0x39, 0x49, 0xb4, 0xe5, 0xd0, 0x5b, 0xf5, 0x7a, 0x41, 0xff,
	0xea, 0xda, 0x49, 0x7f, 0xb6, 0xed, 0xed, 0x46, 0x8d, 0x56, 0x8a, 0xe4, 0x7d, 0x68, 0x97, 0x5c,
	0xc5, 0xfc, 0xeb, 0x12, 0x45, 0x78, 0xce, 0xb0, 0x96, 0xe6, 0x58, 0x9f, 0x73, 0xf5, 0x44, 0x5f,
	0x6f, 0xd4, 0xa8, 0x5f, 0xba, 0x33, 0x79, 0x0f, 0xfc, 0x84, 0x97, 0xcf, 0x8a, 0x3c, 0x51, 0x61,
	0xfd, 0x4c, 0xda, 0x7d, 0x77, 0xad, 0x69, 0x95, 0xea, 0xbd, 0x16, 0x2c, 0xa0, 0x10, 0x5c, 0x44,
	0x6d, 0x68, 0x39, 0x6f, 0x22, 0x00, 0xbf, 0xfa, 0x44, 0x14, 0x81, 0x5f, 0xf1, 0xc8, 0x55, 0x68,
	0x0a, 0x64, 0x92, 0x97, 0x26, 0x9a, 0x36, 0x75, 0x52, 0x74, 0x00, 0x97, 0x76, 0xc4, 0xe1, 0xdd,
	0x34, 0xb5, 0xa1, 0xef, 0x4f, 0x50, 0x2a, 0x12, 0xc1, 0x62, 0xca, 0x14, 0x8b, 0x9f, 0xe5, 0x05,
	0xc6, 0x43, 0x3c, 0x34, 0x9c, 0x0e, 0x0d, 0x34, 0xf8, 0x28, 0x2f, 0xf0, 0x33, 0x3c, 0xd4, 0x3a,
	0x05, 0x4f, 0x86, 0xb1, 0x54, 0x5c, 0x60, 0x9c, 0xa7, 0xc6, 0xf1, 0x06, 0x0d, 0x34, 0xb8, 0xad,
	0xb1, 0x41, 0x4a, 0x96, 0xc1, 0xb7, 0x3a, 0xb8, 0x1f, 0x36, 0xcc, 0x75, 0xcb, 0x5c, 0xe3, 0x7e,
	0x34, 0x00, 0x72, 0xfc, 0xbb, 0x72, 0xcc, 0x4b, 0x89, 0x64, 0x5d, 0x7b, 0xa9, 0xd3, 0xef, 0x72,
	0x7e, 0x6d, 0x3e, 0xe7, 0xc7, 0x2a, 0x44, 0x9d, 0x6a, 0x74, 0x1b, 0xae, 0xec, 0x88, 0xc3, 0xc7,
	0x4c, 0x0c, 0x1f, 0x60, 0x81, 0x0a, 0x5f, 0x21, 0x8a, 0x68, 0x13, 0x5e, 0x9b, 0xe3, 0xfe, 0x17,
	0x4f, 0x42, 0xb8, 0xfa, 0x09, 0xaa, 0x07, 0x46, 0x51, 0x67, 0x3e, 0xcf, 0x9c, 0x2f, 0xd1, 0x57,
	0x70, 0xde, 0xc2, 0xdb, 0xeb, 0xf6, 0x42, 0x17, 0x64, 0x77, 0x92, 0x0c, 0x51, 0x55, 0x05, 0xb1,
	0x12, 0x21, 0xd0, 0x10, 0x9c, 0x2b, 0xd3, 0x3e, 0x6d, 0x6a, 0xce, 0x64, 0x05, 0x7c, 0x2c, 0xd3,
	0x31, 0xcf, 0x4b, 0xdb, 0x1f, 0x6d, 0x3a, 0x95, 0xa3, 0x2f, 0x61, 0xe9, 0xd4, 0x37, 0x5d, 0x0c,
	0xb7, 0xa1, 0x2d, 0xd7, 0xe3, 0xc4, 0x80, 0x2e, 0x8c, 0xeb, 0x73, 0x61, 0x9c, 0x74, 0x8a, 0xfa,
	0xd2, 0x9d, 0xa2, 0x6f, 0xeb, 0x95, 0xc7, 0x3b, 0x4c, 0x0e, 0x1f, 0xa3, 0x62, 0xba, 0x9a, 0x52,
	0x31, 0xa1, 0x62, 0x65, 0x47, 0xa2, 0x41, 0x5b, 0x46, 0xde, 0x91, 0xe4, 0x1a, 0xb4, 0x33, 0xa6,
	0xf6, 0x50, 0xe8, 0x46, 0x68, 0xaf, 0x7a, 0xbd, 0x3a, 0xf5, 0x2d, 0x60, 0xbb, 0x60, 0x7f, 0x82,
	0xe2, 0x50, 0xf3, 0xce, 0x59, 0x9e, 0x91, 0x2d, 0x4f, 0xa2, 0x38, 0xb0, 0x3c, 0xdb, 0x40, 0xbe,
	0x05, 0x06, 0x29, 0x79, 0x1b, 0xce, 0x17, 0x3c, 0x61, 0x45, 0x6c, 0xd9, 0x79, 0xea, 0x7a, 0xa8,
	0x63, 0xd0, 0x2f, 0x34, 0x38, 0x48, 0xc9, 0x12, 0xb4, 0x14, 0x93, 0x43, 0x7d, 0xbd, 0x60, 0x3e,
	0xdc, 0xd4, 0xe2, 0x20, 0x25, 0x6f, 0x40, 0x80, 0xdf, 0x60, 0x32, 0x51, 0xdc, 0x58, 0x6f, 0x9a,
	0xbc, 0x41, 0x05, 0x59, 0x85, 0x21, 0x1e, 0xca, 0x31, 0x4b, 0x4c, 0xff, 0xb6, 0x56, 0xbd, 0xde,
	0x22, 0x85, 0x0a, 0x1a, 0xa4, 0xe4, 0x16, 0x04, 0x6c, 0x9c, 0xc7, 0x07, 0x28, 0x64, 0xce, 0xcb,
	0xd0, 0x5f, 0xf5, 0x7a, 0xe7, 0xfb, 0x97, 0xd7, 0xaa, 0xfd, 0x72, 0x77, 0x6b, 0xf0, 0xd4, 0x5e,
	0x51, 0x60, 0xe3, 0xdc, 0x9d, 0xc9, 0x5b, 0xb0, 0x98, 0xf0, 0xb2, 0xc4, 0x44, 0xe5, 0xbc, 0xd4,
	0x86, 0xc1, 0x7a, 0x3d, 0x03, 0x07, 0x29, 0xb9, 0x01, 0x17, 0x8f, 0x29, 0xb1, 0x22, 0x67, 0x32,
	0x0c, 0x8c, 0x87, 0x17, 0x66, 0xf8, 0x5d, 0x0d, 0x47, 0x1f, 0xc0, 0x05, 0x5b, 0x08, 0x8a, 0x2c,
	0x7d, 0xa8, 0xe7, 0x5d, 0xf7, 0x48, 0xc2, 0x53, 0x34, 0x55, 0x58, 0xa0, 0xe6, 0x4c, 0x2e, 0x42,
	0x7d, 0x24, 0x33, 0xd7, 0x36, 0xfa, 0x18, 0xfd, 0xe1, 0x41, 0xf8, 0x50, 0x2a, 0xb6, 0x5b, 0xe4,
	0x72, 0x6f, 0x56, 0x4b, 0x6b, 0xe2, 0x63, 0xe8, 0x98, 0xdd, 0x11, 0x0b, 0xcc, 0x72, 0xb7, 0x15,
	0x82, 0xfe, 0xca, 0x5c, 0x7b, 0x18, 0x5d, 0x6a, 0x34, 0x36, 0x6a, 0x34, 0xc0, 0x99, 0x38, 0x33,
	0xa0, 0x27, 0x1a, 0x53, 0xb7, 0xee, 0xce, 0x34, 0xb0, 0x69, 0x34, 0xa6, 0x06, 0xac, 0x48, 0x3e,
	0x04, 0x2b, 0xc6, 0x5c, 0xf7, 0x49, 0x98, 0x18, 0xfe, 0xf2, 0x59, 0xfc, 0x27, 0x5a, 0x61, 0xa3,
	0x46, 0x01, 0xa7, 0xd2, 0x3d, 0x1f, 0x9a, 0x46, 0x92, 0xd1, 0x1d, 0x08, 0x8e, 0xb9, 0x59, 0xe5,
	0xc1, 0x9b, 0xe6, 0x81, 0x5c, 0x07, 0xb0, 0x41, 0xc6, 0x79, 0xaa, 0x3b, 0xb0, 0xde, 0x6b, 0xd0,
	0xb6, 0x45, 0x06, 0xa9, 0x8c, 0x3e, 0x75, 0x7c, 0xe7, 0xd6, 0x69, 0xfe, 0x0d, 0x68, 0x4e, 0x63,
	0xac, 0xf7, 0x82, 0xfe, 0xa5, 0x69, 0x07, 0x68, 0xca, 0xa0, 0x7c, 0xc6, 0xa9, 0x53, 0x88, 0xfa,
	0x00, 0x33, 0x8f, 0xff, 0x65, 0x99, 0x7e, 0x39, 0x07, 0x2b, 0x67, 0x94, 0xa9, 0xda, 0x62, 0x37,
	0xa1, 0x31, 0x42, 0xc5, 0xfe, 0x76, 0x7e, 0xab, 0x11, 0xa5, 0x46, 0x95, 0x84, 0xd0, 0x62, 0x69,
	0x2a, 0xf4, 0xd3, 0x65, 0xbf, 0x53, 0x89, 0x7a, 0xde, 0x54, 0x3e, 0x42, 0x3e, 0x51, 0xb1, 0x34,
	0xf3, 0x56, 0xa7, 0xbe, 0x03, 0xb6, 0xc9, 0x4d, 0x68, 0xd9, 0xac, 0xc8, 0xb0, 0x61, 0x02, 0x5d,
	0x5a, 0x3b, 0xfe, 0xba, 0xda, 0xfc, 0x9a, 0x70, 0x2b, 0x3d, 0x9d, 0x5a, 0x99, 0xec, 0xe1, 0x88,
	0xe9, 0x21, 0x71, 0xf3, 0xd7, 0xb6, 0xc8, 0x53, 0x14, 0xe4, 0x0e, 0x2c, 0xea, 0xb8, 0x30, 0xae,
	0xec, 0x36, 0x8d, 0xdd, 0xe5, 0x13, 0x76, 0x77, 0xb4, 0x86, 0x35, 0x2e, 0x69, 0x47, 0x1d, 0x93,
	0xc8, 0x9b, 0xd0, 0xc1, 0x52, 0xa7, 0x2d, 0x8d, 0xc7, 0x05, 0x2b, 0xcd, 0x88, 0x76, 0x68, 0xe0,
	0xb0, 0xad, 0x82, 0x95, 0xd1, 0x6f, 0x1e, 0x5c, 0x3b, 0x33, 0x7b, 0x6e, 0x07, 0x7e, 0xe4, 0xde,
	0x48, 0x97, 0xbf, 0x77, 0xe6, 0xfb, 0xeb, 0x2f, 0xe6, 0x83, 0x5a, 0x96, 0xdd, 0x79, 0x27, 0x1e,
	0xb8, 0x96, 0x74, 0x8f, 0xdb, 0x1d, 0x08, 0x64, 0xc9, 0xc6, 0x72, 0x8f, 0xab, 0x6a, 0x37, 0xfd,
	0x63, 0x7d, 0xa0, 0x62, 0x0c, 0x52, 0xfd, 0x00, 0x98, 0x60, 0x65, 0xb8, 0xb0, 0x5a, 0xef, 0x75,
	0xa8, 0x93, 0xa2, 0x4d, 0x58, 0xba, 0xcf, 0xca, 0x04, 0x8b, 0xff, 0xa3, 0x17, 0xa2, 0x15, 0x08,
	0x4f, 0x5b, 0xb3, 0xb9, 0x89, 0x7e, 0xf2, 0x60, 0xe9, 0x11, 0xaa, 0xc4, 0x05, 0xbf, 0xc5, 0x32,
	0x94, 0xd5, 0xa7, 0xe6, 0xa2, 0xf3, 0x5e, 0x35, 0xba, 0x65, 0xf0, 0x6d, 0xe9, 0x73, 0xbb, 0x1a,
	0xea, 0xb4, 0x65, 0xe4, 0x41, 0x6a, 0x9a, 0x06, 0xb3, 0x11, 0x96, 0x6a, 0x96, 0xd5, 0xb6, 0x43,
	0x2c, 0x73, 0xcc, 0x32, 0x34, 0xc3, 0xda, 0x30, 0xc3, 0xda, 0xd2, 0xb2, 0x1e, 0xd5, 0xef, 0x3c,
	0x08, 0x8c, 0x97, 0x5b, 0xcc, 0xbc, 0x95, 0xb7, 0x4e, 0x16, 0xb7, 0x7b, 0xa6, 0x7b, 0xd3, 0xb5,
	0x59, 0xd5, 0xf4, 0x0a, 0x2c, 0x68, 0x83, 0x76, 0x15, 0x74, 0xa8, 0x15, 0x74, 0x39, 0x92, 0xbd,
	0x49, 0x39, 0xd4, 0x73, 0x61, 0xca, 0x61, 0x25, 0xf2, 0x3a, 0xb4, 0xe5, 0x64, 0x34, 0x62, 0x22,
	0x47, 0xeb, 0x4f, 0x87, 0xce, 0x80, 0x7b, 0xfd, 0x17, 0x3f, 0xfa, 0xde, 0xaf, 0x2f, 0xbb, 0xde,
	0xf3, 0x97, 0x5d, 0xef, 0xf7, 0x97, 0x5d, 0xef, 0xfb, 0xa3, 0x6e, 0xed, 0x87, 0xa3, 0x6e, 0xed,
	0xf9, 0x51, 0xb7, 0xf6, 0xe2, 0xa8, 0x5b, 0x83, 0x8b, 0x5c, 0x64, 0x6b, 0x2a, 0x1f, 0x1e, 0xac,
	0x0d, 0x0f, 0xcc, 0x5f, 0xcd, 0xdd, 0xa6, 0xf9, 0x59, 0xff, 0x33, 0x00, 0x00, 0xff, 0xff, 0x65,
	0x6b, 0x66, 0xa3, 0xe0, 0x0a, 0x00, 0x00,
}

func (m *S3LockResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *S3LockResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *S3LockResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size := m.Error.Size()
			i -= size
			if _, err := m.Error.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *S3LockResult_Success) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *S3LockResult_Success) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Success != nil {
		{
			size, err := m.Success.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *S3LockResult_NotOwner) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *S3LockResult_NotOwner) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.NotOwner != nil {
		{
			size, err := m.NotOwner.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *S3LockResult_Conflict) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *S3LockResult_Conflict) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Conflict != nil {
		{
			size, err := m.Conflict.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *Success) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Success) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Success) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *NotOwner) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NotOwner) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *NotOwner) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *Conflict) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Conflict) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Conflict) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Reason) > 0 {
		i -= len(m.Reason)
		copy(dAtA[i:], m.Reason)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Reason)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TryAddLockRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TryAddLockRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TryAddLockRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.LockSeq != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.LockSeq))
		i--
		dAtA[i] = 0x20
	}
	if m.LockStoreId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.LockStoreId))
		i--
		dAtA[i] = 0x18
	}
	if len(m.DataFileKey) > 0 {
		i -= len(m.DataFileKey)
		copy(dAtA[i:], m.DataFileKey)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.DataFileKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TryAddLockResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TryAddLockResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TryAddLockResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Result != nil {
		{
			size, err := m.Result.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TryMarkDeleteRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TryMarkDeleteRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TryMarkDeleteRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DataFileKey) > 0 {
		i -= len(m.DataFileKey)
		copy(dAtA[i:], m.DataFileKey)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.DataFileKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TryMarkDeleteResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TryMarkDeleteResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TryMarkDeleteResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Result != nil {
		{
			size, err := m.Result.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetDisaggConfigRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDisaggConfigRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetDisaggConfigRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *DisaggS3Config) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisaggS3Config) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DisaggS3Config) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Endpoint) > 0 {
		i -= len(m.Endpoint)
		copy(dAtA[i:], m.Endpoint)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Endpoint)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Root) > 0 {
		i -= len(m.Root)
		copy(dAtA[i:], m.Root)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Root)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Bucket) > 0 {
		i -= len(m.Bucket)
		copy(dAtA[i:], m.Bucket)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Bucket)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetDisaggConfigResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDisaggConfigResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetDisaggConfigResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.S3Config != nil {
		{
			size, err := m.S3Config.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DisaggTaskMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisaggTaskMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DisaggTaskMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ConnectionAlias) > 0 {
		i -= len(m.ConnectionAlias)
		copy(dAtA[i:], m.ConnectionAlias)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.ConnectionAlias)))
		i--
		dAtA[i] = 0x5a
	}
	if m.ConnectionId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.ConnectionId))
		i--
		dAtA[i] = 0x50
	}
	if m.GatherId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.GatherId))
		i--
		dAtA[i] = 0x48
	}
	if m.ApiVersion != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.ApiVersion))
		i--
		dAtA[i] = 0x40
	}
	if m.KeyspaceId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.KeyspaceId))
		i--
		dAtA[i] = 0x38
	}
	if len(m.ExecutorId) > 0 {
		i -= len(m.ExecutorId)
		copy(dAtA[i:], m.ExecutorId)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.ExecutorId)))
		i--
		dAtA[i] = 0x32
	}
	if m.TaskId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.TaskId))
		i--
		dAtA[i] = 0x28
	}
	if m.LocalQueryId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.LocalQueryId))
		i--
		dAtA[i] = 0x20
	}
	if m.ServerId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.ServerId))
		i--
		dAtA[i] = 0x18
	}
	if m.QueryTs != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.QueryTs))
		i--
		dAtA[i] = 0x10
	}
	if m.StartTs != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DisaggReadError) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DisaggReadError) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DisaggReadError) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *EstablishDisaggTaskError) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EstablishDisaggTaskError) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EstablishDisaggTaskError) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Errors != nil {
		{
			size := m.Errors.Size()
			i -= size
			if _, err := m.Errors.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *EstablishDisaggTaskError_ErrorRegion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EstablishDisaggTaskError_ErrorRegion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ErrorRegion != nil {
		{
			size, err := m.ErrorRegion.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *EstablishDisaggTaskError_ErrorLocked) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EstablishDisaggTaskError_ErrorLocked) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ErrorLocked != nil {
		{
			size, err := m.ErrorLocked.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *EstablishDisaggTaskError_ErrorOther) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EstablishDisaggTaskError_ErrorOther) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.ErrorOther != nil {
		{
			size, err := m.ErrorOther.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6
		i--
		dAtA[i] = 0x9a
	}
	return len(dAtA) - i, nil
}
func (m *ErrorRegion) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ErrorRegion) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ErrorRegion) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RegionIds) > 0 {
		dAtA11 := make([]byte, len(m.RegionIds)*10)
		var j10 int
		for _, num := range m.RegionIds {
			for num >= 1<<7 {
				dAtA11[j10] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j10++
			}
			dAtA11[j10] = uint8(num)
			j10++
		}
		i -= j10
		copy(dAtA[i:], dAtA11[:j10])
		i = encodeVarintDisaggregated(dAtA, i, uint64(j10))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ErrorLocked) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ErrorLocked) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ErrorLocked) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Locked) > 0 {
		for iNdEx := len(m.Locked) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Locked[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDisaggregated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ErrorOther) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ErrorOther) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ErrorOther) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *EstablishDisaggTaskRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EstablishDisaggTaskRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EstablishDisaggTaskRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.EncodedPlan) > 0 {
		i -= len(m.EncodedPlan)
		copy(dAtA[i:], m.EncodedPlan)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.EncodedPlan)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.TableRegions) > 0 {
		for iNdEx := len(m.TableRegions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TableRegions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDisaggregated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if m.SchemaVer != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.SchemaVer))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Regions) > 0 {
		for iNdEx := len(m.Regions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Regions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDisaggregated(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if m.TimeoutS != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.TimeoutS))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Address) > 0 {
		i -= len(m.Address)
		copy(dAtA[i:], m.Address)
		i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Address)))
		i--
		dAtA[i] = 0x12
	}
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *EstablishDisaggTaskResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EstablishDisaggTaskResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EstablishDisaggTaskResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Tables) > 0 {
		for iNdEx := len(m.Tables) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Tables[iNdEx])
			copy(dAtA[i:], m.Tables[iNdEx])
			i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Tables[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.SnapshotId != nil {
		{
			size, err := m.SnapshotId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.StoreId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x18
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CancelDisaggTaskRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelDisaggTaskRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CancelDisaggTaskRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CancelDisaggTaskResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CancelDisaggTaskResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CancelDisaggTaskResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *FetchDisaggPagesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchDisaggPagesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FetchDisaggPagesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.PageIds) > 0 {
		dAtA17 := make([]byte, len(m.PageIds)*10)
		var j16 int
		for _, num := range m.PageIds {
			for num >= 1<<7 {
				dAtA17[j16] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j16++
			}
			dAtA17[j16] = uint8(num)
			j16++
		}
		i -= j16
		copy(dAtA[i:], dAtA17[:j16])
		i = encodeVarintDisaggregated(dAtA, i, uint64(j16))
		i--
		dAtA[i] = 0x22
	}
	if m.SegmentId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.SegmentId))
		i--
		dAtA[i] = 0x18
	}
	if m.TableId != 0 {
		i = encodeVarintDisaggregated(dAtA, i, uint64(m.TableId))
		i--
		dAtA[i] = 0x10
	}
	if m.SnapshotId != nil {
		{
			size, err := m.SnapshotId.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PagesPacket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PagesPacket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PagesPacket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Summaries) > 0 {
		for iNdEx := len(m.Summaries) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Summaries[iNdEx])
			copy(dAtA[i:], m.Summaries[iNdEx])
			i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Summaries[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Chunks) > 0 {
		for iNdEx := len(m.Chunks) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Chunks[iNdEx])
			copy(dAtA[i:], m.Chunks[iNdEx])
			i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Chunks[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.Pages) > 0 {
		for iNdEx := len(m.Pages) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Pages[iNdEx])
			copy(dAtA[i:], m.Pages[iNdEx])
			i = encodeVarintDisaggregated(dAtA, i, uint64(len(m.Pages[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintDisaggregated(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintDisaggregated(dAtA []byte, offset int, v uint64) int {
	offset -= sovDisaggregated(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *S3LockResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		n += m.Error.Size()
	}
	return n
}

func (m *S3LockResult_Success) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Success != nil {
		l = m.Success.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}
func (m *S3LockResult_NotOwner) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.NotOwner != nil {
		l = m.NotOwner.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}
func (m *S3LockResult_Conflict) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Conflict != nil {
		l = m.Conflict.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}
func (m *Success) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *NotOwner) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *Conflict) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Reason)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *TryAddLockRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.DataFileKey)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if m.LockStoreId != 0 {
		n += 1 + sovDisaggregated(uint64(m.LockStoreId))
	}
	if m.LockSeq != 0 {
		n += 1 + sovDisaggregated(uint64(m.LockSeq))
	}
	return n
}

func (m *TryAddLockResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Result != nil {
		l = m.Result.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *TryMarkDeleteRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.DataFileKey)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *TryMarkDeleteResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Result != nil {
		l = m.Result.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *GetDisaggConfigRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *DisaggS3Config) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Bucket)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	l = len(m.Root)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	l = len(m.Endpoint)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *GetDisaggConfigResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.S3Config != nil {
		l = m.S3Config.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *DisaggTaskMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StartTs != 0 {
		n += 1 + sovDisaggregated(uint64(m.StartTs))
	}
	if m.QueryTs != 0 {
		n += 1 + sovDisaggregated(uint64(m.QueryTs))
	}
	if m.ServerId != 0 {
		n += 1 + sovDisaggregated(uint64(m.ServerId))
	}
	if m.LocalQueryId != 0 {
		n += 1 + sovDisaggregated(uint64(m.LocalQueryId))
	}
	if m.TaskId != 0 {
		n += 1 + sovDisaggregated(uint64(m.TaskId))
	}
	l = len(m.ExecutorId)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if m.KeyspaceId != 0 {
		n += 1 + sovDisaggregated(uint64(m.KeyspaceId))
	}
	if m.ApiVersion != 0 {
		n += 1 + sovDisaggregated(uint64(m.ApiVersion))
	}
	if m.GatherId != 0 {
		n += 1 + sovDisaggregated(uint64(m.GatherId))
	}
	if m.ConnectionId != 0 {
		n += 1 + sovDisaggregated(uint64(m.ConnectionId))
	}
	l = len(m.ConnectionAlias)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *DisaggReadError) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovDisaggregated(uint64(m.Code))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *EstablishDisaggTaskError) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Errors != nil {
		n += m.Errors.Size()
	}
	return n
}

func (m *EstablishDisaggTaskError_ErrorRegion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ErrorRegion != nil {
		l = m.ErrorRegion.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}
func (m *EstablishDisaggTaskError_ErrorLocked) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ErrorLocked != nil {
		l = m.ErrorLocked.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}
func (m *EstablishDisaggTaskError_ErrorOther) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ErrorOther != nil {
		l = m.ErrorOther.Size()
		n += 2 + l + sovDisaggregated(uint64(l))
	}
	return n
}
func (m *ErrorRegion) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if len(m.RegionIds) > 0 {
		l = 0
		for _, e := range m.RegionIds {
			l += sovDisaggregated(uint64(e))
		}
		n += 1 + sovDisaggregated(uint64(l)) + l
	}
	return n
}

func (m *ErrorLocked) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if len(m.Locked) > 0 {
		for _, e := range m.Locked {
			l = e.Size()
			n += 1 + l + sovDisaggregated(uint64(l))
		}
	}
	return n
}

func (m *ErrorOther) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovDisaggregated(uint64(m.Code))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *EstablishDisaggTaskRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	l = len(m.Address)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if m.TimeoutS != 0 {
		n += 1 + sovDisaggregated(uint64(m.TimeoutS))
	}
	if len(m.Regions) > 0 {
		for _, e := range m.Regions {
			l = e.Size()
			n += 1 + l + sovDisaggregated(uint64(l))
		}
	}
	if m.SchemaVer != 0 {
		n += 1 + sovDisaggregated(uint64(m.SchemaVer))
	}
	if len(m.TableRegions) > 0 {
		for _, e := range m.TableRegions {
			l = e.Size()
			n += 1 + l + sovDisaggregated(uint64(l))
		}
	}
	l = len(m.EncodedPlan)
	if l > 0 {
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *EstablishDisaggTaskResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if m.StoreId != 0 {
		n += 1 + sovDisaggregated(uint64(m.StoreId))
	}
	if m.SnapshotId != nil {
		l = m.SnapshotId.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if len(m.Tables) > 0 {
		for _, b := range m.Tables {
			l = len(b)
			n += 1 + l + sovDisaggregated(uint64(l))
		}
	}
	return n
}

func (m *CancelDisaggTaskRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	return n
}

func (m *CancelDisaggTaskResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *FetchDisaggPagesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SnapshotId != nil {
		l = m.SnapshotId.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if m.TableId != 0 {
		n += 1 + sovDisaggregated(uint64(m.TableId))
	}
	if m.SegmentId != 0 {
		n += 1 + sovDisaggregated(uint64(m.SegmentId))
	}
	if len(m.PageIds) > 0 {
		l = 0
		for _, e := range m.PageIds {
			l += sovDisaggregated(uint64(e))
		}
		n += 1 + sovDisaggregated(uint64(l)) + l
	}
	return n
}

func (m *PagesPacket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovDisaggregated(uint64(l))
	}
	if len(m.Pages) > 0 {
		for _, b := range m.Pages {
			l = len(b)
			n += 1 + l + sovDisaggregated(uint64(l))
		}
	}
	if len(m.Chunks) > 0 {
		for _, b := range m.Chunks {
			l = len(b)
			n += 1 + l + sovDisaggregated(uint64(l))
		}
	}
	if len(m.Summaries) > 0 {
		for _, b := range m.Summaries {
			l = len(b)
			n += 1 + l + sovDisaggregated(uint64(l))
		}
	}
	return n
}

func sovDisaggregated(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDisaggregated(x uint64) (n int) {
	return sovDisaggregated(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *S3LockResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: S3LockResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: S3LockResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Success", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Success{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Error = &S3LockResult_Success{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NotOwner", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &NotOwner{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Error = &S3LockResult_NotOwner{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Conflict", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &Conflict{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Error = &S3LockResult_Conflict{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Success) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Success: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Success: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *NotOwner) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: NotOwner: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: NotOwner: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Conflict) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Conflict: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Conflict: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TryAddLockRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TryAddLockRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TryAddLockRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataFileKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DataFileKey = append(m.DataFileKey[:0], dAtA[iNdEx:postIndex]...)
			if m.DataFileKey == nil {
				m.DataFileKey = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockStoreId", wireType)
			}
			m.LockStoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LockStoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LockSeq", wireType)
			}
			m.LockSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LockSeq |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TryAddLockResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TryAddLockResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TryAddLockResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Result == nil {
				m.Result = &S3LockResult{}
			}
			if err := m.Result.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TryMarkDeleteRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TryMarkDeleteRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TryMarkDeleteRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataFileKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DataFileKey = append(m.DataFileKey[:0], dAtA[iNdEx:postIndex]...)
			if m.DataFileKey == nil {
				m.DataFileKey = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TryMarkDeleteResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TryMarkDeleteResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TryMarkDeleteResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Result == nil {
				m.Result = &S3LockResult{}
			}
			if err := m.Result.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDisaggConfigRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetDisaggConfigRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetDisaggConfigRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisaggS3Config) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DisaggS3Config: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DisaggS3Config: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Bucket", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Bucket = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Root", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Root = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Endpoint", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Endpoint = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDisaggConfigResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetDisaggConfigResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetDisaggConfigResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field S3Config", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.S3Config == nil {
				m.S3Config = &DisaggS3Config{}
			}
			if err := m.S3Config.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisaggTaskMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DisaggTaskMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DisaggTaskMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueryTs", wireType)
			}
			m.QueryTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QueryTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServerId", wireType)
			}
			m.ServerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LocalQueryId", wireType)
			}
			m.LocalQueryId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LocalQueryId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			m.TaskId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ExecutorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyspaceId", wireType)
			}
			m.KeyspaceId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeyspaceId |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApiVersion", wireType)
			}
			m.ApiVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApiVersion |= kvrpcpb.APIVersion(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GatherId", wireType)
			}
			m.GatherId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GatherId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			m.ConnectionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConnectionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionAlias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionAlias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DisaggReadError) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DisaggReadError: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DisaggReadError: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EstablishDisaggTaskError) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EstablishDisaggTaskError: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EstablishDisaggTaskError: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorRegion", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ErrorRegion{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Errors = &EstablishDisaggTaskError_ErrorRegion{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorLocked", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ErrorLocked{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Errors = &EstablishDisaggTaskError_ErrorLocked{v}
			iNdEx = postIndex
		case 99:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorOther", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ErrorOther{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Errors = &EstablishDisaggTaskError_ErrorOther{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ErrorRegion) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ErrorRegion: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ErrorRegion: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowDisaggregated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RegionIds = append(m.RegionIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowDisaggregated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthDisaggregated
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthDisaggregated
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.RegionIds) == 0 {
					m.RegionIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowDisaggregated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RegionIds = append(m.RegionIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ErrorLocked) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ErrorLocked: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ErrorLocked: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Locked", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Locked = append(m.Locked, &kvrpcpb.LockInfo{})
			if err := m.Locked[len(m.Locked)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ErrorOther) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ErrorOther: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ErrorOther: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EstablishDisaggTaskRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EstablishDisaggTaskRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EstablishDisaggTaskRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &DisaggTaskMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Address = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeoutS", wireType)
			}
			m.TimeoutS = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeoutS |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Regions = append(m.Regions, &coprocessor.RegionInfo{})
			if err := m.Regions[len(m.Regions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SchemaVer", wireType)
			}
			m.SchemaVer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SchemaVer |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableRegions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableRegions = append(m.TableRegions, &coprocessor.TableRegions{})
			if err := m.TableRegions[len(m.TableRegions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EncodedPlan", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EncodedPlan = append(m.EncodedPlan[:0], dAtA[iNdEx:postIndex]...)
			if m.EncodedPlan == nil {
				m.EncodedPlan = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EstablishDisaggTaskResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EstablishDisaggTaskResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EstablishDisaggTaskResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &EstablishDisaggTaskError{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SnapshotId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SnapshotId == nil {
				m.SnapshotId = &DisaggTaskMeta{}
			}
			if err := m.SnapshotId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tables", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tables = append(m.Tables, make([]byte, postIndex-iNdEx))
			copy(m.Tables[len(m.Tables)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelDisaggTaskRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CancelDisaggTaskRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CancelDisaggTaskRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &DisaggTaskMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CancelDisaggTaskResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CancelDisaggTaskResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CancelDisaggTaskResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchDisaggPagesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FetchDisaggPagesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FetchDisaggPagesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SnapshotId", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.SnapshotId == nil {
				m.SnapshotId = &DisaggTaskMeta{}
			}
			if err := m.SnapshotId.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableId", wireType)
			}
			m.TableId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TableId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SegmentId", wireType)
			}
			m.SegmentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SegmentId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowDisaggregated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.PageIds = append(m.PageIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowDisaggregated
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthDisaggregated
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthDisaggregated
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.PageIds) == 0 {
					m.PageIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowDisaggregated
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.PageIds = append(m.PageIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field PageIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PagesPacket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PagesPacket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PagesPacket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &DisaggReadError{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pages", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pages = append(m.Pages, make([]byte, postIndex-iNdEx))
			copy(m.Pages[len(m.Pages)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Chunks", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Chunks = append(m.Chunks, make([]byte, postIndex-iNdEx))
			copy(m.Chunks[len(m.Chunks)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Summaries", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthDisaggregated
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Summaries = append(m.Summaries, make([]byte, postIndex-iNdEx))
			copy(m.Summaries[len(m.Summaries)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDisaggregated(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDisaggregated
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDisaggregated(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDisaggregated
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDisaggregated
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDisaggregated
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDisaggregated
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDisaggregated
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDisaggregated        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDisaggregated          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDisaggregated = fmt.Errorf("proto: unexpected end of group")
)
