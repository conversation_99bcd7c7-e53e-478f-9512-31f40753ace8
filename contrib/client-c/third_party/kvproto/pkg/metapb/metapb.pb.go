// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: metapb.proto

package metapb

import (
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	encryptionpb "github.com/pingcap/kvproto/pkg/encryptionpb"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type StoreState int32

const (
	StoreState_Up        StoreState = 0
	StoreState_Offline   StoreState = 1
	StoreState_Tombstone StoreState = 2
)

var StoreState_name = map[int32]string{
	0: "Up",
	1: "Offline",
	2: "Tombstone",
}

var StoreState_value = map[string]int32{
	"Up":        0,
	"Offline":   1,
	"Tombstone": 2,
}

func (x StoreState) String() string {
	return proto.EnumName(StoreState_name, int32(x))
}

func (StoreState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{0}
}

// NodeState is going to replace StoreState to make the state concept more clear.
// "Up" is devided into "Preparing" and "Serving" stages so that we can better describe the online process.
// "Removing" is just like previous `Offline` which is more accurate.
// "Removed" has the same meaning with `Tombstone`.
type NodeState int32

const (
	NodeState_Preparing NodeState = 0
	NodeState_Serving   NodeState = 1
	NodeState_Removing  NodeState = 2
	NodeState_Removed   NodeState = 3
)

var NodeState_name = map[int32]string{
	0: "Preparing",
	1: "Serving",
	2: "Removing",
	3: "Removed",
}

var NodeState_value = map[string]int32{
	"Preparing": 0,
	"Serving":   1,
	"Removing":  2,
	"Removed":   3,
}

func (x NodeState) String() string {
	return proto.EnumName(NodeState_name, int32(x))
}

func (NodeState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{1}
}

type PeerRole int32

const (
	// Voter -> Voter
	PeerRole_Voter PeerRole = 0
	// Learner/None -> Learner
	PeerRole_Learner PeerRole = 1
	// Learner/None -> Voter
	PeerRole_IncomingVoter PeerRole = 2
	// Voter -> Learner
	PeerRole_DemotingVoter PeerRole = 3
)

var PeerRole_name = map[int32]string{
	0: "Voter",
	1: "Learner",
	2: "IncomingVoter",
	3: "DemotingVoter",
}

var PeerRole_value = map[string]int32{
	"Voter":         0,
	"Learner":       1,
	"IncomingVoter": 2,
	"DemotingVoter": 3,
}

func (x PeerRole) String() string {
	return proto.EnumName(PeerRole_name, int32(x))
}

func (PeerRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{2}
}

type Cluster struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// max peer count for a region.
	// pd will do the auto-balance if region peer count mismatches.
	MaxPeerCount uint32 `protobuf:"varint,2,opt,name=max_peer_count,json=maxPeerCount,proto3" json:"max_peer_count,omitempty"`
}

func (m *Cluster) Reset()         { *m = Cluster{} }
func (m *Cluster) String() string { return proto.CompactTextString(m) }
func (*Cluster) ProtoMessage()    {}
func (*Cluster) Descriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{0}
}
func (m *Cluster) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Cluster) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Cluster.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Cluster) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Cluster.Merge(m, src)
}
func (m *Cluster) XXX_Size() int {
	return m.Size()
}
func (m *Cluster) XXX_DiscardUnknown() {
	xxx_messageInfo_Cluster.DiscardUnknown(m)
}

var xxx_messageInfo_Cluster proto.InternalMessageInfo

func (m *Cluster) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Cluster) GetMaxPeerCount() uint32 {
	if m != nil {
		return m.MaxPeerCount
	}
	return 0
}

// Case insensitive key/value for replica constraints.
type StoreLabel struct {
	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *StoreLabel) Reset()         { *m = StoreLabel{} }
func (m *StoreLabel) String() string { return proto.CompactTextString(m) }
func (*StoreLabel) ProtoMessage()    {}
func (*StoreLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{1}
}
func (m *StoreLabel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreLabel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreLabel.Merge(m, src)
}
func (m *StoreLabel) XXX_Size() int {
	return m.Size()
}
func (m *StoreLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreLabel.DiscardUnknown(m)
}

var xxx_messageInfo_StoreLabel proto.InternalMessageInfo

func (m *StoreLabel) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *StoreLabel) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type Store struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Address to handle client requests (kv, cop, etc.)
	Address string        `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	State   StoreState    `protobuf:"varint,3,opt,name=state,proto3,enum=metapb.StoreState" json:"state,omitempty"`
	Labels  []*StoreLabel `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty"`
	Version string        `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	// Address to handle peer requests (raft messages from other store).
	// Empty means same as address.
	PeerAddress string `protobuf:"bytes,6,opt,name=peer_address,json=peerAddress,proto3" json:"peer_address,omitempty"`
	// Status address provides the HTTP service for external components
	StatusAddress string `protobuf:"bytes,7,opt,name=status_address,json=statusAddress,proto3" json:"status_address,omitempty"`
	GitHash       string `protobuf:"bytes,8,opt,name=git_hash,json=gitHash,proto3" json:"git_hash,omitempty"`
	// The start timestamp of the current store
	StartTimestamp int64  `protobuf:"varint,9,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	DeployPath     string `protobuf:"bytes,10,opt,name=deploy_path,json=deployPath,proto3" json:"deploy_path,omitempty"`
	// The last heartbeat timestamp of the store.
	LastHeartbeat int64 `protobuf:"varint,11,opt,name=last_heartbeat,json=lastHeartbeat,proto3" json:"last_heartbeat,omitempty"`
	// If the store is physically destroyed, which means it can never up again.
	PhysicallyDestroyed bool `protobuf:"varint,12,opt,name=physically_destroyed,json=physicallyDestroyed,proto3" json:"physically_destroyed,omitempty"`
	// NodeState is used to replace StoreState which will be deprecated in the future.
	NodeState NodeState `protobuf:"varint,13,opt,name=node_state,json=nodeState,proto3,enum=metapb.NodeState" json:"node_state,omitempty"`
}

func (m *Store) Reset()         { *m = Store{} }
func (m *Store) String() string { return proto.CompactTextString(m) }
func (*Store) ProtoMessage()    {}
func (*Store) Descriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{2}
}
func (m *Store) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Store) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Store.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Store) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Store.Merge(m, src)
}
func (m *Store) XXX_Size() int {
	return m.Size()
}
func (m *Store) XXX_DiscardUnknown() {
	xxx_messageInfo_Store.DiscardUnknown(m)
}

var xxx_messageInfo_Store proto.InternalMessageInfo

func (m *Store) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Store) GetAddress() string {
	if m != nil {
		return m.Address
	}
	return ""
}

func (m *Store) GetState() StoreState {
	if m != nil {
		return m.State
	}
	return StoreState_Up
}

func (m *Store) GetLabels() []*StoreLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *Store) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *Store) GetPeerAddress() string {
	if m != nil {
		return m.PeerAddress
	}
	return ""
}

func (m *Store) GetStatusAddress() string {
	if m != nil {
		return m.StatusAddress
	}
	return ""
}

func (m *Store) GetGitHash() string {
	if m != nil {
		return m.GitHash
	}
	return ""
}

func (m *Store) GetStartTimestamp() int64 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *Store) GetDeployPath() string {
	if m != nil {
		return m.DeployPath
	}
	return ""
}

func (m *Store) GetLastHeartbeat() int64 {
	if m != nil {
		return m.LastHeartbeat
	}
	return 0
}

func (m *Store) GetPhysicallyDestroyed() bool {
	if m != nil {
		return m.PhysicallyDestroyed
	}
	return false
}

func (m *Store) GetNodeState() NodeState {
	if m != nil {
		return m.NodeState
	}
	return NodeState_Preparing
}

type RegionEpoch struct {
	// Conf change version, auto increment when add or remove peer
	ConfVer uint64 `protobuf:"varint,1,opt,name=conf_ver,json=confVer,proto3" json:"conf_ver,omitempty"`
	// Region version, auto increment when split or merge
	Version uint64 `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (m *RegionEpoch) Reset()         { *m = RegionEpoch{} }
func (m *RegionEpoch) String() string { return proto.CompactTextString(m) }
func (*RegionEpoch) ProtoMessage()    {}
func (*RegionEpoch) Descriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{3}
}
func (m *RegionEpoch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionEpoch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionEpoch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionEpoch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionEpoch.Merge(m, src)
}
func (m *RegionEpoch) XXX_Size() int {
	return m.Size()
}
func (m *RegionEpoch) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionEpoch.DiscardUnknown(m)
}

var xxx_messageInfo_RegionEpoch proto.InternalMessageInfo

func (m *RegionEpoch) GetConfVer() uint64 {
	if m != nil {
		return m.ConfVer
	}
	return 0
}

func (m *RegionEpoch) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type BucketStats struct {
	// total read in bytes of each bucket
	ReadBytes []uint64 `protobuf:"varint,1,rep,packed,name=read_bytes,json=readBytes,proto3" json:"read_bytes,omitempty"`
	// total write in bytes of each bucket
	WriteBytes []uint64 `protobuf:"varint,2,rep,packed,name=write_bytes,json=writeBytes,proto3" json:"write_bytes,omitempty"`
	// total read qps of each bucket
	ReadQps []uint64 `protobuf:"varint,3,rep,packed,name=read_qps,json=readQps,proto3" json:"read_qps,omitempty"`
	// total write qps of each bucket
	WriteQps []uint64 `protobuf:"varint,4,rep,packed,name=write_qps,json=writeQps,proto3" json:"write_qps,omitempty"`
	// total read keys of each bucket
	ReadKeys []uint64 `protobuf:"varint,5,rep,packed,name=read_keys,json=readKeys,proto3" json:"read_keys,omitempty"`
	// total write keys of each bucket
	WriteKeys []uint64 `protobuf:"varint,6,rep,packed,name=write_keys,json=writeKeys,proto3" json:"write_keys,omitempty"`
}

func (m *BucketStats) Reset()         { *m = BucketStats{} }
func (m *BucketStats) String() string { return proto.CompactTextString(m) }
func (*BucketStats) ProtoMessage()    {}
func (*BucketStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{4}
}
func (m *BucketStats) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BucketStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BucketStats.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BucketStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BucketStats.Merge(m, src)
}
func (m *BucketStats) XXX_Size() int {
	return m.Size()
}
func (m *BucketStats) XXX_DiscardUnknown() {
	xxx_messageInfo_BucketStats.DiscardUnknown(m)
}

var xxx_messageInfo_BucketStats proto.InternalMessageInfo

func (m *BucketStats) GetReadBytes() []uint64 {
	if m != nil {
		return m.ReadBytes
	}
	return nil
}

func (m *BucketStats) GetWriteBytes() []uint64 {
	if m != nil {
		return m.WriteBytes
	}
	return nil
}

func (m *BucketStats) GetReadQps() []uint64 {
	if m != nil {
		return m.ReadQps
	}
	return nil
}

func (m *BucketStats) GetWriteQps() []uint64 {
	if m != nil {
		return m.WriteQps
	}
	return nil
}

func (m *BucketStats) GetReadKeys() []uint64 {
	if m != nil {
		return m.ReadKeys
	}
	return nil
}

func (m *BucketStats) GetWriteKeys() []uint64 {
	if m != nil {
		return m.WriteKeys
	}
	return nil
}

type Buckets struct {
	RegionId uint64 `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	// A hint indicate if keys have changed.
	Version uint64 `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	// keys of buckets, include start/end key of region
	Keys [][]byte `protobuf:"bytes,3,rep,name=keys,proto3" json:"keys,omitempty"`
	// bucket stats
	Stats *BucketStats `protobuf:"bytes,4,opt,name=stats,proto3" json:"stats,omitempty"`
	// The period in milliseconds that stats are collected with in
	PeriodInMs uint64 `protobuf:"varint,5,opt,name=period_in_ms,json=periodInMs,proto3" json:"period_in_ms,omitempty"`
}

func (m *Buckets) Reset()         { *m = Buckets{} }
func (m *Buckets) String() string { return proto.CompactTextString(m) }
func (*Buckets) ProtoMessage()    {}
func (*Buckets) Descriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{5}
}
func (m *Buckets) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Buckets) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Buckets.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Buckets) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Buckets.Merge(m, src)
}
func (m *Buckets) XXX_Size() int {
	return m.Size()
}
func (m *Buckets) XXX_DiscardUnknown() {
	xxx_messageInfo_Buckets.DiscardUnknown(m)
}

var xxx_messageInfo_Buckets proto.InternalMessageInfo

func (m *Buckets) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *Buckets) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *Buckets) GetKeys() [][]byte {
	if m != nil {
		return m.Keys
	}
	return nil
}

func (m *Buckets) GetStats() *BucketStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

func (m *Buckets) GetPeriodInMs() uint64 {
	if m != nil {
		return m.PeriodInMs
	}
	return 0
}

type Region struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// Region key range [start_key, end_key).
	StartKey    []byte       `protobuf:"bytes,2,opt,name=start_key,json=startKey,proto3" json:"start_key,omitempty"`
	EndKey      []byte       `protobuf:"bytes,3,opt,name=end_key,json=endKey,proto3" json:"end_key,omitempty"`
	RegionEpoch *RegionEpoch `protobuf:"bytes,4,opt,name=region_epoch,json=regionEpoch,proto3" json:"region_epoch,omitempty"`
	Peers       []*Peer      `protobuf:"bytes,5,rep,name=peers,proto3" json:"peers,omitempty"`
	// Encryption metadata for start_key and end_key. encryption_meta.iv is IV for start_key.
	// IV for end_key is calculated from (encryption_meta.iv + len(start_key)).
	// The field is only used by PD and should be ignored otherwise.
	// If encryption_meta is empty (i.e. nil), it means start_key and end_key are unencrypted.
	EncryptionMeta *encryptionpb.EncryptionMeta `protobuf:"bytes,6,opt,name=encryption_meta,json=encryptionMeta,proto3" json:"encryption_meta,omitempty"`
	// The flashback state indicates whether this region is in the flashback state.
	// TODO: only check by `flashback_start_ts` in the future. Keep for compatibility now.
	IsInFlashback bool `protobuf:"varint,7,opt,name=is_in_flashback,json=isInFlashback,proto3" json:"is_in_flashback,omitempty"`
	// The start_ts that the current flashback progress is using.
	FlashbackStartTs uint64 `protobuf:"varint,8,opt,name=flashback_start_ts,json=flashbackStartTs,proto3" json:"flashback_start_ts,omitempty"`
}

func (m *Region) Reset()         { *m = Region{} }
func (m *Region) String() string { return proto.CompactTextString(m) }
func (*Region) ProtoMessage()    {}
func (*Region) Descriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{6}
}
func (m *Region) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Region) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Region.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Region) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Region.Merge(m, src)
}
func (m *Region) XXX_Size() int {
	return m.Size()
}
func (m *Region) XXX_DiscardUnknown() {
	xxx_messageInfo_Region.DiscardUnknown(m)
}

var xxx_messageInfo_Region proto.InternalMessageInfo

func (m *Region) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Region) GetStartKey() []byte {
	if m != nil {
		return m.StartKey
	}
	return nil
}

func (m *Region) GetEndKey() []byte {
	if m != nil {
		return m.EndKey
	}
	return nil
}

func (m *Region) GetRegionEpoch() *RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *Region) GetPeers() []*Peer {
	if m != nil {
		return m.Peers
	}
	return nil
}

func (m *Region) GetEncryptionMeta() *encryptionpb.EncryptionMeta {
	if m != nil {
		return m.EncryptionMeta
	}
	return nil
}

func (m *Region) GetIsInFlashback() bool {
	if m != nil {
		return m.IsInFlashback
	}
	return false
}

func (m *Region) GetFlashbackStartTs() uint64 {
	if m != nil {
		return m.FlashbackStartTs
	}
	return 0
}

type Peer struct {
	Id        uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StoreId   uint64   `protobuf:"varint,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	Role      PeerRole `protobuf:"varint,3,opt,name=role,proto3,enum=metapb.PeerRole" json:"role,omitempty"`
	IsWitness bool     `protobuf:"varint,4,opt,name=is_witness,json=isWitness,proto3" json:"is_witness,omitempty"`
}

func (m *Peer) Reset()         { *m = Peer{} }
func (m *Peer) String() string { return proto.CompactTextString(m) }
func (*Peer) ProtoMessage()    {}
func (*Peer) Descriptor() ([]byte, []int) {
	return fileDescriptor_77b4d575d5a68dda, []int{7}
}
func (m *Peer) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Peer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Peer.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Peer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Peer.Merge(m, src)
}
func (m *Peer) XXX_Size() int {
	return m.Size()
}
func (m *Peer) XXX_DiscardUnknown() {
	xxx_messageInfo_Peer.DiscardUnknown(m)
}

var xxx_messageInfo_Peer proto.InternalMessageInfo

func (m *Peer) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Peer) GetStoreId() uint64 {
	if m != nil {
		return m.StoreId
	}
	return 0
}

func (m *Peer) GetRole() PeerRole {
	if m != nil {
		return m.Role
	}
	return PeerRole_Voter
}

func (m *Peer) GetIsWitness() bool {
	if m != nil {
		return m.IsWitness
	}
	return false
}

func init() {
	proto.RegisterEnum("metapb.StoreState", StoreState_name, StoreState_value)
	proto.RegisterEnum("metapb.NodeState", NodeState_name, NodeState_value)
	proto.RegisterEnum("metapb.PeerRole", PeerRole_name, PeerRole_value)
	proto.RegisterType((*Cluster)(nil), "metapb.Cluster")
	proto.RegisterType((*StoreLabel)(nil), "metapb.StoreLabel")
	proto.RegisterType((*Store)(nil), "metapb.Store")
	proto.RegisterType((*RegionEpoch)(nil), "metapb.RegionEpoch")
	proto.RegisterType((*BucketStats)(nil), "metapb.BucketStats")
	proto.RegisterType((*Buckets)(nil), "metapb.Buckets")
	proto.RegisterType((*Region)(nil), "metapb.Region")
	proto.RegisterType((*Peer)(nil), "metapb.Peer")
}

func init() { proto.RegisterFile("metapb.proto", fileDescriptor_77b4d575d5a68dda) }

var fileDescriptor_77b4d575d5a68dda = []byte{
	// 990 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x55, 0x4d, 0x73, 0x1b, 0x45,
	0x10, 0xf5, 0xea, 0x73, 0xb7, 0x57, 0x92, 0x95, 0x49, 0xaa, 0xd8, 0x24, 0x20, 0x84, 0x8a, 0x0f,
	0xe1, 0xa2, 0x4c, 0x30, 0x14, 0x57, 0x0a, 0x27, 0xa6, 0xec, 0x4a, 0x02, 0x66, 0x1d, 0xc2, 0x71,
	0x6b, 0xa4, 0x6d, 0x4b, 0x53, 0x5a, 0xed, 0x2c, 0x33, 0x23, 0x25, 0xfa, 0x17, 0x1c, 0xb9, 0x72,
	0xe3, 0x6f, 0x70, 0x82, 0x63, 0x8e, 0x39, 0x70, 0xa0, 0xec, 0x3f, 0x42, 0x4d, 0xcf, 0xae, 0x62,
	0xc7, 0xc5, 0x49, 0xd3, 0xef, 0x75, 0xcf, 0xbe, 0x79, 0x33, 0xdd, 0x82, 0xce, 0x12, 0x0d, 0x2f,
	0x26, 0xfb, 0x85, 0x92, 0x46, 0xb2, 0x96, 0x8b, 0xee, 0x31, 0xcc, 0xa7, 0x6a, 0x53, 0x18, 0x21,
	0xf3, 0x8a, 0xbb, 0x77, 0x67, 0x26, 0x67, 0x92, 0x96, 0x9f, 0xdb, 0x55, 0x89, 0xee, 0xaa, 0x95,
	0x36, 0xb4, 0x74, 0xc0, 0xe8, 0x1b, 0x68, 0x3f, 0xcc, 0x56, 0xda, 0xa0, 0x62, 0x3d, 0xa8, 0x89,
	0x34, 0xf2, 0x86, 0xde, 0xb8, 0x11, 0xd7, 0x44, 0xca, 0x3e, 0x84, 0xde, 0x92, 0xbf, 0x4c, 0x0a,
	0x44, 0x95, 0x4c, 0xe5, 0x2a, 0x37, 0x51, 0x6d, 0xe8, 0x8d, 0xbb, 0x71, 0x67, 0xc9, 0x5f, 0x9e,
	0x22, 0xaa, 0x87, 0x16, 0x1b, 0x7d, 0x05, 0x70, 0x66, 0xa4, 0xc2, 0x27, 0x7c, 0x82, 0x19, 0xeb,
	0x43, 0x7d, 0x81, 0x1b, 0xda, 0x24, 0x88, 0xed, 0x92, 0xdd, 0x81, 0xe6, 0x9a, 0x67, 0x2b, 0xa4,
	0xe2, 0x20, 0x76, 0xc1, 0xe8, 0x9f, 0x3a, 0x34, 0xa9, 0xec, 0xc6, 0x57, 0x23, 0x68, 0xf3, 0x34,
	0x55, 0xa8, 0x75, 0x59, 0x51, 0x85, 0x6c, 0x0c, 0x4d, 0x6d, 0xb8, 0xc1, 0xa8, 0x3e, 0xf4, 0xc6,
	0xbd, 0x03, 0xb6, 0x5f, 0x7a, 0x41, 0xfb, 0x9c, 0x59, 0x26, 0x76, 0x09, 0x6c, 0x0f, 0x5a, 0x99,
	0x95, 0xa3, 0xa3, 0xc6, 0xb0, 0x3e, 0x0e, 0xdf, 0x4a, 0x25, 0xa5, 0x71, 0x99, 0x61, 0xbf, 0xb7,
	0x46, 0xa5, 0x85, 0xcc, 0xa3, 0xa6, 0xfb, 0x5e, 0x19, 0xb2, 0x0f, 0xa0, 0x43, 0x67, 0xaf, 0xe4,
	0xb4, 0x88, 0x0e, 0x2d, 0xf6, 0x6d, 0x29, 0xe9, 0x23, 0xe8, 0xd9, 0x2f, 0xae, 0xf4, 0x36, 0xa9,
	0x4d, 0x49, 0x5d, 0x87, 0x56, 0x69, 0x77, 0xc1, 0x9f, 0x09, 0x93, 0xcc, 0xb9, 0x9e, 0x47, 0xbe,
	0xfb, 0xc8, 0x4c, 0x98, 0x63, 0xae, 0xe7, 0xec, 0x13, 0xd8, 0xd5, 0x86, 0x2b, 0x93, 0x18, 0xb1,
	0x44, 0x6d, 0xf8, 0xb2, 0x88, 0x82, 0xa1, 0x37, 0xae, 0xc7, 0x3d, 0x82, 0x9f, 0x55, 0x28, 0x7b,
	0x1f, 0xc2, 0x14, 0x8b, 0x4c, 0x6e, 0x92, 0x82, 0x9b, 0x79, 0x04, 0xb4, 0x0d, 0x38, 0xe8, 0x94,
	0x9b, 0xb9, 0xd5, 0x92, 0x71, 0x6d, 0x92, 0x39, 0x72, 0x65, 0x26, 0xc8, 0x4d, 0x14, 0xd2, 0x46,
	0x5d, 0x8b, 0x1e, 0x57, 0x20, 0xfb, 0x02, 0xee, 0x14, 0xf3, 0x8d, 0x16, 0x53, 0x9e, 0x65, 0x9b,
	0x24, 0x45, 0x6d, 0x94, 0xdc, 0x60, 0x1a, 0x75, 0x86, 0xde, 0xd8, 0x8f, 0x6f, 0xbf, 0xe1, 0x1e,
	0x55, 0x14, 0x7b, 0x00, 0x90, 0xcb, 0x14, 0x13, 0xe7, 0x7e, 0x97, 0xdc, 0xbf, 0x55, 0x59, 0xfa,
	0xbd, 0x4c, 0x4b, 0xf3, 0x83, 0xbc, 0x5a, 0x8e, 0x0e, 0x21, 0x8c, 0x71, 0x26, 0x64, 0x7e, 0x54,
	0xc8, 0xe9, 0xdc, 0x9e, 0x7f, 0x2a, 0xf3, 0xf3, 0x64, 0x8d, 0xaa, 0xbc, 0xe9, 0xb6, 0x8d, 0x9f,
	0xa3, 0xba, 0x6a, 0x7f, 0xcd, 0x31, 0x65, 0x38, 0xfa, 0xd3, 0x83, 0xf0, 0x70, 0x35, 0x5d, 0xa0,
	0xb1, 0x7b, 0x6a, 0xf6, 0x1e, 0x80, 0x42, 0x9e, 0x26, 0x93, 0x8d, 0x41, 0x1d, 0x79, 0xc3, 0xfa,
	0xb8, 0x11, 0x07, 0x16, 0x39, 0xb4, 0x80, 0xf5, 0xe7, 0x85, 0x12, 0x06, 0x4b, 0xbe, 0x46, 0x3c,
	0x10, 0xe4, 0x12, 0xee, 0x82, 0x4f, 0xf5, 0xbf, 0x14, 0x3a, 0xaa, 0x13, 0xdb, 0xb6, 0xf1, 0x8f,
	0x85, 0x66, 0xf7, 0x21, 0x70, 0xb5, 0x96, 0x6b, 0x10, 0xe7, 0x13, 0x50, 0x92, 0x54, 0xb7, 0xc0,
	0x8d, 0x8e, 0x9a, 0x8e, 0xb4, 0xc0, 0x63, 0xdc, 0x90, 0x28, 0x57, 0x49, 0x6c, 0xcb, 0x89, 0x22,
	0xc4, 0xd2, 0xa3, 0xdf, 0x3d, 0x68, 0xbb, 0x33, 0x94, 0xfb, 0x58, 0x4f, 0x92, 0xed, 0x7b, 0xf7,
	0x1d, 0x70, 0x92, 0xfe, 0xbf, 0x0d, 0x8c, 0x41, 0x83, 0xf6, 0xb6, 0x92, 0x3b, 0x31, 0xad, 0xd9,
	0xa7, 0xae, 0x13, 0xac, 0x56, 0x6f, 0x1c, 0x1e, 0xdc, 0xae, 0xee, 0xe2, 0x8a, 0x5d, 0xae, 0x15,
	0x34, 0x1b, 0xda, 0x47, 0xac, 0x84, 0x4c, 0x13, 0x91, 0x27, 0x4b, 0x4d, 0x6f, 0xbc, 0x11, 0x83,
	0xc3, 0x4e, 0xf2, 0xa7, 0x7a, 0xf4, 0x57, 0x0d, 0x5a, 0xee, 0xb2, 0x6e, 0xf4, 0xe2, 0x7d, 0x08,
	0xdc, 0xe3, 0xb4, 0x3d, 0x6d, 0x75, 0x75, 0x62, 0x9f, 0x80, 0xc7, 0xb8, 0x61, 0xef, 0x40, 0x1b,
	0x73, 0xb2, 0x85, 0x1a, 0xb2, 0x13, 0xb7, 0x30, 0xb7, 0xa6, 0xb0, 0xaf, 0xa1, 0x53, 0x1e, 0x14,
	0xed, 0xed, 0xbf, 0x2d, 0xf2, 0xca, 0xc3, 0x88, 0x43, 0x75, 0xe5, 0x95, 0x8c, 0xa0, 0x69, 0x7b,
	0xcb, 0x99, 0x1c, 0x1e, 0x74, 0xaa, 0x02, 0x3b, 0x6b, 0x62, 0x47, 0xb1, 0x23, 0xd8, 0x7d, 0x33,
	0xeb, 0x12, 0x9b, 0x40, 0x6d, 0x19, 0x1e, 0xbc, 0xbb, 0x7f, 0x6d, 0x06, 0x1e, 0x6d, 0x83, 0xa7,
	0x68, 0x78, 0xdc, 0xc3, 0x6b, 0x31, 0xfb, 0x18, 0x76, 0x85, 0xb6, 0x8e, 0x9c, 0x67, 0x5c, 0xcf,
	0x27, 0x7c, 0xba, 0xa0, 0xc6, 0xf5, 0xe3, 0xae, 0xd0, 0x27, 0xf9, 0x77, 0x15, 0xc8, 0x3e, 0x03,
	0xb6, 0xcd, 0x48, 0xca, 0x3e, 0xd5, 0xd4, 0xc2, 0x8d, 0xb8, 0xbf, 0x65, 0xce, 0xa8, 0x53, 0xf5,
	0x68, 0x0d, 0x0d, 0xab, 0xf5, 0x86, 0x8d, 0x77, 0xc1, 0xd7, 0x76, 0xf0, 0xd8, 0x8b, 0x2f, 0x6f,
	0x97, 0xe2, 0x13, 0x3b, 0x63, 0x1b, 0x4a, 0x66, 0xd5, 0x48, 0xeb, 0x5f, 0x3b, 0xb2, 0xcc, 0x30,
	0x26, 0xd6, 0xbe, 0x32, 0xa1, 0x93, 0x17, 0xc2, 0xe4, 0x76, 0xc4, 0x34, 0x48, 0x69, 0x20, 0xf4,
	0xcf, 0x0e, 0xd8, 0x7b, 0x50, 0x8e, 0x60, 0xea, 0x3d, 0xd6, 0x82, 0xda, 0x4f, 0x45, 0x7f, 0x87,
	0x85, 0xd0, 0xfe, 0xe1, 0xfc, 0x3c, 0x13, 0x39, 0xf6, 0x3d, 0xd6, 0x85, 0xe0, 0x99, 0x5c, 0x4e,
	0xb4, 0x91, 0x39, 0xf6, 0x6b, 0x7b, 0x87, 0x10, 0x6c, 0xfb, 0xd6, 0x72, 0xa7, 0x0a, 0x0b, 0xae,
	0x44, 0x3e, 0x73, 0x75, 0x67, 0xa8, 0xd6, 0x36, 0xf0, 0x58, 0x07, 0xfc, 0x18, 0x97, 0x92, 0xa2,
	0x9a, 0xa5, 0x28, 0xc2, 0xb4, 0x5f, 0xdf, 0x3b, 0x06, 0xbf, 0x92, 0xc9, 0x02, 0x68, 0x3e, 0x97,
	0x06, 0x95, 0x2b, 0x7f, 0x82, 0x5c, 0xe5, 0xa8, 0xfa, 0x1e, 0xbb, 0x05, 0xdd, 0x93, 0x7c, 0x2a,
	0x97, 0x22, 0x9f, 0x39, 0xbe, 0x66, 0xa1, 0x47, 0xb8, 0x94, 0x66, 0x0b, 0xd5, 0x0f, 0x0f, 0x5e,
	0xff, 0xe1, 0x7b, 0x7f, 0x5f, 0x0c, 0xbc, 0x57, 0x17, 0x03, 0xef, 0xdf, 0x8b, 0x81, 0xf7, 0xeb,
	0xe5, 0x60, 0xe7, 0xb7, 0xcb, 0xc1, 0xce, 0xab, 0xcb, 0xc1, 0xce, 0xeb, 0xcb, 0xc1, 0x0e, 0xf4,
	0xa5, 0x9a, 0xed, 0x1b, 0xb1, 0x58, 0xef, 0x2f, 0xd6, 0xf4, 0xbf, 0x35, 0x69, 0xd1, 0xcf, 0x97,
	0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0x64, 0x78, 0x52, 0xae, 0x11, 0x07, 0x00, 0x00,
}

func (m *Cluster) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Cluster) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Cluster) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MaxPeerCount != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.MaxPeerCount))
		i--
		dAtA[i] = 0x10
	}
	if m.Id != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *StoreLabel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreLabel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreLabel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Store) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Store) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Store) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.NodeState != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.NodeState))
		i--
		dAtA[i] = 0x68
	}
	if m.PhysicallyDestroyed {
		i--
		if m.PhysicallyDestroyed {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x60
	}
	if m.LastHeartbeat != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.LastHeartbeat))
		i--
		dAtA[i] = 0x58
	}
	if len(m.DeployPath) > 0 {
		i -= len(m.DeployPath)
		copy(dAtA[i:], m.DeployPath)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.DeployPath)))
		i--
		dAtA[i] = 0x52
	}
	if m.StartTimestamp != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.StartTimestamp))
		i--
		dAtA[i] = 0x48
	}
	if len(m.GitHash) > 0 {
		i -= len(m.GitHash)
		copy(dAtA[i:], m.GitHash)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.GitHash)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.StatusAddress) > 0 {
		i -= len(m.StatusAddress)
		copy(dAtA[i:], m.StatusAddress)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.StatusAddress)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.PeerAddress) > 0 {
		i -= len(m.PeerAddress)
		copy(dAtA[i:], m.PeerAddress)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.PeerAddress)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.Version) > 0 {
		i -= len(m.Version)
		copy(dAtA[i:], m.Version)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.Version)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Labels[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintMetapb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if m.State != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.State))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Address) > 0 {
		i -= len(m.Address)
		copy(dAtA[i:], m.Address)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.Address)))
		i--
		dAtA[i] = 0x12
	}
	if m.Id != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RegionEpoch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionEpoch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionEpoch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Version != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x10
	}
	if m.ConfVer != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.ConfVer))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BucketStats) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BucketStats) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BucketStats) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.WriteKeys) > 0 {
		dAtA2 := make([]byte, len(m.WriteKeys)*10)
		var j1 int
		for _, num := range m.WriteKeys {
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		i -= j1
		copy(dAtA[i:], dAtA2[:j1])
		i = encodeVarintMetapb(dAtA, i, uint64(j1))
		i--
		dAtA[i] = 0x32
	}
	if len(m.ReadKeys) > 0 {
		dAtA4 := make([]byte, len(m.ReadKeys)*10)
		var j3 int
		for _, num := range m.ReadKeys {
			for num >= 1<<7 {
				dAtA4[j3] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j3++
			}
			dAtA4[j3] = uint8(num)
			j3++
		}
		i -= j3
		copy(dAtA[i:], dAtA4[:j3])
		i = encodeVarintMetapb(dAtA, i, uint64(j3))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.WriteQps) > 0 {
		dAtA6 := make([]byte, len(m.WriteQps)*10)
		var j5 int
		for _, num := range m.WriteQps {
			for num >= 1<<7 {
				dAtA6[j5] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j5++
			}
			dAtA6[j5] = uint8(num)
			j5++
		}
		i -= j5
		copy(dAtA[i:], dAtA6[:j5])
		i = encodeVarintMetapb(dAtA, i, uint64(j5))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ReadQps) > 0 {
		dAtA8 := make([]byte, len(m.ReadQps)*10)
		var j7 int
		for _, num := range m.ReadQps {
			for num >= 1<<7 {
				dAtA8[j7] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j7++
			}
			dAtA8[j7] = uint8(num)
			j7++
		}
		i -= j7
		copy(dAtA[i:], dAtA8[:j7])
		i = encodeVarintMetapb(dAtA, i, uint64(j7))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.WriteBytes) > 0 {
		dAtA10 := make([]byte, len(m.WriteBytes)*10)
		var j9 int
		for _, num := range m.WriteBytes {
			for num >= 1<<7 {
				dAtA10[j9] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j9++
			}
			dAtA10[j9] = uint8(num)
			j9++
		}
		i -= j9
		copy(dAtA[i:], dAtA10[:j9])
		i = encodeVarintMetapb(dAtA, i, uint64(j9))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ReadBytes) > 0 {
		dAtA12 := make([]byte, len(m.ReadBytes)*10)
		var j11 int
		for _, num := range m.ReadBytes {
			for num >= 1<<7 {
				dAtA12[j11] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j11++
			}
			dAtA12[j11] = uint8(num)
			j11++
		}
		i -= j11
		copy(dAtA[i:], dAtA12[:j11])
		i = encodeVarintMetapb(dAtA, i, uint64(j11))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Buckets) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Buckets) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Buckets) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.PeriodInMs != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.PeriodInMs))
		i--
		dAtA[i] = 0x28
	}
	if m.Stats != nil {
		{
			size, err := m.Stats.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMetapb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.Keys) > 0 {
		for iNdEx := len(m.Keys) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Keys[iNdEx])
			copy(dAtA[i:], m.Keys[iNdEx])
			i = encodeVarintMetapb(dAtA, i, uint64(len(m.Keys[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Version != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x10
	}
	if m.RegionId != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Region) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Region) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Region) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.FlashbackStartTs != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.FlashbackStartTs))
		i--
		dAtA[i] = 0x40
	}
	if m.IsInFlashback {
		i--
		if m.IsInFlashback {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x38
	}
	if m.EncryptionMeta != nil {
		{
			size, err := m.EncryptionMeta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMetapb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if len(m.Peers) > 0 {
		for iNdEx := len(m.Peers) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Peers[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintMetapb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.RegionEpoch != nil {
		{
			size, err := m.RegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintMetapb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.EndKey) > 0 {
		i -= len(m.EndKey)
		copy(dAtA[i:], m.EndKey)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.EndKey)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.StartKey) > 0 {
		i -= len(m.StartKey)
		copy(dAtA[i:], m.StartKey)
		i = encodeVarintMetapb(dAtA, i, uint64(len(m.StartKey)))
		i--
		dAtA[i] = 0x12
	}
	if m.Id != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Peer) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Peer) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Peer) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsWitness {
		i--
		if m.IsWitness {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if m.Role != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.Role))
		i--
		dAtA[i] = 0x18
	}
	if m.StoreId != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.StoreId))
		i--
		dAtA[i] = 0x10
	}
	if m.Id != 0 {
		i = encodeVarintMetapb(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintMetapb(dAtA []byte, offset int, v uint64) int {
	offset -= sovMetapb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Cluster) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovMetapb(uint64(m.Id))
	}
	if m.MaxPeerCount != 0 {
		n += 1 + sovMetapb(uint64(m.MaxPeerCount))
	}
	return n
}

func (m *StoreLabel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	return n
}

func (m *Store) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovMetapb(uint64(m.Id))
	}
	l = len(m.Address)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	if m.State != 0 {
		n += 1 + sovMetapb(uint64(m.State))
	}
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovMetapb(uint64(l))
		}
	}
	l = len(m.Version)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	l = len(m.PeerAddress)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	l = len(m.StatusAddress)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	l = len(m.GitHash)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	if m.StartTimestamp != 0 {
		n += 1 + sovMetapb(uint64(m.StartTimestamp))
	}
	l = len(m.DeployPath)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	if m.LastHeartbeat != 0 {
		n += 1 + sovMetapb(uint64(m.LastHeartbeat))
	}
	if m.PhysicallyDestroyed {
		n += 2
	}
	if m.NodeState != 0 {
		n += 1 + sovMetapb(uint64(m.NodeState))
	}
	return n
}

func (m *RegionEpoch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ConfVer != 0 {
		n += 1 + sovMetapb(uint64(m.ConfVer))
	}
	if m.Version != 0 {
		n += 1 + sovMetapb(uint64(m.Version))
	}
	return n
}

func (m *BucketStats) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ReadBytes) > 0 {
		l = 0
		for _, e := range m.ReadBytes {
			l += sovMetapb(uint64(e))
		}
		n += 1 + sovMetapb(uint64(l)) + l
	}
	if len(m.WriteBytes) > 0 {
		l = 0
		for _, e := range m.WriteBytes {
			l += sovMetapb(uint64(e))
		}
		n += 1 + sovMetapb(uint64(l)) + l
	}
	if len(m.ReadQps) > 0 {
		l = 0
		for _, e := range m.ReadQps {
			l += sovMetapb(uint64(e))
		}
		n += 1 + sovMetapb(uint64(l)) + l
	}
	if len(m.WriteQps) > 0 {
		l = 0
		for _, e := range m.WriteQps {
			l += sovMetapb(uint64(e))
		}
		n += 1 + sovMetapb(uint64(l)) + l
	}
	if len(m.ReadKeys) > 0 {
		l = 0
		for _, e := range m.ReadKeys {
			l += sovMetapb(uint64(e))
		}
		n += 1 + sovMetapb(uint64(l)) + l
	}
	if len(m.WriteKeys) > 0 {
		l = 0
		for _, e := range m.WriteKeys {
			l += sovMetapb(uint64(e))
		}
		n += 1 + sovMetapb(uint64(l)) + l
	}
	return n
}

func (m *Buckets) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovMetapb(uint64(m.RegionId))
	}
	if m.Version != 0 {
		n += 1 + sovMetapb(uint64(m.Version))
	}
	if len(m.Keys) > 0 {
		for _, b := range m.Keys {
			l = len(b)
			n += 1 + l + sovMetapb(uint64(l))
		}
	}
	if m.Stats != nil {
		l = m.Stats.Size()
		n += 1 + l + sovMetapb(uint64(l))
	}
	if m.PeriodInMs != 0 {
		n += 1 + sovMetapb(uint64(m.PeriodInMs))
	}
	return n
}

func (m *Region) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovMetapb(uint64(m.Id))
	}
	l = len(m.StartKey)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	l = len(m.EndKey)
	if l > 0 {
		n += 1 + l + sovMetapb(uint64(l))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovMetapb(uint64(l))
	}
	if len(m.Peers) > 0 {
		for _, e := range m.Peers {
			l = e.Size()
			n += 1 + l + sovMetapb(uint64(l))
		}
	}
	if m.EncryptionMeta != nil {
		l = m.EncryptionMeta.Size()
		n += 1 + l + sovMetapb(uint64(l))
	}
	if m.IsInFlashback {
		n += 2
	}
	if m.FlashbackStartTs != 0 {
		n += 1 + sovMetapb(uint64(m.FlashbackStartTs))
	}
	return n
}

func (m *Peer) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovMetapb(uint64(m.Id))
	}
	if m.StoreId != 0 {
		n += 1 + sovMetapb(uint64(m.StoreId))
	}
	if m.Role != 0 {
		n += 1 + sovMetapb(uint64(m.Role))
	}
	if m.IsWitness {
		n += 2
	}
	return n
}

func sovMetapb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozMetapb(x uint64) (n int) {
	return sovMetapb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Cluster) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Cluster: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Cluster: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxPeerCount", wireType)
			}
			m.MaxPeerCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxPeerCount |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMetapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMetapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreLabel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreLabel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreLabel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMetapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMetapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Store) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Store: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Store: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Address = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= StoreState(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, &StoreLabel{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Version = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeerAddress", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PeerAddress = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StatusAddress", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StatusAddress = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GitHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GitHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimestamp", wireType)
			}
			m.StartTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestamp |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeployPath", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeployPath = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastHeartbeat", wireType)
			}
			m.LastHeartbeat = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastHeartbeat |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PhysicallyDestroyed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PhysicallyDestroyed = bool(v != 0)
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NodeState", wireType)
			}
			m.NodeState = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NodeState |= NodeState(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMetapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMetapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionEpoch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionEpoch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionEpoch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConfVer", wireType)
			}
			m.ConfVer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConfVer |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMetapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMetapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BucketStats) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BucketStats: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BucketStats: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ReadBytes = append(m.ReadBytes, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMetapb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthMetapb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.ReadBytes) == 0 {
					m.ReadBytes = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMetapb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ReadBytes = append(m.ReadBytes, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadBytes", wireType)
			}
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.WriteBytes = append(m.WriteBytes, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMetapb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthMetapb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.WriteBytes) == 0 {
					m.WriteBytes = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMetapb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.WriteBytes = append(m.WriteBytes, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field WriteBytes", wireType)
			}
		case 3:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ReadQps = append(m.ReadQps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMetapb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthMetapb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.ReadQps) == 0 {
					m.ReadQps = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMetapb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ReadQps = append(m.ReadQps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadQps", wireType)
			}
		case 4:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.WriteQps = append(m.WriteQps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMetapb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthMetapb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.WriteQps) == 0 {
					m.WriteQps = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMetapb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.WriteQps = append(m.WriteQps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field WriteQps", wireType)
			}
		case 5:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ReadKeys = append(m.ReadKeys, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMetapb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthMetapb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.ReadKeys) == 0 {
					m.ReadKeys = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMetapb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ReadKeys = append(m.ReadKeys, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadKeys", wireType)
			}
		case 6:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.WriteKeys = append(m.WriteKeys, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMetapb
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMetapb
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthMetapb
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.WriteKeys) == 0 {
					m.WriteKeys = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMetapb
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.WriteKeys = append(m.WriteKeys, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field WriteKeys", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMetapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMetapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Buckets) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Buckets: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Buckets: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keys", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Keys = append(m.Keys, make([]byte, postIndex-iNdEx))
			copy(m.Keys[len(m.Keys)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Stats", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Stats == nil {
				m.Stats = &BucketStats{}
			}
			if err := m.Stats.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeriodInMs", wireType)
			}
			m.PeriodInMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeriodInMs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMetapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMetapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Region) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Region: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Region: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartKey = append(m.StartKey[:0], dAtA[iNdEx:postIndex]...)
			if m.StartKey == nil {
				m.StartKey = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndKey = append(m.EndKey[:0], dAtA[iNdEx:postIndex]...)
			if m.EndKey == nil {
				m.EndKey = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peers", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Peers = append(m.Peers, &Peer{})
			if err := m.Peers[len(m.Peers)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EncryptionMeta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMetapb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthMetapb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EncryptionMeta == nil {
				m.EncryptionMeta = &encryptionpb.EncryptionMeta{}
			}
			if err := m.EncryptionMeta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsInFlashback", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsInFlashback = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlashbackStartTs", wireType)
			}
			m.FlashbackStartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FlashbackStartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMetapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMetapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Peer) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Peer: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Peer: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			m.StoreId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Role", wireType)
			}
			m.Role = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Role |= PeerRole(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsWitness", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsWitness = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMetapb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthMetapb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipMetapb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMetapb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMetapb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthMetapb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupMetapb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthMetapb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthMetapb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMetapb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupMetapb = fmt.Errorf("proto: unexpected end of group")
)
