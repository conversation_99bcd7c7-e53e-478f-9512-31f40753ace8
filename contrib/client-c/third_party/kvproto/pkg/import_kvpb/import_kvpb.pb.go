// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: import_kvpb.proto

package import_kvpb

import (
	"context"
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	import_sstpb "github.com/pingcap/kvproto/pkg/import_sstpb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type Mutation_OP int32

const (
	Mutation_Put Mutation_OP = 0
)

var Mutation_OP_name = map[int32]string{
	0: "Put",
}

var Mutation_OP_value = map[string]int32{
	"Put": 0,
}

func (x Mutation_OP) String() string {
	return proto.EnumName(Mutation_OP_name, int32(x))
}

func (Mutation_OP) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{5, 0}
}

type SwitchModeRequest struct {
	PdAddr  string                          `protobuf:"bytes,1,opt,name=pd_addr,json=pdAddr,proto3" json:"pd_addr,omitempty"`
	Request *import_sstpb.SwitchModeRequest `protobuf:"bytes,2,opt,name=request,proto3" json:"request,omitempty"`
}

func (m *SwitchModeRequest) Reset()         { *m = SwitchModeRequest{} }
func (m *SwitchModeRequest) String() string { return proto.CompactTextString(m) }
func (*SwitchModeRequest) ProtoMessage()    {}
func (*SwitchModeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{0}
}
func (m *SwitchModeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SwitchModeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SwitchModeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SwitchModeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchModeRequest.Merge(m, src)
}
func (m *SwitchModeRequest) XXX_Size() int {
	return m.Size()
}
func (m *SwitchModeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchModeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchModeRequest proto.InternalMessageInfo

func (m *SwitchModeRequest) GetPdAddr() string {
	if m != nil {
		return m.PdAddr
	}
	return ""
}

func (m *SwitchModeRequest) GetRequest() *import_sstpb.SwitchModeRequest {
	if m != nil {
		return m.Request
	}
	return nil
}

type SwitchModeResponse struct {
}

func (m *SwitchModeResponse) Reset()         { *m = SwitchModeResponse{} }
func (m *SwitchModeResponse) String() string { return proto.CompactTextString(m) }
func (*SwitchModeResponse) ProtoMessage()    {}
func (*SwitchModeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{1}
}
func (m *SwitchModeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SwitchModeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SwitchModeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SwitchModeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchModeResponse.Merge(m, src)
}
func (m *SwitchModeResponse) XXX_Size() int {
	return m.Size()
}
func (m *SwitchModeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchModeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchModeResponse proto.InternalMessageInfo

type OpenEngineRequest struct {
	Uuid      []byte `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	KeyPrefix []byte `protobuf:"bytes,2,opt,name=key_prefix,json=keyPrefix,proto3" json:"key_prefix,omitempty"`
}

func (m *OpenEngineRequest) Reset()         { *m = OpenEngineRequest{} }
func (m *OpenEngineRequest) String() string { return proto.CompactTextString(m) }
func (*OpenEngineRequest) ProtoMessage()    {}
func (*OpenEngineRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{2}
}
func (m *OpenEngineRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OpenEngineRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OpenEngineRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OpenEngineRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenEngineRequest.Merge(m, src)
}
func (m *OpenEngineRequest) XXX_Size() int {
	return m.Size()
}
func (m *OpenEngineRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenEngineRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OpenEngineRequest proto.InternalMessageInfo

func (m *OpenEngineRequest) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

func (m *OpenEngineRequest) GetKeyPrefix() []byte {
	if m != nil {
		return m.KeyPrefix
	}
	return nil
}

type OpenEngineResponse struct {
}

func (m *OpenEngineResponse) Reset()         { *m = OpenEngineResponse{} }
func (m *OpenEngineResponse) String() string { return proto.CompactTextString(m) }
func (*OpenEngineResponse) ProtoMessage()    {}
func (*OpenEngineResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{3}
}
func (m *OpenEngineResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OpenEngineResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OpenEngineResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OpenEngineResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenEngineResponse.Merge(m, src)
}
func (m *OpenEngineResponse) XXX_Size() int {
	return m.Size()
}
func (m *OpenEngineResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenEngineResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OpenEngineResponse proto.InternalMessageInfo

type WriteHead struct {
	Uuid []byte `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (m *WriteHead) Reset()         { *m = WriteHead{} }
func (m *WriteHead) String() string { return proto.CompactTextString(m) }
func (*WriteHead) ProtoMessage()    {}
func (*WriteHead) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{4}
}
func (m *WriteHead) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteHead) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteHead.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteHead) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteHead.Merge(m, src)
}
func (m *WriteHead) XXX_Size() int {
	return m.Size()
}
func (m *WriteHead) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteHead.DiscardUnknown(m)
}

var xxx_messageInfo_WriteHead proto.InternalMessageInfo

func (m *WriteHead) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

type Mutation struct {
	Op    Mutation_OP `protobuf:"varint,1,opt,name=op,proto3,enum=import_kvpb.Mutation_OP" json:"op,omitempty"`
	Key   []byte      `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Value []byte      `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *Mutation) Reset()         { *m = Mutation{} }
func (m *Mutation) String() string { return proto.CompactTextString(m) }
func (*Mutation) ProtoMessage()    {}
func (*Mutation) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{5}
}
func (m *Mutation) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Mutation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Mutation.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Mutation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Mutation.Merge(m, src)
}
func (m *Mutation) XXX_Size() int {
	return m.Size()
}
func (m *Mutation) XXX_DiscardUnknown() {
	xxx_messageInfo_Mutation.DiscardUnknown(m)
}

var xxx_messageInfo_Mutation proto.InternalMessageInfo

func (m *Mutation) GetOp() Mutation_OP {
	if m != nil {
		return m.Op
	}
	return Mutation_Put
}

func (m *Mutation) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *Mutation) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type WriteBatch struct {
	CommitTs  uint64      `protobuf:"varint,1,opt,name=commit_ts,json=commitTs,proto3" json:"commit_ts,omitempty"`
	Mutations []*Mutation `protobuf:"bytes,2,rep,name=mutations,proto3" json:"mutations,omitempty"`
}

func (m *WriteBatch) Reset()         { *m = WriteBatch{} }
func (m *WriteBatch) String() string { return proto.CompactTextString(m) }
func (*WriteBatch) ProtoMessage()    {}
func (*WriteBatch) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{6}
}
func (m *WriteBatch) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteBatch) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteBatch.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteBatch) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteBatch.Merge(m, src)
}
func (m *WriteBatch) XXX_Size() int {
	return m.Size()
}
func (m *WriteBatch) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteBatch.DiscardUnknown(m)
}

var xxx_messageInfo_WriteBatch proto.InternalMessageInfo

func (m *WriteBatch) GetCommitTs() uint64 {
	if m != nil {
		return m.CommitTs
	}
	return 0
}

func (m *WriteBatch) GetMutations() []*Mutation {
	if m != nil {
		return m.Mutations
	}
	return nil
}

type WriteEngineRequest struct {
	// Types that are valid to be assigned to Chunk:
	//	*WriteEngineRequest_Head
	//	*WriteEngineRequest_Batch
	Chunk isWriteEngineRequest_Chunk `protobuf_oneof:"chunk"`
}

func (m *WriteEngineRequest) Reset()         { *m = WriteEngineRequest{} }
func (m *WriteEngineRequest) String() string { return proto.CompactTextString(m) }
func (*WriteEngineRequest) ProtoMessage()    {}
func (*WriteEngineRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{7}
}
func (m *WriteEngineRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteEngineRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteEngineRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteEngineRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteEngineRequest.Merge(m, src)
}
func (m *WriteEngineRequest) XXX_Size() int {
	return m.Size()
}
func (m *WriteEngineRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteEngineRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WriteEngineRequest proto.InternalMessageInfo

type isWriteEngineRequest_Chunk interface {
	isWriteEngineRequest_Chunk()
	MarshalTo([]byte) (int, error)
	Size() int
}

type WriteEngineRequest_Head struct {
	Head *WriteHead `protobuf:"bytes,1,opt,name=head,proto3,oneof" json:"head,omitempty"`
}
type WriteEngineRequest_Batch struct {
	Batch *WriteBatch `protobuf:"bytes,2,opt,name=batch,proto3,oneof" json:"batch,omitempty"`
}

func (*WriteEngineRequest_Head) isWriteEngineRequest_Chunk()  {}
func (*WriteEngineRequest_Batch) isWriteEngineRequest_Chunk() {}

func (m *WriteEngineRequest) GetChunk() isWriteEngineRequest_Chunk {
	if m != nil {
		return m.Chunk
	}
	return nil
}

func (m *WriteEngineRequest) GetHead() *WriteHead {
	if x, ok := m.GetChunk().(*WriteEngineRequest_Head); ok {
		return x.Head
	}
	return nil
}

func (m *WriteEngineRequest) GetBatch() *WriteBatch {
	if x, ok := m.GetChunk().(*WriteEngineRequest_Batch); ok {
		return x.Batch
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*WriteEngineRequest) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*WriteEngineRequest_Head)(nil),
		(*WriteEngineRequest_Batch)(nil),
	}
}

type KVPair struct {
	Key   []byte `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *KVPair) Reset()         { *m = KVPair{} }
func (m *KVPair) String() string { return proto.CompactTextString(m) }
func (*KVPair) ProtoMessage()    {}
func (*KVPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{8}
}
func (m *KVPair) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KVPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KVPair.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KVPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KVPair.Merge(m, src)
}
func (m *KVPair) XXX_Size() int {
	return m.Size()
}
func (m *KVPair) XXX_DiscardUnknown() {
	xxx_messageInfo_KVPair.DiscardUnknown(m)
}

var xxx_messageInfo_KVPair proto.InternalMessageInfo

func (m *KVPair) GetKey() []byte {
	if m != nil {
		return m.Key
	}
	return nil
}

func (m *KVPair) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type WriteEngineV3Request struct {
	Uuid     []byte    `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	CommitTs uint64    `protobuf:"varint,2,opt,name=commit_ts,json=commitTs,proto3" json:"commit_ts,omitempty"`
	Pairs    []*KVPair `protobuf:"bytes,3,rep,name=pairs,proto3" json:"pairs,omitempty"`
}

func (m *WriteEngineV3Request) Reset()         { *m = WriteEngineV3Request{} }
func (m *WriteEngineV3Request) String() string { return proto.CompactTextString(m) }
func (*WriteEngineV3Request) ProtoMessage()    {}
func (*WriteEngineV3Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{9}
}
func (m *WriteEngineV3Request) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteEngineV3Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteEngineV3Request.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteEngineV3Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteEngineV3Request.Merge(m, src)
}
func (m *WriteEngineV3Request) XXX_Size() int {
	return m.Size()
}
func (m *WriteEngineV3Request) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteEngineV3Request.DiscardUnknown(m)
}

var xxx_messageInfo_WriteEngineV3Request proto.InternalMessageInfo

func (m *WriteEngineV3Request) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

func (m *WriteEngineV3Request) GetCommitTs() uint64 {
	if m != nil {
		return m.CommitTs
	}
	return 0
}

func (m *WriteEngineV3Request) GetPairs() []*KVPair {
	if m != nil {
		return m.Pairs
	}
	return nil
}

type WriteEngineResponse struct {
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *WriteEngineResponse) Reset()         { *m = WriteEngineResponse{} }
func (m *WriteEngineResponse) String() string { return proto.CompactTextString(m) }
func (*WriteEngineResponse) ProtoMessage()    {}
func (*WriteEngineResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{10}
}
func (m *WriteEngineResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteEngineResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteEngineResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteEngineResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteEngineResponse.Merge(m, src)
}
func (m *WriteEngineResponse) XXX_Size() int {
	return m.Size()
}
func (m *WriteEngineResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteEngineResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WriteEngineResponse proto.InternalMessageInfo

func (m *WriteEngineResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type CloseEngineRequest struct {
	Uuid []byte `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (m *CloseEngineRequest) Reset()         { *m = CloseEngineRequest{} }
func (m *CloseEngineRequest) String() string { return proto.CompactTextString(m) }
func (*CloseEngineRequest) ProtoMessage()    {}
func (*CloseEngineRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{11}
}
func (m *CloseEngineRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CloseEngineRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CloseEngineRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CloseEngineRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseEngineRequest.Merge(m, src)
}
func (m *CloseEngineRequest) XXX_Size() int {
	return m.Size()
}
func (m *CloseEngineRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseEngineRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CloseEngineRequest proto.InternalMessageInfo

func (m *CloseEngineRequest) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

type CloseEngineResponse struct {
	Error *Error `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
}

func (m *CloseEngineResponse) Reset()         { *m = CloseEngineResponse{} }
func (m *CloseEngineResponse) String() string { return proto.CompactTextString(m) }
func (*CloseEngineResponse) ProtoMessage()    {}
func (*CloseEngineResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{12}
}
func (m *CloseEngineResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CloseEngineResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CloseEngineResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CloseEngineResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseEngineResponse.Merge(m, src)
}
func (m *CloseEngineResponse) XXX_Size() int {
	return m.Size()
}
func (m *CloseEngineResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseEngineResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CloseEngineResponse proto.InternalMessageInfo

func (m *CloseEngineResponse) GetError() *Error {
	if m != nil {
		return m.Error
	}
	return nil
}

type ImportEngineRequest struct {
	Uuid   []byte `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	PdAddr string `protobuf:"bytes,2,opt,name=pd_addr,json=pdAddr,proto3" json:"pd_addr,omitempty"`
}

func (m *ImportEngineRequest) Reset()         { *m = ImportEngineRequest{} }
func (m *ImportEngineRequest) String() string { return proto.CompactTextString(m) }
func (*ImportEngineRequest) ProtoMessage()    {}
func (*ImportEngineRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{13}
}
func (m *ImportEngineRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportEngineRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportEngineRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportEngineRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportEngineRequest.Merge(m, src)
}
func (m *ImportEngineRequest) XXX_Size() int {
	return m.Size()
}
func (m *ImportEngineRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportEngineRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ImportEngineRequest proto.InternalMessageInfo

func (m *ImportEngineRequest) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

func (m *ImportEngineRequest) GetPdAddr() string {
	if m != nil {
		return m.PdAddr
	}
	return ""
}

type ImportEngineResponse struct {
}

func (m *ImportEngineResponse) Reset()         { *m = ImportEngineResponse{} }
func (m *ImportEngineResponse) String() string { return proto.CompactTextString(m) }
func (*ImportEngineResponse) ProtoMessage()    {}
func (*ImportEngineResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{14}
}
func (m *ImportEngineResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ImportEngineResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ImportEngineResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ImportEngineResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImportEngineResponse.Merge(m, src)
}
func (m *ImportEngineResponse) XXX_Size() int {
	return m.Size()
}
func (m *ImportEngineResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ImportEngineResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ImportEngineResponse proto.InternalMessageInfo

type CleanupEngineRequest struct {
	Uuid []byte `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (m *CleanupEngineRequest) Reset()         { *m = CleanupEngineRequest{} }
func (m *CleanupEngineRequest) String() string { return proto.CompactTextString(m) }
func (*CleanupEngineRequest) ProtoMessage()    {}
func (*CleanupEngineRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{15}
}
func (m *CleanupEngineRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CleanupEngineRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CleanupEngineRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CleanupEngineRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanupEngineRequest.Merge(m, src)
}
func (m *CleanupEngineRequest) XXX_Size() int {
	return m.Size()
}
func (m *CleanupEngineRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanupEngineRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CleanupEngineRequest proto.InternalMessageInfo

func (m *CleanupEngineRequest) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

type CleanupEngineResponse struct {
}

func (m *CleanupEngineResponse) Reset()         { *m = CleanupEngineResponse{} }
func (m *CleanupEngineResponse) String() string { return proto.CompactTextString(m) }
func (*CleanupEngineResponse) ProtoMessage()    {}
func (*CleanupEngineResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{16}
}
func (m *CleanupEngineResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CleanupEngineResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CleanupEngineResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CleanupEngineResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CleanupEngineResponse.Merge(m, src)
}
func (m *CleanupEngineResponse) XXX_Size() int {
	return m.Size()
}
func (m *CleanupEngineResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CleanupEngineResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CleanupEngineResponse proto.InternalMessageInfo

type CompactClusterRequest struct {
	PdAddr  string                       `protobuf:"bytes,1,opt,name=pd_addr,json=pdAddr,proto3" json:"pd_addr,omitempty"`
	Request *import_sstpb.CompactRequest `protobuf:"bytes,2,opt,name=request,proto3" json:"request,omitempty"`
}

func (m *CompactClusterRequest) Reset()         { *m = CompactClusterRequest{} }
func (m *CompactClusterRequest) String() string { return proto.CompactTextString(m) }
func (*CompactClusterRequest) ProtoMessage()    {}
func (*CompactClusterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{17}
}
func (m *CompactClusterRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactClusterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactClusterRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CompactClusterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactClusterRequest.Merge(m, src)
}
func (m *CompactClusterRequest) XXX_Size() int {
	return m.Size()
}
func (m *CompactClusterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactClusterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CompactClusterRequest proto.InternalMessageInfo

func (m *CompactClusterRequest) GetPdAddr() string {
	if m != nil {
		return m.PdAddr
	}
	return ""
}

func (m *CompactClusterRequest) GetRequest() *import_sstpb.CompactRequest {
	if m != nil {
		return m.Request
	}
	return nil
}

type CompactClusterResponse struct {
}

func (m *CompactClusterResponse) Reset()         { *m = CompactClusterResponse{} }
func (m *CompactClusterResponse) String() string { return proto.CompactTextString(m) }
func (*CompactClusterResponse) ProtoMessage()    {}
func (*CompactClusterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{18}
}
func (m *CompactClusterResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CompactClusterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CompactClusterResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CompactClusterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompactClusterResponse.Merge(m, src)
}
func (m *CompactClusterResponse) XXX_Size() int {
	return m.Size()
}
func (m *CompactClusterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CompactClusterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CompactClusterResponse proto.InternalMessageInfo

type GetVersionRequest struct {
}

func (m *GetVersionRequest) Reset()         { *m = GetVersionRequest{} }
func (m *GetVersionRequest) String() string { return proto.CompactTextString(m) }
func (*GetVersionRequest) ProtoMessage()    {}
func (*GetVersionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{19}
}
func (m *GetVersionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetVersionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetVersionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetVersionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVersionRequest.Merge(m, src)
}
func (m *GetVersionRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetVersionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVersionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetVersionRequest proto.InternalMessageInfo

type GetVersionResponse struct {
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Commit  string `protobuf:"bytes,2,opt,name=commit,proto3" json:"commit,omitempty"`
}

func (m *GetVersionResponse) Reset()         { *m = GetVersionResponse{} }
func (m *GetVersionResponse) String() string { return proto.CompactTextString(m) }
func (*GetVersionResponse) ProtoMessage()    {}
func (*GetVersionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{20}
}
func (m *GetVersionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetVersionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetVersionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetVersionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVersionResponse.Merge(m, src)
}
func (m *GetVersionResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetVersionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVersionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetVersionResponse proto.InternalMessageInfo

func (m *GetVersionResponse) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

func (m *GetVersionResponse) GetCommit() string {
	if m != nil {
		return m.Commit
	}
	return ""
}

type GetMetricsRequest struct {
}

func (m *GetMetricsRequest) Reset()         { *m = GetMetricsRequest{} }
func (m *GetMetricsRequest) String() string { return proto.CompactTextString(m) }
func (*GetMetricsRequest) ProtoMessage()    {}
func (*GetMetricsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{21}
}
func (m *GetMetricsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMetricsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMetricsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetMetricsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMetricsRequest.Merge(m, src)
}
func (m *GetMetricsRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetMetricsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMetricsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMetricsRequest proto.InternalMessageInfo

type GetMetricsResponse struct {
	Prometheus string `protobuf:"bytes,1,opt,name=prometheus,proto3" json:"prometheus,omitempty"`
}

func (m *GetMetricsResponse) Reset()         { *m = GetMetricsResponse{} }
func (m *GetMetricsResponse) String() string { return proto.CompactTextString(m) }
func (*GetMetricsResponse) ProtoMessage()    {}
func (*GetMetricsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{22}
}
func (m *GetMetricsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetMetricsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetMetricsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetMetricsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMetricsResponse.Merge(m, src)
}
func (m *GetMetricsResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetMetricsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMetricsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMetricsResponse proto.InternalMessageInfo

func (m *GetMetricsResponse) GetPrometheus() string {
	if m != nil {
		return m.Prometheus
	}
	return ""
}

type Error struct {
	// This can happen if the client hasn't opened the engine, or the server
	// restarts while the client is writing or closing. An unclosed engine will
	// be removed on server restart, so the client should not continue but
	// restart the previous job in that case.
	EngineNotFound *Error_EngineNotFound `protobuf:"bytes,1,opt,name=engine_not_found,json=engineNotFound,proto3" json:"engine_not_found,omitempty"`
}

func (m *Error) Reset()         { *m = Error{} }
func (m *Error) String() string { return proto.CompactTextString(m) }
func (*Error) ProtoMessage()    {}
func (*Error) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{23}
}
func (m *Error) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error.Merge(m, src)
}
func (m *Error) XXX_Size() int {
	return m.Size()
}
func (m *Error) XXX_DiscardUnknown() {
	xxx_messageInfo_Error.DiscardUnknown(m)
}

var xxx_messageInfo_Error proto.InternalMessageInfo

func (m *Error) GetEngineNotFound() *Error_EngineNotFound {
	if m != nil {
		return m.EngineNotFound
	}
	return nil
}

type Error_EngineNotFound struct {
	Uuid []byte `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
}

func (m *Error_EngineNotFound) Reset()         { *m = Error_EngineNotFound{} }
func (m *Error_EngineNotFound) String() string { return proto.CompactTextString(m) }
func (*Error_EngineNotFound) ProtoMessage()    {}
func (*Error_EngineNotFound) Descriptor() ([]byte, []int) {
	return fileDescriptor_638ccc00b4358d93, []int{23, 0}
}
func (m *Error_EngineNotFound) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Error_EngineNotFound) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Error_EngineNotFound.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Error_EngineNotFound) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Error_EngineNotFound.Merge(m, src)
}
func (m *Error_EngineNotFound) XXX_Size() int {
	return m.Size()
}
func (m *Error_EngineNotFound) XXX_DiscardUnknown() {
	xxx_messageInfo_Error_EngineNotFound.DiscardUnknown(m)
}

var xxx_messageInfo_Error_EngineNotFound proto.InternalMessageInfo

func (m *Error_EngineNotFound) GetUuid() []byte {
	if m != nil {
		return m.Uuid
	}
	return nil
}

func init() {
	proto.RegisterEnum("import_kvpb.Mutation_OP", Mutation_OP_name, Mutation_OP_value)
	proto.RegisterType((*SwitchModeRequest)(nil), "import_kvpb.SwitchModeRequest")
	proto.RegisterType((*SwitchModeResponse)(nil), "import_kvpb.SwitchModeResponse")
	proto.RegisterType((*OpenEngineRequest)(nil), "import_kvpb.OpenEngineRequest")
	proto.RegisterType((*OpenEngineResponse)(nil), "import_kvpb.OpenEngineResponse")
	proto.RegisterType((*WriteHead)(nil), "import_kvpb.WriteHead")
	proto.RegisterType((*Mutation)(nil), "import_kvpb.Mutation")
	proto.RegisterType((*WriteBatch)(nil), "import_kvpb.WriteBatch")
	proto.RegisterType((*WriteEngineRequest)(nil), "import_kvpb.WriteEngineRequest")
	proto.RegisterType((*KVPair)(nil), "import_kvpb.KVPair")
	proto.RegisterType((*WriteEngineV3Request)(nil), "import_kvpb.WriteEngineV3Request")
	proto.RegisterType((*WriteEngineResponse)(nil), "import_kvpb.WriteEngineResponse")
	proto.RegisterType((*CloseEngineRequest)(nil), "import_kvpb.CloseEngineRequest")
	proto.RegisterType((*CloseEngineResponse)(nil), "import_kvpb.CloseEngineResponse")
	proto.RegisterType((*ImportEngineRequest)(nil), "import_kvpb.ImportEngineRequest")
	proto.RegisterType((*ImportEngineResponse)(nil), "import_kvpb.ImportEngineResponse")
	proto.RegisterType((*CleanupEngineRequest)(nil), "import_kvpb.CleanupEngineRequest")
	proto.RegisterType((*CleanupEngineResponse)(nil), "import_kvpb.CleanupEngineResponse")
	proto.RegisterType((*CompactClusterRequest)(nil), "import_kvpb.CompactClusterRequest")
	proto.RegisterType((*CompactClusterResponse)(nil), "import_kvpb.CompactClusterResponse")
	proto.RegisterType((*GetVersionRequest)(nil), "import_kvpb.GetVersionRequest")
	proto.RegisterType((*GetVersionResponse)(nil), "import_kvpb.GetVersionResponse")
	proto.RegisterType((*GetMetricsRequest)(nil), "import_kvpb.GetMetricsRequest")
	proto.RegisterType((*GetMetricsResponse)(nil), "import_kvpb.GetMetricsResponse")
	proto.RegisterType((*Error)(nil), "import_kvpb.Error")
	proto.RegisterType((*Error_EngineNotFound)(nil), "import_kvpb.Error.EngineNotFound")
}

func init() { proto.RegisterFile("import_kvpb.proto", fileDescriptor_638ccc00b4358d93) }

var fileDescriptor_638ccc00b4358d93 = []byte{
	// 897 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0xc1, 0x6e, 0xeb, 0x44,
	0x14, 0xb5, 0x9d, 0x26, 0x69, 0x6e, 0xda, 0xd2, 0x4e, 0xd3, 0xd6, 0x0a, 0xe0, 0xb6, 0x86, 0x45,
	0x40, 0x28, 0x0f, 0xa5, 0x08, 0x89, 0x15, 0xa2, 0xd5, 0x2b, 0x45, 0x55, 0x69, 0x64, 0x1e, 0x01,
	0x09, 0x89, 0xc8, 0x8d, 0xe7, 0x25, 0x96, 0x1b, 0x8f, 0x19, 0x8f, 0x03, 0x45, 0x7c, 0x04, 0x4b,
	0x3e, 0x81, 0x4f, 0x61, 0xf9, 0x96, 0x5d, 0x21, 0xd4, 0xfc, 0x08, 0xf2, 0x78, 0x9c, 0x78, 0x6c,
	0x37, 0x81, 0x55, 0x67, 0xee, 0x1c, 0x9f, 0x7b, 0x73, 0x66, 0xce, 0x51, 0x61, 0xcf, 0x9d, 0x06,
	0x84, 0xb2, 0xa1, 0x37, 0x0b, 0xee, 0xba, 0x01, 0x25, 0x8c, 0xa0, 0x66, 0xa6, 0xd4, 0x46, 0x62,
	0x13, 0x86, 0x2c, 0x05, 0xb4, 0x5b, 0x63, 0x32, 0x26, 0x7c, 0xf9, 0x22, 0x5e, 0x89, 0xea, 0x5b,
	0x34, 0x0a, 0x19, 0x5f, 0x26, 0x05, 0x73, 0x0c, 0x7b, 0xdf, 0xfc, 0xec, 0xb2, 0xd1, 0xe4, 0x86,
	0x38, 0xd8, 0xc2, 0x3f, 0x45, 0x38, 0x64, 0xe8, 0x08, 0xea, 0x81, 0x33, 0xb4, 0x1d, 0x87, 0xea,
	0xea, 0x89, 0xda, 0x69, 0x58, 0xb5, 0xc0, 0xf9, 0xc2, 0x71, 0x28, 0xfa, 0x0c, 0xea, 0x34, 0xc1,
	0xe8, 0xda, 0x89, 0xda, 0x69, 0xf6, 0x8e, 0xbb, 0x52, 0xeb, 0x02, 0x95, 0x95, 0xe2, 0xcd, 0x16,
	0xa0, 0xec, 0x69, 0x18, 0x10, 0x3f, 0xc4, 0xe6, 0x25, 0xec, 0xdd, 0x06, 0xd8, 0x7f, 0xe9, 0x8f,
	0x5d, 0x7f, 0xd1, 0x1e, 0xc1, 0x46, 0x14, 0xb9, 0x0e, 0xef, 0xbd, 0x65, 0xf1, 0x35, 0x7a, 0x17,
	0xc0, 0xc3, 0x0f, 0xc3, 0x80, 0xe2, 0xd7, 0xee, 0x2f, 0xbc, 0xf9, 0x96, 0xd5, 0xf0, 0xf0, 0x43,
	0x9f, 0x17, 0x62, 0xf6, 0x2c, 0x8f, 0x60, 0x3f, 0x86, 0xc6, 0x77, 0xd4, 0x65, 0xf8, 0x0a, 0xdb,
	0x4e, 0x19, 0xab, 0xe9, 0xc1, 0xe6, 0x4d, 0xc4, 0x6c, 0xe6, 0x12, 0x1f, 0x75, 0x40, 0x23, 0x01,
	0x3f, 0xdd, 0xe9, 0xe9, 0xdd, 0xac, 0xe2, 0x29, 0xa4, 0x7b, 0xdb, 0xb7, 0x34, 0x12, 0xa0, 0x5d,
	0xa8, 0x78, 0xf8, 0x41, 0x0c, 0x11, 0x2f, 0x51, 0x0b, 0xaa, 0x33, 0xfb, 0x3e, 0xc2, 0x7a, 0x85,
	0xd7, 0x92, 0x8d, 0xb9, 0x0d, 0xda, 0x6d, 0x1f, 0xd5, 0xa1, 0xd2, 0x8f, 0xd8, 0xae, 0x62, 0xfe,
	0x08, 0xc0, 0xa7, 0x39, 0xb7, 0xd9, 0x68, 0x82, 0xde, 0x86, 0xc6, 0x88, 0x4c, 0xa7, 0x2e, 0x1b,
	0xb2, 0x90, 0x77, 0xdd, 0xb0, 0x36, 0x93, 0xc2, 0xab, 0x10, 0x9d, 0x41, 0x63, 0x2a, 0x9a, 0x86,
	0xba, 0x76, 0x52, 0xe9, 0x34, 0x7b, 0x07, 0xa5, 0x23, 0x59, 0x4b, 0x9c, 0xf9, 0x1b, 0x20, 0xce,
	0x2f, 0x8b, 0xf9, 0x11, 0x6c, 0x4c, 0xb0, 0x9d, 0xfc, 0xec, 0x66, 0xef, 0x50, 0x62, 0x59, 0x88,
	0x73, 0xa5, 0x58, 0x1c, 0x85, 0x5e, 0x40, 0xf5, 0x2e, 0x1e, 0x4f, 0x5c, 0xef, 0x51, 0x11, 0xce,
	0xa7, 0xbf, 0x52, 0xac, 0x04, 0x77, 0x5e, 0x87, 0xea, 0x68, 0x12, 0xf9, 0x9e, 0xf9, 0x31, 0xd4,
	0xae, 0x07, 0x7d, 0xdb, 0xa5, 0xa9, 0x3c, 0x6a, 0x89, 0x3c, 0x5a, 0x56, 0x1e, 0x0a, 0xad, 0xcc,
	0xbc, 0x83, 0xb3, 0x55, 0xd7, 0x2f, 0xa9, 0xa5, 0xe5, 0xd4, 0xfa, 0x00, 0xaa, 0x81, 0xed, 0xd2,
	0x50, 0xaf, 0x70, 0xa5, 0xf6, 0xa5, 0xa1, 0x93, 0xa1, 0xac, 0x04, 0x61, 0x7e, 0x0e, 0xfb, 0x92,
	0x46, 0xc9, 0x43, 0x41, 0x1d, 0xa8, 0x62, 0x4a, 0x09, 0x15, 0x2a, 0x21, 0x89, 0xe1, 0x65, 0x7c,
	0x62, 0x25, 0x00, 0xb3, 0x03, 0xe8, 0xe2, 0x9e, 0x84, 0x78, 0xed, 0x8b, 0x8d, 0x5b, 0x49, 0xc8,
	0xff, 0xdd, 0xea, 0x1c, 0xf6, 0xbf, 0xe2, 0x67, 0xeb, 0xdd, 0x91, 0x31, 0xac, 0x96, 0x35, 0xac,
	0x79, 0x08, 0x2d, 0x99, 0x43, 0x38, 0xe3, 0x43, 0x68, 0x5d, 0xdc, 0x63, 0xdb, 0x8f, 0x82, 0xf5,
	0x3f, 0xe4, 0x08, 0x0e, 0x72, 0x58, 0x41, 0x32, 0x81, 0x83, 0x0b, 0x32, 0x0d, 0xec, 0x11, 0xbb,
	0xb8, 0x8f, 0x42, 0x86, 0xe9, 0xda, 0xfc, 0xf8, 0x34, 0x9f, 0x1f, 0xef, 0xc8, 0xf9, 0x21, 0xe8,
	0x0a, 0xe1, 0xa1, 0xc3, 0x61, 0xbe, 0x93, 0x98, 0x61, 0x1f, 0xf6, 0xbe, 0xc4, 0x6c, 0x80, 0x69,
	0x18, 0xbb, 0x41, 0xc0, 0x2f, 0x01, 0x65, 0x8b, 0x42, 0x79, 0x1d, 0xea, 0xb3, 0xa4, 0x24, 0xa6,
	0x4a, 0xb7, 0xe8, 0x10, 0x6a, 0xc9, 0x63, 0x4a, 0xd5, 0x4b, 0x76, 0x82, 0xfc, 0x06, 0x33, 0xea,
	0x8e, 0xc2, 0x94, 0xfc, 0x13, 0x4e, 0xbe, 0x28, 0x0a, 0x72, 0x03, 0x20, 0xa0, 0x64, 0x8a, 0xd9,
	0x04, 0x47, 0xa1, 0xe0, 0xcf, 0x54, 0xcc, 0x5f, 0xa1, 0xca, 0x2f, 0x17, 0x5d, 0xc3, 0x2e, 0xe6,
	0x32, 0x0e, 0x7d, 0xc2, 0x86, 0xaf, 0x49, 0xe4, 0xa7, 0xde, 0x3c, 0x2d, 0x3e, 0x85, 0x6e, 0xa2,
	0xf8, 0xd7, 0x84, 0x5d, 0xc6, 0x40, 0x6b, 0x07, 0x4b, 0xfb, 0xf6, 0xfb, 0xb0, 0x23, 0x23, 0xca,
	0x2e, 0xb0, 0xf7, 0x77, 0x0d, 0x36, 0x93, 0x57, 0x70, 0x3d, 0x40, 0xb7, 0x00, 0xcb, 0x1c, 0x46,
	0x86, 0xd4, 0xb3, 0x10, 0xdf, 0xed, 0xe3, 0x67, 0xcf, 0x85, 0xfe, 0x4a, 0x4c, 0xb8, 0x8c, 0xde,
	0x1c, 0x61, 0x21, 0xdb, 0x73, 0x84, 0x25, 0x99, 0xad, 0xa0, 0x57, 0xd0, 0xcc, 0x78, 0x14, 0x1d,
	0x17, 0x33, 0x48, 0xa6, 0x3c, 0x79, 0x1e, 0x90, 0x72, 0x76, 0x54, 0x34, 0x80, 0x6d, 0x29, 0x6d,
	0xd0, 0xe9, 0x73, 0x9f, 0x2d, 0x92, 0xe8, 0xbf, 0x30, 0x23, 0x0b, 0x9a, 0x19, 0x9b, 0xe7, 0xa6,
	0x2d, 0x46, 0x45, 0x8e, 0xb3, 0x24, 0x21, 0x4c, 0x05, 0x7d, 0x0b, 0x5b, 0x59, 0xd7, 0x22, 0xf9,
	0x9b, 0x92, 0x50, 0x68, 0x9f, 0xae, 0x40, 0x2c, 0x68, 0xbf, 0x87, 0x6d, 0xc9, 0xc8, 0x39, 0x09,
	0xca, 0x02, 0xa1, 0x6d, 0xae, 0x82, 0x2c, 0x98, 0x7f, 0x80, 0x1d, 0xd9, 0x9f, 0x28, 0xf7, 0x5d,
	0x59, 0x4c, 0xb4, 0xdf, 0x5b, 0x89, 0xc9, 0x3e, 0xb0, 0xa5, 0x9b, 0x73, 0x0f, 0xac, 0xe0, 0xfd,
	0xdc, 0x03, 0x2b, 0xc6, 0xc0, 0x82, 0x50, 0x38, 0xb8, 0x48, 0x28, 0xfb, 0xbd, 0x48, 0x98, 0xb3,
	0xbe, 0xa9, 0x9c, 0xf7, 0x1e, 0xff, 0xdc, 0x54, 0xff, 0x7a, 0x32, 0xd4, 0x37, 0x4f, 0x86, 0xfa,
	0xcf, 0x93, 0xa1, 0xfe, 0x3e, 0x37, 0x94, 0x3f, 0xe6, 0x86, 0xf2, 0x66, 0x6e, 0x28, 0x8f, 0x73,
	0x43, 0x81, 0x5d, 0x42, 0xc7, 0x5d, 0xe6, 0x7a, 0xb3, 0xae, 0x37, 0xe3, 0xff, 0x78, 0xdd, 0xd5,
	0xf8, 0x9f, 0xb3, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x8c, 0xd3, 0x12, 0xda, 0xdc, 0x09, 0x00,
	0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ImportKVClient is the client API for ImportKV service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ImportKVClient interface {
	// Switch the target cluster to normal/import mode.
	SwitchMode(ctx context.Context, in *SwitchModeRequest, opts ...grpc.CallOption) (*SwitchModeResponse, error)
	// Open an engine.
	OpenEngine(ctx context.Context, in *OpenEngineRequest, opts ...grpc.CallOption) (*OpenEngineResponse, error)
	// Open a write stream to the engine.
	WriteEngine(ctx context.Context, opts ...grpc.CallOption) (ImportKV_WriteEngineClient, error)
	// Write to engine, single message version
	WriteEngineV3(ctx context.Context, in *WriteEngineV3Request, opts ...grpc.CallOption) (*WriteEngineResponse, error)
	// Close the engine.
	CloseEngine(ctx context.Context, in *CloseEngineRequest, opts ...grpc.CallOption) (*CloseEngineResponse, error)
	// Import the engine to the target cluster.
	ImportEngine(ctx context.Context, in *ImportEngineRequest, opts ...grpc.CallOption) (*ImportEngineResponse, error)
	// Clean up the engine.
	CleanupEngine(ctx context.Context, in *CleanupEngineRequest, opts ...grpc.CallOption) (*CleanupEngineResponse, error)
	// Compact the target cluster for better performance.
	CompactCluster(ctx context.Context, in *CompactClusterRequest, opts ...grpc.CallOption) (*CompactClusterResponse, error)
	// Get current version and commit hash
	GetVersion(ctx context.Context, in *GetVersionRequest, opts ...grpc.CallOption) (*GetVersionResponse, error)
	// Get importer metrics
	GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error)
}

type importKVClient struct {
	cc *grpc.ClientConn
}

func NewImportKVClient(cc *grpc.ClientConn) ImportKVClient {
	return &importKVClient{cc}
}

func (c *importKVClient) SwitchMode(ctx context.Context, in *SwitchModeRequest, opts ...grpc.CallOption) (*SwitchModeResponse, error) {
	out := new(SwitchModeResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/SwitchMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importKVClient) OpenEngine(ctx context.Context, in *OpenEngineRequest, opts ...grpc.CallOption) (*OpenEngineResponse, error) {
	out := new(OpenEngineResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/OpenEngine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importKVClient) WriteEngine(ctx context.Context, opts ...grpc.CallOption) (ImportKV_WriteEngineClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ImportKV_serviceDesc.Streams[0], "/import_kvpb.ImportKV/WriteEngine", opts...)
	if err != nil {
		return nil, err
	}
	x := &importKVWriteEngineClient{stream}
	return x, nil
}

type ImportKV_WriteEngineClient interface {
	Send(*WriteEngineRequest) error
	CloseAndRecv() (*WriteEngineResponse, error)
	grpc.ClientStream
}

type importKVWriteEngineClient struct {
	grpc.ClientStream
}

func (x *importKVWriteEngineClient) Send(m *WriteEngineRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *importKVWriteEngineClient) CloseAndRecv() (*WriteEngineResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(WriteEngineResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *importKVClient) WriteEngineV3(ctx context.Context, in *WriteEngineV3Request, opts ...grpc.CallOption) (*WriteEngineResponse, error) {
	out := new(WriteEngineResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/WriteEngineV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importKVClient) CloseEngine(ctx context.Context, in *CloseEngineRequest, opts ...grpc.CallOption) (*CloseEngineResponse, error) {
	out := new(CloseEngineResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/CloseEngine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importKVClient) ImportEngine(ctx context.Context, in *ImportEngineRequest, opts ...grpc.CallOption) (*ImportEngineResponse, error) {
	out := new(ImportEngineResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/ImportEngine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importKVClient) CleanupEngine(ctx context.Context, in *CleanupEngineRequest, opts ...grpc.CallOption) (*CleanupEngineResponse, error) {
	out := new(CleanupEngineResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/CleanupEngine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importKVClient) CompactCluster(ctx context.Context, in *CompactClusterRequest, opts ...grpc.CallOption) (*CompactClusterResponse, error) {
	out := new(CompactClusterResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/CompactCluster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importKVClient) GetVersion(ctx context.Context, in *GetVersionRequest, opts ...grpc.CallOption) (*GetVersionResponse, error) {
	out := new(GetVersionResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/GetVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *importKVClient) GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error) {
	out := new(GetMetricsResponse)
	err := c.cc.Invoke(ctx, "/import_kvpb.ImportKV/GetMetrics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ImportKVServer is the server API for ImportKV service.
type ImportKVServer interface {
	// Switch the target cluster to normal/import mode.
	SwitchMode(context.Context, *SwitchModeRequest) (*SwitchModeResponse, error)
	// Open an engine.
	OpenEngine(context.Context, *OpenEngineRequest) (*OpenEngineResponse, error)
	// Open a write stream to the engine.
	WriteEngine(ImportKV_WriteEngineServer) error
	// Write to engine, single message version
	WriteEngineV3(context.Context, *WriteEngineV3Request) (*WriteEngineResponse, error)
	// Close the engine.
	CloseEngine(context.Context, *CloseEngineRequest) (*CloseEngineResponse, error)
	// Import the engine to the target cluster.
	ImportEngine(context.Context, *ImportEngineRequest) (*ImportEngineResponse, error)
	// Clean up the engine.
	CleanupEngine(context.Context, *CleanupEngineRequest) (*CleanupEngineResponse, error)
	// Compact the target cluster for better performance.
	CompactCluster(context.Context, *CompactClusterRequest) (*CompactClusterResponse, error)
	// Get current version and commit hash
	GetVersion(context.Context, *GetVersionRequest) (*GetVersionResponse, error)
	// Get importer metrics
	GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error)
}

// UnimplementedImportKVServer can be embedded to have forward compatible implementations.
type UnimplementedImportKVServer struct {
}

func (*UnimplementedImportKVServer) SwitchMode(ctx context.Context, req *SwitchModeRequest) (*SwitchModeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SwitchMode not implemented")
}
func (*UnimplementedImportKVServer) OpenEngine(ctx context.Context, req *OpenEngineRequest) (*OpenEngineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OpenEngine not implemented")
}
func (*UnimplementedImportKVServer) WriteEngine(srv ImportKV_WriteEngineServer) error {
	return status.Errorf(codes.Unimplemented, "method WriteEngine not implemented")
}
func (*UnimplementedImportKVServer) WriteEngineV3(ctx context.Context, req *WriteEngineV3Request) (*WriteEngineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteEngineV3 not implemented")
}
func (*UnimplementedImportKVServer) CloseEngine(ctx context.Context, req *CloseEngineRequest) (*CloseEngineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseEngine not implemented")
}
func (*UnimplementedImportKVServer) ImportEngine(ctx context.Context, req *ImportEngineRequest) (*ImportEngineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportEngine not implemented")
}
func (*UnimplementedImportKVServer) CleanupEngine(ctx context.Context, req *CleanupEngineRequest) (*CleanupEngineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanupEngine not implemented")
}
func (*UnimplementedImportKVServer) CompactCluster(ctx context.Context, req *CompactClusterRequest) (*CompactClusterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompactCluster not implemented")
}
func (*UnimplementedImportKVServer) GetVersion(ctx context.Context, req *GetVersionRequest) (*GetVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersion not implemented")
}
func (*UnimplementedImportKVServer) GetMetrics(ctx context.Context, req *GetMetricsRequest) (*GetMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetrics not implemented")
}

func RegisterImportKVServer(s *grpc.Server, srv ImportKVServer) {
	s.RegisterService(&_ImportKV_serviceDesc, srv)
}

func _ImportKV_SwitchMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchModeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).SwitchMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/SwitchMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).SwitchMode(ctx, req.(*SwitchModeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportKV_OpenEngine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenEngineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).OpenEngine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/OpenEngine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).OpenEngine(ctx, req.(*OpenEngineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportKV_WriteEngine_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ImportKVServer).WriteEngine(&importKVWriteEngineServer{stream})
}

type ImportKV_WriteEngineServer interface {
	SendAndClose(*WriteEngineResponse) error
	Recv() (*WriteEngineRequest, error)
	grpc.ServerStream
}

type importKVWriteEngineServer struct {
	grpc.ServerStream
}

func (x *importKVWriteEngineServer) SendAndClose(m *WriteEngineResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *importKVWriteEngineServer) Recv() (*WriteEngineRequest, error) {
	m := new(WriteEngineRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _ImportKV_WriteEngineV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteEngineV3Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).WriteEngineV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/WriteEngineV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).WriteEngineV3(ctx, req.(*WriteEngineV3Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportKV_CloseEngine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseEngineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).CloseEngine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/CloseEngine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).CloseEngine(ctx, req.(*CloseEngineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportKV_ImportEngine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportEngineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).ImportEngine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/ImportEngine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).ImportEngine(ctx, req.(*ImportEngineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportKV_CleanupEngine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanupEngineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).CleanupEngine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/CleanupEngine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).CleanupEngine(ctx, req.(*CleanupEngineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportKV_CompactCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompactClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).CompactCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/CompactCluster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).CompactCluster(ctx, req.(*CompactClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportKV_GetVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).GetVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/GetVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).GetVersion(ctx, req.(*GetVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ImportKV_GetMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImportKVServer).GetMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/import_kvpb.ImportKV/GetMetrics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImportKVServer).GetMetrics(ctx, req.(*GetMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ImportKV_serviceDesc = grpc.ServiceDesc{
	ServiceName: "import_kvpb.ImportKV",
	HandlerType: (*ImportKVServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SwitchMode",
			Handler:    _ImportKV_SwitchMode_Handler,
		},
		{
			MethodName: "OpenEngine",
			Handler:    _ImportKV_OpenEngine_Handler,
		},
		{
			MethodName: "WriteEngineV3",
			Handler:    _ImportKV_WriteEngineV3_Handler,
		},
		{
			MethodName: "CloseEngine",
			Handler:    _ImportKV_CloseEngine_Handler,
		},
		{
			MethodName: "ImportEngine",
			Handler:    _ImportKV_ImportEngine_Handler,
		},
		{
			MethodName: "CleanupEngine",
			Handler:    _ImportKV_CleanupEngine_Handler,
		},
		{
			MethodName: "CompactCluster",
			Handler:    _ImportKV_CompactCluster_Handler,
		},
		{
			MethodName: "GetVersion",
			Handler:    _ImportKV_GetVersion_Handler,
		},
		{
			MethodName: "GetMetrics",
			Handler:    _ImportKV_GetMetrics_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "WriteEngine",
			Handler:       _ImportKV_WriteEngine_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "import_kvpb.proto",
}

func (m *SwitchModeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SwitchModeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SwitchModeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Request != nil {
		{
			size, err := m.Request.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportKvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.PdAddr) > 0 {
		i -= len(m.PdAddr)
		copy(dAtA[i:], m.PdAddr)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.PdAddr)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SwitchModeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SwitchModeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SwitchModeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *OpenEngineRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenEngineRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OpenEngineRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.KeyPrefix) > 0 {
		i -= len(m.KeyPrefix)
		copy(dAtA[i:], m.KeyPrefix)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.KeyPrefix)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *OpenEngineResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenEngineResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OpenEngineResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *WriteHead) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteHead) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteHead) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Mutation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Mutation) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Mutation) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x12
	}
	if m.Op != 0 {
		i = encodeVarintImportKvpb(dAtA, i, uint64(m.Op))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *WriteBatch) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteBatch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteBatch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Mutations) > 0 {
		for iNdEx := len(m.Mutations) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Mutations[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportKvpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.CommitTs != 0 {
		i = encodeVarintImportKvpb(dAtA, i, uint64(m.CommitTs))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *WriteEngineRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteEngineRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteEngineRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Chunk != nil {
		{
			size := m.Chunk.Size()
			i -= size
			if _, err := m.Chunk.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *WriteEngineRequest_Head) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteEngineRequest_Head) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Head != nil {
		{
			size, err := m.Head.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportKvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *WriteEngineRequest_Batch) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteEngineRequest_Batch) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Batch != nil {
		{
			size, err := m.Batch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportKvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *KVPair) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KVPair) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KVPair) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WriteEngineV3Request) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteEngineV3Request) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteEngineV3Request) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Pairs) > 0 {
		for iNdEx := len(m.Pairs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Pairs[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintImportKvpb(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.CommitTs != 0 {
		i = encodeVarintImportKvpb(dAtA, i, uint64(m.CommitTs))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WriteEngineResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteEngineResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteEngineResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportKvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CloseEngineRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CloseEngineRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CloseEngineRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CloseEngineResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CloseEngineResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CloseEngineResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportKvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ImportEngineRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportEngineRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportEngineRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.PdAddr) > 0 {
		i -= len(m.PdAddr)
		copy(dAtA[i:], m.PdAddr)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.PdAddr)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ImportEngineResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImportEngineResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ImportEngineResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *CleanupEngineRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanupEngineRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CleanupEngineRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CleanupEngineResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanupEngineResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CleanupEngineResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *CompactClusterRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactClusterRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CompactClusterRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Request != nil {
		{
			size, err := m.Request.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportKvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.PdAddr) > 0 {
		i -= len(m.PdAddr)
		copy(dAtA[i:], m.PdAddr)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.PdAddr)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CompactClusterResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CompactClusterResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CompactClusterResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetVersionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetVersionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetVersionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetVersionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetVersionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetVersionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Commit) > 0 {
		i -= len(m.Commit)
		copy(dAtA[i:], m.Commit)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Commit)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Version) > 0 {
		i -= len(m.Version)
		copy(dAtA[i:], m.Version)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Version)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetMetricsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMetricsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetMetricsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *GetMetricsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMetricsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetMetricsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Prometheus) > 0 {
		i -= len(m.Prometheus)
		copy(dAtA[i:], m.Prometheus)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Prometheus)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Error) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.EngineNotFound != nil {
		{
			size, err := m.EngineNotFound.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintImportKvpb(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Error_EngineNotFound) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Error_EngineNotFound) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Error_EngineNotFound) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Uuid) > 0 {
		i -= len(m.Uuid)
		copy(dAtA[i:], m.Uuid)
		i = encodeVarintImportKvpb(dAtA, i, uint64(len(m.Uuid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintImportKvpb(dAtA []byte, offset int, v uint64) int {
	offset -= sovImportKvpb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *SwitchModeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PdAddr)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	if m.Request != nil {
		l = m.Request.Size()
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *SwitchModeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *OpenEngineRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	l = len(m.KeyPrefix)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *OpenEngineResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *WriteHead) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *Mutation) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Op != 0 {
		n += 1 + sovImportKvpb(uint64(m.Op))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *WriteBatch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CommitTs != 0 {
		n += 1 + sovImportKvpb(uint64(m.CommitTs))
	}
	if len(m.Mutations) > 0 {
		for _, e := range m.Mutations {
			l = e.Size()
			n += 1 + l + sovImportKvpb(uint64(l))
		}
	}
	return n
}

func (m *WriteEngineRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Chunk != nil {
		n += m.Chunk.Size()
	}
	return n
}

func (m *WriteEngineRequest_Head) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Head != nil {
		l = m.Head.Size()
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}
func (m *WriteEngineRequest_Batch) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Batch != nil {
		l = m.Batch.Size()
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}
func (m *KVPair) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *WriteEngineV3Request) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	if m.CommitTs != 0 {
		n += 1 + sovImportKvpb(uint64(m.CommitTs))
	}
	if len(m.Pairs) > 0 {
		for _, e := range m.Pairs {
			l = e.Size()
			n += 1 + l + sovImportKvpb(uint64(l))
		}
	}
	return n
}

func (m *WriteEngineResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *CloseEngineRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *CloseEngineResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *ImportEngineRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	l = len(m.PdAddr)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *ImportEngineResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *CleanupEngineRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *CleanupEngineResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *CompactClusterRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.PdAddr)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	if m.Request != nil {
		l = m.Request.Size()
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *CompactClusterResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetVersionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetVersionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Version)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	l = len(m.Commit)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *GetMetricsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *GetMetricsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Prometheus)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *Error) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.EngineNotFound != nil {
		l = m.EngineNotFound.Size()
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func (m *Error_EngineNotFound) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Uuid)
	if l > 0 {
		n += 1 + l + sovImportKvpb(uint64(l))
	}
	return n
}

func sovImportKvpb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozImportKvpb(x uint64) (n int) {
	return sovImportKvpb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SwitchModeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SwitchModeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SwitchModeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PdAddr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PdAddr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Request", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Request == nil {
				m.Request = &import_sstpb.SwitchModeRequest{}
			}
			if err := m.Request.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SwitchModeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SwitchModeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SwitchModeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenEngineRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OpenEngineRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OpenEngineRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyPrefix", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeyPrefix = append(m.KeyPrefix[:0], dAtA[iNdEx:postIndex]...)
			if m.KeyPrefix == nil {
				m.KeyPrefix = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenEngineResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OpenEngineResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OpenEngineResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteHead) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteHead: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteHead: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Mutation) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Mutation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Mutation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Op", wireType)
			}
			m.Op = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Op |= Mutation_OP(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteBatch) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteBatch: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteBatch: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitTs", wireType)
			}
			m.CommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mutations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Mutations = append(m.Mutations, &Mutation{})
			if err := m.Mutations[len(m.Mutations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteEngineRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteEngineRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteEngineRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Head", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &WriteHead{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &WriteEngineRequest_Head{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Batch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &WriteBatch{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Chunk = &WriteEngineRequest_Batch{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *KVPair) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KVPair: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KVPair: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteEngineV3Request) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteEngineV3Request: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteEngineV3Request: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommitTs", wireType)
			}
			m.CommitTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommitTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Pairs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Pairs = append(m.Pairs, &KVPair{})
			if err := m.Pairs[len(m.Pairs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteEngineResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteEngineResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteEngineResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CloseEngineRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CloseEngineRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CloseEngineRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CloseEngineResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CloseEngineResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CloseEngineResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &Error{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportEngineRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportEngineRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportEngineRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PdAddr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PdAddr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImportEngineResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ImportEngineResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ImportEngineResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanupEngineRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CleanupEngineRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CleanupEngineRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanupEngineResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CleanupEngineResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CleanupEngineResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactClusterRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactClusterRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactClusterRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PdAddr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PdAddr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Request", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Request == nil {
				m.Request = &import_sstpb.CompactRequest{}
			}
			if err := m.Request.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CompactClusterResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CompactClusterResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CompactClusterResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetVersionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetVersionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetVersionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetVersionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetVersionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetVersionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Version = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commit", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Commit = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMetricsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMetricsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMetricsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMetricsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetMetricsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetMetricsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Prometheus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Prometheus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Error: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Error: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EngineNotFound", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EngineNotFound == nil {
				m.EngineNotFound = &Error_EngineNotFound{}
			}
			if err := m.EngineNotFound.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Error_EngineNotFound) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EngineNotFound: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EngineNotFound: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Uuid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthImportKvpb
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Uuid = append(m.Uuid[:0], dAtA[iNdEx:postIndex]...)
			if m.Uuid == nil {
				m.Uuid = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImportKvpb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthImportKvpb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipImportKvpb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowImportKvpb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowImportKvpb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthImportKvpb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupImportKvpb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthImportKvpb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthImportKvpb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowImportKvpb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupImportKvpb = fmt.Errorf("proto: unexpected end of group")
)
