// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: coprocessor.proto

package coprocessor

import (
	"fmt"
	"io"
	"math"
	math_bits "math/bits"

	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/golang/protobuf/proto"
	errorpb "github.com/pingcap/kvproto/pkg/errorpb"
	kvrpcpb "github.com/pingcap/kvproto/pkg/kvrpcpb"
	metapb "github.com/pingcap/kvproto/pkg/metapb"
	github_com_pingcap_kvproto_pkg_sharedbytes "github.com/pingcap/kvproto/pkg/sharedbytes"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// [start, end)
type KeyRange struct {
	Start []byte `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End   []byte `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (m *KeyRange) Reset()         { *m = KeyRange{} }
func (m *KeyRange) String() string { return proto.CompactTextString(m) }
func (*KeyRange) ProtoMessage()    {}
func (*KeyRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{0}
}
func (m *KeyRange) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *KeyRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_KeyRange.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *KeyRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyRange.Merge(m, src)
}
func (m *KeyRange) XXX_Size() int {
	return m.Size()
}
func (m *KeyRange) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyRange.DiscardUnknown(m)
}

var xxx_messageInfo_KeyRange proto.InternalMessageInfo

func (m *KeyRange) GetStart() []byte {
	if m != nil {
		return m.Start
	}
	return nil
}

func (m *KeyRange) GetEnd() []byte {
	if m != nil {
		return m.End
	}
	return nil
}

type Request struct {
	Context *kvrpcpb.Context `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	Tp      int64            `protobuf:"varint,2,opt,name=tp,proto3" json:"tp,omitempty"`
	Data    []byte           `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	StartTs uint64           `protobuf:"varint,7,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	Ranges  []*KeyRange      `protobuf:"bytes,4,rep,name=ranges,proto3" json:"ranges,omitempty"`
	// If cache is enabled, TiKV returns cache hit instead of data if
	// its last version matches this `cache_if_match_version`.
	IsCacheEnabled      bool   `protobuf:"varint,5,opt,name=is_cache_enabled,json=isCacheEnabled,proto3" json:"is_cache_enabled,omitempty"`
	CacheIfMatchVersion uint64 `protobuf:"varint,6,opt,name=cache_if_match_version,json=cacheIfMatchVersion,proto3" json:"cache_if_match_version,omitempty"`
	// Any schema-ful storage to validate schema correctness if necessary.
	SchemaVer      int64 `protobuf:"varint,8,opt,name=schema_ver,json=schemaVer,proto3" json:"schema_ver,omitempty"`
	IsTraceEnabled bool  `protobuf:"varint,9,opt,name=is_trace_enabled,json=isTraceEnabled,proto3" json:"is_trace_enabled,omitempty"`
	// paging_size is 0 when it's disabled, otherwise, it should be a positive number.
	PagingSize uint64 `protobuf:"varint,10,opt,name=paging_size,json=pagingSize,proto3" json:"paging_size,omitempty"`
	// tasks stores the batched coprocessor tasks sent to the same tikv store.
	Tasks           []*StoreBatchTask  `protobuf:"bytes,11,rep,name=tasks,proto3" json:"tasks,omitempty"`
	ConnectionId    uint64             `protobuf:"varint,12,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
	ConnectionAlias string             `protobuf:"bytes,13,opt,name=connection_alias,json=connectionAlias,proto3" json:"connection_alias,omitempty"`
	TableShardInfos []*TableShardInfos `protobuf:"bytes,14,rep,name=table_shard_infos,json=tableShardInfos,proto3" json:"table_shard_infos,omitempty"`
}

func (m *Request) Reset()         { *m = Request{} }
func (m *Request) String() string { return proto.CompactTextString(m) }
func (*Request) ProtoMessage()    {}
func (*Request) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{1}
}
func (m *Request) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Request) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Request.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Request) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Request.Merge(m, src)
}
func (m *Request) XXX_Size() int {
	return m.Size()
}
func (m *Request) XXX_DiscardUnknown() {
	xxx_messageInfo_Request.DiscardUnknown(m)
}

var xxx_messageInfo_Request proto.InternalMessageInfo

func (m *Request) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *Request) GetTp() int64 {
	if m != nil {
		return m.Tp
	}
	return 0
}

func (m *Request) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *Request) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *Request) GetRanges() []*KeyRange {
	if m != nil {
		return m.Ranges
	}
	return nil
}

func (m *Request) GetIsCacheEnabled() bool {
	if m != nil {
		return m.IsCacheEnabled
	}
	return false
}

func (m *Request) GetCacheIfMatchVersion() uint64 {
	if m != nil {
		return m.CacheIfMatchVersion
	}
	return 0
}

func (m *Request) GetSchemaVer() int64 {
	if m != nil {
		return m.SchemaVer
	}
	return 0
}

func (m *Request) GetIsTraceEnabled() bool {
	if m != nil {
		return m.IsTraceEnabled
	}
	return false
}

func (m *Request) GetPagingSize() uint64 {
	if m != nil {
		return m.PagingSize
	}
	return 0
}

func (m *Request) GetTasks() []*StoreBatchTask {
	if m != nil {
		return m.Tasks
	}
	return nil
}

func (m *Request) GetConnectionId() uint64 {
	if m != nil {
		return m.ConnectionId
	}
	return 0
}

func (m *Request) GetConnectionAlias() string {
	if m != nil {
		return m.ConnectionAlias
	}
	return ""
}

func (m *Request) GetTableShardInfos() []*TableShardInfos {
	if m != nil {
		return m.TableShardInfos
	}
	return nil
}

type Response struct {
	Data        github_com_pingcap_kvproto_pkg_sharedbytes.SharedBytes `protobuf:"bytes,1,opt,name=data,proto3,customtype=github.com/pingcap/kvproto/pkg/sharedbytes.SharedBytes" json:"data"`
	RegionError *errorpb.Error                                         `protobuf:"bytes,2,opt,name=region_error,json=regionError,proto3" json:"region_error,omitempty"`
	Locked      *kvrpcpb.LockInfo                                      `protobuf:"bytes,3,opt,name=locked,proto3" json:"locked,omitempty"`
	OtherError  string                                                 `protobuf:"bytes,4,opt,name=other_error,json=otherError,proto3" json:"other_error,omitempty"`
	Range       *KeyRange                                              `protobuf:"bytes,5,opt,name=range,proto3" json:"range,omitempty"`
	// This field is always filled for compatibility consideration. However
	// newer TiDB should respect `exec_details_v2` field instead.
	ExecDetails *kvrpcpb.ExecDetails `protobuf:"bytes,6,opt,name=exec_details,json=execDetails,proto3" json:"exec_details,omitempty"`
	// This field is provided in later versions, containing more detailed
	// information.
	ExecDetailsV2    *kvrpcpb.ExecDetailsV2 `protobuf:"bytes,11,opt,name=exec_details_v2,json=execDetailsV2,proto3" json:"exec_details_v2,omitempty"`
	IsCacheHit       bool                   `protobuf:"varint,7,opt,name=is_cache_hit,json=isCacheHit,proto3" json:"is_cache_hit,omitempty"`
	CacheLastVersion uint64                 `protobuf:"varint,8,opt,name=cache_last_version,json=cacheLastVersion,proto3" json:"cache_last_version,omitempty"`
	CanBeCached      bool                   `protobuf:"varint,9,opt,name=can_be_cached,json=canBeCached,proto3" json:"can_be_cached,omitempty"`
	// Contains the latest buckets version of the region.
	// Clients should query PD to update buckets in cache if its is stale.
	LatestBucketsVersion uint64 `protobuf:"varint,12,opt,name=latest_buckets_version,json=latestBucketsVersion,proto3" json:"latest_buckets_version,omitempty"`
	// StoreBatchTaskResponse is the collection of batch task responses.
	BatchResponses []*StoreBatchTaskResponse `protobuf:"bytes,13,rep,name=batch_responses,json=batchResponses,proto3" json:"batch_responses,omitempty"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{2}
}
func (m *Response) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Response.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(m, src)
}
func (m *Response) XXX_Size() int {
	return m.Size()
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetRegionError() *errorpb.Error {
	if m != nil {
		return m.RegionError
	}
	return nil
}

func (m *Response) GetLocked() *kvrpcpb.LockInfo {
	if m != nil {
		return m.Locked
	}
	return nil
}

func (m *Response) GetOtherError() string {
	if m != nil {
		return m.OtherError
	}
	return ""
}

func (m *Response) GetRange() *KeyRange {
	if m != nil {
		return m.Range
	}
	return nil
}

func (m *Response) GetExecDetails() *kvrpcpb.ExecDetails {
	if m != nil {
		return m.ExecDetails
	}
	return nil
}

func (m *Response) GetExecDetailsV2() *kvrpcpb.ExecDetailsV2 {
	if m != nil {
		return m.ExecDetailsV2
	}
	return nil
}

func (m *Response) GetIsCacheHit() bool {
	if m != nil {
		return m.IsCacheHit
	}
	return false
}

func (m *Response) GetCacheLastVersion() uint64 {
	if m != nil {
		return m.CacheLastVersion
	}
	return 0
}

func (m *Response) GetCanBeCached() bool {
	if m != nil {
		return m.CanBeCached
	}
	return false
}

func (m *Response) GetLatestBucketsVersion() uint64 {
	if m != nil {
		return m.LatestBucketsVersion
	}
	return 0
}

func (m *Response) GetBatchResponses() []*StoreBatchTaskResponse {
	if m != nil {
		return m.BatchResponses
	}
	return nil
}

type RegionInfo struct {
	RegionId    uint64              `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	RegionEpoch *metapb.RegionEpoch `protobuf:"bytes,2,opt,name=region_epoch,json=regionEpoch,proto3" json:"region_epoch,omitempty"`
	Ranges      []*KeyRange         `protobuf:"bytes,3,rep,name=ranges,proto3" json:"ranges,omitempty"`
}

func (m *RegionInfo) Reset()         { *m = RegionInfo{} }
func (m *RegionInfo) String() string { return proto.CompactTextString(m) }
func (*RegionInfo) ProtoMessage()    {}
func (*RegionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{3}
}
func (m *RegionInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RegionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RegionInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RegionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegionInfo.Merge(m, src)
}
func (m *RegionInfo) XXX_Size() int {
	return m.Size()
}
func (m *RegionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RegionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RegionInfo proto.InternalMessageInfo

func (m *RegionInfo) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *RegionInfo) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *RegionInfo) GetRanges() []*KeyRange {
	if m != nil {
		return m.Ranges
	}
	return nil
}

type ShardInfo struct {
	ShardId    uint64      `protobuf:"varint,1,opt,name=shard_id,json=shardId,proto3" json:"shard_id,omitempty"`
	ShardEpoch uint64      `protobuf:"varint,2,opt,name=shard_epoch,json=shardEpoch,proto3" json:"shard_epoch,omitempty"`
	Ranges     []*KeyRange `protobuf:"bytes,3,rep,name=ranges,proto3" json:"ranges,omitempty"`
}

func (m *ShardInfo) Reset()         { *m = ShardInfo{} }
func (m *ShardInfo) String() string { return proto.CompactTextString(m) }
func (*ShardInfo) ProtoMessage()    {}
func (*ShardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{4}
}
func (m *ShardInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ShardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ShardInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ShardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardInfo.Merge(m, src)
}
func (m *ShardInfo) XXX_Size() int {
	return m.Size()
}
func (m *ShardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardInfo proto.InternalMessageInfo

func (m *ShardInfo) GetShardId() uint64 {
	if m != nil {
		return m.ShardId
	}
	return 0
}

func (m *ShardInfo) GetShardEpoch() uint64 {
	if m != nil {
		return m.ShardEpoch
	}
	return 0
}

func (m *ShardInfo) GetRanges() []*KeyRange {
	if m != nil {
		return m.Ranges
	}
	return nil
}

type TableShardInfos struct {
	// The executor ID is used to identify the tici executor.
	ExecutorId string `protobuf:"bytes,1,opt,name=executor_id,json=executorId,proto3" json:"executor_id,omitempty"`
	// The shard_infos contains the shard information for each tici executor.
	ShardInfos []*ShardInfo `protobuf:"bytes,2,rep,name=shard_infos,json=shardInfos,proto3" json:"shard_infos,omitempty"`
}

func (m *TableShardInfos) Reset()         { *m = TableShardInfos{} }
func (m *TableShardInfos) String() string { return proto.CompactTextString(m) }
func (*TableShardInfos) ProtoMessage()    {}
func (*TableShardInfos) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{5}
}
func (m *TableShardInfos) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TableShardInfos) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TableShardInfos.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TableShardInfos) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TableShardInfos.Merge(m, src)
}
func (m *TableShardInfos) XXX_Size() int {
	return m.Size()
}
func (m *TableShardInfos) XXX_DiscardUnknown() {
	xxx_messageInfo_TableShardInfos.DiscardUnknown(m)
}

var xxx_messageInfo_TableShardInfos proto.InternalMessageInfo

func (m *TableShardInfos) GetExecutorId() string {
	if m != nil {
		return m.ExecutorId
	}
	return ""
}

func (m *TableShardInfos) GetShardInfos() []*ShardInfo {
	if m != nil {
		return m.ShardInfos
	}
	return nil
}

type TableRegions struct {
	PhysicalTableId int64         `protobuf:"varint,1,opt,name=physical_table_id,json=physicalTableId,proto3" json:"physical_table_id,omitempty"`
	Regions         []*RegionInfo `protobuf:"bytes,2,rep,name=regions,proto3" json:"regions,omitempty"`
}

func (m *TableRegions) Reset()         { *m = TableRegions{} }
func (m *TableRegions) String() string { return proto.CompactTextString(m) }
func (*TableRegions) ProtoMessage()    {}
func (*TableRegions) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{6}
}
func (m *TableRegions) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TableRegions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TableRegions.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TableRegions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TableRegions.Merge(m, src)
}
func (m *TableRegions) XXX_Size() int {
	return m.Size()
}
func (m *TableRegions) XXX_DiscardUnknown() {
	xxx_messageInfo_TableRegions.DiscardUnknown(m)
}

var xxx_messageInfo_TableRegions proto.InternalMessageInfo

func (m *TableRegions) GetPhysicalTableId() int64 {
	if m != nil {
		return m.PhysicalTableId
	}
	return 0
}

func (m *TableRegions) GetRegions() []*RegionInfo {
	if m != nil {
		return m.Regions
	}
	return nil
}

type BatchRequest struct {
	Context *kvrpcpb.Context `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	Tp      int64            `protobuf:"varint,2,opt,name=tp,proto3" json:"tp,omitempty"`
	Data    []byte           `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	Regions []*RegionInfo    `protobuf:"bytes,4,rep,name=regions,proto3" json:"regions,omitempty"`
	StartTs uint64           `protobuf:"varint,5,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	// Any schema-ful storage to validate schema correctness if necessary.
	SchemaVer int64 `protobuf:"varint,6,opt,name=schema_ver,json=schemaVer,proto3" json:"schema_ver,omitempty"`
	// Used for partition table scan
	TableRegions    []*TableRegions    `protobuf:"bytes,7,rep,name=table_regions,json=tableRegions,proto3" json:"table_regions,omitempty"`
	LogId           string             `protobuf:"bytes,8,opt,name=log_id,json=logId,proto3" json:"log_id,omitempty"`
	ConnectionId    uint64             `protobuf:"varint,9,opt,name=connection_id,json=connectionId,proto3" json:"connection_id,omitempty"`
	ConnectionAlias string             `protobuf:"bytes,10,opt,name=connection_alias,json=connectionAlias,proto3" json:"connection_alias,omitempty"`
	TableShardInfos []*TableShardInfos `protobuf:"bytes,11,rep,name=table_shard_infos,json=tableShardInfos,proto3" json:"table_shard_infos,omitempty"`
}

func (m *BatchRequest) Reset()         { *m = BatchRequest{} }
func (m *BatchRequest) String() string { return proto.CompactTextString(m) }
func (*BatchRequest) ProtoMessage()    {}
func (*BatchRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{7}
}
func (m *BatchRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchRequest.Merge(m, src)
}
func (m *BatchRequest) XXX_Size() int {
	return m.Size()
}
func (m *BatchRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchRequest proto.InternalMessageInfo

func (m *BatchRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *BatchRequest) GetTp() int64 {
	if m != nil {
		return m.Tp
	}
	return 0
}

func (m *BatchRequest) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *BatchRequest) GetRegions() []*RegionInfo {
	if m != nil {
		return m.Regions
	}
	return nil
}

func (m *BatchRequest) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *BatchRequest) GetSchemaVer() int64 {
	if m != nil {
		return m.SchemaVer
	}
	return 0
}

func (m *BatchRequest) GetTableRegions() []*TableRegions {
	if m != nil {
		return m.TableRegions
	}
	return nil
}

func (m *BatchRequest) GetLogId() string {
	if m != nil {
		return m.LogId
	}
	return ""
}

func (m *BatchRequest) GetConnectionId() uint64 {
	if m != nil {
		return m.ConnectionId
	}
	return 0
}

func (m *BatchRequest) GetConnectionAlias() string {
	if m != nil {
		return m.ConnectionAlias
	}
	return ""
}

func (m *BatchRequest) GetTableShardInfos() []*TableShardInfos {
	if m != nil {
		return m.TableShardInfos
	}
	return nil
}

type BatchResponse struct {
	Data         github_com_pingcap_kvproto_pkg_sharedbytes.SharedBytes `protobuf:"bytes,1,opt,name=data,proto3,customtype=github.com/pingcap/kvproto/pkg/sharedbytes.SharedBytes" json:"data"`
	OtherError   string                                                 `protobuf:"bytes,2,opt,name=other_error,json=otherError,proto3" json:"other_error,omitempty"`
	ExecDetails  *kvrpcpb.ExecDetails                                   `protobuf:"bytes,3,opt,name=exec_details,json=execDetails,proto3" json:"exec_details,omitempty"`
	RetryRegions []*metapb.Region                                       `protobuf:"bytes,4,rep,name=retry_regions,json=retryRegions,proto3" json:"retry_regions,omitempty"`
}

func (m *BatchResponse) Reset()         { *m = BatchResponse{} }
func (m *BatchResponse) String() string { return proto.CompactTextString(m) }
func (*BatchResponse) ProtoMessage()    {}
func (*BatchResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{8}
}
func (m *BatchResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BatchResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BatchResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BatchResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchResponse.Merge(m, src)
}
func (m *BatchResponse) XXX_Size() int {
	return m.Size()
}
func (m *BatchResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchResponse proto.InternalMessageInfo

func (m *BatchResponse) GetOtherError() string {
	if m != nil {
		return m.OtherError
	}
	return ""
}

func (m *BatchResponse) GetExecDetails() *kvrpcpb.ExecDetails {
	if m != nil {
		return m.ExecDetails
	}
	return nil
}

func (m *BatchResponse) GetRetryRegions() []*metapb.Region {
	if m != nil {
		return m.RetryRegions
	}
	return nil
}

type StoreBatchTask struct {
	RegionId    uint64              `protobuf:"varint,1,opt,name=region_id,json=regionId,proto3" json:"region_id,omitempty"`
	RegionEpoch *metapb.RegionEpoch `protobuf:"bytes,2,opt,name=region_epoch,json=regionEpoch,proto3" json:"region_epoch,omitempty"`
	Peer        *metapb.Peer        `protobuf:"bytes,3,opt,name=peer,proto3" json:"peer,omitempty"`
	Ranges      []*KeyRange         `protobuf:"bytes,4,rep,name=ranges,proto3" json:"ranges,omitempty"`
	TaskId      uint64              `protobuf:"varint,5,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (m *StoreBatchTask) Reset()         { *m = StoreBatchTask{} }
func (m *StoreBatchTask) String() string { return proto.CompactTextString(m) }
func (*StoreBatchTask) ProtoMessage()    {}
func (*StoreBatchTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{9}
}
func (m *StoreBatchTask) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreBatchTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreBatchTask.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreBatchTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreBatchTask.Merge(m, src)
}
func (m *StoreBatchTask) XXX_Size() int {
	return m.Size()
}
func (m *StoreBatchTask) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreBatchTask.DiscardUnknown(m)
}

var xxx_messageInfo_StoreBatchTask proto.InternalMessageInfo

func (m *StoreBatchTask) GetRegionId() uint64 {
	if m != nil {
		return m.RegionId
	}
	return 0
}

func (m *StoreBatchTask) GetRegionEpoch() *metapb.RegionEpoch {
	if m != nil {
		return m.RegionEpoch
	}
	return nil
}

func (m *StoreBatchTask) GetPeer() *metapb.Peer {
	if m != nil {
		return m.Peer
	}
	return nil
}

func (m *StoreBatchTask) GetRanges() []*KeyRange {
	if m != nil {
		return m.Ranges
	}
	return nil
}

func (m *StoreBatchTask) GetTaskId() uint64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

type StoreBatchTaskResponse struct {
	Data          github_com_pingcap_kvproto_pkg_sharedbytes.SharedBytes `protobuf:"bytes,1,opt,name=data,proto3,customtype=github.com/pingcap/kvproto/pkg/sharedbytes.SharedBytes" json:"data"`
	RegionError   *errorpb.Error                                         `protobuf:"bytes,2,opt,name=region_error,json=regionError,proto3" json:"region_error,omitempty"`
	Locked        *kvrpcpb.LockInfo                                      `protobuf:"bytes,3,opt,name=locked,proto3" json:"locked,omitempty"`
	OtherError    string                                                 `protobuf:"bytes,4,opt,name=other_error,json=otherError,proto3" json:"other_error,omitempty"`
	TaskId        uint64                                                 `protobuf:"varint,5,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	ExecDetailsV2 *kvrpcpb.ExecDetailsV2                                 `protobuf:"bytes,6,opt,name=exec_details_v2,json=execDetailsV2,proto3" json:"exec_details_v2,omitempty"`
}

func (m *StoreBatchTaskResponse) Reset()         { *m = StoreBatchTaskResponse{} }
func (m *StoreBatchTaskResponse) String() string { return proto.CompactTextString(m) }
func (*StoreBatchTaskResponse) ProtoMessage()    {}
func (*StoreBatchTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{10}
}
func (m *StoreBatchTaskResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreBatchTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreBatchTaskResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreBatchTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreBatchTaskResponse.Merge(m, src)
}
func (m *StoreBatchTaskResponse) XXX_Size() int {
	return m.Size()
}
func (m *StoreBatchTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreBatchTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StoreBatchTaskResponse proto.InternalMessageInfo

func (m *StoreBatchTaskResponse) GetRegionError() *errorpb.Error {
	if m != nil {
		return m.RegionError
	}
	return nil
}

func (m *StoreBatchTaskResponse) GetLocked() *kvrpcpb.LockInfo {
	if m != nil {
		return m.Locked
	}
	return nil
}

func (m *StoreBatchTaskResponse) GetOtherError() string {
	if m != nil {
		return m.OtherError
	}
	return ""
}

func (m *StoreBatchTaskResponse) GetTaskId() uint64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *StoreBatchTaskResponse) GetExecDetailsV2() *kvrpcpb.ExecDetailsV2 {
	if m != nil {
		return m.ExecDetailsV2
	}
	return nil
}

type DelegateRequest struct {
	Context *kvrpcpb.Context `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	StartTs uint64           `protobuf:"varint,2,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	Ranges  []*KeyRange      `protobuf:"bytes,3,rep,name=ranges,proto3" json:"ranges,omitempty"`
	// Used for avoid redundant mem-table copying.
	// If the sequence is the same, tikv-server will not return the mem-table.
	MemTableSequence uint64 `protobuf:"varint,4,opt,name=mem_table_sequence,json=memTableSequence,proto3" json:"mem_table_sequence,omitempty"`
	// Used for avoid redundant snapshot copying.
	// If the sequence is the same, tikv-server will not return the snapshot.
	SnapshotSequence uint64 `protobuf:"varint,5,opt,name=snapshot_sequence,json=snapshotSequence,proto3" json:"snapshot_sequence,omitempty"`
}

func (m *DelegateRequest) Reset()         { *m = DelegateRequest{} }
func (m *DelegateRequest) String() string { return proto.CompactTextString(m) }
func (*DelegateRequest) ProtoMessage()    {}
func (*DelegateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{11}
}
func (m *DelegateRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DelegateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DelegateRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DelegateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelegateRequest.Merge(m, src)
}
func (m *DelegateRequest) XXX_Size() int {
	return m.Size()
}
func (m *DelegateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelegateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelegateRequest proto.InternalMessageInfo

func (m *DelegateRequest) GetContext() *kvrpcpb.Context {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *DelegateRequest) GetStartTs() uint64 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *DelegateRequest) GetRanges() []*KeyRange {
	if m != nil {
		return m.Ranges
	}
	return nil
}

func (m *DelegateRequest) GetMemTableSequence() uint64 {
	if m != nil {
		return m.MemTableSequence
	}
	return 0
}

func (m *DelegateRequest) GetSnapshotSequence() uint64 {
	if m != nil {
		return m.SnapshotSequence
	}
	return 0
}

type DelegateResponse struct {
	MemTableData []byte            `protobuf:"bytes,1,opt,name=mem_table_data,json=memTableData,proto3" json:"mem_table_data,omitempty"`
	Snapshot     []byte            `protobuf:"bytes,2,opt,name=snapshot,proto3" json:"snapshot,omitempty"`
	RegionError  *errorpb.Error    `protobuf:"bytes,3,opt,name=region_error,json=regionError,proto3" json:"region_error,omitempty"`
	Locked       *kvrpcpb.LockInfo `protobuf:"bytes,4,opt,name=locked,proto3" json:"locked,omitempty"`
	OtherError   string            `protobuf:"bytes,5,opt,name=other_error,json=otherError,proto3" json:"other_error,omitempty"`
	// Used for avoid redundant mem-table copying.
	MemTableSequence uint64 `protobuf:"varint,6,opt,name=mem_table_sequence,json=memTableSequence,proto3" json:"mem_table_sequence,omitempty"`
}

func (m *DelegateResponse) Reset()         { *m = DelegateResponse{} }
func (m *DelegateResponse) String() string { return proto.CompactTextString(m) }
func (*DelegateResponse) ProtoMessage()    {}
func (*DelegateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_29878c170c3dd019, []int{12}
}
func (m *DelegateResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DelegateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DelegateResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DelegateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelegateResponse.Merge(m, src)
}
func (m *DelegateResponse) XXX_Size() int {
	return m.Size()
}
func (m *DelegateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelegateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelegateResponse proto.InternalMessageInfo

func (m *DelegateResponse) GetMemTableData() []byte {
	if m != nil {
		return m.MemTableData
	}
	return nil
}

func (m *DelegateResponse) GetSnapshot() []byte {
	if m != nil {
		return m.Snapshot
	}
	return nil
}

func (m *DelegateResponse) GetRegionError() *errorpb.Error {
	if m != nil {
		return m.RegionError
	}
	return nil
}

func (m *DelegateResponse) GetLocked() *kvrpcpb.LockInfo {
	if m != nil {
		return m.Locked
	}
	return nil
}

func (m *DelegateResponse) GetOtherError() string {
	if m != nil {
		return m.OtherError
	}
	return ""
}

func (m *DelegateResponse) GetMemTableSequence() uint64 {
	if m != nil {
		return m.MemTableSequence
	}
	return 0
}

func init() {
	proto.RegisterType((*KeyRange)(nil), "coprocessor.KeyRange")
	proto.RegisterType((*Request)(nil), "coprocessor.Request")
	proto.RegisterType((*Response)(nil), "coprocessor.Response")
	proto.RegisterType((*RegionInfo)(nil), "coprocessor.RegionInfo")
	proto.RegisterType((*ShardInfo)(nil), "coprocessor.ShardInfo")
	proto.RegisterType((*TableShardInfos)(nil), "coprocessor.TableShardInfos")
	proto.RegisterType((*TableRegions)(nil), "coprocessor.TableRegions")
	proto.RegisterType((*BatchRequest)(nil), "coprocessor.BatchRequest")
	proto.RegisterType((*BatchResponse)(nil), "coprocessor.BatchResponse")
	proto.RegisterType((*StoreBatchTask)(nil), "coprocessor.StoreBatchTask")
	proto.RegisterType((*StoreBatchTaskResponse)(nil), "coprocessor.StoreBatchTaskResponse")
	proto.RegisterType((*DelegateRequest)(nil), "coprocessor.DelegateRequest")
	proto.RegisterType((*DelegateResponse)(nil), "coprocessor.DelegateResponse")
}

func init() { proto.RegisterFile("coprocessor.proto", fileDescriptor_29878c170c3dd019) }

var fileDescriptor_29878c170c3dd019 = []byte{
	// 1276 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x57, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0x36, 0xf5, 0x4b, 0x0d, 0xa9, 0x1f, 0x6f, 0x1c, 0x87, 0x49, 0x5a, 0x45, 0x50, 0x7a, 0x70,
	0x92, 0x56, 0x46, 0x94, 0x22, 0xb9, 0x05, 0xa8, 0x12, 0x03, 0x51, 0x9b, 0x02, 0xc5, 0xda, 0xc8,
	0x95, 0x58, 0x91, 0x1b, 0x8a, 0x90, 0x44, 0xb2, 0xdc, 0xb5, 0x11, 0xe7, 0x96, 0x37, 0xc8, 0xb1,
	0x8f, 0xd0, 0x57, 0x28, 0xd0, 0x63, 0x0f, 0xb9, 0x35, 0xc7, 0xa0, 0x87, 0xa0, 0xb0, 0xdf, 0xa0,
	0x4f, 0x50, 0xec, 0x2c, 0xa9, 0x1f, 0xdb, 0x29, 0xec, 0x00, 0xbe, 0xf4, 0xa4, 0x9d, 0x9f, 0x9d,
	0x99, 0x9d, 0xf9, 0x66, 0x86, 0x82, 0x75, 0x2f, 0x4e, 0xd2, 0xd8, 0xe3, 0x42, 0xc4, 0x69, 0x2f,
	0x49, 0x63, 0x19, 0x13, 0x6b, 0x89, 0x75, 0xa3, 0xce, 0xd3, 0x34, 0x4e, 0x93, 0x91, 0x96, 0xdd,
	0xa8, 0x4f, 0x0e, 0xd2, 0xc4, 0x9b, 0x93, 0xf6, 0x8c, 0x4b, 0x36, 0xa7, 0x36, 0x82, 0x38, 0x88,
	0xf1, 0xb8, 0xad, 0x4e, 0x19, 0xb7, 0x99, 0xee, 0x0b, 0x89, 0x47, 0xcd, 0xe8, 0xf6, 0xc1, 0xfc,
	0x81, 0x1f, 0x52, 0x16, 0x05, 0x9c, 0x6c, 0x40, 0x59, 0x48, 0x96, 0x4a, 0xc7, 0xe8, 0x18, 0x5b,
	0x36, 0xd5, 0x04, 0x69, 0x41, 0x91, 0x47, 0xbe, 0x53, 0x40, 0x9e, 0x3a, 0x76, 0x7f, 0x2b, 0x41,
	0x95, 0xf2, 0x9f, 0xf7, 0xb9, 0x90, 0xe4, 0x2e, 0x54, 0xbd, 0x38, 0x92, 0xfc, 0x95, 0xbe, 0x65,
	0xf5, 0x5b, 0xbd, 0x3c, 0xaa, 0x27, 0x9a, 0x4f, 0x73, 0x05, 0xd2, 0x80, 0x82, 0x4c, 0xd0, 0x50,
	0x91, 0x16, 0x64, 0x42, 0x08, 0x94, 0x7c, 0x26, 0x99, 0x53, 0x44, 0xd3, 0x78, 0x26, 0xd7, 0xc1,
	0x44, 0xb7, 0xae, 0x14, 0x4e, 0xb5, 0x63, 0x6c, 0x95, 0x68, 0x15, 0xe9, 0x3d, 0x41, 0xbe, 0x81,
	0x4a, 0xaa, 0xe2, 0x14, 0x4e, 0xa9, 0x53, 0xdc, 0xb2, 0xfa, 0x57, 0x7b, 0xcb, 0xe9, 0xca, 0x5f,
	0x41, 0x33, 0x25, 0xb2, 0x05, 0xad, 0x50, 0xb8, 0x1e, 0xf3, 0xc6, 0xdc, 0xe5, 0x11, 0x1b, 0x4d,
	0xb9, 0xef, 0x94, 0x3b, 0xc6, 0x96, 0x49, 0x1b, 0xa1, 0x78, 0xa2, 0xd8, 0x3b, 0x9a, 0x4b, 0x1e,
	0xc0, 0xa6, 0x56, 0x0b, 0x5f, 0xba, 0x33, 0x26, 0xbd, 0xb1, 0x7b, 0xc0, 0x53, 0x11, 0xc6, 0x91,
	0x53, 0xc1, 0x08, 0xae, 0xa0, 0x74, 0xf8, 0xf2, 0x47, 0x25, 0x7b, 0xa1, 0x45, 0xe4, 0x4b, 0x00,
	0xe1, 0x8d, 0xf9, 0x8c, 0x29, 0x65, 0xc7, 0xc4, 0x47, 0xd5, 0x34, 0xe7, 0x05, 0x4f, 0x33, 0xef,
	0x32, 0x65, 0xde, 0xc2, 0x7b, 0x2d, 0xf7, 0xbe, 0xa7, 0xd8, 0xb9, 0xf7, 0x5b, 0x60, 0x25, 0x2c,
	0x08, 0xa3, 0xc0, 0x15, 0xe1, 0x6b, 0xee, 0x00, 0xba, 0x04, 0xcd, 0xda, 0x0d, 0x5f, 0x73, 0x72,
	0x1f, 0xca, 0x92, 0x89, 0x89, 0x70, 0x2c, 0x7c, 0xf6, 0xcd, 0x95, 0x67, 0xef, 0xca, 0x38, 0xe5,
	0x03, 0x15, 0xd8, 0x1e, 0x13, 0x13, 0xaa, 0x35, 0xc9, 0x6d, 0xa8, 0x7b, 0x71, 0x14, 0x71, 0x4f,
	0x86, 0x71, 0xe4, 0x86, 0xbe, 0x63, 0xa3, 0x55, 0x7b, 0xc1, 0x1c, 0xfa, 0xe4, 0x0e, 0xb4, 0x96,
	0x94, 0xd8, 0x34, 0x64, 0xc2, 0xa9, 0x77, 0x8c, 0xad, 0x1a, 0x6d, 0x2e, 0xf8, 0xdf, 0x29, 0x36,
	0x79, 0x06, 0xeb, 0x52, 0x45, 0xeb, 0x8a, 0x31, 0x4b, 0x7d, 0x37, 0x8c, 0x5e, 0xc6, 0xc2, 0x69,
	0x60, 0x38, 0x5f, 0xac, 0x84, 0xb3, 0xa7, 0xb4, 0x76, 0x95, 0xd2, 0x50, 0xe9, 0xd0, 0xa6, 0x5c,
	0x65, 0x74, 0xdf, 0x94, 0xc1, 0xa4, 0x5c, 0x24, 0x71, 0x24, 0x38, 0xa1, 0x19, 0x00, 0x10, 0x6f,
	0x83, 0xc7, 0xef, 0x3e, 0xde, 0x5a, 0xfb, 0xeb, 0xe3, 0xad, 0x87, 0x41, 0x28, 0xc7, 0xfb, 0xa3,
	0x9e, 0x17, 0xcf, 0xb6, 0x93, 0x30, 0x0a, 0x3c, 0x96, 0x6c, 0x4f, 0x0e, 0x34, 0x94, 0x93, 0x49,
	0xb0, 0xad, 0x62, 0xe0, 0xfe, 0xe8, 0x50, 0x72, 0xd1, 0xdb, 0xc5, 0xf3, 0x40, 0x9d, 0x33, 0x00,
	0xdd, 0x07, 0x3b, 0xe5, 0x81, 0x7a, 0x11, 0x36, 0x0b, 0xc2, 0xcd, 0xea, 0x37, 0x7a, 0x79, 0xeb,
	0xec, 0xa8, 0x5f, 0x6a, 0x69, 0x1d, 0x24, 0xc8, 0x1d, 0xa8, 0x4c, 0x63, 0x6f, 0xc2, 0x7d, 0x44,
	0xa2, 0xd5, 0x5f, 0x9f, 0x43, 0xf8, 0x79, 0xec, 0x4d, 0x54, 0xdc, 0x34, 0x53, 0x50, 0xc5, 0x8a,
	0xe5, 0x98, 0xa7, 0x99, 0xf1, 0x12, 0xa6, 0x0b, 0x90, 0xa5, 0x6d, 0xdd, 0x83, 0x32, 0xe2, 0x0f,
	0xa1, 0xf6, 0x49, 0x8c, 0x6a, 0x1d, 0xf2, 0x08, 0x6c, 0xfe, 0x8a, 0x7b, 0xae, 0xcf, 0x25, 0x0b,
	0xa7, 0x02, 0xe1, 0x66, 0xf5, 0x37, 0xe6, 0xee, 0x77, 0x5e, 0x71, 0xef, 0xa9, 0x96, 0x51, 0x8b,
	0x2f, 0x08, 0xf2, 0x18, 0x9a, 0xcb, 0x17, 0xdd, 0x83, 0xbe, 0x63, 0xe1, 0xdd, 0xcd, 0xb3, 0xee,
	0xbe, 0xe8, 0xd3, 0x3a, 0x5f, 0x26, 0x49, 0x07, 0xec, 0x79, 0x6f, 0x8c, 0x43, 0x89, 0x9d, 0x66,
	0x52, 0xc8, 0xfa, 0xe2, 0x59, 0x28, 0xc9, 0xd7, 0x40, 0xb4, 0x78, 0xca, 0x84, 0x9c, 0xf7, 0x83,
	0x89, 0x30, 0x6a, 0xa1, 0xe4, 0x39, 0x13, 0x32, 0x6f, 0x86, 0x2e, 0xd4, 0x3d, 0x16, 0xb9, 0x23,
	0xae, 0x6d, 0xe6, 0x50, 0xb7, 0x3c, 0x16, 0x0d, 0x38, 0xda, 0xf4, 0xc9, 0xb7, 0xb0, 0x39, 0x65,
	0x92, 0x0b, 0xe9, 0x8e, 0xf6, 0xbd, 0x09, 0x97, 0x62, 0x6e, 0x55, 0x83, 0x73, 0x43, 0x4b, 0x07,
	0x5a, 0x98, 0x5b, 0x7e, 0x0e, 0xcd, 0x11, 0xb6, 0x64, 0x9a, 0x81, 0x46, 0x61, 0x54, 0xe1, 0xee,
	0xf6, 0x7f, 0xb5, 0x41, 0xa6, 0x4b, 0x1b, 0x78, 0x37, 0x27, 0xc5, 0xf7, 0x25, 0x13, 0x5a, 0x56,
	0xf7, 0xad, 0x01, 0x40, 0xb1, 0xfe, 0xaa, 0xb6, 0xe4, 0x26, 0xd4, 0x32, 0xc4, 0x84, 0x3e, 0x42,
	0xb1, 0x44, 0x4d, 0xcd, 0x18, 0xfa, 0xe4, 0xe1, 0x02, 0x4e, 0x49, 0xec, 0x8d, 0x33, 0x38, 0x5d,
	0xe9, 0x65, 0xb3, 0x56, 0x9b, 0xd9, 0x51, 0xa2, 0x39, 0xa6, 0x14, 0xb1, 0x34, 0xac, 0x8a, 0xe7,
	0x18, 0x56, 0xdd, 0x03, 0xa8, 0xcd, 0x9b, 0x04, 0x67, 0xa0, 0xee, 0xb3, 0x3c, 0x9e, 0x2a, 0xd2,
	0x43, 0xc4, 0x9f, 0x16, 0x2d, 0xa2, 0x29, 0x51, 0x40, 0xd6, 0x67, 0xf9, 0x9d, 0x40, 0xf3, 0x44,
	0xcb, 0x2a, 0x17, 0x0a, 0x2c, 0xfb, 0x32, 0x4e, 0xf3, 0x00, 0x6a, 0x14, 0x72, 0xd6, 0xd0, 0x27,
	0x8f, 0xf2, 0x18, 0xf4, 0x18, 0x28, 0xa0, 0x9f, 0xcd, 0xd5, 0x72, 0xe4, 0xe6, 0xb2, 0xd8, 0x74,
	0xef, 0xcf, 0xc0, 0x46, 0x67, 0x3a, 0x69, 0x82, 0xdc, 0x85, 0xf5, 0x64, 0x7c, 0x28, 0x42, 0x8f,
	0x4d, 0x5d, 0x3d, 0x5e, 0x32, 0x7f, 0x45, 0xda, 0xcc, 0x05, 0x78, 0x61, 0xe8, 0x93, 0xfb, 0x50,
	0xd5, 0xe9, 0xcd, 0x1d, 0x5e, 0x5b, 0x71, 0xb8, 0x28, 0x27, 0xcd, 0xf5, 0xba, 0xbf, 0x17, 0xc1,
	0x1e, 0xe8, 0xfa, 0x5f, 0xce, 0xae, 0x5a, 0x8a, 0xa9, 0x74, 0xbe, 0x98, 0x56, 0xd6, 0x5b, 0x79,
	0x75, 0xbd, 0xad, 0x2e, 0x94, 0xca, 0xc9, 0x85, 0xf2, 0x18, 0xea, 0x3a, 0x47, 0xb9, 0xcb, 0x2a,
	0xba, 0xbc, 0x7e, 0x7a, 0xfc, 0x66, 0xe9, 0xa5, 0xb6, 0x5c, 0x4e, 0xf6, 0x55, 0x35, 0xe4, 0x02,
	0x95, 0x61, 0x13, 0x2b, 0x5a, 0x9e, 0xc6, 0xc1, 0xd0, 0x3f, 0xbd, 0x29, 0x6a, 0xe7, 0xdc, 0x14,
	0x70, 0x81, 0x4d, 0x61, 0x7d, 0xce, 0xa6, 0xf8, 0xc7, 0x80, 0xfa, 0x60, 0xb9, 0x7d, 0x2f, 0x65,
	0x5d, 0x9c, 0x18, 0xe8, 0x85, 0x53, 0x03, 0xfd, 0xe4, 0x8c, 0x2e, 0x9e, 0x77, 0x46, 0x3f, 0x80,
	0x7a, 0xca, 0x65, 0x7a, 0xe8, 0xae, 0x62, 0xa4, 0xb1, 0x3a, 0x3a, 0xa8, 0x8d, 0x4a, 0x59, 0x95,
	0xba, 0x7f, 0x1a, 0xd0, 0x58, 0x9d, 0x65, 0x97, 0x33, 0x9e, 0x3a, 0x50, 0x4a, 0x38, 0x4f, 0xb3,
	0xd7, 0xd8, 0xb9, 0xfe, 0x4f, 0x9c, 0xa7, 0x14, 0x25, 0x17, 0xfd, 0xda, 0xba, 0x06, 0x55, 0xf5,
	0xe9, 0xa1, 0x62, 0xd4, 0xb8, 0xae, 0x28, 0x72, 0xe8, 0x77, 0xff, 0x28, 0xc0, 0xe6, 0xd9, 0xd3,
	0xf9, 0xff, 0xb8, 0xfe, 0x3f, 0x95, 0x86, 0xb3, 0x36, 0x76, 0xe5, 0x02, 0x1b, 0xbb, 0x7b, 0x64,
	0x40, 0xf3, 0x29, 0x9f, 0xf2, 0x80, 0x49, 0xfe, 0x39, 0xf3, 0x6c, 0x79, 0xf0, 0x14, 0x3e, 0xf5,
	0x5d, 0x7d, 0x9e, 0x95, 0xa1, 0xbe, 0x0c, 0x66, 0x7c, 0x96, 0x0d, 0x6c, 0xa1, 0x42, 0x89, 0x3c,
	0x8e, 0xa9, 0x28, 0xd1, 0xd6, 0x8c, 0xcf, 0x74, 0x63, 0x67, 0x7c, 0x72, 0x0f, 0xd6, 0x45, 0xc4,
	0x12, 0x31, 0x8e, 0xe5, 0x42, 0x59, 0xa7, 0xa6, 0x95, 0x0b, 0x72, 0xe5, 0xee, 0x9b, 0x02, 0xb4,
	0x16, 0x8f, 0xcc, 0x50, 0xf2, 0x15, 0x34, 0x16, 0xfe, 0x16, 0x78, 0xa1, 0x76, 0xee, 0xeb, 0xa9,
	0xaa, 0xfb, 0x0d, 0x30, 0x73, 0x73, 0xd9, 0x5f, 0x95, 0x39, 0x7d, 0x0a, 0x13, 0xc5, 0x8b, 0x60,
	0xa2, 0x74, 0x41, 0x4c, 0x94, 0x4f, 0x61, 0xe2, 0xec, 0x84, 0x55, 0xce, 0x4e, 0xd8, 0xa0, 0xff,
	0xe1, 0x57, 0xd3, 0x78, 0x77, 0xd4, 0x36, 0xde, 0x1f, 0xb5, 0x8d, 0xbf, 0x8f, 0xda, 0xc6, 0xdb,
	0xe3, 0xf6, 0xda, 0x2f, 0xc7, 0xed, 0xb5, 0xf7, 0xc7, 0xed, 0xb5, 0x0f, 0xc7, 0xed, 0x35, 0x68,
	0xc5, 0x69, 0xd0, 0x93, 0xe1, 0xe4, 0xa0, 0x97, 0xb5, 0xc5, 0xa8, 0x82, 0x3f, 0x0f, 0xfe, 0x0d,
	0x00, 0x00, 0xff, 0xff, 0x72, 0x9e, 0x74, 0x44, 0x40, 0x0e, 0x00, 0x00,
}

func (m *KeyRange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KeyRange) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *KeyRange) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.End) > 0 {
		i -= len(m.End)
		copy(dAtA[i:], m.End)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.End)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Start) > 0 {
		i -= len(m.Start)
		copy(dAtA[i:], m.Start)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.Start)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Request) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Request) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Request) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableShardInfos) > 0 {
		for iNdEx := len(m.TableShardInfos) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TableShardInfos[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x72
		}
	}
	if len(m.ConnectionAlias) > 0 {
		i -= len(m.ConnectionAlias)
		copy(dAtA[i:], m.ConnectionAlias)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.ConnectionAlias)))
		i--
		dAtA[i] = 0x6a
	}
	if m.ConnectionId != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.ConnectionId))
		i--
		dAtA[i] = 0x60
	}
	if len(m.Tasks) > 0 {
		for iNdEx := len(m.Tasks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tasks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x5a
		}
	}
	if m.PagingSize != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.PagingSize))
		i--
		dAtA[i] = 0x50
	}
	if m.IsTraceEnabled {
		i--
		if m.IsTraceEnabled {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x48
	}
	if m.SchemaVer != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.SchemaVer))
		i--
		dAtA[i] = 0x40
	}
	if m.StartTs != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x38
	}
	if m.CacheIfMatchVersion != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.CacheIfMatchVersion))
		i--
		dAtA[i] = 0x30
	}
	if m.IsCacheEnabled {
		i--
		if m.IsCacheEnabled {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Tp != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.Tp))
		i--
		dAtA[i] = 0x10
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Response) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Response) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Response) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BatchResponses) > 0 {
		for iNdEx := len(m.BatchResponses) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.BatchResponses[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x6a
		}
	}
	if m.LatestBucketsVersion != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.LatestBucketsVersion))
		i--
		dAtA[i] = 0x60
	}
	if m.ExecDetailsV2 != nil {
		{
			size, err := m.ExecDetailsV2.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.CanBeCached {
		i--
		if m.CanBeCached {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x48
	}
	if m.CacheLastVersion != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.CacheLastVersion))
		i--
		dAtA[i] = 0x40
	}
	if m.IsCacheHit {
		i--
		if m.IsCacheHit {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x38
	}
	if m.ExecDetails != nil {
		{
			size, err := m.ExecDetails.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Range != nil {
		{
			size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if len(m.OtherError) > 0 {
		i -= len(m.OtherError)
		copy(dAtA[i:], m.OtherError)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.OtherError)))
		i--
		dAtA[i] = 0x22
	}
	if m.Locked != nil {
		{
			size, err := m.Locked.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionError != nil {
		{
			size, err := m.RegionError.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	{
		size := m.Data.Size()
		i -= size
		if _, err := m.Data.MarshalTo(dAtA[i:]); err != nil {
			return 0, err
		}
		i = encodeVarintCoprocessor(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *RegionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RegionInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RegionInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RegionEpoch != nil {
		{
			size, err := m.RegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.RegionId != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ShardInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ShardInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ShardInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.ShardEpoch != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.ShardEpoch))
		i--
		dAtA[i] = 0x10
	}
	if m.ShardId != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.ShardId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TableShardInfos) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TableShardInfos) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TableShardInfos) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ShardInfos) > 0 {
		for iNdEx := len(m.ShardInfos) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ShardInfos[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.ExecutorId) > 0 {
		i -= len(m.ExecutorId)
		copy(dAtA[i:], m.ExecutorId)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.ExecutorId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TableRegions) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TableRegions) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TableRegions) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Regions) > 0 {
		for iNdEx := len(m.Regions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Regions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.PhysicalTableId != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.PhysicalTableId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BatchRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableShardInfos) > 0 {
		for iNdEx := len(m.TableShardInfos) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TableShardInfos[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x5a
		}
	}
	if len(m.ConnectionAlias) > 0 {
		i -= len(m.ConnectionAlias)
		copy(dAtA[i:], m.ConnectionAlias)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.ConnectionAlias)))
		i--
		dAtA[i] = 0x52
	}
	if m.ConnectionId != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.ConnectionId))
		i--
		dAtA[i] = 0x48
	}
	if len(m.LogId) > 0 {
		i -= len(m.LogId)
		copy(dAtA[i:], m.LogId)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.LogId)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.TableRegions) > 0 {
		for iNdEx := len(m.TableRegions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TableRegions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x3a
		}
	}
	if m.SchemaVer != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.SchemaVer))
		i--
		dAtA[i] = 0x30
	}
	if m.StartTs != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Regions) > 0 {
		for iNdEx := len(m.Regions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Regions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Data) > 0 {
		i -= len(m.Data)
		copy(dAtA[i:], m.Data)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.Data)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Tp != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.Tp))
		i--
		dAtA[i] = 0x10
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *BatchResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BatchResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RetryRegions) > 0 {
		for iNdEx := len(m.RetryRegions) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.RetryRegions[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if m.ExecDetails != nil {
		{
			size, err := m.ExecDetails.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OtherError) > 0 {
		i -= len(m.OtherError)
		copy(dAtA[i:], m.OtherError)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.OtherError)))
		i--
		dAtA[i] = 0x12
	}
	{
		size := m.Data.Size()
		i -= size
		if _, err := m.Data.MarshalTo(dAtA[i:]); err != nil {
			return 0, err
		}
		i = encodeVarintCoprocessor(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *StoreBatchTask) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreBatchTask) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreBatchTask) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.TaskId != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.TaskId))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x22
		}
	}
	if m.Peer != nil {
		{
			size, err := m.Peer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionEpoch != nil {
		{
			size, err := m.RegionEpoch.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.RegionId != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.RegionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *StoreBatchTaskResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreBatchTaskResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreBatchTaskResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ExecDetailsV2 != nil {
		{
			size, err := m.ExecDetailsV2.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.TaskId != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.TaskId))
		i--
		dAtA[i] = 0x28
	}
	if len(m.OtherError) > 0 {
		i -= len(m.OtherError)
		copy(dAtA[i:], m.OtherError)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.OtherError)))
		i--
		dAtA[i] = 0x22
	}
	if m.Locked != nil {
		{
			size, err := m.Locked.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.RegionError != nil {
		{
			size, err := m.RegionError.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	{
		size := m.Data.Size()
		i -= size
		if _, err := m.Data.MarshalTo(dAtA[i:]); err != nil {
			return 0, err
		}
		i = encodeVarintCoprocessor(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *DelegateRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelegateRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DelegateRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SnapshotSequence != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.SnapshotSequence))
		i--
		dAtA[i] = 0x28
	}
	if m.MemTableSequence != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.MemTableSequence))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Ranges) > 0 {
		for iNdEx := len(m.Ranges) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Ranges[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCoprocessor(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.StartTs != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.StartTs))
		i--
		dAtA[i] = 0x10
	}
	if m.Context != nil {
		{
			size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DelegateResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelegateResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DelegateResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MemTableSequence != 0 {
		i = encodeVarintCoprocessor(dAtA, i, uint64(m.MemTableSequence))
		i--
		dAtA[i] = 0x30
	}
	if len(m.OtherError) > 0 {
		i -= len(m.OtherError)
		copy(dAtA[i:], m.OtherError)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.OtherError)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Locked != nil {
		{
			size, err := m.Locked.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.RegionError != nil {
		{
			size, err := m.RegionError.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCoprocessor(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Snapshot) > 0 {
		i -= len(m.Snapshot)
		copy(dAtA[i:], m.Snapshot)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.Snapshot)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.MemTableData) > 0 {
		i -= len(m.MemTableData)
		copy(dAtA[i:], m.MemTableData)
		i = encodeVarintCoprocessor(dAtA, i, uint64(len(m.MemTableData)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintCoprocessor(dAtA []byte, offset int, v uint64) int {
	offset -= sovCoprocessor(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *KeyRange) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Start)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	l = len(m.End)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	return n
}

func (m *Request) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.Tp != 0 {
		n += 1 + sovCoprocessor(uint64(m.Tp))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	if m.IsCacheEnabled {
		n += 2
	}
	if m.CacheIfMatchVersion != 0 {
		n += 1 + sovCoprocessor(uint64(m.CacheIfMatchVersion))
	}
	if m.StartTs != 0 {
		n += 1 + sovCoprocessor(uint64(m.StartTs))
	}
	if m.SchemaVer != 0 {
		n += 1 + sovCoprocessor(uint64(m.SchemaVer))
	}
	if m.IsTraceEnabled {
		n += 2
	}
	if m.PagingSize != 0 {
		n += 1 + sovCoprocessor(uint64(m.PagingSize))
	}
	if len(m.Tasks) > 0 {
		for _, e := range m.Tasks {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	if m.ConnectionId != 0 {
		n += 1 + sovCoprocessor(uint64(m.ConnectionId))
	}
	l = len(m.ConnectionAlias)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if len(m.TableShardInfos) > 0 {
		for _, e := range m.TableShardInfos {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	return n
}

func (m *Response) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Data.Size()
	n += 1 + l + sovCoprocessor(uint64(l))
	if m.RegionError != nil {
		l = m.RegionError.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.Locked != nil {
		l = m.Locked.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	l = len(m.OtherError)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.Range != nil {
		l = m.Range.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.ExecDetails != nil {
		l = m.ExecDetails.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.IsCacheHit {
		n += 2
	}
	if m.CacheLastVersion != 0 {
		n += 1 + sovCoprocessor(uint64(m.CacheLastVersion))
	}
	if m.CanBeCached {
		n += 2
	}
	if m.ExecDetailsV2 != nil {
		l = m.ExecDetailsV2.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.LatestBucketsVersion != 0 {
		n += 1 + sovCoprocessor(uint64(m.LatestBucketsVersion))
	}
	if len(m.BatchResponses) > 0 {
		for _, e := range m.BatchResponses {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	return n
}

func (m *RegionInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovCoprocessor(uint64(m.RegionId))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	return n
}

func (m *ShardInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShardId != 0 {
		n += 1 + sovCoprocessor(uint64(m.ShardId))
	}
	if m.ShardEpoch != 0 {
		n += 1 + sovCoprocessor(uint64(m.ShardEpoch))
	}
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	return n
}

func (m *TableShardInfos) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ExecutorId)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if len(m.ShardInfos) > 0 {
		for _, e := range m.ShardInfos {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	return n
}

func (m *TableRegions) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PhysicalTableId != 0 {
		n += 1 + sovCoprocessor(uint64(m.PhysicalTableId))
	}
	if len(m.Regions) > 0 {
		for _, e := range m.Regions {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	return n
}

func (m *BatchRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.Tp != 0 {
		n += 1 + sovCoprocessor(uint64(m.Tp))
	}
	l = len(m.Data)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if len(m.Regions) > 0 {
		for _, e := range m.Regions {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	if m.StartTs != 0 {
		n += 1 + sovCoprocessor(uint64(m.StartTs))
	}
	if m.SchemaVer != 0 {
		n += 1 + sovCoprocessor(uint64(m.SchemaVer))
	}
	if len(m.TableRegions) > 0 {
		for _, e := range m.TableRegions {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	l = len(m.LogId)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.ConnectionId != 0 {
		n += 1 + sovCoprocessor(uint64(m.ConnectionId))
	}
	l = len(m.ConnectionAlias)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if len(m.TableShardInfos) > 0 {
		for _, e := range m.TableShardInfos {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	return n
}

func (m *BatchResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Data.Size()
	n += 1 + l + sovCoprocessor(uint64(l))
	l = len(m.OtherError)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.ExecDetails != nil {
		l = m.ExecDetails.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if len(m.RetryRegions) > 0 {
		for _, e := range m.RetryRegions {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	return n
}

func (m *StoreBatchTask) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.RegionId != 0 {
		n += 1 + sovCoprocessor(uint64(m.RegionId))
	}
	if m.RegionEpoch != nil {
		l = m.RegionEpoch.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.Peer != nil {
		l = m.Peer.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	if m.TaskId != 0 {
		n += 1 + sovCoprocessor(uint64(m.TaskId))
	}
	return n
}

func (m *StoreBatchTaskResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Data.Size()
	n += 1 + l + sovCoprocessor(uint64(l))
	if m.RegionError != nil {
		l = m.RegionError.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.Locked != nil {
		l = m.Locked.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	l = len(m.OtherError)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.TaskId != 0 {
		n += 1 + sovCoprocessor(uint64(m.TaskId))
	}
	if m.ExecDetailsV2 != nil {
		l = m.ExecDetailsV2.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	return n
}

func (m *DelegateRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Context != nil {
		l = m.Context.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.StartTs != 0 {
		n += 1 + sovCoprocessor(uint64(m.StartTs))
	}
	if len(m.Ranges) > 0 {
		for _, e := range m.Ranges {
			l = e.Size()
			n += 1 + l + sovCoprocessor(uint64(l))
		}
	}
	if m.MemTableSequence != 0 {
		n += 1 + sovCoprocessor(uint64(m.MemTableSequence))
	}
	if m.SnapshotSequence != 0 {
		n += 1 + sovCoprocessor(uint64(m.SnapshotSequence))
	}
	return n
}

func (m *DelegateResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.MemTableData)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	l = len(m.Snapshot)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.RegionError != nil {
		l = m.RegionError.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.Locked != nil {
		l = m.Locked.Size()
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	l = len(m.OtherError)
	if l > 0 {
		n += 1 + l + sovCoprocessor(uint64(l))
	}
	if m.MemTableSequence != 0 {
		n += 1 + sovCoprocessor(uint64(m.MemTableSequence))
	}
	return n
}

func sovCoprocessor(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozCoprocessor(x uint64) (n int) {
	return sovCoprocessor(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *KeyRange) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: KeyRange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: KeyRange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Start = append(m.Start[:0], dAtA[iNdEx:postIndex]...)
			if m.Start == nil {
				m.Start = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.End = append(m.End[:0], dAtA[iNdEx:postIndex]...)
			if m.End == nil {
				m.End = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Request) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Request: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Request: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tp", wireType)
			}
			m.Tp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tp |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, &KeyRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsCacheEnabled", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCacheEnabled = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CacheIfMatchVersion", wireType)
			}
			m.CacheIfMatchVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CacheIfMatchVersion |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SchemaVer", wireType)
			}
			m.SchemaVer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SchemaVer |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsTraceEnabled", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsTraceEnabled = bool(v != 0)
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PagingSize", wireType)
			}
			m.PagingSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PagingSize |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tasks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tasks = append(m.Tasks, &StoreBatchTask{})
			if err := m.Tasks[len(m.Tasks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			m.ConnectionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConnectionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionAlias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionAlias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableShardInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableShardInfos = append(m.TableShardInfos, &TableShardInfos{})
			if err := m.TableShardInfos[len(m.TableShardInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Response) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Response: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Response: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionError == nil {
				m.RegionError = &errorpb.Error{}
			}
			if err := m.RegionError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Locked", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Locked == nil {
				m.Locked = &kvrpcpb.LockInfo{}
			}
			if err := m.Locked.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OtherError", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OtherError = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Range == nil {
				m.Range = &KeyRange{}
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecDetails", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ExecDetails == nil {
				m.ExecDetails = &kvrpcpb.ExecDetails{}
			}
			if err := m.ExecDetails.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsCacheHit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCacheHit = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CacheLastVersion", wireType)
			}
			m.CacheLastVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CacheLastVersion |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CanBeCached", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanBeCached = bool(v != 0)
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecDetailsV2", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ExecDetailsV2 == nil {
				m.ExecDetailsV2 = &kvrpcpb.ExecDetailsV2{}
			}
			if err := m.ExecDetailsV2.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LatestBucketsVersion", wireType)
			}
			m.LatestBucketsVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LatestBucketsVersion |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchResponses", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BatchResponses = append(m.BatchResponses, &StoreBatchTaskResponse{})
			if err := m.BatchResponses[len(m.BatchResponses)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RegionInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RegionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RegionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, &KeyRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ShardInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ShardInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ShardInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardId", wireType)
			}
			m.ShardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShardId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardEpoch", wireType)
			}
			m.ShardEpoch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShardEpoch |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, &KeyRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TableShardInfos) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TableShardInfos: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TableShardInfos: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutorId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ExecutorId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShardInfos = append(m.ShardInfos, &ShardInfo{})
			if err := m.ShardInfos[len(m.ShardInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TableRegions) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TableRegions: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TableRegions: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PhysicalTableId", wireType)
			}
			m.PhysicalTableId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PhysicalTableId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Regions = append(m.Regions, &RegionInfo{})
			if err := m.Regions[len(m.Regions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tp", wireType)
			}
			m.Tp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tp |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Regions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Regions = append(m.Regions, &RegionInfo{})
			if err := m.Regions[len(m.Regions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SchemaVer", wireType)
			}
			m.SchemaVer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SchemaVer |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableRegions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableRegions = append(m.TableRegions, &TableRegions{})
			if err := m.TableRegions[len(m.TableRegions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LogId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LogId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionId", wireType)
			}
			m.ConnectionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ConnectionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectionAlias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectionAlias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableShardInfos", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableShardInfos = append(m.TableShardInfos, &TableShardInfos{})
			if err := m.TableShardInfos[len(m.TableShardInfos)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BatchResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BatchResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OtherError", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OtherError = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecDetails", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ExecDetails == nil {
				m.ExecDetails = &kvrpcpb.ExecDetails{}
			}
			if err := m.ExecDetails.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RetryRegions", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RetryRegions = append(m.RetryRegions, &metapb.Region{})
			if err := m.RetryRegions[len(m.RetryRegions)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreBatchTask) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreBatchTask: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreBatchTask: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			m.RegionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegionId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionEpoch", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionEpoch == nil {
				m.RegionEpoch = &metapb.RegionEpoch{}
			}
			if err := m.RegionEpoch.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Peer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Peer == nil {
				m.Peer = &metapb.Peer{}
			}
			if err := m.Peer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, &KeyRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			m.TaskId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreBatchTaskResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreBatchTaskResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreBatchTaskResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionError == nil {
				m.RegionError = &errorpb.Error{}
			}
			if err := m.RegionError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Locked", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Locked == nil {
				m.Locked = &kvrpcpb.LockInfo{}
			}
			if err := m.Locked.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OtherError", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OtherError = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			m.TaskId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskId |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecDetailsV2", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ExecDetailsV2 == nil {
				m.ExecDetailsV2 = &kvrpcpb.ExecDetailsV2{}
			}
			if err := m.ExecDetailsV2.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelegateRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DelegateRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DelegateRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Context == nil {
				m.Context = &kvrpcpb.Context{}
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ranges", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ranges = append(m.Ranges, &KeyRange{})
			if err := m.Ranges[len(m.Ranges)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MemTableSequence", wireType)
			}
			m.MemTableSequence = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemTableSequence |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SnapshotSequence", wireType)
			}
			m.SnapshotSequence = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SnapshotSequence |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelegateResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DelegateResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DelegateResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MemTableData", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MemTableData = append(m.MemTableData[:0], dAtA[iNdEx:postIndex]...)
			if m.MemTableData == nil {
				m.MemTableData = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Snapshot", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Snapshot = append(m.Snapshot[:0], dAtA[iNdEx:postIndex]...)
			if m.Snapshot == nil {
				m.Snapshot = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.RegionError == nil {
				m.RegionError = &errorpb.Error{}
			}
			if err := m.RegionError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Locked", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Locked == nil {
				m.Locked = &kvrpcpb.LockInfo{}
			}
			if err := m.Locked.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OtherError", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCoprocessor
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OtherError = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MemTableSequence", wireType)
			}
			m.MemTableSequence = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemTableSequence |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCoprocessor(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCoprocessor
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipCoprocessor(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCoprocessor
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCoprocessor
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthCoprocessor
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupCoprocessor
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthCoprocessor
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthCoprocessor        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCoprocessor          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupCoprocessor = fmt.Errorf("proto: unexpected end of group")
)
