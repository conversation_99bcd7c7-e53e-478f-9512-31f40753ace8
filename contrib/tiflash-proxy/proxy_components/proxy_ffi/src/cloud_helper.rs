// Copyright 2022 TiKV Project Authors. Licensed under Apache-2.0.

use std::{
    path::PathBuf,
    sync::{Arc, RwLock},
    time::Duration,
};

use api_version::{api_v2::KEYSPACE_PREFIX_LEN, ApiV2, KeyMode, KvFormat};
use byteorder::{ByteOrder, LittleEndian};
use bytes::Bytes;
use cloud_encryption::MasterKey;
use dashmap::DashMap;
use engine_traits::CF_RAFT;
use hyper::{Body, Request};
use kvengine::{
    context::{IaCtx, PrepareType, SnapCtx},
    dfs::{self, Dfs},
    ia::{manager::Ia<PERSON>anager, util::IaConfig},
    table::{
        columnar::{
            filter::TableScanCtx, Block, ColumnarFilterReader, ColumnarMvccReader,
            GLOBAL_COMMON_HANDLE_END, HANDLE_COL_ID,
        },
        file::FdCache,
        is_deleted,
        schema_file::{<PERSON>hem<PERSON>, <PERSON>hemaBuf, SchemaFile},
        sstable::BlockCache,
        vector_index::{VectorIndexCache, VectorIndexConfig},
    },
    txn_chunk_manager::TxnChunkManager,
    IdVer, Shard, SnapAccess, GLOBAL_SHARD_END_KEY,
};
use kvproto::{
    coprocessor::DelegateResponse,
    metapb::{Peer, Store},
    raft_serverpb::RegionLocalState,
};
use pd_client::PdClient;
use protobuf::Message;
use security::SecurityManager;
use thiserror::Error;
use tidb_query_datatype::{
    codec::{
        mysql::VectorFloat32Decoder,
        table::{decode_common_handle, decode_int_handle},
    },
    FieldTypeFlag,
};
use tikv_util::{
    codec::{
        bytes::{encode_bytes, BytesEncoder},
        number::NumberEncoder,
    },
    error, info,
    memory::MemoryLimiter,
    time::Instant,
    warn,
};
use tipb::{AnnQueryInfo, ColumnInfo};
use txn_types::{Key, WriteType};

use crate::{
    interfaces_ffi::{BaseBuffView, EngineIteratorSeekType},
    metrics::{
        COLUMNAR_FETCH_SNAPSHOT_HISTOGRAM, COLUMNAR_FETCH_SNAPSHOT_RETRY_COUNTER,
        COLUMNAR_PREFETCH_CACHE_HIT_HISTOGRAM, COLUMNAR_PREFETCH_HISTOGRAM,
    },
    RaftStoreProxyEngineTrait,
};

const CF_WRITE_IDX: usize = 0;
const CF_LOCK_IDX: usize = 1;

const BACKOFF_INITIAL: Duration = Duration::from_millis(100);
const BACKOFF_MAX: Duration = Duration::from_secs(10);
const BACKOFF_RETRY_COUNT: usize = 5;

// Due to the discarding of column_id in the vector index, the value assigned to
// ann_query_info may be different in different versions of tidb. For
// compatibility, use this function to get column_id.
// For field changes, see: https://github.com/pingcap/tipb/pull/358
fn get_ann_vec_col_id(ann_query: &AnnQueryInfo) -> Result<i64, crate::Error> {
    // Check `has_deprecated_column_id` first, because `column` is marked as
    // not null in TiDB side so that it will be always populated with some value.
    if ann_query.has_deprecated_column_id() {
        Ok(ann_query.get_deprecated_column_id())
    } else if ann_query.has_column() {
        Ok(ann_query.get_column().get_column_id())
    } else {
        Err(crate::Error::Other(
            "unexpected empty vector column id in ANNQueryInfo".to_string(),
        ))
    }
}

#[derive(Debug, Error)]
pub enum Error {
    #[error("pd client error {0}")]
    PdClientError(#[from] pd_client::Error),
    #[error("region error {0:?}")]
    RegionError(kvproto::errorpb::Error),
    #[error("key is locked {0:?}")]
    KeyIsLocked(kvproto::kvrpcpb::LockInfo),
    #[error("{0}")]
    Other(String),
}

impl From<String> for Error {
    fn from(error: String) -> Self {
        Error::Other(error)
    }
}

impl From<&str> for Error {
    fn from(error: &str) -> Self {
        Error::Other(error.to_string())
    }
}

impl From<hyper::Error> for Error {
    fn from(error: hyper::Error) -> Self {
        Error::Other(error.to_string())
    }
}

impl From<kvengine::Error> for Error {
    fn from(error: kvengine::Error) -> Self {
        Error::Other(error.to_string())
    }
}

#[derive(Clone)]
pub struct PdClientWithCache {
    pd_client: Arc<dyn PdClient>,
    store_cache: Arc<DashMap<u64, Store>>, // store_id -> Store
    region_cache: Arc<DashMap<u64, Peer>>, // region_id -> Peer
}

impl PdClientWithCache {
    pub fn new(pd_client: Arc<dyn PdClient>) -> PdClientWithCache {
        PdClientWithCache {
            pd_client,
            store_cache: Arc::new(DashMap::new()),
            region_cache: Arc::new(DashMap::new()),
        }
    }

    // Get the leader store by region id.
    pub async fn get_leader_store_by_region_id(&self, region_id: u64) -> Result<Store, Error> {
        // First check if we have the peer in cache
        if let Some(peer) = self.region_cache.get(&region_id) {
            let peer = peer.clone();
            let store_id = peer.get_store_id();

            // Check if we have the store in cache
            if let Some(store) = self.store_cache.get(&store_id) {
                return Ok(store.clone());
            }

            // Get store and update cache
            let store = self.pd_client.get_store(store_id)?;
            self.store_cache.insert(store_id, store.clone());
            return Ok(store);
        }

        // If not in cache, fetch from PD
        let (_, peer) = self
            .pd_client
            .get_region_leader_by_id(region_id)
            .await?
            .ok_or_else(|| {
                let mut error = kvproto::errorpb::Error::default();
                error.mut_region_not_found().set_region_id(region_id);
                Error::RegionError(error)
            })?;

        // Update cache after async operation
        self.region_cache.insert(region_id, peer.clone());

        let store_id = peer.get_store_id();
        let store = self.pd_client.get_store(store_id)?;
        self.store_cache.insert(store_id, store.clone());

        Ok(store)
    }

    pub fn evict_region_cache(&self, region_id: u64) {
        self.region_cache.remove(&region_id);
    }

    pub fn get_security_mgr(&self) -> Arc<SecurityManager> {
        self.pd_client.get_security_mgr()
    }
}

#[derive(Clone)]
pub struct CloudHelper {
    dfs: Arc<dyn Dfs>,
    shard_map: Arc<DashMap<(IdVer, u64), Arc<Shard>>>,
    txn_chunk_mgr: TxnChunkManager,
    pd_client: Option<Arc<PdClientWithCache>>,
    vector_index_cache: VectorIndexCache,
    schema_files: Arc<DashMap<u64, SchemaFile>>,
    runtime: Arc<tokio::runtime::Runtime>,
    ia_ctx: IaCtx,
}

impl CloudHelper {
    pub fn new(
        dfs: Arc<dyn Dfs>,
        txn_chunk_mgr: TxnChunkManager,
        pd_client: Option<Arc<dyn PdClient>>,
        data_dir: &PathBuf,
        ia: &IaConfig,
        vector_index_config: VectorIndexConfig,
    ) -> Self {
        let runtime = Arc::new(
            tokio::runtime::Builder::new_multi_thread()
                .thread_name("cloud_helper")
                .worker_threads(8)
                .enable_all()
                .build()
                .unwrap(),
        );
        let local_path = Arc::new(data_dir.join("ia"));
        let ia_mgr = build_ia_mgr(dfs.clone(), runtime.clone(), local_path.clone(), ia);
        let ia_ctx = IaCtx::Enabled(ia_mgr.clone(), local_path);
        let vector_index_cache =
            VectorIndexCache::new(vector_index_config, runtime.handle().clone(), ia_mgr);
        Self {
            dfs: dfs.clone(),
            shard_map: Arc::new(DashMap::new()),
            txn_chunk_mgr,
            pd_client: pd_client.map(|c| Arc::new(PdClientWithCache::new(c))),
            vector_index_cache,
            schema_files: Arc::new(DashMap::new()),
            runtime,
            ia_ctx,
        }
    }
}

impl CloudHelper {
    pub fn make_sst_reader(
        &self,
        cs_pb: kvenginepb::ChangeSet,
        kv_engine: &RwLock<Option<Box<dyn RaftStoreProxyEngineTrait + Sync + Send>>>,
        master_key: Option<MasterKey>,
    ) -> CloudSstReader {
        let shard = Self::get_shard(&self, cs_pb, kv_engine, master_key);
        CloudSstReader::new(shard, CF_WRITE_IDX, Arc::new(self.clone()))
    }

    pub fn make_lock_sst_reader(
        &self,
        cs_pb: kvenginepb::ChangeSet,
        kv_engine: &RwLock<Option<Box<dyn RaftStoreProxyEngineTrait + Sync + Send>>>,
        master_key: Option<MasterKey>,
    ) -> CloudSstReader {
        let shard = Self::get_shard(&self, cs_pb, kv_engine, master_key);
        CloudSstReader::new(shard, CF_LOCK_IDX, Arc::new(self.clone()))
    }

    fn get_shard(
        &self,
        cs_pb: kvenginepb::ChangeSet,
        kv_engine: &RwLock<Option<Box<dyn RaftStoreProxyEngineTrait + Sync + Send>>>,
        master_key: Option<MasterKey>,
    ) -> Arc<Shard> {
        let id_ver = IdVer::from_change_set(&cs_pb);
        let meta_seq = cs_pb.sequence;
        let cs_pb = Self::transform_cs(cs_pb, kv_engine);

        let entry = self.shard_map.entry((id_ver, meta_seq)).or_insert_with(|| {
            let snap_ctx = SnapCtx {
                dfs: self.dfs.clone(),
                master_key: master_key.clone().unwrap(),
                block_cache: BlockCache::None,
                vector_index_cache: None,
                schema_files: Some(self.schema_files.clone()),
                txn_chunk_manager: self.txn_chunk_mgr.clone(),
                ia_ctx: IaCtx::Disabled,
                prepare_type: PrepareType::SstOnly,
                read_columnar: false,
            };
            let shard = self
                .runtime
                .block_on(Shard::from_change_set(
                    &format!("{:?}", id_ver),
                    &snap_ctx,
                    cs_pb,
                    vec![],
                    false,
                ))
                .unwrap();
            let arc_shard = Arc::new(shard);
            arc_shard
        });
        entry.clone()
    }

    pub fn on_drop_reader(&self, id_ver: IdVer, seq: u64) {
        // All other references of readers are dropped, remove it from map.
        if let dashmap::mapref::entry::Entry::Occupied(entry) = self.shard_map.entry((id_ver, seq))
        {
            if Arc::strong_count(entry.get()) == 1 {
                info!("on_drop_reader, remove shard {:?} from map", id_ver);
                entry.remove();
            }
        }
    }

    fn transform_cs(
        cs_pb: kvenginepb::ChangeSet,
        kv_engine: &RwLock<Option<Box<dyn RaftStoreProxyEngineTrait + Sync + Send>>>,
    ) -> kvenginepb::ChangeSet {
        if cs_pb.has_ingest_files() {
            let ingest_files = cs_pb.get_ingest_files();
            let mut cs = kvenginepb::ChangeSet::default();
            cs.set_shard_id(cs_pb.get_shard_id());
            cs.set_shard_ver(cs_pb.get_shard_ver());
            let mut snap = kvenginepb::Snapshot::default();

            let region_id = cs.get_shard_id();
            let guard = kv_engine.read().unwrap();
            let engine = guard.as_ref().unwrap();
            let region_state_key = keys::region_state_key(region_id);
            let mut state = RegionLocalState::default();
            engine.get_value_cf(CF_RAFT, &region_state_key, &mut |value: Result<
                Option<&[u8]>,
                String,
            >| {
                match value {
                    Ok(Some(s)) => {
                        state.merge_from_bytes(s).unwrap();
                    }
                    e => panic!("failed to get regions state of {:?}: {:?}", region_id, e),
                }
            });
            let enc_outer_start = state.get_region().get_start_key().to_vec();
            let enc_outer_end = state.get_region().get_end_key().to_vec();
            let outer_start = Key::from_encoded_slice(&enc_outer_start).to_raw().unwrap();
            let outer_end = Key::from_encoded_slice(&enc_outer_end).to_raw().unwrap();
            let keyspace_id = if ApiV2::parse_key_mode(&outer_start) == KeyMode::Txn {
                ApiV2::get_u32_keyspace_id(ApiV2::get_keyspace_id(&outer_start))
            } else {
                0
            };
            // Set to 4 when the region belongs to a keyspace, otherwise set to 0.
            let inner_key_off = if keyspace_id == 0 { 0 } else { 4 };
            snap.set_outer_start(outer_start);
            snap.set_outer_end(outer_end);
            snap.set_inner_key_off(inner_key_off as u32);
            snap.set_l0_creates(ingest_files.get_l0_creates().into());
            snap.set_table_creates(ingest_files.get_table_creates().into());
            snap.set_properties(ingest_files.get_properties().clone());
            cs.set_snapshot(snap);
            info!(
                "transform ingest_files to snapshot";
                "keyspace_id" => keyspace_id,
                "region_id" => region_id,
                "cs" => ?cs
            );
            cs
        } else {
            assert!(cs_pb.has_snapshot());
            let mut cs = cs_pb.clone();
            let mut snap = cs.take_snapshot();
            let table_creates = snap.take_table_creates();
            snap.set_table_creates(table_creates);
            cs.set_snapshot(snap);
            cs
        }
    }

    pub fn make_columnar_reader(
        &self,
        shard_id: u64,
        shard_ver: u64,
        table_id: i64,
        start_ts: u64,
        mut ranges: Vec<tipb::KeyRange>,
        columns: &[ColumnInfo],
        table_scan: tipb::Executor,
        filter_conditions: Vec<tipb::Expr>,
        ann_query_info: tipb::AnnQueryInfo,
        master_key: Option<MasterKey>,
    ) -> Result<CloudColumnarReader, Error> {
        let dfs = self.dfs.clone();
        let pd_client = self.pd_client.as_ref().unwrap().clone();
        let schema_files = self.schema_files.clone();
        let txn_mgr = self.txn_chunk_mgr.clone();
        let master_key_clone = master_key.clone();
        let ia_ctx = self.ia_ctx.clone();
        // Make sure the ranges are monotonically increasing to support merge reader
        // seek multiple times.
        ranges.sort_by(|a, b| a.get_low().cmp(b.get_low()));

        info!(
            "make_columnar_reader, start_ts: {:?}, table_scan: {:?}, filter_conditions: {:?}, ranges: {:?}",
            start_ts, table_scan, filter_conditions, ranges
        );
        let scan_ctx = TableScanCtx::new(table_scan, filter_conditions);
        let (tx, rx) = tikv_util::mpsc::bounded(1);
        let start = Instant::now_coarse();
        let vector_index_cache = self.vector_index_cache.clone();
        self.runtime.spawn(async move {
            let snap = request_snapshot_from_leader(
                pd_client,
                dfs,
                ia_ctx,
                vector_index_cache,
                schema_files,
                txn_mgr,
                shard_id,
                shard_ver,
                start_ts,
                &master_key_clone.unwrap(),
            )
            .await;
            tx.send(snap).unwrap();
        });
        match rx.recv() {
            Ok(Ok(snap)) => {
                info!(
                    "begin to make columnar reader, elapsed: {:?}",
                    start.saturating_elapsed_secs()
                );
                match CloudColumnarReader::new(
                    snap,
                    self.ia_ctx.clone(),
                    table_id,
                    ranges,
                    columns,
                    &scan_ctx,
                    ann_query_info,
                    start_ts,
                ) {
                    Ok(reader) => {
                        let elapsed = start.saturating_elapsed_secs();
                        info!("make columnar reader"; "shard_id" => shard_id, "shard_ver" => shard_ver, "table_id" => table_id, "cost" => elapsed);
                        COLUMNAR_FETCH_SNAPSHOT_HISTOGRAM.observe(elapsed);
                        Ok(reader)
                    }
                    Err(err) => {
                        error!("failed to make columnar reader"; "err" => ?err);
                        Err(err)
                    }
                }
            }
            Ok(Err(e)) => {
                error!("failed to get snapshot"; "err" => ?e);
                Err(e)
            }
            Err(e) => {
                error!("failed to recv"; "err" => e.to_string());
                Err(Error::Other(e.to_string()))
            }
        }
    }
}

fn build_ia_mgr(
    dfs: Arc<dyn dfs::Dfs>,
    runtime: Arc<tokio::runtime::Runtime>,
    data_dir: Arc<PathBuf>,
    ia: &IaConfig,
) -> IaManager {
    let options = ia.to_manager_options(data_dir.to_path_buf()).unwrap();
    let handle = runtime.handle().clone();
    let fd_cache = FdCache::new(ia.fd_cache_capacity);
    let mgr = IaManager::new(options, dfs, Some(fd_cache), handle.into()).unwrap();
    mgr
}

async fn request_snapshot_from_leader(
    pd_client: Arc<PdClientWithCache>,
    dfs: Arc<dyn dfs::Dfs>,
    ia_ctx: IaCtx,
    vector_index_cache: VectorIndexCache,
    schema_files: Arc<DashMap<u64, SchemaFile>>,
    txn_chunk_manager: TxnChunkManager,
    shard_id: u64,
    shard_ver: u64,
    start_ts: u64,
    master_key: &MasterKey,
) -> Result<SnapAccess, Error> {
    let mut last_err = None;
    let mut leader_changed = true;
    let tag = format!("{}:{}", shard_id, shard_ver);
    let mut store = None;
    let mut backoff = tikv_util::backoff::ExponentialBackoff::new(
        BACKOFF_INITIAL,
        BACKOFF_MAX,
        BACKOFF_RETRY_COUNT,
    );
    let start = Instant::now_coarse();
    while let Ok(next_delay) = backoff.next_delay() {
        if leader_changed {
            let store_res = pd_client.get_leader_store_by_region_id(shard_id).await;
            match store_res {
                Ok(s) => store = Some(s),
                Err(Error::RegionError(err)) if err.has_region_not_found() => {
                    error!("{} get leader store failed, region not found", tag);
                    pd_client.evict_region_cache(shard_id);
                    return Err(Error::RegionError(err));
                }
                Err(e) => {
                    error!(
                        "{} get leader store failed, other error {:?}, will retry",
                        tag, e
                    );
                    last_err = Some(e);
                    pd_client.evict_region_cache(shard_id);
                    COLUMNAR_FETCH_SNAPSHOT_RETRY_COUNTER.inc();
                    tokio::time::sleep(next_delay).await;
                    continue;
                }
            }
        }

        let security_mgr = pd_client.get_security_mgr();
        let uri = security_mgr
        .build_uri(format! {"{}/kvengine/snapshot/{}?start_ts={}&shard_ver={}", store.as_ref().unwrap().status_address, shard_id, start_ts, shard_ver}).unwrap();
        let req = Request::get(uri.clone()).body(Body::empty()).unwrap();
        match send_req_to_store(req, security_mgr.clone()).await {
            Ok(resp) => {
                let mut delegate_resp = DelegateResponse::default();
                delegate_resp.merge_from_bytes(&resp).unwrap();
                if delegate_resp.get_region_error().has_not_leader() {
                    error!("{} request_snapshot_from_leader failed, not leader", tag);
                    pd_client.evict_region_cache(shard_id);
                    leader_changed = true;
                    last_err = Some(Error::RegionError(delegate_resp.take_region_error()));
                    tokio::time::sleep(next_delay).await;
                    continue;
                }
                if delegate_resp.get_region_error().has_epoch_not_match() {
                    // Return epoch not match error to TiDB to retry.
                    error!(
                        "{} request_snapshot_from_leader failed, epoch not match, {:?}",
                        tag,
                        delegate_resp.get_region_error()
                    );
                    return Err(Error::RegionError(delegate_resp.take_region_error()));
                }
                if delegate_resp.has_locked() {
                    error!("{} request_snapshot_from_leader failed, has locked", tag);
                    let lock_info = delegate_resp.take_locked();
                    return Err(Error::KeyIsLocked(lock_info));
                }
                let elapsed = start.saturating_elapsed_secs();
                info!(
                    "{} request_snapshot_from_leader, resp size: {}, memtable size: {}, snapshot size: {}, cost: {:?}",
                    tag,
                    resp.len(),
                    delegate_resp.get_mem_table_data().len(),
                    delegate_resp.get_snapshot().len(),
                    elapsed
                );

                let snap_ctx = SnapCtx {
                    dfs: dfs.clone(),
                    master_key: master_key.clone(),
                    block_cache: BlockCache::None,
                    vector_index_cache: Some(vector_index_cache),
                    schema_files: Some(schema_files),
                    txn_chunk_manager,
                    ia_ctx,
                    prepare_type: PrepareType::ColumnarOnly,
                    read_columnar: true,
                };
                let memory_limiter = MemoryLimiter::new(u64::MAX, None);
                let (snap, _) = SnapAccess::construct_snapshot(
                    &tag,
                    &snap_ctx,
                    delegate_resp.get_mem_table_data(),
                    delegate_resp.get_snapshot(),
                    memory_limiter,
                )
                .await?;
                return Ok(snap);
            }
            Err(err) => {
                error!(
                    "{} request_snapshot_from_leader failed, other error: {:?}, will retry",
                    tag, err
                );
                last_err = Some(err);
                // region maybe merged and destroyed, set leader_changed and get region from pd.
                leader_changed = true;
                pd_client.evict_region_cache(shard_id);
                COLUMNAR_FETCH_SNAPSHOT_RETRY_COUNTER.inc();
                tokio::time::sleep(next_delay).await;
            }
        }
    }
    error!("get snapshot failed"; "shard_id" => shard_id, "shard_ver" => shard_ver, "tag" => &tag, "err" => ?last_err);
    Err(last_err.unwrap())
}

async fn send_req_to_store(
    req: Request<Body>,
    security_mgr: Arc<SecurityManager>,
) -> Result<Bytes, Error> {
    let client = security_mgr.http_client(hyper::Client::builder()).unwrap();
    let uri = req.uri().clone();
    let resp = client.request(req).await?;
    if !resp.status().is_success() {
        let status = resp.status();
        let body = hyper::body::to_bytes(resp.into_body()).await.unwrap();
        return Err(Error::Other(format!("{:?} {:?}: {:?}", uri, status, body)));
    }
    match hyper::body::to_bytes(resp.into_body()).await {
        Ok(body) => Ok(body),
        Err(e) => Err(Error::Other(format!("{:?}", e))),
    }
}

pub struct CloudSstReader {
    // Use Option to allow drop shard in drop.
    shard: Option<Arc<Shard>>,
    iter: kvengine::read::Iterator,
    key_buf: Vec<u8>,
    val_buf: Vec<u8>,
    cf: usize,
    helper: Arc<CloudHelper>,
}

impl CloudSstReader {
    pub fn new(shard: Arc<Shard>, cf: usize, helper: Arc<CloudHelper>) -> Self {
        let all_versions = cf == 0;
        let snap_access = SnapAccess::new(&shard);
        let iter = snap_access.new_iterator(cf, false, all_versions, None, false);
        let outer_start = snap_access.get_start_key();
        let outer_end = snap_access.get_end_key();

        info!(
            "create new sst reader, cf: {} start: {:?} end: {:?}",
            cf, outer_start, outer_end
        );
        let mut reader = Self {
            shard: Some(shard),
            iter,
            key_buf: vec![],
            val_buf: vec![],
            cf,
            helper,
        };
        reader.iter.seek(&outer_start);
        reader.sync_iter();
        reader
    }

    pub fn ffi_remained(&self) -> u8 {
        (!self.key_buf.is_empty()) as u8
    }

    pub fn ffi_key(&self) -> BaseBuffView {
        self.key_buf.as_slice().into()
    }

    pub fn ffi_val(&self) -> BaseBuffView {
        self.val_buf.as_slice().into()
    }

    pub fn ffi_get_split_keys(&self, count: u64) -> Vec<Vec<u8>> {
        let mut split_keys = self
            .shard
            .as_ref()
            .unwrap()
            .get_evenly_split_keys(count as usize)
            .unwrap_or_default()
            .into_iter()
            .map(|k| encode_bytes(&k))
            .collect::<Vec<Vec<u8>>>();
        split_keys.sort_unstable();
        split_keys
    }

    pub fn ffi_approx_size(&self, cf: usize) -> u64 {
        let total = self.shard.as_ref().unwrap().get_cf_total_size(cf);
        total
    }

    fn sync_write_cf_iter(&mut self) {
        self.key_buf.truncate(0);
        while self.iter.valid() {
            assert!(
                !is_deleted(self.iter.meta()),
                "key: {:?}, meta: {:?}",
                self.iter.key(),
                self.iter.meta()
            );

            self.key_buf.encode_bytes(self.iter.key(), false).unwrap();
            let short_value = self.iter.val().to_vec();
            let user_meta = UserMeta::from_slice(self.iter.user_meta());
            self.key_buf.encode_u64_desc(user_meta.commit_ts).unwrap();
            let write_type = if short_value.len() > 0 {
                WriteType::Put
            } else {
                WriteType::Delete
            };
            let write =
                txn_types::Write::new(write_type, user_meta.start_ts.into(), Some(short_value));
            self.val_buf.truncate(0);
            self.val_buf = write.as_ref().to_bytes();
            return;
        }
    }

    fn sync_lock_cf_iter(&mut self) {
        self.key_buf.truncate(0);
        while self.iter.valid() {
            assert!(
                !is_deleted(self.iter.meta()),
                "key: {:?}, meta: {:?}",
                self.iter.key(),
                self.iter.meta()
            );

            self.key_buf.encode_bytes(self.iter.key(), false).unwrap();
            self.val_buf.truncate(0);
            self.val_buf.extend_from_slice(self.iter.val());
            return;
        }
    }

    fn sync_iter(&mut self) {
        if self.cf == 0 {
            self.sync_write_cf_iter();
        } else if self.cf == 1 {
            self.sync_lock_cf_iter();
        } else {
            panic!("invalid cf: {}", self.cf);
        }
    }

    pub fn ffi_next(&mut self) {
        self.iter.next();
        self.sync_iter();
    }

    // ffi_seek only used for TiFlash transform data in concurrency.
    // NOTE: key is in encoded format.
    pub fn ffi_seek(&mut self, seek_type: EngineIteratorSeekType, key: &[u8]) {
        match seek_type {
            EngineIteratorSeekType::First => {
                self.iter.rewind();
            }
            EngineIteratorSeekType::Last => {
                self.iter.seek(GLOBAL_SHARD_END_KEY);
            }
            EngineIteratorSeekType::Key => {
                if key.len() == 0 {
                    self.iter.rewind();
                    self.sync_iter();
                    return;
                }
                self.iter
                    .seek(&Key::from_encoded_slice(key).into_raw().unwrap());
            }
        }
        self.sync_iter();
        return;
    }
}

impl Drop for CloudSstReader {
    fn drop(&mut self) {
        let id_ver = self.shard.as_ref().unwrap().id_ver();
        let seq = self.shard.as_ref().unwrap().get_meta_sequence();
        // We cannot move self.shard out of self. Assign a empty shard to release the
        // real one.
        self.shard = None;
        self.helper.on_drop_reader(id_ver, seq);
    }
}

pub struct CloudColumnarReader {
    tag: String,
    reader: Box<ColumnarMvccReader>,
    ranges: Vec<tipb::KeyRange>,
    block: Block,
    schema: Schema,
    keyspace_id: u32,
    ia_ctx: IaCtx,
    inited: bool,

    read_block_cost: f64,
    serialize_cost: f64,
}

impl CloudColumnarReader {
    pub fn new(
        snap_access: SnapAccess,
        ia_ctx: IaCtx,
        table_id: i64,
        ranges: Vec<tipb::KeyRange>,
        columns: &[ColumnInfo],
        scan_ctx: &TableScanCtx,
        ann_query_info: tipb::AnnQueryInfo,
        start_ts: u64,
    ) -> Result<Self, crate::Error> {
        let ann_query = Arc::new(ann_query_info);
        let mvcc_reader = if ann_query.get_query_type() != tipb::AnnQueryType::InvalidQueryType {
            let index_id = ann_query.get_index_id();
            let target = ann_query
                .get_ref_vec_f32()
                .read_vector_float32()
                .ok()
                .ok_or(crate::Error::Other(
                    "read vector float32 failed".to_string(),
                ))?;
            let col_id = get_ann_vec_col_id(&ann_query)?;
            let top_k = ann_query.get_top_k();
            let schema = snap_access
                .new_schema_from_columns(table_id, columns)
                .ok_or(crate::Error::Other(
                    "construct schema from columns failed".to_string(),
                ))?;
            snap_access
                .new_vector_index_reader(
                    table_id,
                    index_id,
                    col_id,
                    target.as_ref().data(),
                    top_k as usize,
                    schema,
                    start_ts,
                    None, // Pass None to disable filter by handle range.
                    None,
                    Arc::clone(&ann_query),
                )
                .or_else(|_| {
                    warn!("no vector index reader available, use columnar reader instead");
                    snap_access.new_columnar_mvcc_reader(
                        table_id,
                        columns,
                        Some(scan_ctx),
                        start_ts,
                        Some(Arc::clone(&ann_query)),
                    )
                })?
                .ok_or(crate::Error::Other(
                    "no vector index reader available".to_string(),
                ))?
        } else {
            snap_access
                .new_columnar_mvcc_reader(table_id, columns, Some(scan_ctx), start_ts, None)?
                .ok_or(crate::Error::Other("no columnar available".to_string()))?
        };
        let schema_file = snap_access.get_schema_file().unwrap();
        let table_schema = schema_file.get_table(table_id).unwrap();
        let mut columns = columns.to_vec();
        columns.retain(|c| !c.get_pk_handle() && c.get_column_id() != HANDLE_COL_ID as i64);
        let schema_buf = SchemaBuf::new(
            table_id,
            table_schema.handle_column.clone(),
            table_schema.version_column.clone(),
            columns,
            table_schema.pk_col_ids.clone(),
            table_schema.max_col_id,
            table_schema.vector_indexes.clone(),
            table_schema.get_storage_class_spec().clone(),
            table_schema.partitions.clone(),
        );
        let schema = Schema::new(schema_buf);
        let block = Block::new(&schema);

        let reader = Self {
            tag: snap_access.get_tag().to_string(),
            reader: Box::new(mvcc_reader),
            ranges,
            block,
            schema: schema.clone(),
            keyspace_id: snap_access.get_keyspace_id(),
            read_block_cost: 0.0,
            serialize_cost: 0.0,
            ia_ctx,
            inited: false,
        };
        // Do not call init here to avoid blocking the dispatch thread.
        Ok(reader)
    }

    fn init(&mut self) -> Result<(), crate::Error> {
        if let IaCtx::Enabled(ia_mgr, _) = &self.ia_ctx {
            let start = Instant::now_coarse();
            let prefetch = futures::executor::block_on(self.reader.prefetch_ia_remote_segments(
                &self.tag,
                &ia_mgr,
                self.keyspace_id,
                Duration::from_secs(600), // 10 minutes is enough for prefetching one region.
            ))
            .map_err(|e| {
                crate::Error::Other(format!("prefetch_ia_remote_segments failed: {:?}", e))
            })?;
            COLUMNAR_PREFETCH_HISTOGRAM.observe(start.saturating_elapsed_secs());
            COLUMNAR_PREFETCH_CACHE_HIT_HISTOGRAM.observe(prefetch.unwrap_or(1.0) * 100.0);
        }
        futures::executor::block_on(self.update_range_handle());
        self.inited = true;
        Ok(())
    }

    // Set next range handle.
    // Return true if next range handle is set, false if no more range.
    async fn update_range_handle(&mut self) -> bool {
        if self.ranges.is_empty() {
            return false;
        }
        let range = self.ranges.remove(0);
        info!(
            "{} update range handle, table_id: {}, range: {:?}",
            self.tag, self.schema.table_id, range
        );
        let is_common_handle = self.reader.get_schema().is_common_handle();
        if is_common_handle {
            let start_handle =
                decode_common_handle(&range.get_low()[KEYSPACE_PREFIX_LEN..]).unwrap_or(&[]);
            let end_handle = decode_common_handle(&range.get_high()[KEYSPACE_PREFIX_LEN..])
                .unwrap_or(GLOBAL_COMMON_HANDLE_END);
            self.reader
                .set_handle_range(start_handle, end_handle)
                .await
                .unwrap();
        } else {
            let start_handle =
                decode_int_handle(&range.get_low()[KEYSPACE_PREFIX_LEN..]).unwrap_or(i64::MIN);
            let end_handle = decode_int_handle(&range.get_high()[KEYSPACE_PREFIX_LEN..])
                .map(|h| Some(h))
                .unwrap_or(None);
            self.reader
                .set_int_handle_range(start_handle, end_handle)
                .await
                .unwrap();
        }
        true
    }

    pub fn ffi_read_block(&mut self, read_limit: usize) -> kvengine::table::Result<usize> {
        if !self.inited {
            // Postpone the initialization to here to use concurrent threads.
            self.init()
                .map_err(|e| kvengine::table::Error::Other(e.to_string()))?;
        }
        self.block.reset();
        let start = Instant::now_coarse();
        let ret = futures::executor::block_on(self.read_block_with_range(read_limit));
        self.read_block_cost += start.saturating_elapsed_secs();
        ret
    }

    pub async fn read_block_with_range(
        &mut self,
        read_limit: usize,
    ) -> kvengine::table::Result<usize> {
        loop {
            let read_count = self.reader.read_block(&mut self.block, read_limit).await?;
            if read_count > 0 {
                return Ok(read_count);
            }
            // Data may be read out and filtered out in previous range, we need to reset the
            // reader here to continue reading.
            self.reader.reset();
            if !self.update_range_handle().await {
                return Ok(0);
            }
        }
    }

    pub fn ffi_read_handle(&mut self) -> Vec<u8> {
        let start = Instant::now_coarse();
        let mut data = vec![];
        let col_id = self.block.get_handle_buf().col_id();
        let col_info = self.schema.find_column_by_id(col_id as i64).unwrap();
        self.block.get_handle_buf().serialize_for_tiflash(
            &mut data,
            col_info.get_tp() as i32,
            Self::is_unsigned(&col_info),
            col_info.get_column_len() as usize,
        );
        self.serialize_cost += start.saturating_elapsed_secs();
        data
    }

    pub fn ffi_read_version(&mut self) -> Vec<u8> {
        let start = Instant::now_coarse();
        let mut data = vec![];
        let col_id = self.block.get_version_buf().col_id();
        let col_info = self.schema.find_column_by_id(col_id as i64).unwrap();
        self.block.get_version_buf().serialize_for_tiflash(
            &mut data,
            col_info.get_tp() as i32,
            Self::is_unsigned(&col_info),
            col_info.get_column_len() as usize,
        );
        self.serialize_cost += start.saturating_elapsed_secs();
        data
    }

    fn is_unsigned(col_info: &ColumnInfo) -> bool {
        match FieldTypeFlag::from_bits(col_info.get_flag() as u32) {
            Some(flag) => flag.contains(FieldTypeFlag::UNSIGNED),
            None => false,
        }
    }

    pub fn ffi_read_column(&mut self, col_id: i64) -> Vec<u8> {
        let start = Instant::now_coarse();
        let mut data = vec![];
        let col_info = self.schema.find_column_by_id(col_id).unwrap();
        if col_info.get_pk_handle() {
            self.block.get_handle_buf().serialize_for_tiflash(
                &mut data,
                col_info.get_tp() as i32,
                Self::is_unsigned(&col_info),
                col_info.get_column_len() as usize,
            );
        } else {
            self.block.get_column(col_id).serialize_for_tiflash(
                &mut data,
                col_info.get_tp() as i32,
                Self::is_unsigned(&col_info),
                col_info.get_column_len() as usize,
            );
        }
        self.serialize_cost += start.saturating_elapsed_secs();
        data
    }
}

impl Drop for CloudColumnarReader {
    fn drop(&mut self) {
        info!(
            "{} CloudColumnarReader dropped, table_id: {} read_block_cost: {:.3}s, serialize_cost: {:.3}s",
            self.tag, self.schema.table_id, self.read_block_cost, self.serialize_cost
        );
    }
}

pub const USER_META_FORMAT_V1: u8 = 1;

#[derive(Clone, Copy)]
pub struct UserMeta {
    pub start_ts: u64,
    pub commit_ts: u64,
}

impl UserMeta {
    pub fn from_slice(buf: &[u8]) -> Self {
        assert_eq!(buf[0], USER_META_FORMAT_V1);
        Self {
            start_ts: LittleEndian::read_u64(&buf[1..]),
            commit_ts: LittleEndian::read_u64(&buf[9..]),
        }
    }
}
