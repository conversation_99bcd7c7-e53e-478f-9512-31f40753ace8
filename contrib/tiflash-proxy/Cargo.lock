# This file is automatically @generated by Car<PERSON>.
# It is not intended for manual editing.
version = 3

[[package]]
name = "RustyXML"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b5ace29ee3216de37c0546865ad08edef58b0f9e76838ed8959a84a990e58c5"

[[package]]
name = "addr2line"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e61f2b7f93d2c7d2b08263acaa4a363b3e276806c68af6134c44f523bf1aacd"
dependencies = [
 "gimli",
]

[[package]]
name = "adler"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26201604c87b1e01bd3d98f8d5d9a8fcbb815e8cedb41ffccbeb4bf593a35fe"

[[package]]
name = "adler2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "320119579fcad9c21884f5c4861d16174d0e06250625266f50fe6898340abefa"

[[package]]
name = "adler32"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d2e7343e7fc9de883d1b0341e0b13970f764c14101234857d2ddafa1cb1cac2"

[[package]]
name = "afl"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59206260f98d163b3ca42fb29fe551dbcda1d43cf70a244066b2a0666a8fb2a9"
dependencies = [
 "cc",
 "clap",
 "rustc_version 0.2.3",
 "xdg",
]

[[package]]
name = "ahash"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43bb833f0bf979d8475d38fbf09ed3b8a55e1885fe93ad3f93239fc6a4f17b98"
dependencies = [
 "getrandom 0.2.15",
 "once_cell",
 "version_check 0.9.4",
]

[[package]]
name = "ahash"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89da841a80418a9b391ebaea17f5c112ffaaa96f621d2c285b5174da76b9011"
dependencies = [
 "cfg-if 1.0.0",
 "getrandom 0.2.15",
 "once_cell",
 "version_check 0.9.4",
 "zerocopy",
]

[[package]]
name = "aho-corasick"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e60d3430d3a69478ad0993f19238d2df97c507009a52b3c10addcd7f6bcb916"
dependencies = [
 "memchr",
]

[[package]]
name = "aligned-vec"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e0966165eaf052580bd70eb1b32cb3d6245774c0104d1b2793e9650bf83b52a"
dependencies = [
 "equator",
]

[[package]]
name = "aliyun"
version = "0.1.0"
dependencies = [
 "async-trait",
 "aws",
 "base64 0.13.0",
 "chrono",
 "cloud_encryption",
 "http",
 "hyper",
 "hyper-tls",
 "rusoto_core",
 "rusoto_credential",
 "rust-crypto",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
]

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "ansi_term"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee49baf6cb617b853aa8d93bf420db2383fab46d314482ca2803b40d5fde979b"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "anyhow"
version = "1.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e16d2d3311acee920a9eb8d33b8cbc1787ce4a264e85f964c2404b969bdcd487"

[[package]]
name = "api_version"
version = "0.1.0"
dependencies = [
 "bitflags 1.3.2",
 "bytes",
 "codec",
 "engine_traits",
 "kvproto",
 "log_wrappers",
 "match-template",
 "panic_hook",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
 "txn_types",
]

[[package]]
name = "arbitrary"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16971f2f0ce65c5cf2a1546cc6a0af102ecb11e265ddaa9433fb3e5bfdf676a4"

[[package]]
name = "arc-swap"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dabe5a181f83789739c194cbe5a897dde195078fac08568d09221fd6137a7ba8"

[[package]]
name = "arc-swap"
version = "1.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69f7f8c3906b62b754cd5326047894316021dcfe5a194c8ea52bdd94934a3457"

[[package]]
name = "arrayvec"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8d73f9beda665eaa98ab9e4f7442bd4e7de6652587de55b2525e52e29c1b0ba"
dependencies = [
 "nodrop",
]

[[package]]
name = "arrow"
version = "13.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c6bee230122beb516ead31935a61f683715f987c6f003eff44ad6986624105a"
dependencies = [
 "bitflags 1.3.2",
 "chrono",
 "csv",
 "flatbuffers",
 "half 1.8.2",
 "hex 0.4.2",
 "indexmap 1.6.2",
 "lazy_static",
 "lexical-core",
 "multiversion",
 "num 0.4.0",
 "rand 0.8.5",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "arrow-buffer"
version = "53.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a1f34f0faae77da6b142db61deba2cb6d60167592b178be317b341440acba80"
dependencies = [
 "bytes",
 "half 2.1.0",
 "num 0.4.0",
]

[[package]]
name = "assert-type-eq"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd49a41856ee21a0cfb2b1cfbfcca0f1d3e6c257c38939f0d6ecfaf177f2ea47"

[[package]]
name = "async-channel"
version = "1.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2114d64672151c0c5eaa5e131ec84a74f06e1e559830dabba01ca30605d66319"
dependencies = [
 "concurrent-queue",
 "event-listener",
 "futures-core",
]

[[package]]
name = "async-compression"
version = "0.3.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "345fd392ab01f746c717b1357165b76f0b67a60192007b234058c9045fdcf695"
dependencies = [
 "futures-core",
 "futures-io",
 "memchr",
 "pin-project-lite",
 "tokio",
 "zstd 0.11.2+zstd.1.5.2",
 "zstd-safe 5.0.2+zstd.1.5.2",
]

[[package]]
name = "async-io"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8121296a9f05be7f34aa4196b1747243b3b62e048bb7906f644f3fbfc490cf7"
dependencies = [
 "async-lock",
 "autocfg",
 "concurrent-queue",
 "futures-lite",
 "libc 0.2.174",
 "log",
 "parking",
 "polling",
 "slab",
 "socket2",
 "waker-fn",
 "winapi 0.3.9",
]

[[package]]
name = "async-lock"
version = "2.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa24f727524730b077666307f2734b4a1a1c57acb79193127dcc8914d5242dd7"
dependencies = [
 "event-listener",
]

[[package]]
name = "async-recursion"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b43422f69d8ff38f95f1b2bb76517c91589a924d1559a0e935d7c8ce0274c11"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "async-speed-limit"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "481ce9cb6a828f4679495f7376cb6779978d925dd9790b99b48d1bbde6d0f00b"
dependencies = [
 "futures-core",
 "futures-io",
 "futures-timer",
 "pin-project-lite",
]

[[package]]
name = "async-stream"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58982858be7540a465c790b95aaea6710e5139bf8956b1d1344d014fa40100b0"
dependencies = [
 "async-stream-impl 0.2.0",
 "futures-core",
]

[[package]]
name = "async-stream"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dad5c83079eae9969be7fadefe640a1c566901f05ff91ab221de4b6f68d9507e"
dependencies = [
 "async-stream-impl 0.3.3",
 "futures-core",
]

[[package]]
name = "async-stream-impl"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "393356ed99aa7bff0ac486dde592633b83ab02bd254d8c209d5b9f1d0f533480"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "async-stream-impl"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10f203db73a71dfa2fb6dd22763990fa26f3d2625a6da2da900d23b87d26be27"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "async-timer"
version = "1.0.0-beta.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d962799a5863fdf06fbf594e04102130582d010379137e9a98a7e2e693a5885"
dependencies = [
 "error-code",
 "libc 0.2.174",
 "wasm-bindgen",
 "winapi 0.3.9",
]

[[package]]
name = "async-trait"
version = "0.1.58"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e805d94e6b5001b651426cf4cd446b1ab5f319d27bab5c644f61de0a804360c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "atomic"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3410529e8288c463bedb5930f82833bc0c90e5d2fe639a56582a4d09220b281"
dependencies = [
 "autocfg",
]

[[package]]
name = "atty"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1803c647a3ec87095e7ae7acfca019e98de5ec9a7d01343f611cf3152ed71a90"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "autocfg"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d468802bab17cbc0cc575e9b053f41e72aa36bfa6b7f55e3529ffa43161b97fa"

[[package]]
name = "aws"
version = "0.0.1"
dependencies = [
 "async-trait",
 "base64 0.13.0",
 "bytes",
 "chrono",
 "cloud",
 "fail",
 "futures 0.3.30",
 "futures-util",
 "grpcio",
 "http",
 "hyper",
 "hyper-tls",
 "kvproto",
 "lazy_static",
 "md5",
 "prometheus",
 "rusoto_core",
 "rusoto_credential",
 "rusoto_kms",
 "rusoto_mock",
 "rusoto_s3",
 "rusoto_sts",
 "slog",
 "slog-global",
 "thiserror 1.0.30",
 "tikv_util",
 "tokio",
 "url",
 "uuid 0.8.2",
]

[[package]]
name = "axum"
version = "0.5.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acee9fd5073ab6b045a275b3e709c163dd36c90685219cb21804a147b58dba43"
dependencies = [
 "async-trait",
 "axum-core",
 "bitflags 1.3.2",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "itoa 1.0.1",
 "matchit",
 "memchr",
 "mime",
 "percent-encoding",
 "pin-project-lite",
 "serde",
 "sync_wrapper",
 "tokio",
 "tower",
 "tower-http",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-core"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37e5939e02c56fecd5c017c37df4238c0a839fa76b7f97acdd7efb804fd181cc"
dependencies = [
 "async-trait",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "mime",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "azure"
version = "0.0.1"
dependencies = [
 "async-trait",
 "azure_core",
 "azure_identity",
 "azure_storage",
 "azure_storage_blobs",
 "base64 0.13.0",
 "chrono",
 "cloud",
 "futures 0.3.30",
 "futures-util",
 "kvproto",
 "lazy_static",
 "oauth2",
 "regex",
 "slog",
 "slog-global",
 "tikv_util",
 "tokio",
 "url",
]

[[package]]
name = "azure_core"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c61455ab776eedabfc7e166dda27c6c6bc2a882c043c35817501f1bd7440158"
dependencies = [
 "async-trait",
 "base64 0.13.0",
 "bytes",
 "chrono",
 "dyn-clone",
 "futures 0.3.30",
 "getrandom 0.2.15",
 "http",
 "log",
 "oauth2",
 "rand 0.8.5",
 "reqwest",
 "rustc_version 0.4.0",
 "serde",
 "serde_derive",
 "serde_json",
 "thiserror 1.0.30",
 "url",
 "uuid 0.8.2",
]

[[package]]
name = "azure_identity"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebda98657980528a8f0f0f7cc85c88c7dabc160e026bf258d06e54b77b698b08"
dependencies = [
 "async-timer",
 "async-trait",
 "azure_core",
 "chrono",
 "futures 0.3.30",
 "log",
 "oauth2",
 "reqwest",
 "serde",
 "serde_json",
 "thiserror 1.0.30",
 "url",
]

[[package]]
name = "azure_storage"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22c413e8459badf86c9e6e0c84f5894609663bcc8fa5eb1e49bfb985273dac58"
dependencies = [
 "RustyXML",
 "async-trait",
 "azure_core",
 "base64 0.13.0",
 "bytes",
 "chrono",
 "futures 0.3.30",
 "http",
 "log",
 "once_cell",
 "ring 0.16.16",
 "serde",
 "serde-xml-rs",
 "serde_derive",
 "serde_json",
 "thiserror 1.0.30",
 "url",
 "uuid 0.8.2",
]

[[package]]
name = "azure_storage_blobs"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a70ec6fab8a2cae5d774098267870c0f3fbef1cb63cac12afab38b8c17cc8d97"
dependencies = [
 "RustyXML",
 "azure_core",
 "azure_storage",
 "base64 0.13.0",
 "bytes",
 "chrono",
 "futures 0.3.30",
 "http",
 "log",
 "md5",
 "serde",
 "serde-xml-rs",
 "serde_derive",
 "serde_json",
 "thiserror 1.0.30",
 "url",
 "uuid 0.8.2",
]

[[package]]
name = "backtrace"
version = "0.3.61"
source = "git+https://github.com/hehechen/backtrace-rs?branch=v0.3.61#d0aeebbea2298174e4c6edd3d1e54bda0e6624e4"
dependencies = [
 "addr2line",
 "cc",
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "miniz_oxide 0.4.4",
 "object",
 "rustc-demangle",
]

[[package]]
name = "backup"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-channel",
 "aws",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "encryption",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "external_storage",
 "external_storage_export",
 "file_system",
 "futures 0.3.30",
 "futures-util",
 "grpcio",
 "hex 0.4.2",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "pd_client",
 "prometheus",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "security",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tempfile",
 "thiserror 1.0.30",
 "tidb_query_common",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-stream",
 "txn_types",
 "yatp",
]

[[package]]
name = "backup-stream"
version = "0.1.0"
dependencies = [
 "async-compression",
 "async-trait",
 "bytes",
 "chrono",
 "concurrency_manager",
 "crossbeam",
 "crossbeam-channel",
 "dashmap 5.1.0",
 "engine_panic",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "etcd-client",
 "external_storage",
 "external_storage_export",
 "fail",
 "file_system",
 "futures 0.3.30",
 "futures-io",
 "grpcio",
 "hex 0.4.2",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "openssl",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "regex",
 "resolved_ts",
 "security",
 "slog",
 "slog-global",
 "tempdir",
 "tempfile",
 "test_raftstore",
 "test_util",
 "thiserror 1.0.30",
 "tidb_query_datatype",
 "tikv",
 "tikv_alloc",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tonic",
 "txn_types",
 "url",
 "uuid 0.8.2",
 "walkdir",
 "yatp",
]

[[package]]
name = "base64"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "904dfeac50f3cdaba28fc6f57fdcddb75f49ed61346676a78c4ffe55877802fd"

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b3254f16251a8381aa12e40e3c4d2f0199f8c6508fbecb9d91f575e0fbb8c6"

[[package]]
name = "batch-system"
version = "0.1.0"
dependencies = [
 "collections",
 "criterion",
 "crossbeam",
 "derive_more",
 "fail",
 "file_system",
 "kvproto",
 "lazy_static",
 "online_config",
 "prometheus",
 "resource_control",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "bcc"
version = "0.0.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5dbbe5cc2887bc0bc8506b26dcd4c41d1b54bdf4ff1de8e12d404deee60e4ec"
dependencies = [
 "bcc-sys",
 "bitflags 1.3.2",
 "byteorder",
 "libc 0.2.174",
 "regex",
 "thiserror 1.0.30",
]

[[package]]
name = "bcc-sys"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42d3c07869b846ba3306739375e9ed2f8055a8759fcf7f72ab7bf3bc4df38b9b"

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.59.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bd2a9a458e8f4304c52c43ebb0cfbd520289f8379a52e329a38afda99bf8eb8"
dependencies = [
 "bitflags 1.3.2",
 "cexpr",
 "clang-sys",
 "clap",
 "env_logger",
 "lazy_static",
 "lazycell",
 "log",
 "peeking_take_while",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex 1.1.0",
 "which",
]

[[package]]
name = "bindgen"
version = "0.65.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfdf7b466f9a4903edc73f95d6d2bcd5baf8ae620638762244d3f60143643cc5"
dependencies = [
 "bitflags 1.3.2",
 "cexpr",
 "clang-sys",
 "lazy_static",
 "lazycell",
 "log",
 "peeking_take_while",
 "prettyplease 0.2.20",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex 1.1.0",
 "syn 2.0.87",
 "which",
]

[[package]]
name = "bindgen"
version = "0.71.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f58bf3d7db68cfbac37cfc485a8d711e87e064c3d0fe0435b92f7a407f9d6b3"
dependencies = [
 "bitflags 2.5.0",
 "cexpr",
 "clang-sys",
 "itertools 0.13.0",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 2.1.1",
 "shlex 1.1.0",
 "syn 2.0.87",
]

[[package]]
name = "bit-set"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0700ddab506f33b20a03b13996eccd309a48e5ff77d0d95926aa0210fb4e95f1"
dependencies = [
 "bit-vec",
]

[[package]]
name = "bit-vec"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "349f9b6a179ed607305526ca489b34ad0a41aed5f7980fa90eb03160b69598fb"

[[package]]
name = "bit_field"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dcb6dd1c2376d2e096796e234a70e17e94cc2d5d54ff8ce42b28cef1d0d359a4"

[[package]]
name = "bitfield"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46afbd2983a5d5a7bd740ccb198caf5b82f45c40c09c0eed36052d91cb92e719"

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf4b9d6a944f767f8e5e0db018570623c85f3d925ac718db4e06d0187adb21c1"

[[package]]
name = "bitpacking"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c1d3e2bfd8d06048a179f7b17afc3188effa10385e7b00dc65af6aae732ea92"
dependencies = [
 "crunchy",
]

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "generic-array",
]

[[package]]
name = "boolinator"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfa8873f51c92e232f9bac4065cddef41b714152812bfc5f7672ba16d6ef8cd9"

[[package]]
name = "bstr"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d6c2c5b58ab920a4f5aeaaca34b4488074e8cc7596af94e6f8c6ff247c60245"
dependencies = [
 "lazy_static",
 "memchr",
 "regex-automata 0.1.8",
]

[[package]]
name = "bumpalo"
version = "3.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12ae9db68ad7fac5fe51304d20f016c911539251075a214f8e663babefa35187"

[[package]]
name = "bytecount"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c676a478f63e9fa2dd5368a42f28bba0d6c560b775f38583c8bbaa7fcd67c9c"

[[package]]
name = "bytemuck"
version = "1.16.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b236fc92302c97ed75b38da1f4917b5cdda4984745740f153a5d3059e48d725e"

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89b2fd2a0dcf38d7971e2194b6b6eebab45ae01067456a7fd93d5547a61b70be"
dependencies = [
 "serde",
]

[[package]]
name = "bzip2-sys"
version = "0.1.11+1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "736a955f3fa7875102d57c82b8cac37ec45224a07fd32d58f9f7a186b6cd4cdc"
dependencies = [
 "cc",
 "libc 0.2.174",
 "pkg-config",
]

[[package]]
name = "c2-chacha"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d64d04786e0f528460fc884753cf8dddcc466be308f6026f8e355c41a0e4101"
dependencies = [
 "lazy_static",
 "ppv-lite86",
]

[[package]]
name = "cache-padded"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "631ae5198c9be5e753e5cc215e1bd73c2b466a3565173db433f52bb9d3e66dba"

[[package]]
name = "callgrind"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7f788eaf239475a3c1e1acf89951255a46c4b9b46cf3e866fc4d0707b4b9e36"
dependencies = [
 "libc 0.2.174",
 "valgrind_request",
]

[[package]]
name = "camino"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6031a462f977dd38968b6f23378356512feeace69cef817e1a4475108093cec3"
dependencies = [
 "serde",
]

[[package]]
name = "cargo-platform"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbdb825da8a5df079a43676dbe042702f1707b1109f713a01420fbb4cc71fa27"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46e3374c604fb39d1a2f35ed5e4a4e30e60d01fab49446e08f1b3e9a90aef202"
dependencies = [
 "semver 0.9.0",
 "serde",
 "serde_derive",
 "serde_json",
]

[[package]]
name = "cargo_metadata"
version = "0.14.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4acbb09d9ee8e23699b9634375c72795d095bf268439da88562cf9b501f181fa"
dependencies = [
 "camino",
 "cargo-platform",
 "semver 1.0.4",
 "serde",
 "serde_json",
]

[[package]]
name = "case_macros"
version = "0.1.0"

[[package]]
name = "cast"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "926013f2860c46252efceabb19f4a6b308197505082c609025aa6706c011d427"

[[package]]
name = "causal_ts"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-trait",
 "criterion",
 "engine_rocks",
 "engine_traits",
 "enum_dispatch",
 "error_code",
 "fail",
 "futures 0.3.30",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "parking_lot 0.12.1",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "raft",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "test_pd_client",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "cc"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9e8aabfac534be767c909e0690571677d49f41bd8465ae876fe043d52ba5292"
dependencies = [
 "jobserver",
 "libc 0.2.174",
]

[[package]]
name = "cdc"
version = "0.0.1"
dependencies = [
 "api_version",
 "bitflags 1.3.2",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "criterion",
 "crossbeam",
 "engine_rocks",
 "engine_traits",
 "fail",
 "futures 0.3.30",
 "futures-timer",
 "getset",
 "grpcio",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "raftstore",
 "resolved_ts",
 "security",
 "semver 1.0.4",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "test_raftstore",
 "test_util",
 "thiserror 1.0.30",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "cedarwood"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d910bedd62c24733263d0bed247460853c9d22e8956bd4cd964302095e04e90"
dependencies = [
 "smallvec",
]

[[package]]
name = "census"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f4c707c6a209cbe82d10abd08e1ea8995e9ea937d2550646e02798948992be0"

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "cfg-if"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4785bdd1c96b2a846b2bd7cc02e86b6b3dbf14e7e53446c4f54c92a361040822"

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "charabia"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b01abfd2db0eb8c4e7a47ccab5d1f67993736f4e76923ed9ae281c49070645d"
dependencies = [
 "aho-corasick",
 "csv",
 "either",
 "fst",
 "irg-kvariants",
 "jieba-rs",
 "lindera",
 "once_cell",
 "serde",
 "slice-group-by",
 "unicode-normalization",
 "wana_kana",
 "whatlang",
]

[[package]]
name = "chrono"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80094f509cf8b5ae86a4966a39b3ff66cd7e2a3e594accec3743ff3fabeab5b2"
dependencies = [
 "num-integer",
 "num-traits",
 "serde",
 "time 0.1.42",
]

[[package]]
name = "chrono-tz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0e430fad0384e4defc3dc6b1223d1b886087a8bf9b7080e5ae027f73851ea15"
dependencies = [
 "chrono",
 "parse-zoneinfo",
]

[[package]]
name = "clang-sys"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f54d78e30b388d4815220c8dd03fea5656b6c6d32adb59e89061552a102f8da1"
dependencies = [
 "glob",
 "libc 0.2.174",
 "libloading",
]

[[package]]
name = "clap"
version = "2.33.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5067f5bb2d80ef5d68b4c87db81601f0b75bca627bc2ef76b141d7b846a3c6d9"
dependencies = [
 "ansi_term",
 "atty",
 "bitflags 1.3.2",
 "strsim 0.8.0",
 "textwrap",
 "unicode-width",
 "vec_map",
]

[[package]]
name = "clara_fts"
version = "0.0.1"
dependencies = [
 "anyhow",
 "charabia",
 "fastrand 2.3.0",
 "flate2",
 "lazy_static",
 "memmap2 0.9.5",
 "once_cell",
 "ordered-float 5.0.0",
 "paste",
 "prost 0.13.5",
 "prost-build 0.13.5",
 "serde_json",
 "stable_deref_trait",
 "tantivy",
 "tantivy-tokenizer-api",
 "tempfile",
]

[[package]]
name = "cloud"
version = "0.0.1"
dependencies = [
 "async-trait",
 "derive_more",
 "error_code",
 "fail",
 "futures-io",
 "kvproto",
 "lazy_static",
 "openssl",
 "prometheus",
 "protobuf",
 "rusoto_core",
 "thiserror 1.0.30",
 "tikv_util",
 "url",
]

[[package]]
name = "cloud_encryption"
version = "0.1.0"
dependencies = [
 "bytes",
 "derive_more",
 "hmac",
 "openssl",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "serde_json",
 "sha2",
]

[[package]]
name = "cmake"
version = "0.1.48"
source = "git+https://github.com/rust-lang/cmake-rs#00e6b220342a8b0ec4548071928ade38fd5f691b"
dependencies = [
 "cc",
]

[[package]]
name = "codec"
version = "0.0.1"
dependencies = [
 "byteorder",
 "bytes",
 "error_code",
 "libc 0.2.174",
 "panic_hook",
 "protobuf",
 "rand 0.8.5",
 "static_assertions",
 "thiserror 1.0.30",
 "tikv_alloc",
]

[[package]]
name = "codespan-reporting"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e"
dependencies = [
 "termcolor",
 "unicode-width",
]

[[package]]
name = "collections"
version = "0.1.0"
dependencies = [
 "fxhash",
 "tikv_alloc",
]

[[package]]
name = "concurrency_manager"
version = "0.0.1"
dependencies = [
 "criterion",
 "crossbeam-skiplist",
 "fail",
 "futures 0.3.30",
 "kvproto",
 "parking_lot 0.12.1",
 "rand 0.8.5",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "concurrent-queue"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30ed07550be01594c6026cff2a1d7fe9c8f683caa798e12b68694ac9e88286a3"
dependencies = [
 "cache-padded",
]

[[package]]
name = "coprocessor_plugin_api"
version = "0.1.0"
dependencies = [
 "async-trait",
 "atomic",
 "rustc_version 0.3.3",
]

[[package]]
name = "core-foundation"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a89e2ae426ea83155dccf10c0fa6b1463ef6d5fcb44cee0b224a408fa640a62"
dependencies = [
 "core-foundation-sys",
 "libc 0.2.174",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea221b5284a47e40033bf9b66f35f984ec0ea2931eb03505246cd27a963f981b"

[[package]]
name = "core2"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b49ba7ef1ad6107f8824dbe97de947cbaac53c44e7f9756a1fba0d37c1eec505"
dependencies = [
 "memchr",
]

[[package]]
name = "cpp_demangle"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eeaa953eaad386a53111e47172c2fedba671e5684c8dd601a5f474f4f118710f"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "cpu-time"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9e393a7668fe1fad3075085b86c781883000b4ede868f43627b34a87c8b7ded"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "cpuid-bool"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8aebca1129a03dc6dc2b127edd729435bbc4a37e1d5f4d7513165089ceb02634"

[[package]]
name = "crc32c"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8f48d60e5b4d2c53d5c2b1d8a58c849a70ae5e5509b08a48d047e3b65714a74"
dependencies = [
 "rustc_version 0.4.0",
]

[[package]]
name = "crc32fast"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97769d94ddab943e4510d138150169a2758b5ef3eb191a9ee688de3e23ef7b3"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "crc64fast"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a82510de0a7cadd51dc68ff17da70aea0c80157f902230f9b157cecc2566318"

[[package]]
name = "criterion"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1604dafd25fba2fe2d5895a9da139f8dc9b319a5fe5354ca137cbbce4e178d10"
dependencies = [
 "atty",
 "cast",
 "clap",
 "criterion-plot",
 "csv",
 "itertools 0.10.5",
 "lazy_static",
 "num-traits",
 "oorandom",
 "plotters",
 "rayon",
 "regex",
 "serde",
 "serde_cbor",
 "serde_derive",
 "serde_json",
 "tinytemplate",
 "walkdir",
]

[[package]]
name = "criterion-cpu-time"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63aaaf47e457badbcb376c65a49d0f182c317ebd97dc6d1ced94c8e1d09c0f3a"
dependencies = [
 "criterion",
 "libc 0.2.174",
]

[[package]]
name = "criterion-perf-events"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eba5111e09fabb08bfaedbe28c832876bb38d4f9519f715466332880d80b0eac"
dependencies = [
 "criterion",
 "perfcnt",
]

[[package]]
name = "criterion-plot"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d00996de9f2f7559f7f4dc286073197f83e92256a59ed395f9aac01fe717da57"
dependencies = [
 "cast",
 "itertools 0.10.5",
]

[[package]]
name = "crossbeam"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ae5588f6b3c3cb05239e90bd110f257254aecd01e4635400391aeae07497845"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-channel",
 "crossbeam-deque",
 "crossbeam-epoch 0.9.14",
 "crossbeam-queue",
 "crossbeam-utils 0.8.8",
]

[[package]]
name = "crossbeam-channel"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2dd04ddaf88237dc3b8d8f9a3c1004b506b54b3313403944054d23c0870c521"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.8",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.2"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-epoch 0.9.10",
 "crossbeam-utils 0.8.11",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.10"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "autocfg",
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.11",
 "memoffset 0.6.4",
 "once_cell",
 "scopeguard",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46bd5f3f85273295a9d14aedfb86f6aadbff6d8f5295c4a9edb08e819dcf5695"
dependencies = [
 "autocfg",
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.8",
 "memoffset 0.8.0",
 "scopeguard",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f25d8400f4a7a5778f0e4e52384a48cbd9b5c495d110786187fc750075277a2"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.8",
]

[[package]]
name = "crossbeam-skiplist"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "883a5821d7d079fcf34ac55f27a833ee61678110f6b97637cc74513c0d0b42fc"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-epoch 0.9.14",
 "crossbeam-utils 0.8.8",
 "scopeguard",
]

[[package]]
name = "crossbeam-utils"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3c7c73a2d1e9fc0886a08b93e98eb643461230d5f1925e4036204d5f2e261a8"
dependencies = [
 "autocfg",
 "cfg-if 0.1.10",
 "lazy_static",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bf124c720b7686e3c2663cf54062ab0f68a88af2fb6a030e87e30bf721fcb38"
dependencies = [
 "cfg-if 1.0.0",
 "lazy_static",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.11"
source = "git+https://github.com/crossbeam-rs/crossbeam?rev=41ed3d948720f26149b2ebeaf58fe8a193134056#41ed3d948720f26149b2ebeaf58fe8a193134056"
dependencies = [
 "cfg-if 1.0.0",
 "once_cell",
]

[[package]]
name = "crunchy"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a81dae078cea95a014a339291cec439d2f232ebe854a9d672b796c6afafa9b7"

[[package]]
name = "crypto-mac"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4857fd85a0c34b3c3297875b747c1e02e06b6a0ea32dd892d8192b9ce0813ea6"
dependencies = [
 "generic-array",
 "subtle",
]

[[package]]
name = "csv"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acdc4883a9c96732e4733212c01447ebd805833b7275a73ca3ee080fd77afdaf"
dependencies = [
 "csv-core",
 "itoa 1.0.1",
 "ryu",
 "serde",
]

[[package]]
name = "csv-core"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d02f3b0da4c6504f86e9cd789d8dbafab48c2321be74e9987593de5a894d93d"
dependencies = [
 "memchr",
]

[[package]]
name = "cxx"
version = "1.0.129"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbdc8cca144dce1c4981b5c9ab748761619979e515c3d53b5df385c677d1d007"
dependencies = [
 "cc",
 "cxxbridge-flags",
 "cxxbridge-macro",
 "link-cplusplus",
]

[[package]]
name = "cxx-build"
version = "1.0.129"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5764c3142ab44fcf857101d12c0ddf09c34499900557c764f5ad0597159d1fc"
dependencies = [
 "cc",
 "codespan-reporting",
 "once_cell",
 "proc-macro2",
 "quote",
 "scratch",
 "syn 2.0.87",
]

[[package]]
name = "cxxbridge-flags"
version = "1.0.129"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d422aff542b4fa28c2ce8e5cc202d42dbf24702345c1fba3087b2d3f8a1b90ff"

[[package]]
name = "cxxbridge-macro"
version = "1.0.129"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1719100f31492cd6adeeab9a0f46cdbc846e615fdb66d7b398aa46ec7fdd06f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "darling"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fe629a532efad5526454efb0700f86d5ad7ff001acb37e431c8bf017a432a8e"
dependencies = [
 "darling_core 0.10.1",
 "darling_macro 0.10.1",
]

[[package]]
name = "darling"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc7f46116c46ff9ab3eb1597a45688b6715c6e628b5c133e288e709a29bcb4ee"
dependencies = [
 "darling_core 0.20.11",
 "darling_macro 0.20.11",
]

[[package]]
name = "darling_core"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee54512bec54b41cf2337a22ddfadb53c7d4c738494dc2a186d7b037ad683b85"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim 0.9.2",
 "syn 1.0.103",
]

[[package]]
name = "darling_core"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d00b9596d185e565c2207a0b01f8bd1a135483d02d9b7b0a54b11da8d53412e"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim 0.11.1",
 "syn 2.0.87",
]

[[package]]
name = "darling_macro"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0cd3e432e52c0810b72898296a69d66b1d78d1517dff6cde7a130557a55a62c1"
dependencies = [
 "darling_core 0.10.1",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "darling_macro"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc34b93ccb385b40dc71c6fceac4b2ad23662c7eeb248cf10d529b7e055b6ead"
dependencies = [
 "darling_core 0.20.11",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "dary_heap"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04d2cd9c18b9f454ed67da600630b021a8a80bf33f8c95896ab33aaf1c26b728"

[[package]]
name = "dashmap"
version = "4.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e77a43b28d0668df09411cb0bc9a8c2adc40f9a048afe863e05fd43251e8e39c"
dependencies = [
 "cfg-if 1.0.0",
 "num_cpus",
]

[[package]]
name = "dashmap"
version = "5.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0834a35a3fce649144119e18da2a4d8ed12ef3862f47183fd46f625d072d96c"
dependencies = [
 "cfg-if 1.0.0",
 "num_cpus",
 "parking_lot 0.12.1",
]

[[package]]
name = "dashmap"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5041cc499144891f3790297212f32a74fb938e5136a14943f338ef9e0ae276cf"
dependencies = [
 "cfg-if 1.0.0",
 "crossbeam-utils 0.8.8",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "debugid"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef552e6f588e446098f6ba40d89ac146c8c7b64aade83c051ee00bb5d2bc18d"
dependencies = [
 "uuid 1.2.1",
]

[[package]]
name = "deranged"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c9e6a11ca8224451684bc0d7d5a7adbf8f2fd6887261a1cfc3c0432f9d4068e"
dependencies = [
 "powerfmt",
 "serde",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "derive_builder"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "507dfb09ea8b7fa618fcf76e953f4f5e192547945816d5358edffe39f6f94947"
dependencies = [
 "derive_builder_macro",
]

[[package]]
name = "derive_builder_core"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d5bcf7b024d6835cfb3d473887cd966994907effbe9227e8c8219824d06c4e8"
dependencies = [
 "darling 0.20.11",
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "derive_builder_macro"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab63b0e2bf4d5928aff72e83a7dace85d7bba5fe12dcc3c5a572d78caffd3f3c"
dependencies = [
 "derive_builder_core",
 "syn 2.0.87",
]

[[package]]
name = "derive_more"
version = "0.99.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a806e96c59a76a5ba6e18735b6cf833344671e61e7863f2edb5c518ea2cac95c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array",
]

[[package]]
name = "dirs-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b98cf8ebf19c3d1b223e151f99a4f9f0690dca41414773390fc824184ac833e1"
dependencies = [
 "cfg-if 1.0.0",
 "dirs-sys-next",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc 0.2.174",
 "redox_users",
 "winapi 0.3.9",
]

[[package]]
name = "downcast-rs"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b325c5dbd37f80359721ad39aca5a29fb04c89279657cffdda8736d0c0b9d2"

[[package]]
name = "dyn-clone"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee2626afccd7561a06cf1367e2950c4718ea04565e20fb5029b6c7d8ad09abcf"

[[package]]
name = "either"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c757948c5ede0e46177b7add2e67155f70e33c07fea8284df6576da70b3719"

[[package]]
name = "encoding"
version = "0.2.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b0d943856b990d12d3b55b359144ff341533e516d94098b1d3fc1ac666d36ec"
dependencies = [
 "encoding-index-japanese",
 "encoding-index-korean",
 "encoding-index-simpchinese",
 "encoding-index-singlebyte",
 "encoding-index-tradchinese",
]

[[package]]
name = "encoding-index-japanese"
version = "1.20141219.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04e8b2ff42e9a05335dbf8b5c6f7567e5591d0d916ccef4e0b1710d32a0d0c91"
dependencies = [
 "encoding_index_tests",
]

[[package]]
name = "encoding-index-korean"
version = "1.20141219.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4dc33fb8e6bcba213fe2f14275f0963fd16f0a02c878e3095ecfdf5bee529d81"
dependencies = [
 "encoding_index_tests",
]

[[package]]
name = "encoding-index-simpchinese"
version = "1.20141219.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d87a7194909b9118fc707194baa434a4e3b0fb6a5a757c73c3adb07aa25031f7"
dependencies = [
 "encoding_index_tests",
]

[[package]]
name = "encoding-index-singlebyte"
version = "1.20141219.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3351d5acffb224af9ca265f435b859c7c01537c0849754d3db3fdf2bfe2ae84a"
dependencies = [
 "encoding_index_tests",
]

[[package]]
name = "encoding-index-tradchinese"
version = "1.20141219.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd0e20d5688ce3cab59eb3ef3a2083a5c77bf496cb798dc6fcdb75f323890c18"
dependencies = [
 "encoding_index_tests",
]

[[package]]
name = "encoding_index_tests"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a246d82be1c9d791c5dfde9a2bd045fc3cbba3fa2b11ad558f27d01712f00569"

[[package]]
name = "encoding_rs"
version = "0.8.29"
source = "git+https://github.com/xiongjiwei/encoding_rs.git?rev=68e0bc5a72a37a78228d80cd98047326559cf43c#68e0bc5a72a37a78228d80cd98047326559cf43c"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "encoding_rs"
version = "0.8.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75030f3c4f45dafd7586dd6780965a8c7e8e285a5ecb86713e63a79c5b2766f3"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "encoding_rs_io"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cc3c5651fb62ab8aa3103998dade57efdd028544bd300516baa31840c252a83"
dependencies = [
 "encoding_rs 0.8.35",
]

[[package]]
name = "encryption"
version = "0.0.1"
dependencies = [
 "async-trait",
 "byteorder",
 "bytes",
 "crc32fast",
 "crossbeam",
 "derive_more",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "futures 0.3.30",
 "futures-util",
 "hex 0.4.2",
 "kvproto",
 "lazy_static",
 "matches",
 "online_config",
 "openssl",
 "prometheus",
 "protobuf",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tempfile",
 "test_util",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "toml",
]

[[package]]
name = "encryption_export"
version = "0.0.1"
dependencies = [
 "async-trait",
 "aws",
 "cloud",
 "derive_more",
 "encryption",
 "error_code",
 "file_system",
 "kvproto",
 "openssl",
 "protobuf",
 "rust-ini",
 "slog",
 "slog-global",
 "structopt",
 "tikv_util",
]

[[package]]
name = "engine_panic"
version = "0.0.1"
dependencies = [
 "engine_traits",
 "kvproto",
 "raft",
 "tikv_alloc",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_rocks"
version = "0.0.1"
dependencies = [
 "api_version",
 "case_macros",
 "collections",
 "derive_more",
 "encryption",
 "engine_traits",
 "fail",
 "file_system",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "num_cpus",
 "online_config",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "rand 0.8.5",
 "regex",
 "rocksdb",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "slog_derive",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.42",
 "toml",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_rocks_helper"
version = "0.1.0"
dependencies = [
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "fail",
 "futures 0.3.30",
 "keys",
 "kvproto",
 "lazy_static",
 "pd_client",
 "prometheus",
 "protobuf",
 "raftstore",
 "slog",
 "slog-global",
 "tempfile",
 "tikv_util",
]

[[package]]
name = "engine_store_ffi"
version = "0.0.1"
dependencies = [
 "api_version",
 "batch-system",
 "bitflags 1.3.2",
 "byteorder",
 "bytes",
 "cloud_encryption",
 "collections",
 "crossbeam",
 "dashmap 4.0.2",
 "derivative",
 "encryption",
 "encryption_export",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_tiflash",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "fs2",
 "futures 0.3.30",
 "futures-util",
 "getset",
 "grpcio-health",
 "into_other",
 "itertools 0.10.5",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "log",
 "log_wrappers",
 "online_config",
 "ordered-float 2.10.0",
 "panic_hook",
 "parking_lot 0.12.1",
 "pd_client",
 "portable-atomic",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "proxy_ffi",
 "raft",
 "raft-proto",
 "raftstore",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "serde_with",
 "slog",
 "slog-global",
 "smallvec",
 "sst_importer",
 "tempfile",
 "test_sst_importer",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.42",
 "tokio",
 "tokio-timer",
 "tracker",
 "txn_types",
 "uuid 0.8.2",
 "yatp",
]

[[package]]
name = "engine_test"
version = "0.0.1"
dependencies = [
 "collections",
 "encryption",
 "engine_panic",
 "engine_rocks",
 "engine_traits",
 "file_system",
 "raft_log_engine",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "engine_tiflash"
version = "0.0.1"
dependencies = [
 "api_version",
 "case_macros",
 "cloud_encryption",
 "collections",
 "derive_more",
 "encryption",
 "engine_rocks",
 "engine_traits",
 "fail",
 "file_system",
 "keys",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log_wrappers",
 "num_cpus",
 "online_config",
 "portable-atomic",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "proxy_ffi",
 "raft",
 "rand 0.8.5",
 "regex",
 "rocksdb",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "slog_derive",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.42",
 "toml",
 "tracker",
 "txn_types",
 "yatp",
]

[[package]]
name = "engine_traits"
version = "0.0.1"
dependencies = [
 "bytes",
 "case_macros",
 "cloud_encryption",
 "collections",
 "error_code",
 "fail",
 "file_system",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "protobuf",
 "raft",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
 "toml",
 "tracker",
 "txn_types",
]

[[package]]
name = "engine_traits_tests"
version = "0.0.1"
dependencies = [
 "engine_test",
 "engine_traits",
 "panic_hook",
 "tempfile",
 "tikv_alloc",
]

[[package]]
name = "enum_dispatch"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eb359f1476bf611266ac1f5355bc14aeca37b299d0ebccc038ee7058891c9cb"
dependencies = [
 "once_cell",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "env_logger"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b2cf0344971ee6c64c31be0d530793fba457d322dfec2810c453d0ef228f9c3"
dependencies = [
 "atty",
 "humantime",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "equator"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c35da53b5a021d2484a7cc49b2ac7f2d840f8236a286f84202369bd338d761ea"
dependencies = [
 "equator-macro",
]

[[package]]
name = "equator-macro"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bf679796c0322556351f287a51b49e48f7c4986e727b5dd78c972d30e2e16cc"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "equivalent"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5443807d6dff69373d433ab9ef5378ad8df50ca6298caf15de6e52e24aaf54d5"

[[package]]
name = "errno"
version = "0.3.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "778e2ac28f6c47af28e4907f13ffd1e1ddbd400980a9abd7c8df189bf578a5ad"
dependencies = [
 "libc 0.2.174",
 "windows-sys 0.52.0",
]

[[package]]
name = "error-chain"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ab49e9dcb602294bc42f9a7dfc9bc6e936fca4418ea300dbfb84fe16de0b7d9"
dependencies = [
 "backtrace",
 "version_check 0.1.5",
]

[[package]]
name = "error-code"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5115567ac25674e0043e472be13d14e537f37ea8aa4bdc4aef0c89add1db1ff"
dependencies = [
 "libc 0.2.174",
 "str-buf",
]

[[package]]
name = "error_code"
version = "0.0.1"
dependencies = [
 "grpcio",
 "kvproto",
 "lazy_static",
 "raft",
 "serde",
 "tikv_alloc",
]

[[package]]
name = "etcd-client"
version = "0.10.2"
source = "git+https://github.com/pingcap/etcd-client?rev=14a6f8731f1890d5fd2f6e16a9f0d0a306b0599e#14a6f8731f1890d5fd2f6e16a9f0d0a306b0599e"
dependencies = [
 "http",
 "hyper",
 "hyper-openssl",
 "openssl",
 "prost 0.11.2",
 "tokio",
 "tokio-stream",
 "tonic",
 "tonic-build",
 "tower",
 "tower-service",
 "visible",
]

[[package]]
name = "event-listener"
version = "2.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7531096570974c3a9dcf9e4b8e1cede1ec26cf5046219fb3b9d897503b9be59"

[[package]]
name = "example_coprocessor_plugin"
version = "0.1.0"
dependencies = [
 "coprocessor_plugin_api",
]

[[package]]
name = "external_storage"
version = "0.0.1"
dependencies = [
 "async-compression",
 "async-trait",
 "bytes",
 "encryption",
 "engine_traits",
 "fail",
 "ffi-support",
 "file_system",
 "futures 0.3.30",
 "futures-executor",
 "futures-io",
 "futures-util",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libloading",
 "matches",
 "openssl",
 "prometheus",
 "protobuf",
 "rand 0.8.5",
 "rusoto_core",
 "rust-ini",
 "slog",
 "slog-global",
 "structopt",
 "tempfile",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-util",
 "url",
]

[[package]]
name = "external_storage_export"
version = "0.0.1"
dependencies = [
 "async-compression",
 "async-trait",
 "aws",
 "azure",
 "cloud",
 "encryption",
 "engine_traits",
 "external_storage",
 "ffi-support",
 "file_system",
 "futures 0.3.30",
 "futures-executor",
 "futures-io",
 "futures-util",
 "gcp",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "libloading",
 "matches",
 "nix 0.24.1",
 "once_cell",
 "protobuf",
 "rust-ini",
 "signal-hook",
 "slog",
 "slog-global",
 "slog-term",
 "structopt",
 "tempfile",
 "tikv_util",
 "tokio",
 "tokio-util",
 "url",
]

[[package]]
name = "fail"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3245a0ca564e7f3c797d20d833a6870f57a728ac967d5225b3ffdef4465011"
dependencies = [
 "lazy_static",
 "log",
 "rand 0.8.5",
]

[[package]]
name = "farmhash"
version = "1.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f35ce9c8fb9891c75ceadbc330752951a4e369b50af10775955aeb9af3eee34b"

[[package]]
name = "fastdivide"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9afc2bd4d5a73106dd53d10d73d3401c2f32730ba2c0b93ddb888a8983680471"

[[package]]
name = "fastrand"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e51093e27b0797c359783294ca4f0a911c270184cb10f85783b118614a1501be"
dependencies = [
 "instant",
]

[[package]]
name = "fastrand"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37909eebbb50d72f9059c3b6d82c0463f2ff062c9e95845c43a6c9c0355411be"

[[package]]
name = "ffi-support"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f85d4d1be103c0b2d86968f0b0690dc09ac0ba205b90adb0389b552869e5000e"
dependencies = [
 "lazy_static",
 "log",
]

[[package]]
name = "file_system"
version = "0.1.0"
dependencies = [
 "bcc",
 "collections",
 "crc32fast",
 "crossbeam-utils 0.8.8",
 "fs2",
 "lazy_static",
 "libc 0.2.174",
 "maligned",
 "online_config",
 "openssl",
 "parking_lot 0.12.1",
 "prometheus",
 "prometheus-static-metric",
 "rand 0.8.5",
 "serde",
 "slog",
 "slog-global",
 "strum 0.20.0",
 "tempfile",
 "thread_local",
 "tikv_alloc",
 "tikv_util",
 "tokio",
]

[[package]]
name = "filedescriptor"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed3d8a5e20435ff00469e51a0d82049bae66504b5c429920dadf9bb54d47b3f"
dependencies = [
 "libc 0.2.174",
 "thiserror 1.0.30",
 "winapi 0.3.9",
]

[[package]]
name = "filetime"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d34cfa13a63ae058bfa601fe9e313bbdb3746427c1459185464ce0fcf62e1e8"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "redox_syscall 0.2.11",
 "winapi 0.3.9",
]

[[package]]
name = "findshlibs"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d691fdb3f817632d259d09220d4cf0991dbb2c9e59e044a02a59194bf6e14484"
dependencies = [
 "cc",
 "lazy_static",
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "flatbuffers"
version = "2.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b428b715fdbdd1c364b84573b5fdc0f84f8e423661b9f398735278bc7f2b6a"
dependencies = [
 "bitflags 1.3.2",
 "smallvec",
 "thiserror 1.0.30",
]

[[package]]
name = "flate2"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a3d7db9596fecd151c5f638c0ee5d5bd487b6e0ea232e5dc96d5250f6f94b1d"
dependencies = [
 "crc32fast",
 "libz-sys",
 "miniz_oxide 0.8.9",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foldhash"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9c4f5dac5e15c24eb999c26181a6ca40b39fe946cbe4c263c7209467bc83af2"

[[package]]
name = "foreign-types"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1"
dependencies = [
 "foreign-types-shared",
]

[[package]]
name = "foreign-types-shared"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b"

[[package]]
name = "form_urlencoded"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fc25a87fa4fd2094bffb06925852034d90a17f0d1e05197d4956d3555752191"
dependencies = [
 "matches",
 "percent-encoding",
]

[[package]]
name = "fs2"
version = "0.4.3"
source = "git+https://github.com/tabokie/fs2-rs?branch=tikv#cd503764a19a99d74c1ab424dd13d6bcd093fcae"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "fs4"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7e180ac76c23b45e767bd7ae9579bc0bb458618c4bc71835926e098e61d15f8"
dependencies = [
 "rustix 0.38.44",
 "windows-sys 0.52.0",
]

[[package]]
name = "fs_extra"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f2a4a2034423744d2cc7ca2068453168dcdb82c438419e639a26bd87839c674"

[[package]]
name = "fsevent"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ab7d1bd1bd33cc98b0889831b72da23c0aa4df9cec7e0702f46ecea04b35db6"
dependencies = [
 "bitflags 1.3.2",
 "fsevent-sys",
]

[[package]]
name = "fsevent-sys"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f41b048a94555da0f42f1d632e2e19510084fb8e303b0daa2816e733fb3644a0"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "fslock"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57eafdd0c16f57161105ae1b98a1238f97645f2f588438b2949c99a2af9616bf"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "fst"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ab85b9b05e3978cc9a9cf8fea7f01b494e1a09ed3037e16ba39edc7a29eb61a"

[[package]]
name = "fuchsia-cprng"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a06f77d526c1a601b7c4cdd98f54b5eaabffc14d5f2f0296febdc7f357c6d3ba"

[[package]]
name = "fuchsia-zircon"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e9763c69ebaae630ba35f74888db465e49e259ba1bc0eda7d06f4a067615d82"
dependencies = [
 "bitflags 1.3.2",
 "fuchsia-zircon-sys",
]

[[package]]
name = "fuchsia-zircon-sys"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3dcaa9ae7725d12cdb85b3ad99a434db70b468c09ded17e012d86b5c1010f7a7"

[[package]]
name = "futures"
version = "0.1.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a471a38ef8ed83cd6e40aa59c1ffe17db6855c18e3604d9c4ed8c08ebc28678"

[[package]]
name = "futures"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "645c6916888f6cb6350d2550b80fb63e734897a8498abe35cfb732b6487804b0"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eac8f7d7865dcb88bd4373ab671c8cf4508703796caa2b1985a9ca867b3fcb78"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfc6580bb841c5a68e9ef15c77ccc837b40a7504914d52e47b8b0e9bbda25a1d"

[[package]]
name = "futures-executor"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a576fc72ae164fca6b9db127eaa9a9dda0d61316034f33a0a0d4eda41f02b01d"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
 "num_cpus",
]

[[package]]
name = "futures-io"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a44623e20b9681a318efdd71c299b6b222ed6f231972bfe2f224ebad6311f0c1"

[[package]]
name = "futures-lite"
version = "1.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49a9d51ce47660b1e808d3c990b4709f2f415d928835a17dfd16991515c46bce"
dependencies = [
 "fastrand 1.9.0",
 "futures-core",
 "futures-io",
 "memchr",
 "parking",
 "pin-project-lite",
 "waker-fn",
]

[[package]]
name = "futures-macro"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87750cf4b7a4c0625b1529e4c543c2182106e4dedc60a2a6455e00d212c489ac"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "futures-sink"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fb8e00e87438d937621c1c6269e53f536c14d3fbd6a042bb24879e57d474fb5"

[[package]]
name = "futures-task"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38d84fa142264698cdce1a9f9172cf383a0c82de1bddcf3092901442c4097004"

[[package]]
name = "futures-timer"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e64b03909df88034c26dc1547e8970b91f98bdb65165d6a4e9110d94263dbb2c"

[[package]]
name = "futures-util"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d6401deb83407ab3da39eba7e33987a73c3df0c82b4bb5813ee871c19c41d48"
dependencies = [
 "futures 0.1.31",
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "fuzz"
version = "0.0.1"
dependencies = [
 "anyhow",
 "cargo_metadata 0.9.1",
 "lazy_static",
 "regex",
 "structopt",
]

[[package]]
name = "fuzz-targets"
version = "0.0.1"
dependencies = [
 "anyhow",
 "byteorder",
 "tidb_query_datatype",
 "tikv_util",
]

[[package]]
name = "fuzzer-afl"
version = "0.0.1"
dependencies = [
 "afl",
 "fuzz-targets",
]

[[package]]
name = "fuzzer-honggfuzz"
version = "0.0.1"
dependencies = [
 "fuzz-targets",
 "honggfuzz",
]

[[package]]
name = "fuzzer-libfuzzer"
version = "0.0.1"
dependencies = [
 "fuzz-targets",
 "libfuzzer-sys",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "gag"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a713bee13966e9fbffdf7193af71d54a6b35a0bb34997cd6c9519ebeb5005972"
dependencies = [
 "filedescriptor",
 "tempfile",
]

[[package]]
name = "gcc"
version = "0.3.55"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f5f3913fa0bfe7ee1fd8248b6b9f42a5af4b9d65ec2dd2c3c26132b950ecfc2"

[[package]]
name = "gcp"
version = "0.0.1"
dependencies = [
 "async-trait",
 "cloud",
 "futures-util",
 "http",
 "hyper",
 "hyper-tls",
 "kvproto",
 "matches",
 "pin-project",
 "slog",
 "slog-global",
 "tame-gcs",
 "tame-oauth",
 "tikv_util",
 "tokio",
 "url",
]

[[package]]
name = "gen-proxy-ffi"
version = "0.1.0"
dependencies = [
 "bindgen 0.65.1",
 "clap",
 "walkdir",
]

[[package]]
name = "generic-array"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "501466ecc8a30d1d3b7fc9229b122b2ce8ed6e9d9223f1138d4babb253e51817"
dependencies = [
 "typenum",
 "version_check 0.9.4",
]

[[package]]
name = "getrandom"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "473a1265acc8ff1e808cd0a1af8cee3c2ee5200916058a2ca113c29f2d903571"
dependencies = [
 "cfg-if 0.1.10",
 "libc 0.2.174",
 "wasi 0.7.0",
]

[[package]]
name = "getrandom"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4567c8db10ae91089c99af84c68c38da3ec2f087c3f82960bcdbf3656b6f4d7"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "libc 0.2.174",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "getrandom"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26145e563e54f2cadc477553f1ec5ee650b00862f0a58bcd12cbdc5f0ea2d2f4"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "r-efi",
 "wasi 0.14.2+wasi-0.2.4",
]

[[package]]
name = "getset"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24b328c01a4d71d2d8173daa93562a73ab0fe85616876f02500f53d82948c504"
dependencies = [
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "gimli"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0a01e0497841a3b2db4f8afa483cce65f7e96a3498bd6c541734792aeac8fe7"

[[package]]
name = "glob"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8d1add55171497b4705a648c6b583acafb01d58050a51727785f0b2c8e0a2b2"

[[package]]
name = "gperftools"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20a3fc5818b1223ec628fc6998c8900486208b577f78c07500d4b52f983ebc9d"
dependencies = [
 "error-chain",
 "lazy_static",
 "pkg-config",
]

[[package]]
name = "grpcio"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f2506de56197d01821c2d1d21082d2dcfd6c82d7a1d6e04d33f37aab6130632"
dependencies = [
 "futures-executor",
 "futures-util",
 "grpcio-sys",
 "libc 0.2.174",
 "log",
 "parking_lot 0.11.1",
 "protobuf",
]

[[package]]
name = "grpcio-compiler"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed97a17310fd00ff4109357584a00244e2a785d05b7ee0ef4d1e8fb1d84266df"
dependencies = [
 "protobuf",
]

[[package]]
name = "grpcio-health"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a37eae605cd21f144b7c7fd0e64e57af9f73d132756fef5b706db110c3ec7ea0"
dependencies = [
 "futures-executor",
 "futures-util",
 "grpcio",
 "log",
 "protobuf",
]

[[package]]
name = "grpcio-sys"
version = "0.10.3+1.44.0-patched"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f23adc509a3c4dea990e0ab8d2add4a65389ee69c288b7851d75dd1df7a6d6c6"
dependencies = [
 "bindgen 0.59.2",
 "cc",
 "cmake",
 "libc 0.2.174",
 "libz-sys",
 "openssl-sys",
 "pkg-config",
 "walkdir",
]

[[package]]
name = "h2"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f9f29bc9dda355256b2916cf526ab02ce0aeaaaf2bad60d65ef3f12f11dd0f4"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http",
 "indexmap 1.6.2",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "half"
version = "1.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eabb4a44450da02c90444cf74558da904edde8fb4e9035a9a6a4e15445af0bd7"

[[package]]
name = "half"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad6a9459c9c30b177b925162351f97e7d967c7ea8bab3b8352805327daf45554"
dependencies = [
 "crunchy",
]

[[package]]
name = "hashbrown"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7afe4a420e3fe79967a00898cc1f4db7c8a49a9333a29f8a4bd76a253d5cd04"

[[package]]
name = "hashbrown"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c21d40587b92fa6a6c6e3c1bdbf87d75511db5672f9c93175574b3a00df1758"
dependencies = [
 "ahash 0.7.4",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"
dependencies = [
 "ahash 0.8.11",
 "allocator-api2",
]

[[package]]
name = "hashbrown"
version = "0.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5971ac85611da7067dbfcabef3c70ebb5606018acd9e2a3903a0da507521e0d5"
dependencies = [
 "allocator-api2",
 "equivalent",
 "foldhash",
]

[[package]]
name = "heck"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20564e78d53d2bb135c343b3f47714a56af2061f1c928fdb541dc7b9fdd94205"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "heck"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2540771e65fc8cb83cd6e8a237f70c319bd5c29f78ed1084ba5d50eeac86f7f9"

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "307c3c9f937f38e3534b1d6447ecf090cafcc9744e4a6360e8b037b2cf5af120"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "hex"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "805026a5d0141ffc30abb3be3173848ad46a1b1664fe632428479619a3644d77"

[[package]]
name = "hex"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "644f9158b2f133fd50f5fb3242878846d9eb792e445c893805ff0e3824006e35"

[[package]]
name = "hmac"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1441c6b1e930e2817404b5046f1f989899143a12bf92de603b69f4e0aee1e15"
dependencies = [
 "crypto-mac",
 "digest",
]

[[package]]
name = "honggfuzz"
version = "0.5.47"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3de2c3273ef7735df1c5a72128ca85b1d20105b9aac643cdfd7a6e581311150"
dependencies = [
 "arbitrary",
 "lazy_static",
 "memmap",
]

[[package]]
name = "htmlescape"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9025058dae765dee5070ec375f591e2ba14638c63feff74f13805a72e523163"

[[package]]
name = "http"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75f43d41e26995c17e71ee126451dd3941010b0514a81a9d11f3b341debc2399"
dependencies = [
 "bytes",
 "fnv",
 "itoa 1.0.1",
]

[[package]]
name = "http-body"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5f38f16d184e36f2408a55281cd658ecbd3ca05cce6d6510a176eca393e26d1"
dependencies = [
 "bytes",
 "http",
 "pin-project-lite",
]

[[package]]
name = "http-range-header"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bfe8eed0a9285ef776bb792479ea3834e8b94e13d615c2f66d03dd50a435a29"

[[package]]
name = "httparse"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d897f394bad6a705d5f4104762e116a75639e470d80901eed05a860a95cb1904"

[[package]]
name = "httpdate"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05842d0d43232b23ccb7060ecb0f0626922c21f30012e97b767b30afd4a5d4b9"

[[package]]
name = "humantime"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a3a5bfb195931eeb336b2a7b4d761daec841b97f947d34394601737a7bba5e4"

[[package]]
name = "hyper"
version = "0.14.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "034711faac9d2166cb1baf1a2fb0b60b1f277f8492fd72176c17f3515e1abd3c"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "httparse",
 "httpdate",
 "itoa 1.0.1",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper-openssl"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9d52322a69f0a93f177d76ca82073fcec8d5b4eb6e28525d5b3142fa718195c"
dependencies = [
 "http",
 "hyper",
 "linked_hash_set",
 "once_cell",
 "openssl",
 "openssl-sys",
 "parking_lot 0.11.1",
 "tokio",
 "tokio-openssl",
 "tower-layer",
]

[[package]]
name = "hyper-rustls"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f9f7a97316d44c0af9b0301e65010573a853a9fc97046d7331d7f6bc0fd5a64"
dependencies = [
 "futures-util",
 "hyper",
 "log",
 "rustls 0.19.1",
 "tokio",
 "tokio-rustls 0.22.0",
 "webpki",
]

[[package]]
name = "hyper-rustls"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3efd23720e2049821a693cbc7e65ea87c72f1c58ff2f9522ff332b1491e590"
dependencies = [
 "futures-util",
 "http",
 "hyper",
 "log",
 "rustls 0.21.12",
 "rustls-native-certs",
 "tokio",
 "tokio-rustls 0.24.1",
]

[[package]]
name = "hyper-timeout"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbb958482e8c7be4bc3cf272a766a2b0bf1a6755e7a6ae777f017a31d11b13b1"
dependencies = [
 "hyper",
 "pin-project-lite",
 "tokio",
 "tokio-io-timeout",
]

[[package]]
name = "hyper-tls"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6183ddfa99b85da61a140bea0efc93fdf56ceaa041b37d553518030827f9905"
dependencies = [
 "bytes",
 "hyper",
 "native-tls",
 "tokio",
 "tokio-native-tls",
]

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02e2673c30ee86b5b96a9cb52ad15718aa1f966f5ab9ad54a8b95d5ca33120a9"
dependencies = [
 "matches",
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "if_chain"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb56e1aa765b4b4f3aadfab769793b7087bb03a4ea4920644a6d238e2df5b9ed"

[[package]]
name = "include-flate"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df49c16750695486c1f34de05da5b7438096156466e7f76c38fcdf285cf0113e"
dependencies = [
 "include-flate-codegen",
 "lazy_static",
 "libflate",
]

[[package]]
name = "include-flate-codegen"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c5b246c6261be723b85c61ecf87804e8ea4a35cb68be0ff282ed84b95ffe7d7"
dependencies = [
 "libflate",
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "indexmap"
version = "1.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "824845a0bf897a9042383849b02c1bc219c2383772efcd5c6f9766fa4b81aef3"
dependencies = [
 "autocfg",
 "hashbrown 0.9.1",
]

[[package]]
name = "indexmap"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe4cd85333e22411419a0bcae1297d25e58c9443848b11dc6a86fefe8c78a661"
dependencies = [
 "equivalent",
 "hashbrown 0.15.4",
]

[[package]]
name = "inferno"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16d4bde3a7105e59c66a4104cfe9606453af1c7a0eac78cb7d5bc263eb762a70"
dependencies = [
 "ahash 0.7.4",
 "atty",
 "indexmap 1.6.2",
 "itoa 1.0.1",
 "lazy_static",
 "log",
 "num-format",
 "quick-xml 0.22.0",
 "rgb",
 "str_stack",
]

[[package]]
name = "inotify"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4816c66d2c8ae673df83366c18341538f234a26d65a9ecea5c348b453ac1d02f"
dependencies = [
 "bitflags 1.3.2",
 "inotify-sys",
 "libc 0.2.174",
]

[[package]]
name = "inotify-sys"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e05c02b5e89bff3b946cedeca278abc628fe811e604f027c45a8aa3cf793d0eb"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "instant"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a5bbe824c507c5da5956355e86a746d82e0e1464f65d862cc5e71da70e94b2c"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "int-enum"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cff87d3cc4b79b4559e3c75068d64247284aceb6a038bd4bb38387f3f164476d"
dependencies = [
 "int-enum-impl",
]

[[package]]
name = "int-enum-impl"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df1f2f068675add1a3fc77f5f5ab2e29290c841ee34d151abc007bce902e5d34"
dependencies = [
 "proc-macro-crate",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "into_other"
version = "0.0.1"
dependencies = [
 "engine_traits",
 "kvproto",
 "raft",
]

[[package]]
name = "iovec"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2b3ea6ff95e175473f8ffe6a7eb7c00d054240321b84c57051175fe3c1e075e"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "ipnet"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47be2f14c678be2fdcab04ab1171db51b2762ce6f0a8ee87c8dd4a04ed216135"

[[package]]
name = "ipnetwork"
version = "0.15.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a69dd5e3613374e74da81c251750153abe3bd0ad17641ea63d43d1e21d0dbd4d"
dependencies = [
 "serde",
]

[[package]]
name = "irg-kvariants"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef2af7c331f2536964a32b78a7d2e0963d78b42f4a76323b16cc7d94b1ddce26"
dependencies = [
 "csv",
 "once_cell",
 "serde",
]

[[package]]
name = "itertools"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "284f18f85651fe11e8a991b2adb42cb078325c996ed026d994719efcfca1d54b"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba291022dbbd398a455acf126c1e341954079855bc60dfdda641363bd6922569"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "413ee7dfc52ee1a4949ceeb7dbc8a33f2d6c088194d9f922fb8318faf1f01186"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b192c782037fadd9cfa75548310488aabdbf3d2da73885b31bd0abd03351285"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "501266b7edd0174f8530248f87f99c88fbe60ca4ef3dd486835b8d8d53136f7f"

[[package]]
name = "itoa"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1aab8fc367588b89dcee83ab0fd66b72b50b72fa1904d7095045ace2b0c81c35"

[[package]]
name = "jieba-macros"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c676b32a471d3cfae8dac2ad2f8334cd52e53377733cca8c1fb0a5062fec192"
dependencies = [
 "phf_codegen 0.11.3",
]

[[package]]
name = "jieba-rs"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b06096b4b61fb4bfdbf16c6a968ea2d6be1ac9617cf3db741c3b641e6c290a35"
dependencies = [
 "cedarwood",
 "fxhash",
 "include-flate",
 "jieba-macros",
 "lazy_static",
 "phf 0.11.3",
 "regex",
]

[[package]]
name = "jobserver"
version = "0.1.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2b099aaa34a9751c5bf0878add70444e1ed2dd73f347be99003d4577277de6e"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "js-sys"
version = "0.3.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a38fc24e30fd564ce974c02bf1d337caddff65be6cc4735a1f7eab22a7440f04"
dependencies = [
 "wasm-bindgen",
]

[[package]]
name = "kanaria"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0f9d9652540055ac4fded998a73aca97d965899077ab1212587437da44196ff"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "kernel32-sys"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7507624b29483431c0ba2d82aece8ca6cdba9382bff4ddd0f7490560c056098d"
dependencies = [
 "winapi 0.2.8",
 "winapi-build",
]

[[package]]
name = "keyed_priority_queue"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d63b6407b66fc81fc539dccf3ddecb669f393c5101b6a2be3976c95099a06e8"
dependencies = [
 "indexmap 1.6.2",
]

[[package]]
name = "keys"
version = "0.1.0"
dependencies = [
 "byteorder",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "kvengine"
version = "0.0.1"
dependencies = [
 "aligned-vec",
 "aliyun",
 "anyhow",
 "api_version",
 "arrow-buffer",
 "async-recursion",
 "async-trait",
 "aws",
 "backtrace",
 "base64 0.13.0",
 "bincode",
 "bstr",
 "bytemuck",
 "byteorder",
 "bytes",
 "cloud_encryption",
 "codec",
 "collections",
 "crc32c",
 "crc32fast",
 "criterion",
 "crossbeam",
 "dashmap 6.1.0",
 "dyn-clone",
 "engine_panic",
 "engine_rocks",
 "engine_traits",
 "fail",
 "farmhash",
 "file_system",
 "filetime",
 "fslock",
 "futures 0.3.30",
 "hex 0.4.2",
 "http",
 "hyper",
 "hyper-rustls 0.24.2",
 "hyper-tls",
 "itertools 0.10.5",
 "keys",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log_wrappers",
 "lz4",
 "maybe-async",
 "memmap2 0.9.5",
 "nix 0.24.1",
 "num_cpus",
 "parking_lot 0.12.1",
 "prometheus",
 "prometheus-static-metric",
 "proptest",
 "protobuf",
 "quick-xml 0.23.1",
 "quick_cache",
 "rand 0.8.5",
 "recovery",
 "regex",
 "rocksdb",
 "rstest",
 "rusoto_core",
 "rusoto_credential",
 "rusoto_mock",
 "rusoto_s3",
 "schema",
 "security",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "slog-term",
 "tempfile",
 "test_util",
 "thiserror 1.0.30",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.42",
 "tipb",
 "tokio",
 "tokio-util",
 "tracker",
 "txn_types",
 "url",
 "usearch",
 "xorf",
 "zipf 7.0.1",
 "zstd-sys",
]

[[package]]
name = "kvenginepb"
version = "0.0.1"
dependencies = [
 "protobuf",
 "protobuf-codegen-pure",
]

[[package]]
name = "kvproto"
version = "0.0.2"
source = "git+https://github.com/tidbcloud/kvproto.git?branch=release-7.5-serverless#3bb337ea31a53967d20148eea2a0b3b99d2cd7f0"
dependencies = [
 "futures 0.3.30",
 "grpcio",
 "protobuf",
 "protobuf-build",
 "raft-proto",
]

[[package]]
name = "lazy_static"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbd2bcb4c963f2ddae06a2efc7e9f3591312473c50c6685e1f298068316e66fe"

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "levenshtein_automata"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c2cdeb66e45e9f36bfad5bbdb4d2384e70936afbee843c6f6543f0c551ebb25"

[[package]]
name = "lexical-core"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92912c4af2e7d9075be3e5e3122c4d7263855fa6cce34fbece4dd08e5884624d"
dependencies = [
 "lexical-parse-float",
 "lexical-parse-integer",
 "lexical-util",
 "lexical-write-float",
 "lexical-write-integer",
]

[[package]]
name = "lexical-parse-float"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f518eed87c3be6debe6d26b855c97358d8a11bf05acec137e5f53080f5ad2dd8"
dependencies = [
 "lexical-parse-integer",
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "lexical-parse-integer"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "afc852ec67c6538bbb2b9911116a385b24510e879a69ab516e6a151b15a79168"
dependencies = [
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "lexical-util"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c72a9d52c5c4e62fa2cdc2cb6c694a39ae1382d9c2a17a466f18e272a0930eb1"
dependencies = [
 "static_assertions",
]

[[package]]
name = "lexical-write-float"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a89ec1d062e481210c309b672f73a0567b7855f21e7d2fae636df44d12e97f9"
dependencies = [
 "lexical-util",
 "lexical-write-integer",
 "static_assertions",
]

[[package]]
name = "lexical-write-integer"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "094060bd2a7c2ff3a16d5304a6ae82727cb3cc9d1c70f813cc73f744c319337e"
dependencies = [
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "libc"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e32a70cf75e5846d53a673923498228bbec6a8624708a9ea5645f075d6276122"

[[package]]
name = "libc"
version = "0.2.174"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1171693293099992e19cddea4e8b849964e9846f4acee11b3948bcc337be8776"

[[package]]
name = "libflate"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45d9dfdc14ea4ef0900c1cddbc8dcd553fbaacd8a4a282cf4018ae9dd04fb21e"
dependencies = [
 "adler32",
 "core2",
 "crc32fast",
 "dary_heap",
 "libflate_lz77",
]

[[package]]
name = "libflate_lz77"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6e0d73b369f386f1c44abd9c570d5318f55ccde816ff4b562fa452e5182863d"
dependencies = [
 "core2",
 "hashbrown 0.14.5",
 "rle-decode-fast",
]

[[package]]
name = "libfuzzer-sys"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb789afcc589a08928d1e466087445ab740a0f70a2ee23d9349a0e3723d65e1b"
dependencies = [
 "arbitrary",
 "cc",
]

[[package]]
name = "libloading"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f84d96438c15fcd6c3f244c8fce01d1e2b9c6b5623e9c711dc9286d8fc92d6a"
dependencies = [
 "cfg-if 1.0.0",
 "winapi 0.3.9",
]

[[package]]
name = "libm"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "348108ab3fba42ec82ff6e9564fc4ca0247bdccdc68dd8af9764bbc79c3c8ffb"

[[package]]
name = "libmimalloc-sys"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2396cf99d2f58611cd69f0efeee4af3d2e2c7b61bed433515029163aa567e65c"
dependencies = [
 "cc",
]

[[package]]
name = "librocksdb_sys"
version = "0.1.0"
source = "git+https://github.com/tikv/rust-rocksdb.git#2096b9a161f93e437f7adee49e68cd1570aea42f"
dependencies = [
 "bindgen 0.65.1",
 "bzip2-sys",
 "cc",
 "cmake",
 "libc 0.2.174",
 "libtitan_sys",
 "libz-sys",
 "lz4-sys",
 "openssl-sys",
 "snappy-sys",
 "tikv-jemalloc-sys",
 "zstd-sys",
]

[[package]]
name = "libtitan_sys"
version = "0.0.1"
source = "git+https://github.com/tikv/rust-rocksdb.git#2096b9a161f93e437f7adee49e68cd1570aea42f"
dependencies = [
 "bzip2-sys",
 "cc",
 "cmake",
 "libc 0.2.174",
 "libz-sys",
 "lz4-sys",
 "snappy-sys",
 "zstd-sys",
]

[[package]]
name = "libz-sys"
version = "1.1.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b70e7a7df205e92a1a4cd9aaae7898dac0aa555503cc0a649494d0d60e7651d"
dependencies = [
 "cc",
 "libc 0.2.174",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "lindera"
version = "0.43.1"
source = "git+https://github.com/breezewish/lindera?branch=v0.43.1-tokio-1.24#3e266dd69cae7ff1e894208cbfb9afa20d3e9965"
dependencies = [
 "anyhow",
 "bincode",
 "byteorder",
 "csv",
 "kanaria",
 "lindera-cc-cedict",
 "lindera-dictionary",
 "lindera-ipadic",
 "lindera-ipadic-neologd",
 "lindera-ko-dic",
 "lindera-unidic",
 "once_cell",
 "regex",
 "serde",
 "serde_json",
 "serde_yaml",
 "strum 0.27.1",
 "strum_macros 0.27.1",
 "unicode-blocks",
 "unicode-normalization",
 "unicode-segmentation",
 "yada",
]

[[package]]
name = "lindera-cc-cedict"
version = "0.43.1"
source = "git+https://github.com/breezewish/lindera?branch=v0.43.1-tokio-1.24#3e266dd69cae7ff1e894208cbfb9afa20d3e9965"
dependencies = [
 "bincode",
 "byteorder",
 "lindera-dictionary",
 "once_cell",
 "tokio",
]

[[package]]
name = "lindera-dictionary"
version = "0.43.1"
source = "git+https://github.com/breezewish/lindera?branch=v0.43.1-tokio-1.24#3e266dd69cae7ff1e894208cbfb9afa20d3e9965"
dependencies = [
 "anyhow",
 "bincode",
 "byteorder",
 "csv",
 "derive_builder",
 "encoding",
 "encoding_rs 0.8.35",
 "encoding_rs_io",
 "flate2",
 "glob",
 "log",
 "md5",
 "once_cell",
 "rand 0.9.1",
 "reqwest",
 "serde",
 "tar",
 "thiserror 2.0.12",
 "tokio",
 "yada",
]

[[package]]
name = "lindera-ipadic"
version = "0.43.1"
source = "git+https://github.com/breezewish/lindera?branch=v0.43.1-tokio-1.24#3e266dd69cae7ff1e894208cbfb9afa20d3e9965"
dependencies = [
 "bincode",
 "byteorder",
 "lindera-dictionary",
 "once_cell",
 "tokio",
]

[[package]]
name = "lindera-ipadic-neologd"
version = "0.43.1"
source = "git+https://github.com/breezewish/lindera?branch=v0.43.1-tokio-1.24#3e266dd69cae7ff1e894208cbfb9afa20d3e9965"
dependencies = [
 "bincode",
 "byteorder",
 "lindera-dictionary",
 "once_cell",
 "tokio",
]

[[package]]
name = "lindera-ko-dic"
version = "0.43.1"
source = "git+https://github.com/breezewish/lindera?branch=v0.43.1-tokio-1.24#3e266dd69cae7ff1e894208cbfb9afa20d3e9965"
dependencies = [
 "bincode",
 "byteorder",
 "lindera-dictionary",
 "once_cell",
 "tokio",
]

[[package]]
name = "lindera-unidic"
version = "0.43.1"
source = "git+https://github.com/breezewish/lindera?branch=v0.43.1-tokio-1.24#3e266dd69cae7ff1e894208cbfb9afa20d3e9965"
dependencies = [
 "bincode",
 "byteorder",
 "lindera-dictionary",
 "once_cell",
 "tokio",
]

[[package]]
name = "link-cplusplus"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d240c6f7e1ba3a28b0249f774e6a9dd0175054b52dfbb61b16eb8505c3785c9"
dependencies = [
 "cc",
]

[[package]]
name = "linked-hash-map"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fb9b38af92608140b86b693604b9ffcc5824240a484d1ecd4795bacb2fe88f3"

[[package]]
name = "linked_hash_set"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47186c6da4d81ca383c7c47c1bfc80f4b95f4720514d860a5407aaf4233f9588"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "linux-raw-sys"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d26c52dbd32dccf2d10cac7725f8eae5296885fb5703b261f7d0a0739ec807ab"

[[package]]
name = "linux-raw-sys"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd945864f07fe9f5371a27ad7b52a172b4b499999f1d97574c9fa68373937e12"

[[package]]
name = "lock_api"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07af8b9cdd281b7915f413fa73f29ebd5d55d0d3f0155584dade1ff18cea1b17"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13dc2df351e3202783a1fe0d44375f7295ffb4049267b0f3018346dc122a1d94"

[[package]]
name = "log_wrappers"
version = "0.0.1"
dependencies = [
 "hex 0.4.2",
 "protobuf",
 "slog",
 "slog-term",
 "tikv_alloc",
]

[[package]]
name = "lru"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "234cf4f4a04dc1f57e24b96cc0cd600cf2af460d4161ac5ecdd0af8e1f3b2a38"
dependencies = [
 "hashbrown 0.15.4",
]

[[package]]
name = "lz4"
version = "1.24.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e9e2dd86df36ce760a60f6ff6ad526f7ba1f14ba0356f8254fb6905e6494df1"
dependencies = [
 "libc 0.2.174",
 "lz4-sys",
]

[[package]]
name = "lz4-sys"
version = "1.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57d27b317e207b10f69f5e75494119e391a96f48861ae870d1da6edac98ca900"
dependencies = [
 "cc",
 "libc 0.2.174",
]

[[package]]
name = "lz4_flex"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75761162ae2b0e580d7e7c390558127e5f01b4194debd6221fd8c207fc80e3f5"

[[package]]
name = "mach"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b823e83b2affd8f40a9ee8c29dbc56404c1e34cd2710921f2801e2cf29527afa"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "maligned"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e88c3cbe8288f77f293e48a28b3232e3defd203a6d839fa7f68ea4329e83464"

[[package]]
name = "match-template"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c334ac67725febd94c067736ac46ef1c7cacf1c743ca14b9f917c2df2c20acd8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "matches"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ffc5c5338469d4d3ea17d269fa8ea3512ad247247c30bd2df69e68309ed0a08"

[[package]]
name = "matchit"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73cbba799671b762df5a175adf59ce145165747bb891505c43d09aefbbf38beb"

[[package]]
name = "maybe-async"
version = "0.2.6"
source = "git+https://github.com/pingyu/maybe-async-rs?branch=cse#a48a423009593f443046724aa88d6b5408ec5d1b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "md-5"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5a279bb9607f9f53c22d496eade00d138d1bdcccd07d74650387cf94942a15"
dependencies = [
 "block-buffer",
 "digest",
 "opaque-debug",
]

[[package]]
name = "md5"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "490cc448043f947bae3cbee9c203358d62dbee0db12107a74be5c30ccfd09771"

[[package]]
name = "measure_time"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbefd235b0aadd181626f281e1d684e116972988c14c264e42069d5e8a5775cc"
dependencies = [
 "instant",
 "log",
]

[[package]]
name = "memchr"
version = "2.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a282da65faaf38286cf3be983213fcf1d2e2a58700e808f83f4ea9a4804bc0"

[[package]]
name = "memmap"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6585fd95e7bb50d6cc31e20d4cf9afb4e2ba16c5846fc76793f11218da9c475b"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "memmap2"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "057a3db23999c867821a7a59feb06a578fcb03685e983dff90daf9e7d24ac08f"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "memmap2"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd3f7eed9d3848f8b98834af67102b720745c4ec028fcd0aa0239277e7de374f"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "memoffset"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59accc507f1338036a0477ef61afdae33cde60840f4dfe481319ce3ad116ddf9"
dependencies = [
 "autocfg",
]

[[package]]
name = "memoffset"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5de893c32cde5f383baa4c04c5d6dbdd735cfd4a794b0debdb2bb1b421da5ff4"
dependencies = [
 "autocfg",
]

[[package]]
name = "memoffset"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d61c719bcfbcf5d62b3a09efa6088de8c54bc0bfcd3ea7ae39fcc186108b8de1"
dependencies = [
 "autocfg",
]

[[package]]
name = "memory_trace_macros"
version = "0.1.0"
dependencies = [
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "mimalloc"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e7c6b11afd1e5e689ac96b6d18b1fc763398fe3d7eed99e8773426bc2033dfb"
dependencies = [
 "libmimalloc-sys",
]

[[package]]
name = "mime"
version = "0.3.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a60c7ce501c71e03a9c9c0d35b861413ae925bd979cc7a4e30d060069aaac8d"

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a92518e98c078586bc6c934028adcca4c92a53d6a958196de835170a01d84e4b"
dependencies = [
 "adler",
 "autocfg",
]

[[package]]
name = "miniz_oxide"
version = "0.8.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fa76a2c86f704bdb222d66965fb3d63269ce38518b83cb0575fca855ebb6316"
dependencies = [
 "adler2",
]

[[package]]
name = "mio"
version = "0.6.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4afd66f5b91bf2a3bc13fad0e21caedac168ca4c707504e75585648ae80e4cc4"
dependencies = [
 "cfg-if 0.1.10",
 "fuchsia-zircon",
 "fuchsia-zircon-sys",
 "iovec",
 "kernel32-sys",
 "libc 0.2.174",
 "log",
 "miow",
 "net2",
 "slab",
 "winapi 0.2.8",
]

[[package]]
name = "mio"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d732bc30207a6423068df043e3d02e0735b155ad7ce1a6f76fe2baa5b158de"
dependencies = [
 "libc 0.2.174",
 "log",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.42.0",
]

[[package]]
name = "mio-extras"
version = "2.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52403fe290012ce777c4626790c8951324a2b9e3316b3143779c72b029742f19"
dependencies = [
 "lazycell",
 "log",
 "mio 0.6.23",
 "slab",
]

[[package]]
name = "miow"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebd808424166322d4a38da87083bfddd3ac4c131334ed55856112eb06d46944d"
dependencies = [
 "kernel32-sys",
 "net2",
 "winapi 0.2.8",
 "ws2_32-sys",
]

[[package]]
name = "mmap"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bc85448a6006dd2ba26a385a564a8a0f1f2c7e78c70f1a70b2e0f4af286b823"
dependencies = [
 "libc 0.1.12",
 "tempdir",
]

[[package]]
name = "mnt"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1587ebb20a5b04738f16cffa7e2526f1b8496b84f92920facd518362ff1559eb"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "mock-engine-store"
version = "0.0.1"
dependencies = [
 "api_version",
 "assert-type-eq",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_store_ffi",
 "engine_test",
 "engine_tiflash",
 "engine_traits",
 "fail",
 "file_system",
 "futures 0.3.30",
 "grpcio",
 "grpcio-health",
 "int-enum",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "pd_client",
 "protobuf",
 "proxy_server",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "resolved_ts",
 "resource_control",
 "resource_metering",
 "security",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "test_raftstore",
 "test_util",
 "tikv",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
]

[[package]]
name = "moka"
version = "0.9.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b9268097a2cf211ac9955b1cc95e80fa84fff5c2d13ba292916445dc8a311f"
dependencies = [
 "async-io",
 "async-lock",
 "crossbeam-channel",
 "crossbeam-epoch 0.9.14",
 "crossbeam-utils 0.8.8",
 "futures-util",
 "num_cpus",
 "once_cell",
 "parking_lot 0.12.1",
 "quanta",
 "rustc_version 0.4.0",
 "scheduled-thread-pool",
 "skeptic",
 "smallvec",
 "tagptr",
 "thiserror 1.0.30",
 "triomphe",
 "uuid 1.2.1",
]

[[package]]
name = "more-asserts"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0debeb9fcf88823ea64d64e4a815ab1643f33127d995978e099942ce38f25238"

[[package]]
name = "multimap"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97fbd5d00e0e37bfb10f433af8f5aaf631e739368dc9fc28286ca81ca4948dc"
dependencies = [
 "serde",
]

[[package]]
name = "multiversion"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "025c962a3dd3cc5e0e520aa9c612201d127dcdf28616974961a649dca64f5373"
dependencies = [
 "multiversion-macros",
]

[[package]]
name = "multiversion-macros"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8a3e2bde382ebf960c1f3e79689fa5941625fe9bf694a1cb64af3e85faff3af"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "mur3"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97af489e1e21b68de4c390ecca6703318bc1aa16e9733bcb62c089b73c6fbb1b"

[[package]]
name = "murmurhash32"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2195bf6aa996a481483b29d62a7663eed3fe39600c460e323f8ff41e90bdd89b"

[[package]]
name = "native-tls"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8d96b2e1c8da3957d58100b09f102c6d9cfdfced01b7ec5a8974044bb09dbd4"
dependencies = [
 "lazy_static",
 "libc 0.2.174",
 "log",
 "openssl",
 "openssl-probe",
 "openssl-sys",
 "schannel",
 "security-framework",
 "security-framework-sys",
 "tempfile",
]

[[package]]
name = "net2"
version = "0.2.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "391630d12b68002ae1e25e8f974306474966550ad82dac6886fb8910c19568ae"
dependencies = [
 "cfg-if 0.1.10",
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "nix"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "becb657d662f1cd2ef38c7ad480ec6b8cf9e96b27adb543e594f9cf0f2e6065c"
dependencies = [
 "bitflags 1.3.2",
 "cc",
 "cfg-if 0.1.10",
 "libc 0.2.174",
 "void",
]

[[package]]
name = "nix"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f3790c00a0150112de0f4cd161e3d7fc4b2d8a5542ffc35f099a2562aecb35c"
dependencies = [
 "bitflags 1.3.2",
 "cc",
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "memoffset 0.6.4",
]

[[package]]
name = "nix"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f17df307904acd05aa8e32e97bb20f2a0df1728bbc2d771ae8f9a90463441e9"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "memoffset 0.6.4",
]

[[package]]
name = "nix"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfdda3d196821d6af13126e40375cdf7da646a96114af134d5f417a9a1dc8e1a"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "memoffset 0.7.1",
 "pin-utils",
 "static_assertions",
]

[[package]]
name = "nodrop"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f9667ddcc6cc8a43afc9b7917599d7216aa09c463919ea32c59ed6cac8bc945"

[[package]]
name = "nom"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf51a729ecf40266a2368ad335a5fdde43471f545a967109cd62146ecf8b66ff"

[[package]]
name = "nom"
version = "4.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ad2a91a8e869eeb30b9cb3119ae87773a8f4ae617f41b1eb9c154b2905f7bd6"
dependencies = [
 "memchr",
 "version_check 0.1.5",
]

[[package]]
name = "nom"
version = "5.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c433f4d505fe6ce7ff78523d2fa13a0b9f2690e181fc26168bcbe5ccc5d14e07"
dependencies = [
 "memchr",
 "version_check 0.1.5",
]

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "notify"
version = "4.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae03c8c853dba7bfd23e571ff0cff7bc9dceb40a4cd684cd1681824183f45257"
dependencies = [
 "bitflags 1.3.2",
 "filetime",
 "fsevent",
 "fsevent-sys",
 "inotify",
 "libc 0.2.174",
 "mio 0.6.23",
 "mio-extras",
 "walkdir",
 "winapi 0.3.9",
]

[[package]]
name = "ntapi"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc51db7b362b205941f71232e56c625156eb9a929f8cf74a428fd5bc094a4afc"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "num"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab3e176191bc4faad357e3122c4747aa098ac880e88b168f106386128736cf4a"
dependencies = [
 "num-complex 0.3.0",
 "num-integer",
 "num-iter",
 "num-rational 0.3.0",
 "num-traits",
]

[[package]]
name = "num"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43db66d1170d347f9a065114077f7dccb00c1b9478c89384490a3425279a4606"
dependencies = [
 "num-bigint",
 "num-complex 0.4.1",
 "num-integer",
 "num-iter",
 "num-rational 0.4.0",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f93ab6289c7b344a8a9f60f88d80aa20032336fe78da341afc91c8a2341fc75f"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b05ad05bd8977050b171b3f6b48175fea6e0565b7981059b486075e1026a9fb5"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fbc387afefefd5e9e39493299f3069e14a140dd34dc19b4c1c1a8fddb6a790"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c8b15b261814f992e33760b1fca9fe8b693d8a65299f20c9901688636cfb746"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "num-format"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bafe4179722c2894288ee77a9f044f02811c86af699344c498b0840c698a2465"
dependencies = [
 "arrayvec",
 "itoa 0.4.4",
]

[[package]]
name = "num-integer"
version = "0.1.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2cc698a63b549a70bc047073d2949cce27cd1c7b0a4a862d08a8031bc2801db"
dependencies = [
 "autocfg",
 "num-traits",
]

[[package]]
name = "num-iter"
version = "0.1.42"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2021c8337a54d21aca0d59a92577a029af9431cb59b909b03252b9c164fad59"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5b4d7360f362cfb50dde8143501e6940b22f644be75a4cc90b2d81968908138"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d41702bd167c2df5520b384281bc111a4b5efcf7fbc4c9c222c815b07e0a6a6a"
dependencies = [
 "autocfg",
 "num-bigint",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19e64526ebdee182341572e50e9ad03965aa510cd94427a4549448f285e957a1"
dependencies = [
 "hermit-abi",
 "libc 0.2.174",
]

[[package]]
name = "oauth2"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80e47cfc4c0a1a519d9a025ebfbac3a2439d1b5cdf397d72dcb79b11d9920dab"
dependencies = [
 "base64 0.13.0",
 "chrono",
 "getrandom 0.2.15",
 "http",
 "rand 0.8.5",
 "reqwest",
 "serde",
 "serde_json",
 "serde_path_to_error",
 "sha2",
 "thiserror 1.0.30",
 "url",
]

[[package]]
name = "object"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39f37e50073ccad23b6d09bcb5b263f4e76d3bb6038e4a3c08e52162ffa8abc2"
dependencies = [
 "memchr",
]

[[package]]
name = "once_cell"
version = "1.21.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42f5e15c9953c5e4ccceeb2e7382a716482c34515315f7b03532b8b4e8393d2d"

[[package]]
name = "oneshot"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4ce411919553d3f9fa53a0880544cda985a112117a0444d5ff1e870a893d6ea"

[[package]]
name = "online_config"
version = "0.1.0"
dependencies = [
 "online_config_derive",
 "serde",
 "serde_derive",
 "toml",
]

[[package]]
name = "online_config_derive"
version = "0.1.0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "oorandom"
version = "11.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ab1bc2a289d34bd04a330323ac98a1b4bc82c9d9fcb1e66b63caa84da26b575"

[[package]]
name = "opaque-debug"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "624a8340c38c1b80fd549087862da4ba43e08858af025b236e509b6649fc13d5"

[[package]]
name = "openssl"
version = "0.10.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "618febf65336490dfcf20b73f885f5651a0c89c64c2d4a8c3662585a70bf5bd0"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "foreign-types",
 "libc 0.2.174",
 "once_cell",
 "openssl-macros",
 "openssl-sys",
]

[[package]]
name = "openssl-macros"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b501e44f11665960c7e7fcf062c7d96a14ade4aa98116c004b2e37b5be7d736c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "openssl-probe"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77af24da69f9d9341038eba93a073b1fdaaa1b788221b00a69bce9e762cb32de"

[[package]]
name = "openssl-src"
version = "111.25.0****.1t"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3173cd3626c43e3854b1b727422a276e568d9ec5fe8cec197822cf52cfb743d6"
dependencies = [
 "cc",
]

[[package]]
name = "openssl-sys"
version = "0.9.75"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5f9bd0c2710541a3cda73d6f9ac4f1b240de4ae261065d309dbe73d9dceb42f"
dependencies = [
 "autocfg",
 "cc",
 "libc 0.2.174",
 "openssl-src",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "ordered-float"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7940cf2ca942593318d07fcf2596cdca60a85c9e7fab408a5e21a4f9dcd40d87"
dependencies = [
 "num-traits",
]

[[package]]
name = "ordered-float"
version = "5.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2c1f9f56e534ac6a9b8a4600bdf0f530fb393b5f393e7b4d03489c3cf0c3f01"
dependencies = [
 "num-traits",
]

[[package]]
name = "overload_protector"
version = "0.1.0"
dependencies = [
 "online_config",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tikv_util",
 "txn_types",
]

[[package]]
name = "ownedbytes"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3a059efb063b8f425b948e042e6b9bd85edfe60e913630ed727b23e2dfcc558"
dependencies = [
 "stable_deref_trait",
]

[[package]]
name = "page_size"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eebde548fbbf1ea81a99b128872779c437752fb99f217c45245e1a61dcd9edcd"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "panic_hook"
version = "0.0.1"

[[package]]
name = "parking"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14f2252c834a40ed9bb5422029649578e63aa341ac401f74e719dd1afda8394e"

[[package]]
name = "parking_lot"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d7744ac029df22dca6284efe4e898991d28e3085c706c972bcd7da4a27a15eb"
dependencies = [
 "instant",
 "lock_api",
 "parking_lot_core 0.8.3",
]

[[package]]
name = "parking_lot"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3742b2c103b9f06bc9fff0a37ff4912935851bee6d36f3c02bcc755bcfec228f"
dependencies = [
 "lock_api",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "parking_lot_core"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa7a782938e745763fe6907fc6ba86946d72f49fe7e21de074e08128a99fb018"
dependencies = [
 "cfg-if 1.0.0",
 "instant",
 "libc 0.2.174",
 "redox_syscall 0.2.11",
 "smallvec",
 "winapi 0.3.9",
]

[[package]]
name = "parking_lot_core"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e401f977ab385c9e4e3ab30627d6f26d00e2c73eef317493c4ec6d468726cf8"
dependencies = [
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "redox_syscall 0.5.7",
 "smallvec",
 "windows-targets",
]

[[package]]
name = "parse-zoneinfo"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "089a398ccdcdd77b8c38909d5a1e4b67da1bc4c9dbfe6d5b536c828eddb779e5"
dependencies = [
 "regex",
]

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "pd_client"
version = "0.1.0"
dependencies = [
 "bstr",
 "bytes",
 "cloud_encryption",
 "collections",
 "error_code",
 "fail",
 "futures 0.3.30",
 "grpcio",
 "hex 0.4.2",
 "http",
 "kvproto",
 "lazy_static",
 "log",
 "log_wrappers",
 "prometheus",
 "prometheus-static-metric",
 "security",
 "semver 0.10.0",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
 "yatp",
]

[[package]]
name = "pdqselect"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ec91767ecc0a0bbe558ce8c9da33c068066c57ecc8bb8477ef8c1ad3ef77c27"

[[package]]
name = "peeking_take_while"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b17cddbe7ec3f8bc800887bab5e717348c95ea2ca0b1bf0837fb964dc67099"

[[package]]
name = "percent-encoding"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4fd5641d01c8f18a23da7b6fe29298ff4b55afcccdf78973b24cf3175fee32e"

[[package]]
name = "perfcnt"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8f94885300e262ef461aa9fd1afbf7df3caf9e84e271a74925d1c6c8b24830f"
dependencies = [
 "bitflags 1.3.2",
 "byteorder",
 "libc 0.2.174",
 "mmap",
 "nom 4.2.3",
 "phf 0.9.0",
 "x86",
]

[[package]]
name = "pest"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "10f4872ae94d7b90ae48754df22fd42ad52ce740b8f370b03da4835417403e53"
dependencies = [
 "ucd-trie",
]

[[package]]
name = "petgraph"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a13a2fa9d0b63e5f22328828741e523766fff0ee9e779316902290dff3f824f"
dependencies = [
 "fixedbitset",
 "indexmap 1.6.2",
]

[[package]]
name = "phf"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2ac8b67553a7ca9457ce0e526948cad581819238f4a9d1ea74545851fa24f37"
dependencies = [
 "phf_shared 0.9.0",
]

[[package]]
name = "phf"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd6780a80ae0c52cc120a26a1a42c1ae51b247a253e4e06113d23d2c2edd078"
dependencies = [
 "phf_shared 0.11.3",
]

[[package]]
name = "phf_codegen"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "963adb11cf22ee65dfd401cf75577c1aa0eca58c0b97f9337d2da61d3e640503"
dependencies = [
 "phf_generator 0.9.1",
 "phf_shared 0.9.0",
]

[[package]]
name = "phf_codegen"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aef8048c789fa5e851558d709946d6d79a8ff88c0440c587967f8e94bfb1216a"
dependencies = [
 "phf_generator 0.11.3",
 "phf_shared 0.11.3",
]

[[package]]
name = "phf_generator"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d43f3220d96e0080cc9ea234978ccd80d904eafb17be31bb0f76daaea6493082"
dependencies = [
 "phf_shared 0.9.0",
 "rand 0.8.5",
]

[[package]]
name = "phf_generator"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c80231409c20246a13fddb31776fb942c38553c51e871f8cbd687a4cfb5843d"
dependencies = [
 "phf_shared 0.11.3",
 "rand 0.8.5",
]

[[package]]
name = "phf_shared"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a68318426de33640f02be62b4ae8eb1261be2efbc337b60c54d845bf4484e0d9"
dependencies = [
 "siphasher 0.3.3",
]

[[package]]
name = "phf_shared"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67eabc2ef2a60eb7faa00097bd1ffdb5bd28e62bf39990626a582201b7a754e5"
dependencies = [
 "siphasher 1.0.1",
]

[[package]]
name = "pin-project"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78203e83c48cffbe01e4a2d35d566ca4de445d79a85372fc64e378bfc812a260"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "710faf75e1b33345361201d36d04e98ac1ed8909151a017ed384700836104c74"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "pin-project-lite"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0a7ae3ac2f1173085d398531c705756c94a4c56843785df85a60c1a0afac116"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "pkg-config"
version = "0.3.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7edddbd0b52d732b21ad9a5fab5c704c14cd949e5e9a1ec5929a24fded1b904c"

[[package]]
name = "plotters"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a3fd9ec30b9749ce28cd91f255d569591cdf937fe280c312143e3c4bad6f2a"
dependencies = [
 "num-traits",
 "plotters-backend",
 "plotters-svg",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "plotters-backend"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b07fffcddc1cb3a1de753caa4e4df03b79922ba43cf882acc1bdd7e8df9f4590"

[[package]]
name = "plotters-svg"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b38a02e23bd9604b842a812063aec4ef702b57989c37b655254bb61c471ad211"
dependencies = [
 "plotters-backend",
]

[[package]]
name = "pnet_base"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4df28acf2fcc77436dd2b91a9a0c2bb617f9ca5f2acefee1a4135058b9f9801f"

[[package]]
name = "pnet_datalink"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d27361d7578b410d0eb5fe815c2b2105b01ab770a7c738cb9a231457a809fcc7"
dependencies = [
 "ipnetwork",
 "libc 0.2.174",
 "pnet_base",
 "pnet_sys",
 "winapi 0.2.8",
]

[[package]]
name = "pnet_sys"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82f881a6d75ac98c5541db6144682d1773bb14c6fc50c6ebac7086c8f7f23c29"
dependencies = [
 "libc 0.2.174",
 "winapi 0.2.8",
 "ws2_32-sys",
]

[[package]]
name = "polling"
version = "2.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22122d5ec4f9fe1b3916419b76be1e80bcb93f618d071d2edf841b137b2a2bd6"
dependencies = [
 "autocfg",
 "cfg-if 1.0.0",
 "libc 0.2.174",
 "log",
 "wepoll-ffi",
 "windows-sys 0.42.0",
]

[[package]]
name = "portable-atomic"
version = "0.3.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26f6a7b87c2e435a3241addceeeff740ff8b7e76b74c13bf9acb17fa454ea00b"

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "pprof"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e20150f965e0e4c925982b9356da71c84bcd56cb66ef4e894825837cbcf6613e"
dependencies = [
 "backtrace",
 "cfg-if 1.0.0",
 "findshlibs",
 "inferno",
 "libc 0.2.174",
 "log",
 "nix 0.24.1",
 "once_cell",
 "parking_lot 0.12.1",
 "protobuf",
 "protobuf-codegen-pure",
 "smallvec",
 "symbolic-demangle",
 "tempfile",
 "thiserror 1.0.30",
]

[[package]]
name = "ppv-lite86"
version = "0.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac74c624d6b2d21f425f752262f42188365d7b8ff1aff74c82e45136510a4857"

[[package]]
name = "prettyplease"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c142c0e46b57171fe0c528bee8c5b7569e80f0c17e377cd0e30ea57dbc11bb51"
dependencies = [
 "proc-macro2",
 "syn 1.0.103",
]

[[package]]
name = "prettyplease"
version = "0.2.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f12335488a2f3b0a83b14edad48dca9879ce89b2edd10e80237e4e852dd645e"
dependencies = [
 "proc-macro2",
 "syn 2.0.87",
]

[[package]]
name = "proc-macro-crate"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eda0fc3b0fb7c975631757e14d9049da17374063edb6ebbcbc54d880d4fe94e9"
dependencies = [
 "once_cell",
 "thiserror 1.0.30",
 "toml",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
 "version_check 0.9.4",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2",
 "quote",
 "version_check 0.9.4",
]

[[package]]
name = "proc-macro2"
version = "1.0.86"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e719e8df665df0d1c8fbfd238015744736151d4445ec0836b8e628aae103b77"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "procfs"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0941606b9934e2d98a3677759a971756eb821f75764d0e0d26946d08e74d9104"
dependencies = [
 "bitflags 1.3.2",
 "byteorder",
 "hex 0.4.2",
 "lazy_static",
 "libc 0.2.174",
]

[[package]]
name = "procinfo"
version = "0.4.2"
source = "git+https://github.com/tikv/procinfo-rs?rev=6599eb9dca74229b2c1fcc44118bef7eff127128#6599eb9dca74229b2c1fcc44118bef7eff127128"
dependencies = [
 "byteorder",
 "libc 0.2.174",
 "nom 2.2.1",
 "rustc_version 0.2.3",
]

[[package]]
name = "profiler"
version = "0.0.1"
dependencies = [
 "callgrind",
 "gperftools",
 "lazy_static",
 "tikv_alloc",
 "valgrind_request",
]

[[package]]
name = "prometheus"
version = "0.13.0"
source = "git+https://github.com/solotzg/rust-prometheus.git?rev=b4fe98a06a58d29f9b9987a0d7186f6ed5230193#b4fe98a06a58d29f9b9987a0d7186f6ed5230193"
dependencies = [
 "cfg-if 1.0.0",
 "fnv",
 "lazy_static",
 "libc 0.2.174",
 "memchr",
 "parking_lot 0.11.1",
 "protobuf",
 "reqwest",
 "thiserror 1.0.30",
]

[[package]]
name = "prometheus-static-metric"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8f30cdb09c39930b8fa5e0f23cbb895ab3f766b187403a0ba0956fc1ef4f0e5"
dependencies = [
 "lazy_static",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "proptest"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31b476131c3c86cb68032fdc5cb6d5a1045e3e42d96b69fa599fd77701e1f5bf"
dependencies = [
 "bit-set",
 "bit-vec",
 "bitflags 2.5.0",
 "lazy_static",
 "num-traits",
 "rand 0.8.5",
 "rand_chacha 0.3.0",
 "rand_xorshift",
 "regex-syntax",
 "rusty-fork",
 "tempfile",
 "unarray",
]

[[package]]
name = "prost"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e6984d2f1a23009bd270b8bb56d0926810a3d483f59c987d77969e9d8e840b2"
dependencies = [
 "bytes",
 "prost-derive 0.7.0",
]

[[package]]
name = "prost"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0841812012b2d4a6145fae9a6af1534873c32aa67fff26bd09f8fa42c83f95a"
dependencies = [
 "bytes",
 "prost-derive 0.11.2",
]

[[package]]
name = "prost"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2796faa41db3ec313a31f7624d9286acf277b52de526150b7e69f3debf891ee5"
dependencies = [
 "bytes",
 "prost-derive 0.13.5",
]

[[package]]
name = "prost-build"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d8b442418ea0822409d9e7d047cbf1e7e9e1760b172bf9982cf29d517c93511"
dependencies = [
 "bytes",
 "heck 0.4.0",
 "itertools 0.10.5",
 "lazy_static",
 "log",
 "multimap",
 "petgraph",
 "prettyplease 0.1.21",
 "prost 0.11.2",
 "prost-types 0.11.2",
 "regex",
 "syn 1.0.103",
 "tempfile",
 "which",
]

[[package]]
name = "prost-build"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be769465445e8c1474e9c5dac2018218498557af32d9ed057325ec9a41ae81bf"
dependencies = [
 "heck 0.4.0",
 "itertools 0.14.0",
 "log",
 "multimap",
 "once_cell",
 "petgraph",
 "prettyplease 0.2.20",
 "prost 0.13.5",
 "prost-types 0.13.5",
 "regex",
 "syn 2.0.87",
 "tempfile",
]

[[package]]
name = "prost-derive"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "169a15f3008ecb5160cba7d37bcd690a7601b6d30cfb87a117d45e59d52af5d4"
dependencies = [
 "anyhow",
 "itertools 0.9.0",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "prost-derive"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "164ae68b6587001ca506d3bf7f1000bfa248d0e1217b618108fba4ec1d0cc306"
dependencies = [
 "anyhow",
 "itertools 0.10.5",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "prost-derive"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a56d757972c98b346a9b766e3f02746cde6dd1cd1d1d563472929fdd74bec4d"
dependencies = [
 "anyhow",
 "itertools 0.14.0",
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "prost-types"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "747761bc3dc48f9a34553bf65605cf6cb6288ba219f3450b4275dbd81539551a"
dependencies = [
 "bytes",
 "prost 0.11.2",
]

[[package]]
name = "prost-types"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52c2c1bf36ddb1a1c396b3601a3cec27c2462e45f07c386894ec3ccf5332bd16"
dependencies = [
 "prost 0.13.5",
]

[[package]]
name = "protobuf"
version = "2.8.0"
source = "git+https://github.com/pingcap/rust-protobuf?branch=v2.8#6642ebaae4352ea01bf00e160480d8da966d3109"
dependencies = [
 "bytes",
 "heck 0.3.1",
 "hex 0.3.2",
]

[[package]]
name = "protobuf-build"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fb3c02f54ecaf12572c1a60dbdb36b1f8f713a16105881143f2be84cca5bbe3"
dependencies = [
 "bitflags 1.3.2",
 "grpcio-compiler",
 "protobuf",
 "protobuf-codegen",
 "regex",
]

[[package]]
name = "protobuf-codegen"
version = "2.8.0"
source = "git+https://github.com/pingcap/rust-protobuf?branch=v2.8#6642ebaae4352ea01bf00e160480d8da966d3109"
dependencies = [
 "heck 0.3.1",
 "protobuf",
]

[[package]]
name = "protobuf-codegen-pure"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00993dc5fbbfcf9d8a005f6b6c29fd29fd6d86deba3ae3f41fd20c624c414616"
dependencies = [
 "protobuf",
 "protobuf-codegen",
]

[[package]]
name = "proxy_ffi"
version = "0.0.1"
dependencies = [
 "api_version",
 "byteorder",
 "bytes",
 "cloud_encryption",
 "collections",
 "dashmap 6.1.0",
 "encryption",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "fail",
 "file_system",
 "futures 0.3.30",
 "futures-util",
 "hyper",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "openssl",
 "parking_lot 0.12.1",
 "pd_client",
 "prometheus",
 "protobuf",
 "raftstore",
 "security",
 "slog",
 "slog-global",
 "thiserror 1.0.30",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv_util",
 "tipb",
 "tokio",
 "tokio-timer",
 "tracker",
 "txn_types",
]

[[package]]
name = "proxy_server"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-stream 0.2.0",
 "backtrace",
 "backup",
 "backup-stream",
 "causal_ts",
 "chrono",
 "clap",
 "cloud_encryption",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_store_ffi",
 "engine_tiflash",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "fs2",
 "futures 0.3.30",
 "grpcio",
 "grpcio-health",
 "hex 0.4.2",
 "hyper",
 "itertools 0.10.5",
 "keys",
 "kvengine",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log",
 "log_wrappers",
 "mime",
 "nix 0.23.2",
 "online_config",
 "openssl",
 "pd_client",
 "pin-project",
 "pprof",
 "prometheus",
 "protobuf",
 "proxy_ffi",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "regex",
 "resolved_ts",
 "resource_control",
 "resource_metering",
 "security",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "serde_with",
 "server",
 "signal",
 "slog",
 "slog-global",
 "tempfile",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "tokio-openssl",
 "toml",
 "txn_types",
 "url",
 "yatp",
]

[[package]]
name = "proxy_tests"
version = "0.0.1"
dependencies = [
 "api_version",
 "arrow",
 "async-trait",
 "batch-system",
 "byteorder",
 "causal_ts",
 "cdc",
 "clap",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "criterion",
 "criterion-cpu-time",
 "criterion-perf-events",
 "crossbeam",
 "encryption",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_store_ffi",
 "engine_test",
 "engine_tiflash",
 "engine_traits",
 "error_code",
 "external_storage_export",
 "fail",
 "file_system",
 "futures 0.3.30",
 "grpcio",
 "grpcio-health",
 "hyper",
 "keys",
 "kvproto",
 "libc 0.2.174",
 "log_wrappers",
 "mock-engine-store",
 "more-asserts",
 "online_config",
 "panic_hook",
 "paste",
 "pd_client",
 "perfcnt",
 "procinfo",
 "profiler",
 "protobuf",
 "proxy_ffi",
 "proxy_server",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "rand_xorshift",
 "resource_metering",
 "security",
 "serde_json",
 "slog",
 "slog-global",
 "sst_importer",
 "tempfile",
 "test_backup",
 "test_coprocessor",
 "test_pd",
 "test_pd_client",
 "test_raftstore",
 "test_sst_importer",
 "test_storage",
 "test_util",
 "tidb_query_aggr",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_executors",
 "tidb_query_expr",
 "tikv",
 "tikv_util",
 "time 0.1.42",
 "tipb",
 "tipb_helper",
 "tokio",
 "toml",
 "txn_types",
 "uuid 0.8.2",
]

[[package]]
name = "pulldown-cmark"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d9cc634bc78768157b5cbfe988ffcd1dcba95cd2b2f03a88316c08c6d00ed63"
dependencies = [
 "bitflags 1.3.2",
 "memchr",
 "unicase",
]

[[package]]
name = "quanta"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7e31331286705f455e56cca62e0e717158474ff02b7936c1fa596d983f4ae27"
dependencies = [
 "crossbeam-utils 0.8.8",
 "libc 0.2.174",
 "mach",
 "once_cell",
 "raw-cpuid",
 "wasi 0.10.2+wasi-snapshot-preview1",
 "web-sys",
 "winapi 0.3.9",
]

[[package]]
name = "quick-error"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d01941d82fa2ab50be1e79e6714289dd7cde78eba4c074bc5a4374f650dfe0"

[[package]]
name = "quick-xml"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8533f14c8382aaad0d592c812ac3b826162128b65662331e1127b45c3d18536b"
dependencies = [
 "memchr",
]

[[package]]
name = "quick-xml"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11bafc859c6815fbaffbbbf4229ecb767ac913fecb27f9ad4343662e9ef099ea"
dependencies = [
 "memchr",
 "serde",
]

[[package]]
name = "quick_cache"
version = "0.6.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b450dad8382b1b95061d5ca1eb792081fb082adf48c678791fe917509596d5f"
dependencies = [
 "ahash 0.8.11",
 "equivalent",
 "hashbrown 0.15.4",
 "parking_lot 0.12.1",
]

[[package]]
name = "quote"
version = "1.0.36"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fa76aaf39101c457836aec0ce2316dbdc3ab723cdda1c6bd4e6ad4208acaca7"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "r-efi"
version = "5.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69cdb34c158ceb288df11e18b4bd39de994f6657d83847bdffdbd7f346754b0f"

[[package]]
name = "raft"
version = "0.7.0"
source = "git+https://github.com/tikv/raft-rs?branch=master#f73766712a538c2f6eb135b455297ad6c03fc58d"
dependencies = [
 "bytes",
 "fxhash",
 "getset",
 "protobuf",
 "raft-proto",
 "rand 0.8.5",
 "slog",
 "thiserror 1.0.30",
]

[[package]]
name = "raft-engine"
version = "0.3.0"
source = "git+https://github.com/tikv/raft-engine.git#33530112c3a4acaf8c50ca9d0470284109926296"
dependencies = [
 "byteorder",
 "crc32fast",
 "crossbeam",
 "fail",
 "fs2",
 "hashbrown 0.12.0",
 "hex 0.4.2",
 "if_chain",
 "lazy_static",
 "libc 0.2.174",
 "log",
 "lz4-sys",
 "memmap2 0.5.3",
 "nix 0.26.2",
 "num-derive",
 "num-traits",
 "parking_lot 0.12.1",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "rayon",
 "scopeguard",
 "serde",
 "serde_repr",
 "strum 0.24.1",
 "thiserror 1.0.30",
]

[[package]]
name = "raft-proto"
version = "0.7.0"
source = "git+https://github.com/tikv/raft-rs?branch=master#f73766712a538c2f6eb135b455297ad6c03fc58d"
dependencies = [
 "bytes",
 "protobuf",
 "protobuf-build",
]

[[package]]
name = "raft_log_engine"
version = "0.0.1"
dependencies = [
 "codec",
 "encryption",
 "engine_traits",
 "file_system",
 "kvproto",
 "lazy_static",
 "num_cpus",
 "online_config",
 "protobuf",
 "raft",
 "raft-engine",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tempfile",
 "tikv_util",
 "time 0.1.42",
 "tracker",
]

[[package]]
name = "raftstore"
version = "0.0.1"
dependencies = [
 "api_version",
 "batch-system",
 "bitflags 1.3.2",
 "byteorder",
 "bytes",
 "causal_ts",
 "cloud_encryption",
 "collections",
 "concurrency_manager",
 "crc32fast",
 "crossbeam",
 "derivative",
 "encryption",
 "encryption_export",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "fs2",
 "futures 0.3.30",
 "futures-util",
 "getset",
 "grpcio-health",
 "into_other",
 "itertools 0.10.5",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "log",
 "log_wrappers",
 "memory_trace_macros",
 "online_config",
 "openssl",
 "ordered-float 2.10.0",
 "panic_hook",
 "parking_lot 0.12.1",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "raft",
 "raft-proto",
 "rand 0.8.5",
 "resource_control",
 "resource_metering",
 "serde",
 "serde_derive",
 "serde_with",
 "slog",
 "slog-global",
 "smallvec",
 "sst_importer",
 "tempfile",
 "test_sst_importer",
 "thiserror 1.0.30",
 "tidb_query_datatype",
 "tikv_alloc",
 "tikv_util",
 "time 0.1.42",
 "tokio",
 "tracker",
 "txn_types",
 "uuid 0.8.2",
 "yatp",
]

[[package]]
name = "raftstore-proxy"
version = "0.0.1"
dependencies = [
 "proxy_server",
]

[[package]]
name = "raftstore-proxy-main"
version = "0.0.1"
dependencies = [
 "engine_store_ffi",
 "mock-engine-store",
 "proxy_server",
]

[[package]]
name = "raftstore-v2"
version = "0.1.0"
dependencies = [
 "batch-system",
 "bytes",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "fs2",
 "futures 0.3.30",
 "keys",
 "kvproto",
 "log_wrappers",
 "parking_lot 0.12.1",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raft-proto",
 "raftstore",
 "rand 0.8.5",
 "resource_control",
 "resource_metering",
 "slog",
 "slog-global",
 "smallvec",
 "tempfile",
 "test_pd",
 "test_util",
 "thiserror 1.0.30",
 "tikv_util",
 "time 0.1.42",
 "tracker",
 "txn_types",
 "yatp",
]

[[package]]
name = "rand"
version = "0.3.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64ac302d8f83c0c1974bf758f6b041c6c8ada916fbb44a609158ca8b064cc76c"
dependencies = [
 "libc 0.2.174",
 "rand 0.4.6",
]

[[package]]
name = "rand"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "552840b97013b1a26992c11eac34bdd778e464601a4c2054b5f0bff7c6761293"
dependencies = [
 "fuchsia-cprng",
 "libc 0.2.174",
 "rand_core 0.3.1",
 "rdrand",
 "winapi 0.3.9",
]

[[package]]
name = "rand"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a6b1679d49b24bbfe0c803429aa1874472f50d9b363131f0e89fc356b544d03"
dependencies = [
 "getrandom 0.1.12",
 "libc 0.2.174",
 "rand_chacha 0.2.1",
 "rand_core 0.5.1",
 "rand_hc",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc 0.2.174",
 "rand_chacha 0.3.0",
 "rand_core 0.6.2",
]

[[package]]
name = "rand"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fbfd9d094a40bf3ae768db9361049ace4c0e04a4fd6b359518bd7b73a73dd97"
dependencies = [
 "rand_core 0.9.3",
]

[[package]]
name = "rand_chacha"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03a2a90da8c7523f554344f921aa97283eadf6ac484a6d2a7d0212fa7f8d6853"
dependencies = [
 "c2-chacha",
 "rand_core 0.5.1",
]

[[package]]
name = "rand_chacha"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e12735cf05c9e10bf21534da50a147b924d555dc7a547c42e6bb2d5b6017ae0d"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.2",
]

[[package]]
name = "rand_core"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a6fdeb83b075e8266dcc8762c22776f6877a63111121f5f8c7411e5be7eed4b"
dependencies = [
 "rand_core 0.4.2",
]

[[package]]
name = "rand_core"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c33a3c44ca05fa6f1807d8e6743f3824e8509beca625669633be0acbdf509dc"

[[package]]
name = "rand_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bde5296fc891b0cef12a6d03ddccc162ce7b2aff54160af9338f8d40df6d19"
dependencies = [
 "getrandom 0.1.12",
]

[[package]]
name = "rand_core"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34cf66eb183df1c5876e2dcf6b13d57340741e8dc255b48e40a26de954d06ae7"
dependencies = [
 "getrandom 0.2.15",
]

[[package]]
name = "rand_core"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99d9a13982dcf210057a8a78572b2217b667c3beacbf3a0d8b454f6f82837d38"

[[package]]
name = "rand_distr"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32cb0b9bc82b0a0876c2dd994a7e7a2683d3e7390ca40e6886785ef0c7e3ee31"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "rand_hc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3129af7b92a17112d59ad498c6f81eaf463253766b90396d39ea7a39d6613c"
dependencies = [
 "rand_core 0.5.1",
]

[[package]]
name = "rand_isaac"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fac4373cd91b4f55722c553fb0f286edbb81ef3ff6eec7b99d1898a4110a0b28"
dependencies = [
 "rand_core 0.6.2",
]

[[package]]
name = "rand_xorshift"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d25bf25ec5ae4a3f1b92f929810509a2f53d7dca2f50b794ff57e3face536c8f"
dependencies = [
 "rand_core 0.6.2",
]

[[package]]
name = "raw-cpuid"
version = "10.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "929f54e29691d4e6a9cc558479de70db7aa3d98cd6fe7ab86d7507aa2886b9d2"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "rayon"
version = "1.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd99e5772ead8baa5215278c9b15bf92087709e9c1b2d1f97cdb5a183c933a7d"
dependencies = [
 "autocfg",
 "crossbeam-deque",
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "258bcdb5ac6dad48491bb2992db6b7cf74878b0384908af124823d118c99683f"
dependencies = [
 "crossbeam-channel",
 "crossbeam-deque",
 "crossbeam-utils 0.8.8",
 "num_cpus",
]

[[package]]
name = "rdrand"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "678054eb77286b51581ba43620cc911abf02758c91f93f479767aed0f90458b2"
dependencies = [
 "rand_core 0.3.1",
]

[[package]]
name = "recovery"
version = "0.1.0"
dependencies = [
 "dashmap 6.1.0",
 "http",
 "hyper",
 "lazy_static",
 "security",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "url",
]

[[package]]
name = "redox_syscall"
version = "0.1.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2439c63f3f6139d1b57529d16bc3b8bb855230c8efcc5d3a896c8bea7c3b1e84"

[[package]]
name = "redox_syscall"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8380fe0152551244f0747b1bf41737e0f8a74f97a14ccefd1148187271634f3c"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b6dfecf2c74bce2466cabf93f6664d6998a69eb21e39f4207930065b27b771f"
dependencies = [
 "bitflags 2.5.0",
]

[[package]]
name = "redox_users"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "528532f3d801c87aec9def2add9ca802fe569e44a544afe633765267840abe64"
dependencies = [
 "getrandom 0.2.15",
 "redox_syscall 0.2.11",
]

[[package]]
name = "regex"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b544ef1b4eac5dc2db33ea63606ae9ffcfac26c1416a2806ae0bf5f56b201191"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata 0.4.9",
 "regex-syntax",
]

[[package]]
name = "regex-automata"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92b73c2a1770c255c240eaa4ee600df1704a38dc3feaa6e949e7fcd4f8dc09f9"
dependencies = [
 "byteorder",
]

[[package]]
name = "regex-automata"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "809e8dc61f6de73b46c85f4c96486310fe304c434cfa43669d7b40f711150908"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax",
]

[[package]]
name = "regex-syntax"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b15c43186be67a4fd63bee50d0303afffcef381492ebe2c5d87f324e1b8815c"

[[package]]
name = "relative-path"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba39f3699c378cd8970968dcbff9c43159ea4cfbd88d43c00b22f2ef10a435d2"

[[package]]
name = "remove_dir_all"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a83fa3702a688b9359eccba92d153ac33fd2e8462f9e0e3fdf155239ea7792e"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "reqwest"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0460542b551950620a3648c6aa23318ac6b3cd779114bd873209e6e8b5eb1c34"
dependencies = [
 "base64 0.13.0",
 "bytes",
 "encoding_rs 0.8.35",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "hyper-rustls 0.22.1",
 "hyper-tls",
 "ipnet",
 "js-sys",
 "lazy_static",
 "log",
 "mime",
 "native-tls",
 "percent-encoding",
 "pin-project-lite",
 "rustls 0.19.1",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "tokio",
 "tokio-native-tls",
 "tokio-rustls 0.22.0",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "webpki-roots",
 "winreg",
]

[[package]]
name = "resolved_ts"
version = "0.0.1"
dependencies = [
 "collections",
 "concurrency_manager",
 "crossbeam",
 "engine_rocks",
 "engine_traits",
 "fail",
 "futures 0.3.30",
 "grpcio",
 "hex 0.4.2",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "online_config",
 "panic_hook",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raftstore",
 "security",
 "slog",
 "slog-global",
 "tempfile",
 "test_raftstore",
 "test_sst_importer",
 "test_util",
 "thiserror 1.0.30",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "tokio",
 "txn_types",
]

[[package]]
name = "resource_control"
version = "0.0.1"
dependencies = [
 "byteorder",
 "collections",
 "crossbeam",
 "crossbeam-skiplist",
 "dashmap 5.1.0",
 "fail",
 "futures 0.3.30",
 "kvproto",
 "lazy_static",
 "online_config",
 "pd_client",
 "pin-project",
 "prometheus",
 "protobuf",
 "serde",
 "slog",
 "slog-global",
 "test_pd",
 "test_pd_client",
 "tikv_util",
 "yatp",
]

[[package]]
name = "resource_metering"
version = "0.0.1"
dependencies = [
 "collections",
 "crossbeam",
 "futures 0.3.30",
 "grpcio",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log",
 "online_config",
 "pdqselect",
 "pin-project",
 "procinfo",
 "prometheus",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tikv_util",
]

[[package]]
name = "rev_lines"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18eb52b6664d331053136fcac7e4883bdc6f5fc04a6aab3b0f75eafb80ab88b3"

[[package]]
name = "rgb"
version = "0.8.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e74fdc210d8f24a7dbfedc13b04ba5764f5232754ccebfdf5fff1bad791ccbc6"
dependencies = [
 "bytemuck",
]

[[package]]
name = "ring"
version = "0.16.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b72b84d47e8ec5a4f2872e8262b8f8256c5be1c938a7d6d3a867a3ba8f722f74"
dependencies = [
 "cc",
 "libc 0.2.174",
 "once_cell",
 "spin 0.5.2",
 "untrusted 0.7.1",
 "web-sys",
 "winapi 0.3.9",
]

[[package]]
name = "ring"
version = "0.17.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c17fa4cb658e3583423e915b9f3acc01cceaee1860e33d59ebae66adc3a2dc0d"
dependencies = [
 "cc",
 "cfg-if 1.0.0",
 "getrandom 0.2.15",
 "libc 0.2.174",
 "spin 0.9.8",
 "untrusted 0.9.0",
 "windows-sys 0.52.0",
]

[[package]]
name = "rle-decode-fast"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3582f63211428f83597b51b2ddb88e2a91a9d52d12831f9d08f5e624e8977422"

[[package]]
name = "rocksdb"
version = "0.3.0"
source = "git+https://github.com/tikv/rust-rocksdb.git#2096b9a161f93e437f7adee49e68cd1570aea42f"
dependencies = [
 "libc 0.2.174",
 "librocksdb_sys",
]

[[package]]
name = "rstest"
version = "0.18.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97eeab2f3c0a199bc4be135c36c924b6590b88c377d416494288c14f2db30199"
dependencies = [
 "futures 0.3.30",
 "futures-timer",
 "rstest_macros",
 "rustc_version 0.4.0",
]

[[package]]
name = "rstest_macros"
version = "0.18.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d428f8247852f894ee1be110b375111b586d4fa431f6c46e64ba5a0dcccbe605"
dependencies = [
 "cfg-if 1.0.0",
 "glob",
 "proc-macro2",
 "quote",
 "regex",
 "relative-path",
 "rustc_version 0.4.0",
 "syn 2.0.87",
 "unicode-ident",
]

[[package]]
name = "rusoto_core"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#4ebb63fc2ce107fcee66f5a5f1653f6a3b46b927"
dependencies = [
 "async-trait",
 "base64 0.13.0",
 "bytes",
 "crc32fast",
 "futures 0.3.30",
 "http",
 "hyper",
 "hyper-tls",
 "lazy_static",
 "log",
 "rusoto_credential",
 "rusoto_signature",
 "rustc_version 0.3.3",
 "serde",
 "serde_json",
 "tokio",
 "xml-rs",
]

[[package]]
name = "rusoto_credential"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#4ebb63fc2ce107fcee66f5a5f1653f6a3b46b927"
dependencies = [
 "async-trait",
 "chrono",
 "dirs-next",
 "futures 0.3.30",
 "hyper",
 "serde",
 "serde_json",
 "shlex 0.1.1",
 "tokio",
 "zeroize",
]

[[package]]
name = "rusoto_kms"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#4ebb63fc2ce107fcee66f5a5f1653f6a3b46b927"
dependencies = [
 "async-trait",
 "bytes",
 "futures 0.3.30",
 "rusoto_core",
 "serde",
 "serde_json",
]

[[package]]
name = "rusoto_mock"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#4ebb63fc2ce107fcee66f5a5f1653f6a3b46b927"
dependencies = [
 "async-trait",
 "chrono",
 "futures 0.3.30",
 "http",
 "rusoto_core",
 "serde",
 "serde_json",
]

[[package]]
name = "rusoto_s3"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#4ebb63fc2ce107fcee66f5a5f1653f6a3b46b927"
dependencies = [
 "async-trait",
 "bytes",
 "futures 0.3.30",
 "rusoto_core",
 "serde",
 "serde_derive",
 "xml-rs",
]

[[package]]
name = "rusoto_signature"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#4ebb63fc2ce107fcee66f5a5f1653f6a3b46b927"
dependencies = [
 "base64 0.13.0",
 "bytes",
 "chrono",
 "digest",
 "futures 0.3.30",
 "hex 0.4.2",
 "hmac",
 "http",
 "hyper",
 "log",
 "md-5",
 "percent-encoding",
 "pin-project-lite",
 "rusoto_credential",
 "rustc_version 0.3.3",
 "serde",
 "sha2",
 "tokio",
]

[[package]]
name = "rusoto_sts"
version = "0.46.0"
source = "git+https://github.com/tikv/rusoto?branch=cse#4ebb63fc2ce107fcee66f5a5f1653f6a3b46b927"
dependencies = [
 "async-trait",
 "bytes",
 "chrono",
 "futures 0.3.30",
 "rusoto_core",
 "serde_urlencoded",
 "xml-rs",
]

[[package]]
name = "rust-crypto"
version = "0.2.36"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f76d05d3993fd5f4af9434e8e436db163a12a9d40e1a58a726f27a01dfd12a2a"
dependencies = [
 "gcc",
 "libc 0.2.174",
 "rand 0.3.23",
 "rustc-serialize",
 "time 0.1.42",
]

[[package]]
name = "rust-ini"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c96a7d6722944454c68ff2ba2a252a4e9b0635c03dd510fdf482a2c8981cbf2"
dependencies = [
 "multimap",
]

[[package]]
name = "rust-stemmers"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e46a2036019fdb888131db7a4c847a1063a7493f971ed94ea82c67eada63ca54"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "rustc-demangle"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c691c0e608126e00913e33f0ccf3727d5fc84573623b8d65b2df340b5201783"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hash"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "357703d41365b4b27c590e3ed91eabb1b663f07c4c084095e60cbed4362dff0d"

[[package]]
name = "rustc-serialize"
version = "0.3.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe834bc780604f4674073badbad26d7219cadfb4a2275802db12cbae17498401"

[[package]]
name = "rustc_version"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "138e3e0acb6c9fb258b19b67cb8abd63c00679d2851805ea151465464fe9030a"
dependencies = [
 "semver 0.9.0",
]

[[package]]
name = "rustc_version"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0dfe2087c51c460008730de8b57e6a320782fbfb312e1f4d520e6c6fae155ee"
dependencies = [
 "semver 0.11.0",
]

[[package]]
name = "rustc_version"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa0f585226d2e68097d4f95d113b15b83a82e819ab25717ec0590d9584ef366"
dependencies = [
 "semver 1.0.4",
]

[[package]]
name = "rustix"
version = "0.38.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdb5bc1ae2baa591800df16c9ca78619bf65c0488b41b96ccec5d11220d8c154"
dependencies = [
 "bitflags 2.5.0",
 "errno",
 "libc 0.2.174",
 "linux-raw-sys 0.4.15",
 "windows-sys 0.52.0",
]

[[package]]
name = "rustix"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c71e83d6afe7ff64890ec6b71d6a69bb8a610ab78ce364b3352876bb4c801266"
dependencies = [
 "bitflags 2.5.0",
 "errno",
 "libc 0.2.174",
 "linux-raw-sys 0.9.4",
 "windows-sys 0.52.0",
]

[[package]]
name = "rustls"
version = "0.19.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35edb675feee39aec9c99fa5ff985081995a06d594114ae14cbe797ad7b7a6d7"
dependencies = [
 "base64 0.13.0",
 "log",
 "ring 0.16.16",
 "sct 0.6.0",
 "webpki",
]

[[package]]
name = "rustls"
version = "0.21.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f56a14d1f48b391359b22f731fd4bd7e43c97f3c50eee276f3aa09c94784d3e"
dependencies = [
 "log",
 "ring 0.17.8",
 "rustls-webpki",
 "sct 0.7.1",
]

[[package]]
name = "rustls-native-certs"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9aace74cb666635c918e9c12bc0d348266037aa8eb599b5cba565709a8dff00"
dependencies = [
 "openssl-probe",
 "rustls-pemfile",
 "schannel",
 "security-framework",
]

[[package]]
name = "rustls-pemfile"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c74cae0a4cf6ccbbf5f359f08efdf8ee7e1dc532573bf0db71968cb56b1448c"
dependencies = [
 "base64 0.21.7",
]

[[package]]
name = "rustls-webpki"
version = "0.101.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6275d1ee7a1cd780b64aca7726599a1dbc893b1e64144529e55c3c2f745765"
dependencies = [
 "ring 0.17.8",
 "untrusted 0.9.0",
]

[[package]]
name = "rustversion"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb5d2a036dc6d2d8fd16fde3498b04306e29bd193bf306a57427019b823d5acd"

[[package]]
name = "rusty-fork"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb3dcc6e454c328bb824492db107ab7c0ae8fcffe4ad210136ef014458c1bc4f"
dependencies = [
 "fnv",
 "quick-error",
 "tempfile",
 "wait-timeout",
]

[[package]]
name = "ryu"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed3d612bc64430efeb3f7ee6ef26d590dce0c43249217bddc62112540c7941e1"

[[package]]
name = "safemem"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2b08423011dae9a5ca23f07cf57dac3857f5c885d352b76f6d95f4aea9434d0"

[[package]]
name = "same-file"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "585e8ddcedc187886a30fa705c47985c3fa88d06624095856b36ca0b82ff4421"
dependencies = [
 "winapi-util",
]

[[package]]
name = "schannel"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87f550b06b6cba9c8b8be3ee73f391990116bf527450d2556e9b9ce263b9a021"
dependencies = [
 "lazy_static",
 "winapi 0.3.9",
]

[[package]]
name = "scheduled-thread-pool"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3cbc66816425a074528352f5789333ecff06ca41b36b0b0efdfbb29edc391a19"
dependencies = [
 "parking_lot 0.12.1",
]

[[package]]
name = "schema"
version = "0.1.0"
dependencies = [
 "api_version",
 "async-trait",
 "bytes",
 "chrono",
 "kvenginepb",
 "log_wrappers",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "serde_repr",
 "slog",
 "slog-global",
 "slog-term",
 "tidb_query_datatype",
 "tikv_util",
 "tipb",
]

[[package]]
name = "scopeguard"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d29ab0c6d3fc0ee92fe66e2d99f700eab17a8d57d1c1d3b748380fb20baa78cd"

[[package]]
name = "scratch"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3cf7c11c38cb994f3d40e8a8cde3bbd1f72a435e4c49e85d6553d8312306152"

[[package]]
name = "sct"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3042af939fca8c3453b7af0f1c66e533a15a86169e39de2657310ade8f98d3c"
dependencies = [
 "ring 0.16.16",
 "untrusted 0.7.1",
]

[[package]]
name = "sct"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da046153aa2352493d6cb7da4b6e5c0c057d8a1d0a9aa8560baffdd945acd414"
dependencies = [
 "ring 0.17.8",
 "untrusted 0.9.0",
]

[[package]]
name = "seahash"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c107b6f4780854c8b126e228ea8869f4d7b71260f962fefb57b996b8959ba6b"

[[package]]
name = "security"
version = "0.0.1"
dependencies = [
 "aliyun",
 "aws",
 "base64 0.13.0",
 "bstr",
 "bytes",
 "cloud",
 "cloud_encryption",
 "collections",
 "encryption",
 "grpcio",
 "http",
 "hyper",
 "hyper-rustls 0.24.2",
 "kvproto",
 "rustls 0.21.12",
 "rustls-pemfile",
 "serde",
 "serde_derive",
 "serde_json",
 "slog",
 "slog-global",
 "tempfile",
 "tikv_util",
]

[[package]]
name = "security-framework"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3670b1d2fdf6084d192bc71ead7aabe6c06aa2ea3fbd9cc3ac111fa5c2b1bd84"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation",
 "core-foundation-sys",
 "libc 0.2.174",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3676258fd3cfe2c9a0ec99ce3038798d847ce3e4bb17746373eb9f0f1ac16339"
dependencies = [
 "core-foundation-sys",
 "libc 0.2.174",
]

[[package]]
name = "semver"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7eb9ef2c18661902cc47e535f9bc51b78acd254da71d375c2f6720d9a40403"
dependencies = [
 "semver-parser 0.7.0",
 "serde",
]

[[package]]
name = "semver"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "394cec28fa623e00903caf7ba4fa6fb9a0e260280bb8cdbbba029611108a0190"
dependencies = [
 "semver-parser 0.7.0",
]

[[package]]
name = "semver"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f301af10236f6df4160f7c3f04eec6dbc70ace82d23326abad5edee88801c6b6"
dependencies = [
 "semver-parser 0.10.2",
]

[[package]]
name = "semver"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "568a8e6258aa33c13358f81fd834adb854c6f7c9468520910a9b1e8fac068012"
dependencies = [
 "serde",
]

[[package]]
name = "semver-parser"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388a1df253eca08550bef6c72392cfe7c30914bf41df5269b68cbd6ff8f570a3"

[[package]]
name = "semver-parser"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0bef5b7f9e0df16536d3961cfb6e84331c065b4066afb39768d0e319411f7"
dependencies = [
 "pest",
]

[[package]]
name = "serde"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f0e2c6ed6606019b4e29e69dbaba95b11854410e5347d525002456dbbb786b6"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde-xml-rs"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65162e9059be2f6a3421ebbb4fef3e74b7d9e7c60c50a0e292c6239f19f1edfa"
dependencies = [
 "log",
 "serde",
 "thiserror 1.0.30",
 "xml-rs",
]

[[package]]
name = "serde_cbor"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e18acfa2f90e8b735b2836ab8d538de304cbb6729a7360729ea5a895d15a622"
dependencies = [
 "half 1.8.2",
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b0276cf7f2c73365f7157c8123c21cd9a50fbbd844757af28ca1f5925fc2a00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "serde_ignored"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c2c7d39d14f2f2ea82239de71594782f186fd03501ac81f0ce08e674819ff2f"
dependencies = [
 "serde",
]

[[package]]
name = "serde_json"
version = "1.0.140"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20068b6e96dc6c9bd23e01df8827e6c7e1f2fddd43c21810382803c136b99373"
dependencies = [
 "indexmap 2.10.0",
 "itoa 1.0.1",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_path_to_error"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0421d4f173fab82d72d6babf36d57fae38b994ca5c2d78e704260ba6d12118b"
dependencies = [
 "serde",
]

[[package]]
name = "serde_repr"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fe39d9fbb0ebf5eb2c7cb7e2a47e4f462fad1379f1166b8ae49ad9eae89a7ca"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edfa57a7f8d9c1d260a549e7224100f6c43d43f9103e06dd8b4095a9b2b43ce9"
dependencies = [
 "form_urlencoded",
 "itoa 0.4.4",
 "ryu",
 "serde",
]

[[package]]
name = "serde_with"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89d3d595d64120bbbc70b7f6d5ae63298b62a3d9f373ec2f56acf5365ca8a444"
dependencies = [
 "serde",
 "serde_with_macros",
]

[[package]]
name = "serde_with_macros"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4070d2c9b9d258465ad1d82aabb985b84cd9a3afa94da25ece5a9938ba5f1606"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "serde_yaml"
version = "0.9.34+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a8b1a1a2ebf674015cc02edccce75287f1a0130d394307b36743c2f5d504b47"
dependencies = [
 "indexmap 2.10.0",
 "itoa 1.0.1",
 "ryu",
 "serde",
 "unsafe-libyaml",
]

[[package]]
name = "server"
version = "0.0.1"
dependencies = [
 "api_version",
 "backup",
 "backup-stream",
 "causal_ts",
 "cdc",
 "chrono",
 "clap",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_traits",
 "error_code",
 "file_system",
 "fs2",
 "futures 0.3.30",
 "grpcio",
 "grpcio-health",
 "hex 0.4.2",
 "keys",
 "kvengine",
 "kvproto",
 "libc 0.2.174",
 "log",
 "log_wrappers",
 "pd_client",
 "prometheus",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "raftstore-v2",
 "rand 0.8.5",
 "resolved_ts",
 "resource_control",
 "resource_metering",
 "security",
 "serde_json",
 "signal-hook",
 "slog",
 "slog-global",
 "snap_recovery",
 "tempfile",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "toml",
 "txn_types",
 "yatp",
]

[[package]]
name = "sha2"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2933378ddfeda7ea26f48c555bdad8bb446bf8a3d17832dc83e380d444cfb8c1"
dependencies = [
 "block-buffer",
 "cfg-if 0.1.10",
 "cpuid-bool",
 "digest",
 "opaque-debug",
]

[[package]]
name = "shlex"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fdf1b9db47230893d76faad238fd6097fd6d6a9245cd7a4d90dbd639536bbd2"

[[package]]
name = "shlex"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43b2853a4d09f215c24cc5489c992ce46052d359b5109343cbafbf26bc62f8a3"

[[package]]
name = "signal"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "106428d9d96840ecdec5208c13ab8a4e28c38da1e0ccf2909fb44e41b992f897"
dependencies = [
 "libc 0.2.174",
 "nix 0.11.1",
]

[[package]]
name = "signal-hook"
version = "0.3.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a253b5e89e2698464fc26b545c9edceb338e18a89effeeecfea192c3025be29d"
dependencies = [
 "libc 0.2.174",
 "signal-hook-registry",
]

[[package]]
name = "signal-hook-registry"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e51e73328dc4ac0c7ccbda3a494dfa03df1de2f46018127f60c693f2648455b0"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "simsimd"
version = "5.9.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9638f2829f4887c62a01958903b58fa1b740a64d5dc2bbc4a75a33827ee1bd53"
dependencies = [
 "cc",
]

[[package]]
name = "siphasher"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa8f3741c7372e75519bd9346068370c9cdaabcc1f9599cbcf2a2719352286b7"

[[package]]
name = "siphasher"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56199f7ddabf13fe5074ce809e7d3f42b42ae711800501b5b16ea82ad029c39d"

[[package]]
name = "skeptic"
version = "0.13.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16d23b015676c90a0f01c197bfdc786c20342c73a0afdda9025adb0bc42940a8"
dependencies = [
 "bytecount",
 "cargo_metadata 0.14.2",
 "error-chain",
 "glob",
 "pulldown-cmark",
 "tempfile",
 "walkdir",
]

[[package]]
name = "sketches-ddsketch"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85636c14b73d81f541e525f585c0a2109e6744e1565b5c1668e31c70c10ed65c"
dependencies = [
 "serde",
]

[[package]]
name = "slab"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c111b5bd5695e56cffe5129854aa230b39c93a305372fdbb2668ca2394eea9f8"

[[package]]
name = "slice-group-by"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "826167069c09b99d56f31e9ae5c99049e932a98c9dc2dac47645b08dbbf76ba7"

[[package]]
name = "slog"
version = "2.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cc9c640a4adbfbcc11ffb95efe5aa7af7309e002adab54b185507dbf2377b99"

[[package]]
name = "slog-async"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c60813879f820c85dbc4eabf3269befe374591289019775898d56a81a804fbdc"
dependencies = [
 "crossbeam-channel",
 "slog",
 "take_mut",
 "thread_local",
]

[[package]]
name = "slog-global"
version = "0.1.0"
source = "git+https://github.com/breeswish/slog-global.git?rev=d592f88e4dbba5eb439998463054f1a44fbf17b9#d592f88e4dbba5eb439998463054f1a44fbf17b9"
dependencies = [
 "arc-swap 0.4.8",
 "lazy_static",
 "log",
 "slog",
]

[[package]]
name = "slog-json"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ddc0d2aff1f8f325ef660d9a0eb6e6dcd20b30b3f581a5897f58bf42d061c37a"
dependencies = [
 "chrono",
 "serde",
 "serde_json",
 "slog",
]

[[package]]
name = "slog-term"
version = "2.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3668dd2252f4381d64de0c79e6c8dc6bd509d1cab3535b35a3fc9bafd1241d5"
dependencies = [
 "atty",
 "chrono",
 "slog",
 "term",
 "thread_local",
]

[[package]]
name = "slog_derive"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a945ec7f7ce853e89ffa36be1e27dce9a43e82ff9093bf3461c30d5da74ed11b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "smallvec"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2dd574626839106c320a323308629dcb1acfc96e32a8cba364ddc61ac23ee83"

[[package]]
name = "snap_recovery"
version = "0.1.0"
dependencies = [
 "chrono",
 "encryption",
 "encryption_export",
 "engine_rocks",
 "engine_traits",
 "futures 0.3.30",
 "grpcio",
 "keys",
 "kvproto",
 "log",
 "pd_client",
 "protobuf",
 "raft_log_engine",
 "raftstore",
 "slog",
 "slog-global",
 "structopt",
 "tempfile",
 "thiserror 1.0.30",
 "tikv",
 "tikv_alloc",
 "tikv_util",
 "toml",
 "txn_types",
]

[[package]]
name = "snappy-sys"
version = "0.1.0"
source = "git+https://github.com/busyjay/rust-snappy.git?branch=static-link#8c12738bad811397600455d6982aff754ea2ac44"
dependencies = [
 "cmake",
 "libc 0.2.174",
 "pkg-config",
]

[[package]]
name = "snmalloc-rs"
version = "0.2.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f5a6194d59b08fc87381e7c8a04ab4ab9967282b00f409bb742e08f3514ed0b"
dependencies = [
 "snmalloc-sys",
]

[[package]]
name = "snmalloc-sys"
version = "0.2.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9518813a25ab2704a6df4968f609aa6949705409b6a854dcc87018d12961cbc8"
dependencies = [
 "cmake",
]

[[package]]
name = "socket2"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02e2d2db9033d13a1567121ddd7a095ee144db4e1ca1b1bda3419bc0da294ebd"
dependencies = [
 "libc 0.2.174",
 "winapi 0.3.9",
]

[[package]]
name = "spin"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e63cff320ae2c57904679ba7cb63280a3dc4613885beafb148ee7bf9aa9042d"

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"

[[package]]
name = "sst_importer"
version = "0.1.0"
dependencies = [
 "api_version",
 "crc32fast",
 "dashmap 5.1.0",
 "encryption",
 "engine_rocks",
 "engine_traits",
 "error_code",
 "external_storage_export",
 "file_system",
 "futures 0.3.30",
 "futures-util",
 "grpcio",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "openssl",
 "prometheus",
 "rand 0.8.5",
 "serde",
 "serde_derive",
 "slog",
 "slog-global",
 "tempfile",
 "test_sst_importer",
 "test_util",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
 "tokio",
 "txn_types",
 "uuid 0.8.2",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "str-buf"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d44a3643b4ff9caf57abcee9c2c621d6c03d9135e0d8b589bd9afb5992cb176a"

[[package]]
name = "str_stack"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9091b6114800a5f2141aee1d1b9d6ca3592ac062dc5decb3764ec5895a47b4eb"

[[package]]
name = "strsim"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ea5119cdb4c55b55d432abb513a0429384878c15dde60cc77b1c99de1a95a6a"

[[package]]
name = "strsim"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "032c03039aae92b350aad2e3779c352e104d919cb192ba2fabbd7b831ce4f0f6"

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "structopt"
version = "0.3.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "126d630294ec449fae0b16f964e35bf3c74f940da9dca17ee9b905f7b3112eb8"
dependencies = [
 "clap",
 "lazy_static",
 "structopt-derive",
]

[[package]]
name = "structopt-derive"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65e51c492f9e23a220534971ff5afc14037289de430e3c83f9daf6a1b6ae91e8"
dependencies = [
 "heck 0.3.1",
 "proc-macro-error",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "strum"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7318c509b5ba57f18533982607f24070a55d353e90d4cae30c467cdb2ad5ac5c"
dependencies = [
 "strum_macros 0.20.1",
]

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"
dependencies = [
 "strum_macros 0.24.2",
]

[[package]]
name = "strum"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f64def088c51c9510a8579e3c5d67c65349dcf755e5479ad3d010aa6454e2c32"
dependencies = [
 "strum_macros 0.27.1",
]

[[package]]
name = "strum_macros"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee8bc6b87a5112aeeab1f4a9f7ab634fe6cbefc4850006df31267f4cfb9e3149"
dependencies = [
 "heck 0.3.1",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "strum_macros"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4faebde00e8ff94316c01800f9054fd2ba77d30d9e922541913051d1d978918b"
dependencies = [
 "heck 0.4.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 1.0.103",
]

[[package]]
name = "strum_macros"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c77a8c5abcaf0f9ce05d62342b7d298c346515365c36b673df4ebe3ced01fde8"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.87",
]

[[package]]
name = "subtle"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "343f3f510c2915908f155e94f17220b19ccfacf2a64a2a5d8004f2c3e311e7fd"

[[package]]
name = "symbolic-common"
version = "10.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac457d054f793cedfde6f32d21d692b8351cfec9084fefd0470c0373f6d799bc"
dependencies = [
 "debugid",
 "memmap2 0.5.3",
 "stable_deref_trait",
 "uuid 1.2.1",
]

[[package]]
name = "symbolic-demangle"
version = "10.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48808b846eef84e0ac06365dc620f028ae632355e5dcffc007bf1b2bf5eab17b"
dependencies = [
 "cpp_demangle",
 "rustc-demangle",
 "symbolic-common",
]

[[package]]
name = "syn"
version = "1.0.103"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a864042229133ada95abf3b54fdc62ef5ccabe9515b64717bcb9a1919e59445d"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.87"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25aa4ce346d03a6dcd68dd8b4010bcb74e54e62c90c573f394c46eae99aba32d"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "sync_wrapper"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20518fe4a4c9acf048008599e464deb21beeae3d3578418951a189c235a7a9a8"

[[package]]
name = "sysinfo"
version = "0.26.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ade661fa5e048ada64ad7901713301c21d2dbc5b65ee7967de8826c111452960"
dependencies = [
 "cfg-if 1.0.0",
 "core-foundation-sys",
 "libc 0.2.174",
 "ntapi",
 "once_cell",
 "rayon",
 "winapi 0.3.9",
]

[[package]]
name = "tagptr"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b2093cf4c8eb1e67749a6762251bc9cd836b6fc171623bd0a9d324d37af2417"

[[package]]
name = "take_mut"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f764005d11ee5f36500a149ace24e00e3da98b0158b3e2d53a7495660d3f4d60"

[[package]]
name = "tame-gcs"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d20ec2d6525a66afebdff9e1d8ef143c9deae9a3b040c61d3cfa9ae6fda80060"
dependencies = [
 "base64 0.13.0",
 "bytes",
 "chrono",
 "futures-util",
 "http",
 "percent-encoding",
 "pin-utils",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "thiserror 1.0.30",
 "url",
]

[[package]]
name = "tame-oauth"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9435c9348e480fad0f2215d5602e2dfad03df8a6398c4e7ceaeaa42758f26a8a"
dependencies = [
 "base64 0.13.0",
 "chrono",
 "http",
 "lock_api",
 "parking_lot 0.11.1",
 "ring 0.16.16",
 "serde",
 "serde_json",
 "twox-hash",
 "url",
]

[[package]]
name = "tantivy"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8d0582f186c0a6d55655d24543f15e43607299425c5ad8352c242b914b31856"
dependencies = [
 "aho-corasick",
 "arc-swap 1.7.1",
 "base64 0.22.1",
 "bitpacking",
 "byteorder",
 "census",
 "crc32fast",
 "crossbeam-channel",
 "downcast-rs",
 "fastdivide",
 "fnv",
 "fs4",
 "htmlescape",
 "itertools 0.12.1",
 "levenshtein_automata",
 "log",
 "lru",
 "lz4_flex",
 "measure_time",
 "memmap2 0.9.5",
 "num_cpus",
 "once_cell",
 "oneshot",
 "rayon",
 "regex",
 "rust-stemmers",
 "rustc-hash 1.1.0",
 "serde",
 "serde_json",
 "sketches-ddsketch",
 "smallvec",
 "tantivy-bitpacker",
 "tantivy-columnar",
 "tantivy-common",
 "tantivy-fst",
 "tantivy-query-grammar",
 "tantivy-stacker",
 "tantivy-tokenizer-api",
 "tempfile",
 "thiserror 1.0.30",
 "time 0.3.41",
 "uuid 1.2.1",
 "winapi 0.3.9",
]

[[package]]
name = "tantivy-bitpacker"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "284899c2325d6832203ac6ff5891b297fc5239c3dc754c5bc1977855b23c10df"
dependencies = [
 "bitpacking",
]

[[package]]
name = "tantivy-columnar"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12722224ffbe346c7fec3275c699e508fd0d4710e629e933d5736ec524a1f44e"
dependencies = [
 "downcast-rs",
 "fastdivide",
 "itertools 0.12.1",
 "serde",
 "tantivy-bitpacker",
 "tantivy-common",
 "tantivy-sstable",
 "tantivy-stacker",
]

[[package]]
name = "tantivy-common"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8019e3cabcfd20a1380b491e13ff42f57bb38bf97c3d5fa5c07e50816e0621f4"
dependencies = [
 "async-trait",
 "byteorder",
 "ownedbytes",
 "serde",
 "time 0.3.41",
]

[[package]]
name = "tantivy-fst"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d60769b80ad7953d8a7b2c70cdfe722bbcdcac6bccc8ac934c40c034d866fc18"
dependencies = [
 "byteorder",
 "regex-syntax",
 "utf8-ranges",
]

[[package]]
name = "tantivy-query-grammar"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "847434d4af57b32e309f4ab1b4f1707a6c566656264caa427ff4285c4d9d0b82"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "tantivy-sstable"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c69578242e8e9fc989119f522ba5b49a38ac20f576fc778035b96cc94f41f98e"
dependencies = [
 "tantivy-bitpacker",
 "tantivy-common",
 "tantivy-fst",
 "zstd 0.13.3",
]

[[package]]
name = "tantivy-stacker"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c56d6ff5591fc332739b3ce7035b57995a3ce29a93ffd6012660e0949c956ea8"
dependencies = [
 "murmurhash32",
 "rand_distr",
 "tantivy-common",
]

[[package]]
name = "tantivy-tokenizer-api"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a0dcade25819a89cfe6f17d932c9cedff11989936bf6dd4f336d50392053b04"
dependencies = [
 "serde",
]

[[package]]
name = "tar"
version = "0.4.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d863878d212c87a19c1a610eb53bb01fe12951c0501cf5a0d65f724914a667a"
dependencies = [
 "filetime",
 "libc 0.2.174",
 "xattr",
]

[[package]]
name = "tcmalloc"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "375205113d84a1c5eeed67beaa0ce08e41be1a9d5acc3425ad2381fddd9d819b"
dependencies = [
 "tcmalloc-sys",
]

[[package]]
name = "tcmalloc-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b7ad73e635dd232c2c2106d59269f59a61de421cc6b95252d2d932094ff1f40"

[[package]]
name = "tempdir"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15f2b5fb00ccdf689e0149d1b1b3c03fead81c2b37735d812fa8bddbbf41b6d8"
dependencies = [
 "rand 0.4.6",
 "remove_dir_all",
]

[[package]]
name = "tempfile"
version = "3.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a64e3985349f2441a1a9ef0b853f869006c3855f2cda6862a94d26ebb9d6a1"
dependencies = [
 "fastrand 2.3.0",
 "getrandom 0.3.3",
 "once_cell",
 "rustix 1.0.7",
 "windows-sys 0.52.0",
]

[[package]]
name = "term"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c59df8ac95d96ff9bede18eb7300b0fda5e5d8d90960e76f8e14ae765eedbf1f"
dependencies = [
 "dirs-next",
 "rustversion",
 "winapi 0.3.9",
]

[[package]]
name = "termcolor"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bab24d30b911b2376f3a13cc2cd443142f0c81dda04c118693e35b3835757755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "test_backup"
version = "0.0.1"
dependencies = [
 "api_version",
 "backup",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "engine_traits",
 "external_storage_export",
 "file_system",
 "futures 0.3.30",
 "futures-executor",
 "futures-util",
 "grpcio",
 "kvproto",
 "protobuf",
 "rand 0.8.5",
 "tempfile",
 "test_raftstore",
 "tidb_query_common",
 "tikv",
 "tikv_util",
 "txn_types",
]

[[package]]
name = "test_coprocessor"
version = "0.0.1"
dependencies = [
 "api_version",
 "collections",
 "concurrency_manager",
 "engine_rocks",
 "futures 0.3.30",
 "kvproto",
 "protobuf",
 "resource_metering",
 "test_storage",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv",
 "tikv_util",
 "tipb",
 "txn_types",
]

[[package]]
name = "test_pd"
version = "0.0.1"
dependencies = [
 "collections",
 "fail",
 "futures 0.3.30",
 "grpcio",
 "kvproto",
 "log_wrappers",
 "pd_client",
 "security",
 "slog",
 "slog-global",
 "tikv_util",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "test_pd_client"
version = "0.0.1"
dependencies = [
 "collections",
 "fail",
 "futures 0.3.30",
 "grpcio",
 "keys",
 "kvproto",
 "log_wrappers",
 "pd_client",
 "raft",
 "security",
 "slog",
 "slog-global",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
]

[[package]]
name = "test_raftstore"
version = "0.0.1"
dependencies = [
 "api_version",
 "backtrace",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption_export",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_test",
 "engine_traits",
 "fail",
 "file_system",
 "futures 0.3.30",
 "grpcio",
 "grpcio-health",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "pd_client",
 "protobuf",
 "raft",
 "raftstore",
 "rand 0.8.5",
 "resolved_ts",
 "resource_control",
 "resource_metering",
 "security",
 "server",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "test_util",
 "tikv",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
]

[[package]]
name = "test_raftstore-v2"
version = "0.0.1"
dependencies = [
 "api_version",
 "backtrace",
 "causal_ts",
 "collections",
 "concurrency_manager",
 "crossbeam",
 "encryption_export",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_test",
 "engine_traits",
 "fail",
 "file_system",
 "futures 0.3.30",
 "grpcio",
 "grpcio-health",
 "keys",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "pd_client",
 "protobuf",
 "raft",
 "raftstore",
 "raftstore-v2",
 "rand 0.8.5",
 "resolved_ts",
 "resource_control",
 "resource_metering",
 "security",
 "server",
 "slog",
 "slog-global",
 "tempfile",
 "test_pd_client",
 "test_raftstore",
 "test_util",
 "tikv",
 "tikv_util",
 "tokio",
 "tokio-timer",
 "txn_types",
]

[[package]]
name = "test_raftstore_macro"
version = "0.0.1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "test_sst_importer"
version = "0.1.0"
dependencies = [
 "crc32fast",
 "engine_rocks",
 "engine_traits",
 "keys",
 "kvproto",
 "uuid 0.8.2",
]

[[package]]
name = "test_storage"
version = "0.0.1"
dependencies = [
 "api_version",
 "collections",
 "futures 0.3.30",
 "kvproto",
 "pd_client",
 "raftstore",
 "test_raftstore",
 "tikv",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "test_util"
version = "0.0.1"
dependencies = [
 "backtrace",
 "collections",
 "encryption_export",
 "fail",
 "grpcio",
 "kvproto",
 "rand 0.8.5",
 "rand_isaac",
 "security",
 "slog",
 "slog-global",
 "tempfile",
 "tikv_util",
 "time 0.1.42",
]

[[package]]
name = "tests"
version = "0.0.1"
dependencies = [
 "api_version",
 "arrow",
 "async-trait",
 "batch-system",
 "byteorder",
 "causal_ts",
 "cdc",
 "collections",
 "concurrency_manager",
 "crc64fast",
 "criterion",
 "criterion-cpu-time",
 "criterion-perf-events",
 "crossbeam",
 "encryption",
 "engine_rocks",
 "engine_rocks_helper",
 "engine_test",
 "engine_traits",
 "error_code",
 "external_storage_export",
 "fail",
 "file_system",
 "futures 0.3.30",
 "grpcio",
 "grpcio-health",
 "hyper",
 "keys",
 "kvproto",
 "libc 0.2.174",
 "log_wrappers",
 "more-asserts",
 "online_config",
 "panic_hook",
 "paste",
 "pd_client",
 "perfcnt",
 "procinfo",
 "profiler",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "rand 0.8.5",
 "rand_xorshift",
 "resource_control",
 "resource_metering",
 "security",
 "serde_json",
 "slog",
 "slog-global",
 "sst_importer",
 "tempfile",
 "test_backup",
 "test_coprocessor",
 "test_pd",
 "test_pd_client",
 "test_raftstore",
 "test_raftstore-v2",
 "test_raftstore_macro",
 "test_sst_importer",
 "test_storage",
 "test_util",
 "tidb_query_aggr",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_executors",
 "tidb_query_expr",
 "tikv",
 "tikv_kv",
 "tikv_util",
 "time 0.1.42",
 "tipb",
 "tipb_helper",
 "tokio",
 "toml",
 "txn_types",
 "uuid 0.8.2",
]

[[package]]
name = "textwrap"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d326610f408c7a4eb6f51c37c330e496b08506c9457c9d34287ecc38809fb060"
dependencies = [
 "unicode-width",
]

[[package]]
name = "thiserror"
version = "1.0.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "854babe52e4df1653706b98fcfc05843010039b406875930a70e4d9644e5c417"
dependencies = [
 "thiserror-impl 1.0.30",
]

[[package]]
name = "thiserror"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567b8a2dae586314f7be2a752ec7474332959c6460e02bde30d702a66d488708"
dependencies = [
 "thiserror-impl 2.0.12",
]

[[package]]
name = "thiserror-impl"
version = "1.0.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa32fd3f627f367fe16f893e2597ae3c05020f8bba2666a4e6ea73d377e5714b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "thiserror-impl"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f7cf42b4507d8ea322120659672cf1b9dbb93f8f2d4ecfd6e51350ff5b17a1d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "thread_local"
version = "1.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5516c27b78311c50bf42c071425c560ac799b11c30b31f87e3081965fe5e0180"
dependencies = [
 "once_cell",
]

[[package]]
name = "tidb_query_aggr"
version = "0.0.1"
dependencies = [
 "match-template",
 "panic_hook",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_expr",
 "tikv_util",
 "tipb",
 "tipb_helper",
]

[[package]]
name = "tidb_query_codegen"
version = "0.0.1"
dependencies = [
 "darling 0.10.1",
 "heck 0.3.1",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "tidb_query_common"
version = "0.0.1"
dependencies = [
 "anyhow",
 "api_version",
 "async-trait",
 "byteorder",
 "derive_more",
 "error_code",
 "futures 0.3.30",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "prometheus",
 "prometheus-static-metric",
 "serde_json",
 "thiserror 1.0.30",
 "tikv_util",
 "time 0.1.42",
 "yatp",
]

[[package]]
name = "tidb_query_datatype"
version = "0.0.1"
dependencies = [
 "api_version",
 "base64 0.13.0",
 "bitfield",
 "bitflags 1.3.2",
 "boolinator",
 "bstr",
 "bytemuck",
 "chrono",
 "chrono-tz",
 "codec",
 "collections",
 "criterion",
 "encoding_rs 0.8.29",
 "error_code",
 "hex 0.4.2",
 "kvproto",
 "lazy_static",
 "log_wrappers",
 "match-template",
 "nom 7.1.3",
 "num 0.3.0",
 "num-derive",
 "num-traits",
 "ordered-float 2.10.0",
 "protobuf",
 "regex",
 "serde",
 "serde_json",
 "simsimd",
 "slog",
 "slog-global",
 "static_assertions",
 "thiserror 1.0.30",
 "tidb_query_common",
 "tikv_alloc",
 "tikv_util",
 "tipb",
]

[[package]]
name = "tidb_query_executors"
version = "0.0.1"
dependencies = [
 "api_version",
 "async-trait",
 "codec",
 "collections",
 "fail",
 "futures 0.3.30",
 "itertools 0.10.5",
 "kvproto",
 "log_wrappers",
 "match-template",
 "protobuf",
 "slog",
 "slog-global",
 "smallvec",
 "tidb_query_aggr",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_expr",
 "tikv_util",
 "tipb",
 "tipb_helper",
 "yatp",
]

[[package]]
name = "tidb_query_expr"
version = "0.0.1"
dependencies = [
 "base64 0.13.0",
 "bstr",
 "byteorder",
 "chrono",
 "codec",
 "file_system",
 "flate2",
 "hex 0.4.2",
 "log_wrappers",
 "match-template",
 "num 0.3.0",
 "num-traits",
 "openssl",
 "panic_hook",
 "profiler",
 "protobuf",
 "rand 0.8.5",
 "regex",
 "safemem",
 "serde",
 "serde_json",
 "static_assertions",
 "tidb_query_codegen",
 "tidb_query_common",
 "tidb_query_datatype",
 "tikv_util",
 "time 0.1.42",
 "tipb",
 "tipb_helper",
 "twoway",
 "uuid 0.8.2",
]

[[package]]
name = "tikv"
version = "6.7.0-alpha"
dependencies = [
 "anyhow",
 "api_version",
 "async-stream 0.2.0",
 "async-trait",
 "backtrace",
 "batch-system",
 "byteorder",
 "bytes",
 "case_macros",
 "causal_ts",
 "chrono",
 "codec",
 "collections",
 "concurrency_manager",
 "coprocessor_plugin_api",
 "crc32fast",
 "crc64fast",
 "crossbeam",
 "dashmap 5.1.0",
 "encryption_export",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "engine_traits_tests",
 "error_code",
 "example_coprocessor_plugin",
 "fail",
 "farmhash",
 "file_system",
 "flate2",
 "futures 0.3.30",
 "futures-executor",
 "futures-timer",
 "futures-util",
 "fxhash",
 "getset",
 "grpcio",
 "grpcio-health",
 "hex 0.4.2",
 "http",
 "hyper",
 "hyper-openssl",
 "hyper-tls",
 "into_other",
 "itertools 0.10.5",
 "keyed_priority_queue",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "libloading",
 "log",
 "log_wrappers",
 "match-template",
 "memory_trace_macros",
 "mime",
 "moka",
 "more-asserts",
 "mur3",
 "nom 5.1.0",
 "notify",
 "num-traits",
 "num_cpus",
 "online_config",
 "openssl",
 "overload_protector",
 "panic_hook",
 "parking_lot 0.12.1",
 "paste",
 "pd_client",
 "pin-project",
 "pnet_datalink",
 "pprof",
 "procinfo",
 "prometheus",
 "prometheus-static-metric",
 "prost 0.7.0",
 "protobuf",
 "raft",
 "raft_log_engine",
 "raftstore",
 "raftstore-v2",
 "rand 0.7.3",
 "recovery",
 "regex",
 "reqwest",
 "resource_control",
 "resource_metering",
 "rev_lines",
 "seahash",
 "security",
 "semver 0.11.0",
 "serde",
 "serde_derive",
 "serde_ignored",
 "serde_json",
 "slog",
 "slog-global",
 "smallvec",
 "sst_importer",
 "strum 0.20.0",
 "sync_wrapper",
 "sysinfo",
 "tempfile",
 "test_sst_importer",
 "test_util",
 "thiserror 1.0.30",
 "tidb_query_aggr",
 "tidb_query_common",
 "tidb_query_datatype",
 "tidb_query_executors",
 "tidb_query_expr",
 "tikv_alloc",
 "tikv_kv",
 "tikv_util",
 "time 0.1.42",
 "tipb",
 "tokio",
 "tokio-openssl",
 "tokio-timer",
 "toml",
 "tracker",
 "txn_types",
 "url",
 "uuid 0.8.2",
 "walkdir",
 "yatp",
 "zipf 6.1.0",
]

[[package]]
name = "tikv-jemalloc-ctl"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e37706572f4b151dff7a0146e040804e9c26fe3a3118591112f05cf12a4216c1"
dependencies = [
 "libc 0.2.174",
 "paste",
 "tikv-jemalloc-sys",
]

[[package]]
name = "tikv-jemalloc-sys"
version = "0.5.0+5.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aeab4310214fe0226df8bfeb893a291a58b19682e8a07e1e1d4483ad4200d315"
dependencies = [
 "cc",
 "fs_extra",
 "libc 0.2.174",
]

[[package]]
name = "tikv-jemallocator"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20612db8a13a6c06d57ec83953694185a367e16945f66565e8028d2c0bd76979"
dependencies = [
 "libc 0.2.174",
 "tikv-jemalloc-sys",
]

[[package]]
name = "tikv_alloc"
version = "0.1.0"
dependencies = [
 "fxhash",
 "lazy_static",
 "libc 0.2.174",
 "mimalloc",
 "snmalloc-rs",
 "tcmalloc",
 "tempfile",
 "tikv-jemalloc-ctl",
 "tikv-jemalloc-sys",
 "tikv-jemallocator",
]

[[package]]
name = "tikv_kv"
version = "0.1.0"
dependencies = [
 "backtrace",
 "collections",
 "engine_panic",
 "engine_rocks",
 "engine_test",
 "engine_traits",
 "error_code",
 "fail",
 "file_system",
 "futures 0.3.30",
 "into_other",
 "keys",
 "kvengine",
 "kvenginepb",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "pd_client",
 "prometheus",
 "prometheus-static-metric",
 "raft",
 "raftstore",
 "slog",
 "slog-global",
 "slog_derive",
 "tempfile",
 "thiserror 1.0.30",
 "tikv_util",
 "tracker",
 "txn_types",
]

[[package]]
name = "tikv_util"
version = "0.1.0"
dependencies = [
 "async-speed-limit",
 "backtrace",
 "byteorder",
 "bytes",
 "chrono",
 "codec",
 "collections",
 "cpu-time",
 "crc32fast",
 "crossbeam",
 "crossbeam-skiplist",
 "derive_more",
 "error_code",
 "fail",
 "futures 0.3.30",
 "futures-util",
 "gag",
 "grpcio",
 "http",
 "kvproto",
 "lazy_static",
 "libc 0.2.174",
 "log",
 "log_wrappers",
 "mnt",
 "nix 0.24.1",
 "num-traits",
 "num_cpus",
 "online_config",
 "openssl",
 "page_size",
 "panic_hook",
 "parking_lot_core 0.9.10",
 "pin-project",
 "procfs",
 "procinfo",
 "prometheus",
 "prometheus-static-metric",
 "protobuf",
 "rand 0.8.5",
 "regex",
 "rusoto_core",
 "serde",
 "serde_json",
 "slog",
 "slog-async",
 "slog-global",
 "slog-json",
 "slog-term",
 "sysinfo",
 "tempfile",
 "thiserror 1.0.30",
 "tikv_alloc",
 "time 0.1.42",
 "tokio",
 "tokio-executor",
 "tokio-timer",
 "toml",
 "tracker",
 "url",
 "utime",
 "yatp",
]

[[package]]
name = "time"
version = "0.1.42"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db8dcfca086c1143c9270ac42a2bbd8a7ee477b78ac8e45b19abfb0cbede4b6f"
dependencies = [
 "libc 0.2.174",
 "redox_syscall 0.1.56",
 "winapi 0.3.9",
]

[[package]]
name = "time"
version = "0.3.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a7619e19bc266e0f9c5e6686659d394bc57973859340060a69221e57dbc0c40"
dependencies = [
 "deranged",
 "itoa 1.0.1",
 "num-conv",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9e9a38711f559d9e3ce1cdb06dd7c5b8ea546bc90052da6d06bb76da74bb07c"

[[package]]
name = "time-macros"
version = "0.2.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3526739392ec93fd8b359c8e98514cb3e8e021beb4e5f597b00a0221f8ed8a49"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tinytemplate"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2ada8616fad06a2d0c455adc530de4ef57605a8120cc65da9653e0e9623ca74"
dependencies = [
 "serde",
 "serde_json",
]

[[package]]
name = "tinyvec"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09b3661f17e86524eccd4371ab0429194e0d7c008abb45f7a7495b1719463c71"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "tipb"
version = "0.0.1"
source = "git+https://github.com/pingcap/tipb.git#775c2379cbc717451d5b9c99ae26c753aeb4e8b0"
dependencies = [
 "futures 0.3.30",
 "grpcio",
 "protobuf",
 "protobuf-build",
]

[[package]]
name = "tipb_helper"
version = "0.0.1"
dependencies = [
 "codec",
 "tidb_query_datatype",
 "tipb",
]

[[package]]
name = "tokio"
version = "1.25.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8666f87015685834a42aa61a391303d3bee0b1442dd9cf93e3adf4cbaf8de75a"
dependencies = [
 "autocfg",
 "bytes",
 "libc 0.2.174",
 "mio 0.8.5",
 "num_cpus",
 "parking_lot 0.12.1",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2",
 "tokio-macros",
 "windows-sys 0.42.0",
]

[[package]]
name = "tokio-executor"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb2d1b8f4548dbf5e1f7818512e9c406860678f29c300cdf0ebac72d1a3a1671"
dependencies = [
 "crossbeam-utils 0.7.2",
 "futures 0.1.31",
]

[[package]]
name = "tokio-io-timeout"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30b74022ada614a1b4834de765f9bb43877f910cc8ce4be40e89042c9223a8bf"
dependencies = [
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-macros"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b557f72f448c511a979e2564e55d74e6c4432fc96ff4f6241bc6bded342643b7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "tokio-native-tls"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7d995660bd2b7f8c1568414c1126076c13fbb725c40112dc0120b78eb9b717b"
dependencies = [
 "native-tls",
 "tokio",
]

[[package]]
name = "tokio-openssl"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac1bec5c0a4aa71e3459802c7a12e8912c2091ce2151004f9ce95cc5d1c6124e"
dependencies = [
 "futures 0.3.30",
 "openssl",
 "pin-project",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc6844de72e57df1980054b38be3a9f4702aba4858be64dd700181a8a6d0e1b6"
dependencies = [
 "rustls 0.19.1",
 "tokio",
 "webpki",
]

[[package]]
name = "tokio-rustls"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28327cf380ac148141087fbfb9de9d7bd4e84ab5d2c28fbc911d753de8a7081"
dependencies = [
 "rustls 0.21.12",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d660770404473ccd7bc9f8b28494a811bc18542b915c0855c51e8f419d5223ce"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-timer"
version = "0.2.13"
source = "git+https://github.com/tikv/tokio?branch=tokio-timer-hotfix#e8ac149d93f4a9bf49ea569d8d313ee40c5eb448"
dependencies = [
 "crossbeam-utils 0.7.2",
 "futures 0.1.31",
 "slab",
 "tokio-executor",
]

[[package]]
name = "tokio-util"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f988a1a1adc2fb21f9c12aa96441da33a1728193ae0b95d2be22dbd17fcb4e5c"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "tokio",
 "tracing",
]

[[package]]
name = "toml"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75cf45bb0bef80604d001caaec0d09da99611b3c0fd39d3080468875cdb65645"
dependencies = [
 "serde",
]

[[package]]
name = "tonic"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55b9af819e54b8f33d453655bef9b9acc171568fb49523078d0cc4e7484200ec"
dependencies = [
 "async-stream 0.3.3",
 "async-trait",
 "axum",
 "base64 0.13.0",
 "bytes",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "hyper",
 "hyper-timeout",
 "percent-encoding",
 "pin-project",
 "prost 0.11.2",
 "prost-derive 0.11.2",
 "tokio",
 "tokio-stream",
 "tokio-util",
 "tower",
 "tower-layer",
 "tower-service",
 "tracing",
 "tracing-futures",
]

[[package]]
name = "tonic-build"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c6fd7c2581e36d63388a9e04c350c21beb7a8b059580b2e93993c526899ddc"
dependencies = [
 "prettyplease 0.1.21",
 "proc-macro2",
 "prost-build 0.11.2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "indexmap 1.6.2",
 "pin-project",
 "pin-project-lite",
 "rand 0.8.5",
 "slab",
 "tokio",
 "tokio-util",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower-http"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c530c8675c1dbf98facee631536fa116b5fb6382d7dd6dc1b118d970eafe3ba"
dependencies = [
 "bitflags 1.3.2",
 "bytes",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "http-range-header",
 "pin-project-lite",
 "tower",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-layer"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "343bc9466d3fe6b0f960ef45960509f84480bf4fd96f92901afe7ff3df9d3a62"

[[package]]
name = "tower-service"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6bc1c9ce2b5135ac7f93c72918fc37feb872bdc6a5533a8b85eb4b86bfdae52"

[[package]]
name = "tracing"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01ebdc2bb4498ab1ab5f5b73c5803825e60199229ccba0698170e3be0e7f959f"
dependencies = [
 "cfg-if 1.0.0",
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc6b8ad3567499f98a1db7a752b07a7c8c7c7c34c332ec00effb2b0027974b7c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "tracing-core"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f50de3927f93d202783f4513cda820ab47ef17f624b03c096e86ef00c67e6b5f"
dependencies = [
 "lazy_static",
]

[[package]]
name = "tracing-futures"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97d095ae15e245a057c8e8451bab9b3ee1e1f68e9ba2b4fbc18d0ac5237835f2"
dependencies = [
 "pin-project",
 "tracing",
]

[[package]]
name = "tracker"
version = "0.0.1"
dependencies = [
 "collections",
 "crossbeam-utils 0.8.8",
 "kvproto",
 "lazy_static",
 "parking_lot 0.12.1",
 "pin-project",
 "prometheus",
 "slab",
]

[[package]]
name = "triomphe"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1ee9bd9239c339d714d657fac840c6d2a4f9c45f4f9ec7b0975113458be78db"

[[package]]
name = "try-lock"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e604eb7b43c06650e854be16a2a03155743d3752dd1c943f6829e26b7a36e382"

[[package]]
name = "twoway"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "766345ed3891b291d01af307cd3ad2992a4261cb6c0c7e665cd3e01cf379dd24"
dependencies = [
 "memchr",
 "unchecked-index",
]

[[package]]
name = "twox-hash"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bfd5b7557925ce778ff9b9ef90e3ade34c524b5ff10e239c69a42d546d2af56"

[[package]]
name = "txn_types"
version = "0.1.0"
dependencies = [
 "bitflags 1.3.2",
 "byteorder",
 "codec",
 "collections",
 "error_code",
 "farmhash",
 "kvproto",
 "log_wrappers",
 "panic_hook",
 "rand 0.8.5",
 "slog",
 "thiserror 1.0.30",
 "tikv_alloc",
 "tikv_util",
]

[[package]]
name = "typenum"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "373c8a200f9e67a0c95e62a4f52fbf80c23b4381c05a17845531982fa99e6b33"

[[package]]
name = "ucd-trie"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56dee185309b50d1f11bfedef0fe6d036842e3fb77413abef29f8f8d1c5d4c1c"

[[package]]
name = "unarray"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaea85b334db583fe3274d12b4cd1880032beab409c0d774be044d4480ab9a94"

[[package]]
name = "unchecked-index"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eeba86d422ce181a719445e51872fa30f1f7413b62becb52e95ec91aa262d85c"

[[package]]
name = "unicase"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50f37be617794602aabbeee0be4f259dc1778fabe05e2d67ee8f79326d5cb4f6"
dependencies = [
 "version_check 0.9.4",
]

[[package]]
name = "unicode-bidi"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49f2bd0c6468a8230e1db229cff8029217cf623c767ea5d60bfbd42729ea54d5"
dependencies = [
 "matches",
]

[[package]]
name = "unicode-blocks"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b12e05d9e06373163a9bb6bb8c263c261b396643a99445fe6b9811fd376581b"

[[package]]
name = "unicode-ident"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ceab39d59e4c9499d4e5a8ee0e2735b891bb7308ac83dfb4e80cad195c9f6f3"

[[package]]
name = "unicode-normalization"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5033c97c4262335cded6d6fc3e5c18ab755e1a3dc96376350f3d8e9f009ad956"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-segmentation"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6ccf251212114b54433ec949fd6a7841275f9ada20dddd2f29e9ceea4501493"

[[package]]
name = "unicode-width"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7007dbd421b92cc6e28410fe7362e2e0a2503394908f417b68ec8d1c364c4e20"

[[package]]
name = "unsafe-libyaml"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "673aac59facbab8a9007c7f6108d11f63b603f7cabff99fabf650fea5c32b861"

[[package]]
name = "untrusted"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a156c684c91ea7d62626509bce3cb4e1d9ed5c4d978f7b4352658f96a4c26b4a"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "url"
version = "2.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a507c383b2d33b5fc35d1861e77e6b383d158b2da5e14fe51b83dfedf6fd578c"
dependencies = [
 "form_urlencoded",
 "idna",
 "matches",
 "percent-encoding",
 "serde",
]

[[package]]
name = "usearch"
version = "2.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a34040838dcba06e4e21437f383975a0e3d6631ddbd54efd29490b444c693238"
dependencies = [
 "cxx",
 "cxx-build",
]

[[package]]
name = "utf8-ranges"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcfc827f90e53a02eaef5e535ee14266c1d569214c6aa70133a624d8a3164ba"

[[package]]
name = "utime"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "055058552ca15c566082fc61da433ae678f78986a6f16957e33162d1b218792a"
dependencies = [
 "kernel32-sys",
 "libc 0.2.174",
 "winapi 0.2.8",
]

[[package]]
name = "uuid"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc5cf98d8186244414c848017f0e2676b3fcb46807f6668a97dfe67359a3c4b7"
dependencies = [
 "getrandom 0.2.15",
 "serde",
]

[[package]]
name = "uuid"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "feb41e78f93363bb2df8b0e86a2ca30eed7806ea16ea0c790d757cf93f79be83"
dependencies = [
 "getrandom 0.2.15",
 "serde",
]

[[package]]
name = "valgrind_request"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0fb139b14473e1350e34439c888e44c805f37b4293d17f87ea920a66a20a99a"

[[package]]
name = "vcpkg"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b00bca6106a5e23f3eee943593759b7fcddb00554332e856d990c893966879fb"

[[package]]
name = "vec_map"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05c78687fb1a80548ae3250346c3db86a80a7cdd77bda190189f2d0a0987c81a"

[[package]]
name = "version_check"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "914b1a6776c4c929a602fafd8bc742e06365d4bcbe48c30f9cca5824f70dc9dd"

[[package]]
name = "version_check"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f"

[[package]]
name = "visible"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a044005fd5c0fc1ebd79c622e5606431c6b879a6a19acafb754be9926a2de73e"
dependencies = [
 "quote",
 "syn 1.0.103",
]

[[package]]
name = "void"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a02e4885ed3bc0f2de90ea6dd45ebcbb66dacffe03547fadbb0eeae2770887d"

[[package]]
name = "wait-timeout"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f200f5b12eb75f8c1ed65abd4b2db8a6e1b138a20de009dacee265a2498f3f6"
dependencies = [
 "libc 0.2.174",
]

[[package]]
name = "waker-fn"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d5b2c62b4012a3e1eca5a7e077d13b3bf498c4073e33ccd58626607748ceeca"

[[package]]
name = "walkdir"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "777182bc735b6424e1a57516d35ed72cb8019d85c8c9bf536dccb3445c1a2f7d"
dependencies = [
 "same-file",
 "winapi 0.3.9",
 "winapi-util",
]

[[package]]
name = "wana_kana"
version = "4.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a74666202acfcb4f9b995be2e3e9f7f530deb65e05a1407b8d0b30c9c451238a"
dependencies = [
 "fnv",
 "itertools 0.10.5",
 "lazy_static",
]

[[package]]
name = "want"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ce8a968cb1cd110d136ff8b819a556d6fb6d919363c61534f6860c7eb172ba0"
dependencies = [
 "log",
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b89c3ce4ce14bdc6fb6beaf9ec7928ca331de5df7e5ea278375642a2f478570d"

[[package]]
name = "wasi"
version = "0.10.2+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd6fbd9a79829dd1ad0cc20627bf1ed606756a7f77edff7b66b7064f9cb327c6"

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasi"
version = "0.14.2+wasi-0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9683f9a5a998d873c0d21fcbe3c083009670149a8fab228644b8bd36b2c48cb3"
dependencies = [
 "wit-bindgen-rt",
]

[[package]]
name = "wasm-bindgen"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25f1af7423d8588a3d840681122e72e6a24ddbcb3f0ec385cac0d12d24256c06"
dependencies = [
 "cfg-if 1.0.0",
 "serde",
 "serde_json",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b21c0df030f5a177f3cba22e9bc4322695ec43e7257d865302900290bcdedca"
dependencies = [
 "bumpalo",
 "lazy_static",
 "log",
 "proc-macro2",
 "quote",
 "syn 1.0.103",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3de431a2910c86679c34283a33f66f4e4abd7e0aec27b6669060148872aadf94"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f4203d69e40a52ee523b2529a773d5ffc1dc0071801c87b3d270b471b80ed01"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa8a30d46208db204854cadbb5d4baf5fcf8071ba5bf48190c3e59937962ebc"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.103",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d958d035c4438e28c70e4321a2911302f10135ce78a9c7834c0cab4123d06a2"

[[package]]
name = "web-sys"
version = "0.3.56"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c060b319f29dd25724f09a2ba1418f142f539b2be99fbf4d2d5a8f7330afb8eb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki"
version = "0.21.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab146130f5f790d45f82aeeb09e55a256573373ec64409fc19a6fb82fb1032ae"
dependencies = [
 "ring 0.16.16",
 "untrusted 0.7.1",
]

[[package]]
name = "webpki-roots"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aabe153544e473b775453675851ecc86863d2a81d786d741f6b76778f2a48940"
dependencies = [
 "webpki",
]

[[package]]
name = "wepoll-ffi"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d743fdedc5c64377b5fc2bc036b01c7fd642205a0d96356034ae3404d49eb7fb"
dependencies = [
 "cc",
]

[[package]]
name = "whatlang"
version = "0.16.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "471d1c1645d361eb782a1650b1786a8fb58dd625e681a04c09f5ff7c8764a7b0"
dependencies = [
 "hashbrown 0.14.5",
 "once_cell",
]

[[package]]
name = "which"
version = "4.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a5a7e487e921cf220206864a94a89b6c6905bfc19f1057fa26a4cb360e5c1d2"
dependencies = [
 "either",
 "lazy_static",
 "libc 0.2.174",
]

[[package]]
name = "winapi"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "167dc9d6949a9b857f3451275e911c3f44255842c1f7a76f33c55103a909087a"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-build"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d315eee3b34aca4797b2da6b13ed88266e6d612562a0c46390af8299fc699bc"

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70ec6ce85bb158151cae5e5c87f95a8e97d2c0c4b001223f33a334e3ce5de178"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows-sys"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a3e1820f08b8513f676f7ab6c1f99ff312fb97b553d30ff4dd86f9f15728aa7"
dependencies = [
 "windows_aarch64_gnullvm 0.42.0",
 "windows_aarch64_msvc 0.42.0",
 "windows_i686_gnu 0.42.0",
 "windows_i686_msvc 0.42.0",
 "windows_x86_64_gnu 0.42.0",
 "windows_x86_64_gnullvm 0.42.0",
 "windows_x86_64_msvc 0.42.0",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets",
]

[[package]]
name = "windows-targets"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f0713a46559409d202e70e28227288446bf7841d3211583a4b53e3f6d96e7eb"
dependencies = [
 "windows_aarch64_gnullvm 0.52.5",
 "windows_aarch64_msvc 0.52.5",
 "windows_i686_gnu 0.52.5",
 "windows_i686_gnullvm",
 "windows_i686_msvc 0.52.5",
 "windows_x86_64_gnu 0.52.5",
 "windows_x86_64_gnullvm 0.52.5",
 "windows_x86_64_msvc 0.52.5",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41d2aa71f6f0cbe00ae5167d90ef3cfe66527d6f613ca78ac8024c3ccab9a19e"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7088eed71e8b8dda258ecc8bac5fb1153c5cffaf2578fc8ff5d61e23578d3263"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd0f252f5a35cac83d6311b2e795981f5ee6e67eb1f9a7f64eb4500fbc4dcdb4"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9985fd1504e250c615ca5f281c3f7a6da76213ebd5ccc9561496568a2752afb6"

[[package]]
name = "windows_i686_gnu"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbeae19f6716841636c28d695375df17562ca208b2b7d0dc47635a50ae6c5de7"

[[package]]
name = "windows_i686_gnu"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88ba073cf16d5372720ec942a8ccbf61626074c6d4dd2e745299726ce8b89670"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87f4261229030a858f36b459e748ae97545d6f1ec60e5e0d6a3d32e0dc232ee9"

[[package]]
name = "windows_i686_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84c12f65daa39dd2babe6e442988fc329d6243fdce47d7d2d155b8d874862246"

[[package]]
name = "windows_i686_msvc"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db3c2bf3d13d5b658be73463284eaf12830ac9a26a90c717b7f771dfe97487bf"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf7b1b21b5362cbc318f686150e5bcea75ecedc74dd157d874d754a2ca44b0ed"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e4246f76bdeff09eb48875a0fd3e2af6aada79d409d33011886d3e1581517d9"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09d525d2ba30eeb3297665bd434a54297e4170c7f1a44cad4ef58095b4cd2028"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "852298e482cd67c356ddd9570386e2862b5673c85bd5f88df9ab6802b334c596"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40009d85759725a34da6d89a94e63d7bdc50a862acf0dbc7c8e488f1edcb6f5"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bec47e5bfd1bff0eeaf6d8b485cc1074891a197ab4225d504cb7a1ab88b02bf0"

[[package]]
name = "winreg"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0120db82e8a1e0b9fb3345a539c478767c0048d842860994d96113d5b667bd69"
dependencies = [
 "winapi 0.3.9",
]

[[package]]
name = "wit-bindgen-rt"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f42320e61fe2cfd34354ecb597f86f413484a798ba44a8ca1165c58d42da6c1"
dependencies = [
 "bitflags 2.5.0",
]

[[package]]
name = "ws2_32-sys"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d59cefebd0c892fa2dd6de581e937301d8552cb44489cdff035c6187cb63fa5e"
dependencies = [
 "winapi 0.2.8",
 "winapi-build",
]

[[package]]
name = "x86"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "637be4bacc6c06570eb05a3ba513f81d63e52862ced82db542215dd48dbab1e5"
dependencies = [
 "bit_field",
 "bitflags 1.3.2",
 "csv",
 "phf 0.9.0",
 "phf_codegen 0.9.0",
 "raw-cpuid",
 "serde_json",
]

[[package]]
name = "xattr"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af3a19837351dc82ba89f8a125e22a3c475f05aba604acc023d62b2739ae2909"
dependencies = [
 "libc 0.2.174",
 "rustix 1.0.7",
]

[[package]]
name = "xdg"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d089681aa106a86fade1b0128fb5daf07d5867a509ab036d99988dec80429a57"

[[package]]
name = "xml-rs"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "541b12c998c5b56aa2b4e6f18f03664eef9a4fd0a246a55594efae6cc2d964b5"

[[package]]
name = "xorf"
version = "0.8.0"
source = "git+https://github.com/youjiali1995/xorf?branch=0.8.0-custom-serde#96518a2160bdc8603a346aa4d94fdce22604abda"
dependencies = [
 "bytes",
 "libm",
 "rand 0.8.5",
 "serde",
]

[[package]]
name = "yada"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aed111bd9e48a802518765906cbdadf0b45afb72b9c81ab049a3b86252adffdd"

[[package]]
name = "yatp"
version = "0.0.1"
source = "git+https://github.com/tikv/yatp.git?branch=master#7ed25299d60a5338bea4ac0ed7470887ab74a010"
dependencies = [
 "crossbeam-deque",
 "crossbeam-skiplist",
 "crossbeam-utils 0.8.8",
 "dashmap 5.1.0",
 "fail",
 "lazy_static",
 "num_cpus",
 "parking_lot_core 0.9.10",
 "prometheus",
 "rand 0.8.5",
]

[[package]]
name = "zerocopy"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b9b4fd18abc82b8136838da5d50bae7bdea537c574d8dc1a34ed098d6c166f0"
dependencies = [
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa4f8080344d4671fb4e831a13ad1e68092748387dfc4f55e356242fae12ce3e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.87",
]

[[package]]
name = "zeroize"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3cbac2ed2ba24cc90f5e06485ac8c7c1e5449fe8911aef4d8877218af021a5b8"

[[package]]
name = "zipf"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e12b8667a4fff63d236f8363be54392f93dbb13616be64a83e761a9319ab589"
dependencies = [
 "rand 0.7.3",
]

[[package]]
name = "zipf"
version = "7.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "390e51da0ed8cc3ade001d15fa5ba6f966b99c858fb466ec6b06d1682f1f94dd"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "zstd"
version = "0.11.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc960326ece64f010d2d2107537f26dc589a6573a316bd5b1dba685fa5fde4"
dependencies = [
 "zstd-safe 5.0.2+zstd.1.5.2",
]

[[package]]
name = "zstd"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e91ee311a569c327171651566e07972200e76fcfe2242a4fa446149a3881c08a"
dependencies = [
 "zstd-safe 7.2.4",
]

[[package]]
name = "zstd-safe"
version = "5.0.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d2a5585e04f9eea4b2a3d1eca508c4dee9592a89ef6f450c11719da0726f4db"
dependencies = [
 "libc 0.2.174",
 "zstd-sys",
]

[[package]]
name = "zstd-safe"
version = "7.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f49c4d5f0abb602a93fb8736af2a4f4dd9512e36f7f570d66e65ff867ed3b9d"
dependencies = [
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.15+zstd.1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb81183ddd97d0c74cedf1d50d85c8d08c1b8b68ee863bdee9e706eedba1a237"
dependencies = [
 "bindgen 0.71.1",
 "cc",
 "pkg-config",
]
