// Copyright 2021-present PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// See the License for the specific language governing permissions and
// limitations under the License.

// Use ./gen.sh to generate .pb.go files.
syntax = "proto3";

package enginepb;

message ChangeSet {
  uint64 shardID = 1;
  uint64 shardVer = 2;
  Compaction compaction = 4;
  Flush flush = 5;
  Snapshot snapshot = 6;
  Snapshot initial_flush = 7;
  Split split = 10;
  bool shardDelete = 11;
  // The sequence value is not set by the DB when generating the change set,
  // user have to assign the value. If specified, we check sequence is
  // monotonically increasing.
  uint64 sequence = 12;
  ChangeSet parent = 13;
  IngestFiles ingest_files = 14;
  string property_key = 15;
  bytes property_value = 16;
  bool property_merge = 17;
  TableChange destroy_range = 18;
  TableChange truncate_ts = 19;
  TableChange trim_over_bound = 20;
  Snapshot restore_shard = 21;
  MajorCompaction major_compaction = 22;
  SchemaMeta update_schema_meta = 23;
  ColumnarCompaction columnar_compaction = 24;
  UpdateVectorIndex update_vector_index = 25;
  bool clear_columnar = 26;
  // Used for shard meta persist by diff.
  Snapshot snapshot_diff = 27;
}

// A set of small changesets of the same peer.
message ChangeSets { repeated ChangeSet change_sets = 1; }

message Compaction {
  int32 cf = 1;
  uint32 level = 2;
  repeated TableCreate tableCreates = 3;
  repeated uint64 topDeletes = 4;
  repeated uint64 bottomDeletes = 5;
  bool conflicted = 6;
  repeated BlobCreate blobTables = 7;
}

message MajorCompaction {
  TableChange sstableChange = 1;
  repeated BlobCreate newBlobTables = 2;
  repeated uint64 oldBlobTables = 3;
  bool conflicted = 4;
  bool update_inner_key_offset = 5;
}

message SchemaMeta {
  uint32 keyspace_id = 1;
  uint64 file_id = 2;
  int64 version = 3;
  uint64 restore_version = 4;
}

message ColumnarCompaction {
  TableChange columnar_change = 1;
  uint64 snap_version = 2;
  repeated uint64 row_l0s = 3;
  uint32 target_level = 4;
  repeated int64 columnar_table_ids = 5;
  repeated int64 columnar_table_ids_to_clear = 6;
}

message UpdateVectorIndex {
  int64 table_id = 1;
  int64 index_id = 2;
  int64 col_id = 3;
  repeated VectorIndexFile added = 4;
  repeated uint64 removed = 5;
}

message Flush {
  L0Create l0Create = 1;
  Properties properties = 2;
  uint64 version = 3;
  uint64 max_ts = 5;
  // used for txn file transaction which may be too large to flush a single L0
  // file.
  repeated L0Create l0Creates = 6;
}

message Snapshot {
  reserved 14; // deprecated columnar_snap_version.
  bytes outer_start = 1;
  bytes outer_end = 2;
  Properties properties = 3;
  repeated L0Create l0Creates = 5;
  repeated TableCreate tableCreates = 6;
  uint64 baseVersion = 7;
  uint64 data_sequence = 8;
  repeated BlobCreate BlobCreates = 9;
  uint64 max_ts = 10;
  uint32 inner_key_off = 11;
  repeated ColumnarCreate columnarCreates = 12;
  SchemaMeta schema_meta = 13;
  repeated uint64 unconverted_l0s = 15;
  repeated VectorIndex vector_indexes = 16;
  repeated int64 columnar_table_ids = 17;
  uint64 columnar_l2_snap_version = 18;
}

message L0Create {
  uint64 ID = 1;
  bytes smallest = 2;
  bytes biggest = 3;
  uint32 size = 4;
}

message BlobCreate {
  uint64 ID = 1;
  bytes smallest = 2;
  bytes biggest = 3;
}

message TableCreate {
  reserved 6; // deprecated field.
  uint64 ID = 1;
  uint32 level = 2;
  int32 CF = 3;
  bytes smallest = 4;
  bytes biggest = 5;
  uint32 meta_offset = 7;
}

message TableDelete {
  uint64 ID = 1;
  uint32 level = 2;
  int32 CF = 3;
}

message ColumnarCreate {
  uint64 ID = 1;
  uint32 level = 2;
  bytes smallest = 3;
  bytes biggest = 4;
  uint32 meta_offset = 5;
}

message ColumnarDelete {
  uint64 ID = 1;
  uint32 level = 2;
}

message Split {
  repeated Properties newShards = 1;
  repeated bytes Keys = 3;
}

message IngestFiles {
  repeated L0Create l0Creates = 1;
  repeated TableCreate tableCreates = 2;
  Properties properties = 3;
  repeated BlobCreate BlobCreates = 4;
  uint64 max_ts = 5;
}

message Properties {
  uint64 shardID = 1;
  repeated string keys = 2;
  repeated bytes values = 3;
}

message TableChange {
  // Tables that deleted by destroy range / truncate ts.
  repeated TableDelete tableDeletes = 1;
  // Tables that created by destroy range / truncate ts.
  repeated TableCreate tableCreates = 2;
  // For in-place compaction that each deleted file id followed by a created
  // file id.
  repeated uint64 file_ids_map = 3;
  // Columnar Tables that deleted by destroy range / truncate ts / compaction /
  // trim over bound.
  repeated ColumnarDelete columnarDeletes = 4;
  // Columnar Tables that created by destroy range / truncate ts / compaction /
  // trim over bound.
  repeated ColumnarCreate columnarCreates = 5;
}

message TxnFileRefs { repeated TxnFileRef txn_file_refs = 1; }

message TxnFileRef {
  uint64 start_ts = 1;
  repeated uint64 chunk_ids = 2;
  // The value of version is data_sequence for lock CF, commit_ts for write CF.
  uint64 version = 3;
  // empty for Lock CF,
  bytes user_meta = 4;
  // encoded txn_types::Lock without short value.
  bytes lock_val_prefix = 5;
  // Not used for now, but maybe useful for merge/split
  uint64 shard_ver = 6;

  // Lower bound without keyspace prefix.
  bytes inner_lower_bound = 7;
  // Upper bound (exclusive) without keyspace prefix.
  bytes inner_upper_bound = 8;
}

message Schema {
  int64 table_id = 1;
  // serialized tipb::ColumnInfo.
  repeated bytes columns = 2;
  repeated int64 pk_col_ids = 3;
  repeated VectorIndexDef vector_indexes = 4;
  repeated string keys = 5;
  repeated bytes values = 6;
  repeated Partition partitions = 7;
  int64 max_col_id = 8;
}

message Partition {
  int64 id = 1;
  repeated string keys = 2;
  repeated bytes values = 3;
}

message VectorIndex {
  int64 table_id = 1;
  int64 index_id = 2;
  int64 col_id = 3;
  repeated VectorIndexFile files = 4;
}

message VectorIndexFile {
  uint64 id = 1;
  uint64 snap_version = 2;

  // smallest handle of the file.
  bytes smallest = 3;
  // biggest handle of the file.
  bytes biggest = 4;
  uint32 meta_offset = 6;

  // Deprecated field. (old meta_offset)
  reserved 5;
}

message VectorIndexDef {
  int64 index_id = 1;
  int64 col_id = 2;
  string index_kind = 3;
  repeated string spec_keys = 4;
  repeated bytes spec_values = 5;
}
