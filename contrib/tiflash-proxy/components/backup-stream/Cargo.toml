[package]
name = "backup-stream"
version = "0.1.0"
edition = "2018"

[features]
default = ["test-engine-kv-rocksdb", "test-engine-raft-raft-engine"]
test-engine-kv-rocksdb = ["tikv/test-engine-kv-rocksdb"]
test-engine-raft-raft-engine = ["tikv/test-engine-raft-raft-engine"]
test-engines-rocksdb = ["tikv/test-engines-rocksdb"]
failpoints = ["tikv/failpoints", "fail/failpoints"]
backup-stream-debug = []

[[test]]
name = "integration"
path = "tests/mod.rs"
required-features = ["failpoints"]
test = true
harness = true

[dependencies]
async-compression = { version = "0.3.14", features = ["tokio", "zstd"] }
async-trait = { version = "0.1" }
bytes = "1"
chrono = "0.4"
concurrency_manager = { workspace = true }
crossbeam = "0.8"
crossbeam-channel = "0.5"
dashmap = "5"
engine_rocks = { workspace = true }
engine_traits = { workspace = true }
error_code = { workspace = true }
# We cannot update the etcd-client to latest version because of the cyclic requirement.
# Also we need wait until https://github.com/etcdv3/etcd-client/pull/43/files to be merged.
etcd-client = { git = "https://github.com/pingcap/etcd-client", rev = "14a6f8731f1890d5fd2f6e16a9f0d0a306b0599e", features = ["pub-response-field", "tls-openssl-vendored"] }
external_storage = { workspace = true }
external_storage_export = { workspace = true }
fail = "0.5"
file_system = { workspace = true }
futures = "0.3"
futures-io = "0.3"
grpcio = { workspace = true }
hex = "0.4"
kvproto = { workspace = true }
lazy_static = "1.4"
log_wrappers = { workspace = true }
online_config = { workspace = true }
openssl = "0.10"
pd_client = { workspace = true }
prometheus = { version = "0.13", default-features = false, features = ["nightly"] }
protobuf = { version = "2.8", features = ["bytes"] }
raft = { version = "0.7.0", default-features = false, features = ["protobuf-codec"] }
raftstore = { workspace = true }
regex = "1"
resolved_ts = { workspace = true }
security = { path = "../security" }
slog = { workspace = true }
slog-global = { workspace = true }
thiserror = "1"
tidb_query_datatype = { workspace = true }
tikv = { workspace = true }
tikv_alloc = { workspace = true }
tikv_kv = { workspace = true }
tikv_util = { workspace = true }
tokio = { version = "1.5", features = ["rt-multi-thread", "macros", "time", "sync"] }
tokio-stream = "0.1"
tokio-util = { version = "0.7", features = ["compat"] } 
tonic = "0.8"
txn_types = { workspace = true }
uuid = "0.8"
yatp = { workspace = true }

[dev-dependencies]
async-trait = "0.1"
engine_panic = { workspace = true }
grpcio = { workspace = true }
hex = "0.4"
protobuf = { version = "2.8", features = ["bytes"] }
rand = "0.8.0"
tempdir = "0.3"
tempfile = "3.0"
test_raftstore = { workspace = true }
test_util = { workspace = true }
url = "2"
walkdir = "2"
