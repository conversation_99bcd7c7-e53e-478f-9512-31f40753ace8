#
# Copyright 2019 The Abseil Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

load(
    "//absl:copts/configure_copts.bzl",
    "ABSL_DEFAULT_COPTS",
    "ABSL_DEFAULT_LINKOPTS",
    "ABSL_TEST_COPTS",
)

package(default_visibility = ["//visibility:public"])

licenses(["notice"])

cc_library(
    name = "bind_front",
    srcs = ["internal/front_binder.h"],
    hdrs = ["bind_front.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:base_internal",
        "//absl/container:compressed_tuple",
        "//absl/meta:type_traits",
        "//absl/utility",
    ],
)

cc_test(
    name = "bind_front_test",
    srcs = ["bind_front_test.cc"],
    copts = ABSL_TEST_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        ":bind_front",
        "//absl/memory",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_library(
    name = "function_ref",
    srcs = ["internal/function_ref.h"],
    hdrs = ["function_ref.h"],
    copts = ABSL_DEFAULT_COPTS,
    linkopts = ABSL_DEFAULT_LINKOPTS,
    deps = [
        "//absl/base:base_internal",
        "//absl/base:core_headers",
        "//absl/meta:type_traits",
    ],
)

cc_test(
    name = "function_ref_test",
    size = "small",
    srcs = ["function_ref_test.cc"],
    copts = ABSL_TEST_COPTS,
    deps = [
        ":function_ref",
        "//absl/container:test_instance_tracker",
        "//absl/memory",
        "@com_google_googletest//:gtest_main",
    ],
)

cc_test(
    name = "function_ref_benchmark",
    srcs = [
        "function_ref_benchmark.cc",
    ],
    copts = ABSL_TEST_COPTS,
    tags = ["benchmark"],
    visibility = ["//visibility:private"],
    deps = [
        ":function_ref",
        "//absl/base:core_headers",
        "@com_github_google_benchmark//:benchmark_main",
    ],
)
