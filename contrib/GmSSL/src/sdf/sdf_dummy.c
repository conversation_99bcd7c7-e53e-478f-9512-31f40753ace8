/*
 *  Copyright 2014-2022 The GmSSL Project. All Rights Reserved.
 *
 *  Licensed under the Apache License, Version 2.0 (the License); you may
 *  not use this file except in compliance with the License.
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 */


#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../sgd.h"
#include "sdf.h"

static char *deviceHandle = "SDF Device Handle";
static char *sessionHandle = "SDF Session Handle";
static char *keyHandle = "SDF Key Handle";
static char *agreementHandle = "SDF Agreement Handle";

unsigned char rsaPublicKeyBuf[516] = {
	0x00,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0xd5,0x43,0xbf,0x24,0xd2,0x69,0x56,0x21,0x20,0x57,0x8a,0xd8,
	0x67,0x4f,0xbd,0xd5,0xf5,0x3a,0xf5,0x9e,0xa5,0x87,0x52,0x39,0x47,0xc3,0xce,0x32,
	0x56,0xb6,0x06,0x2d,0xdc,0x8d,0xc2,0x18,0x53,0x5c,0xb0,0xcb,0xb6,0xe8,0x7c,0x82,
	0x97,0x38,0xbb,0x85,0x45,0x2e,0xc8,0x24,0x08,0x96,0x9e,0xb0,0x00,0xaf,0xd9,0xa7,
	0x1f,0x50,0x7f,0xc4,0x93,0x14,0x74,0x9a,0x43,0x8e,0x04,0x95,0xa0,0xd6,0xd9,0xdd,
	0xb4,0x97,0xb3,0x52,0x93,0xe4,0xbe,0xd1,0x1f,0x8c,0xf9,0xcd,0xe1,0xae,0x54,0xae,
	0x72,0xdf,0x94,0xe3,0x15,0x6a,0x5c,0x99,0xd6,0x80,0x46,0x94,0xad,0xb3,0x76,0x95,
	0x4e,0x14,0x8f,0x8f,0xe5,0x55,0xf1,0x3f,0xd0,0xd3,0x96,0x01,0xf6,0x94,0x3e,0x61,
	0xc1,0x8e,0xb3,0x89,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x01,0x00,0x01,};
unsigned char rsaPrivateKeyBuf[1412] = {
	0x00,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0xd5,0x43,0xbf,0x24,0xd2,0x69,0x56,0x21,0x20,0x57,0x8a,0xd8,
	0x67,0x4f,0xbd,0xd5,0xf5,0x3a,0xf5,0x9e,0xa5,0x87,0x52,0x39,0x47,0xc3,0xce,0x32,
	0x56,0xb6,0x06,0x2d,0xdc,0x8d,0xc2,0x18,0x53,0x5c,0xb0,0xcb,0xb6,0xe8,0x7c,0x82,
	0x97,0x38,0xbb,0x85,0x45,0x2e,0xc8,0x24,0x08,0x96,0x9e,0xb0,0x00,0xaf,0xd9,0xa7,
	0x1f,0x50,0x7f,0xc4,0x93,0x14,0x74,0x9a,0x43,0x8e,0x04,0x95,0xa0,0xd6,0xd9,0xdd,
	0xb4,0x97,0xb3,0x52,0x93,0xe4,0xbe,0xd1,0x1f,0x8c,0xf9,0xcd,0xe1,0xae,0x54,0xae,
	0x72,0xdf,0x94,0xe3,0x15,0x6a,0x5c,0x99,0xd6,0x80,0x46,0x94,0xad,0xb3,0x76,0x95,
	0x4e,0x14,0x8f,0x8f,0xe5,0x55,0xf1,0x3f,0xd0,0xd3,0x96,0x01,0xf6,0x94,0x3e,0x61,
	0xc1,0x8e,0xb3,0x89,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x01,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x1e,0xd9,0x55,0xe4,0xf5,0xaa,0xd7,0x12,0xa3,0xa3,0x06,0x2a,
	0x97,0x87,0x29,0x66,0xb1,0xba,0x7d,0x9d,0x1d,0x44,0x9d,0xd8,0x3b,0x51,0x4f,0x9a,
	0x68,0x80,0x9c,0x14,0x36,0x3b,0x2b,0x40,0x69,0x8e,0x96,0xe4,0x60,0xe8,0xf0,0x59,
	0xd3,0x96,0x19,0x4a,0x05,0xdf,0xe6,0x83,0x8f,0xda,0x79,0xc9,0xeb,0xcf,0x84,0x24,
	0x70,0x9b,0x2c,0x5f,0xf7,0x56,0xe2,0xe0,0xc7,0xfb,0x67,0x92,0xd2,0xf6,0x59,0x19,
	0xe9,0xdd,0xb4,0x54,0x52,0x0d,0xf8,0xda,0x64,0x67,0xe0,0xb9,0xe6,0x52,0x08,0xff,
	0x28,0x06,0x89,0x5c,0x2b,0xd5,0x6e,0x21,0xe1,0x6d,0x1d,0xe3,0xf8,0x1e,0x0f,0x20,
	0x9f,0x0a,0x60,0xd1,0xff,0x4e,0xa2,0x45,0xa1,0xee,0x96,0x90,0xc0,0xc4,0xa8,0x25,
	0x5a,0xe8,0xe8,0xa1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0xf5,0xde,0x0d,0x0c,0xc5,0x03,0x53,0x44,0xfa,0x70,0xc7,0x44,
	0x63,0xf8,0x57,0x7e,0x49,0x76,0xe4,0x7a,0x76,0x01,0x7d,0xda,0x65,0xaa,0x9d,0xbe,
	0xfe,0x24,0x9b,0x48,0xf9,0xa8,0x18,0x42,0x47,0xf3,0x1a,0x1e,0x61,0xe9,0xb8,0xb3,
	0x07,0xee,0xfd,0x83,0x2e,0xf2,0xf8,0xb9,0x1f,0x9a,0xee,0xeb,0x21,0xd0,0xc0,0x13,
	0xa2,0x31,0x33,0xe7,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0xde,0x0d,0xba,0xf3,0x62,0x8f,0x75,0x16,0xe6,0x87,0x72,0xba,
	0x12,0x6a,0x43,0x5c,0xde,0x22,0x60,0xea,0xef,0x7a,0x7e,0xb6,0x28,0x16,0x4f,0xda,
	0xe7,0xb8,0xfe,0x48,0x17,0x65,0x1a,0x73,0x38,0x98,0xdb,0xa2,0xda,0x50,0xc8,0x81,
	0x53,0x07,0x1d,0x0e,0xa7,0x3f,0x48,0x57,0xea,0x5b,0x34,0x64,0x9f,0x0a,0x8b,0x36,
	0x7e,0x08,0xef,0x0f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0xa8,0xd9,0xe6,0x7c,0x6e,0x90,0xea,0x0e,0xe5,0x2f,0xae,0xa9,
	0xf9,0x3e,0x04,0x58,0x66,0x7b,0x90,0x4d,0xc9,0xdd,0x1c,0x61,0x70,0x90,0xcb,0xe4,
	0xef,0x04,0x94,0xe0,0x79,0x14,0x48,0x14,0xbc,0xf4,0xe7,0x6b,0x16,0x33,0x3c,0xf5,
	0x36,0xed,0x9a,0x8d,0x0d,0x21,0x30,0x4f,0x72,0xb5,0x24,0x7f,0xb6,0xa9,0x76,0x40,
	0x05,0x93,0x64,0xe1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x85,0x35,0x31,0x68,0x9e,0x40,0xb7,0x1a,0x34,0xd3,0x1e,0x84,
	0xf7,0x55,0x1d,0xf2,0x11,0x24,0x08,0x86,0x07,0x81,0xb1,0x8f,0xee,0xfe,0x6b,0x8b,
	0x43,0xa5,0x5b,0x8d,0xbd,0xd3,0x1e,0x09,0xee,0xf2,0xec,0x17,0x86,0xe6,0x1d,0x52,
	0x4f,0x8f,0x9d,0xe3,0xd3,0x7b,0x08,0x18,0x0d,0x74,0x07,0x3b,0x31,0x99,0x6e,0xa8,
	0x12,0xf5,0xa3,0x0b,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x23,0x60,0x23,0xc4,0x44,0x67,0x91,0xb7,0xde,0x06,0x9a,0x17,
	0x49,0x3a,0x8e,0x66,0xb4,0x54,0x61,0x4b,0xc4,0x9e,0xf8,0xe6,0xbc,0xf8,0x87,0xef,
	0x06,0xb5,0x40,0x4b,0xab,0xaf,0xf0,0xa0,0x46,0x43,0xc5,0xbd,0xec,0xff,0x57,0xfd,
	0x51,0x8a,0x6b,0x7b,0x32,0xee,0xeb,0x2f,0x81,0xd0,0xa0,0xa2,0x09,0x18,0xab,0x5c,
	0x85,0x1b,0x0f,0x57,};
unsigned char eccPublicKeyBuf[132] = {
	0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x0e,0x42,0x92,0x4a,0x1b,0x01,0xb6,0x64,0x89,0x97,0xfb,0x67,
	0x3f,0xa5,0xa6,0xc4,0xc4,0x82,0xa2,0xfa,0xe6,0x96,0xc9,0x0a,0x37,0xf2,0x44,0x6c,
	0xac,0x37,0x85,0x67,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0xf8,0xbb,0x32,0x55,0xe2,0x47,0x34,0x9a,0xc9,0xb5,0xdb,0xc7,
	0x17,0x4a,0xd9,0x84,0xbf,0xc5,0x3e,0x99,0x92,0xc6,0xd8,0x2d,0x6f,0xea,0xff,0x79,
	0x6b,0xde,0x3d,0x37,};
unsigned char eccPrivateKeyBuf[68] = {
	0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0xe6,0x51,0x2e,0xf8,0xca,0x14,0x84,0xa2,0xd9,0x76,0xc9,0x0d,
	0x37,0x1d,0xf1,0x95,0x49,0xbe,0x83,0x8e,0x70,0x09,0x1d,0x81,0xbd,0x6e,0xd9,0x5c,
	0xad,0x02,0x19,0x44,};


#define SDF_TRACE() fprintf(stderr, "SDF_Dummy->%s\n", __FUNCTION__)

int SDF_OpenDevice(
	void **phDeviceHandle)
{
	if (!phDeviceHandle /* || !(*phDeviceHandle) */)
		return SDR_INARGERR;

	*phDeviceHandle = deviceHandle;
	return SDR_OK;
}

int SDF_CloseDevice(
	void *hDeviceHandle)
{
	return SDR_OK;
}

int SDF_OpenSession(
	void *hDeviceHandle,
	void **phSessionHandle)
{
	if (!phSessionHandle  /* || !(*phSessionHandle) */)
		return SDR_INARGERR;
	*phSessionHandle = sessionHandle;
	return SDR_OK;
}

int SDF_CloseSession(
	void *hSessionHandle)
{
	return SDR_OK;
}

#define SDF_DEV_DATE		"20140101"
#define SDF_DEV_BATCH_NUM	"001"
#define SDF_DEV_SERIAL_NUM	"00123"
#define SDF_DEV_SERIAL		SDF_DEV_DATE \
				SDF_DEV_BATCH_NUM \
				SDF_DEV_SERIAL_NUM

int SDF_GetDeviceInfo(
	void *hSessionHandle,
	DEVICEINFO *pstDeviceInfo)
{
	if (!pstDeviceInfo)
		return SDR_INARGERR;
	memset(pstDeviceInfo, 0, sizeof(*pstDeviceInfo));
	strncpy((char *)pstDeviceInfo->IssuerName, "GmSSL Project (http://gmssl.org)",
		sizeof(pstDeviceInfo->IssuerName));
	strncpy((char *)pstDeviceInfo->DeviceName, "Dummy SDF",
		sizeof(pstDeviceInfo->DeviceName));
	strncpy((char *)pstDeviceInfo->DeviceSerial, SDF_DEV_SERIAL,
		sizeof(pstDeviceInfo->DeviceSerial));
	pstDeviceInfo->DeviceVersion = 1;
	pstDeviceInfo->StandardVersion = 1;
	pstDeviceInfo->AsymAlgAbility[0] = SGD_RSA_SIGN|SGD_RSA_ENC|
		SGD_SM2_1|SGD_SM2_2|SGD_SM2_3;
	pstDeviceInfo->AsymAlgAbility[1] = 256|512|1024|2048|4096;
	pstDeviceInfo->SymAlgAbility = SGD_SM1|SGD_SSF33|SGD_SM4|SGD_ZUC|
		SGD_ECB|SGD_CBC|SGD_CFB|SGD_OFB|SGD_MAC;
	pstDeviceInfo->HashAlgAbility = SGD_SM3|SGD_SHA1|SGD_SHA256;
	pstDeviceInfo->BufferSize = 256*1024;
	return SDR_OK;
}

int SDF_GenerateRandom(
	void *hSessionHandle,
	unsigned int uiLength,
	unsigned char *pucRandom)
{
	return SDR_OK;
}

int SDF_GetPrivateKeyAccessRight(
	void *hSessionHandle,
	unsigned int uiKeyIndex,
	unsigned char *pucPassword,
	unsigned int uiPwdLength)
{
	return SDR_OK;
}

int SDF_ReleasePrivateKeyAccessRight(
	void *hSessionHandle,
	unsigned int uiKeyIndex)
{
	return SDR_OK;
}

int SDF_ExportSignPublicKey_RSA(
	void *hSessionHandle,
	unsigned int uiKeyIndex,
	RSArefPublicKey *pucPublicKey)
{
	if (!pucPublicKey)
		return SDR_INARGERR;
	memcpy(pucPublicKey, rsaPublicKeyBuf, sizeof(*pucPublicKey));
	return SDR_OK;
}

int SDF_ExportEncPublicKey_RSA(
	void *hSessionHandle,
	unsigned int uiKeyIndex,
	RSArefPublicKey *pucPublicKey)
{
	if (!pucPublicKey)
		return SDR_INARGERR;
	memcpy(pucPublicKey, rsaPublicKeyBuf, sizeof(*pucPublicKey));
	return SDR_OK;
}

int SDF_GenerateKeyPair_RSA(
	void *hSessionHandle,
	unsigned int uiKeyBits,
	RSArefPublicKey *pucPublicKey,
	RSArefPrivateKey *pucPrivateKey)
{
	if (!pucPublicKey || !pucPrivateKey)
		return SDR_INARGERR;
	memcpy(pucPublicKey, rsaPublicKeyBuf, sizeof(*pucPublicKey));
	memcpy(pucPrivateKey, rsaPrivateKeyBuf, sizeof(*pucPrivateKey));
	return SDR_OK;
}

int SDF_GenerateKeyWithIPK_RSA(
	void *hSessionHandle,
	unsigned int uiIPKIndex,
	unsigned int uiKeyBits,
	unsigned char *pucKey,
	unsigned int *puiKeyLength,
	void **phKeyHandle)
{
	if (!puiKeyLength)
		return SDR_INARGERR;
	*puiKeyLength = 2048/8;
	if (phKeyHandle && *phKeyHandle)
		*phKeyHandle = keyHandle;
	return SDR_OK;
}

int SDF_GenerateKeyWithEPK_RSA(
	void *hSessionHandle,
	unsigned int uiKeyBits,
	RSArefPublicKey *pucPublicKey,
	unsigned char *pucKey,
	unsigned int *puiKeyLength,
	void **phKeyHandle)
{
	if (!puiKeyLength)
		return SDR_INARGERR;
	*puiKeyLength = 2048/8;
	if (phKeyHandle && *phKeyHandle)
		*phKeyHandle = keyHandle;
	return SDR_OK;
}

int SDF_ImportKeyWithISK_RSA(
	void *hSessionHandle,
	unsigned int uiISKIndex,
	unsigned char *pucKey,
	unsigned int uiKeyLength,
	void **phKeyHandle)
{
	if (!phKeyHandle || !(*phKeyHandle))
		return SDR_INARGERR;
	*phKeyHandle = keyHandle;
	return SDR_OK;
}

int SDF_ExchangeDigitEnvelopeBaseOnRSA(
	void *hSessionHandle,
	unsigned int uiKeyIndex,
	RSArefPublicKey *pucPublicKey,
	unsigned char *pucDEInput,
	unsigned int uiDELength,
	unsigned char *pucDEOutput,
	unsigned int *puiDELength)
{
	if (!puiDELength)
		return SDR_INARGERR;
	*puiDELength = 2048/8;
	return SDR_OK;
}

int SDF_ExportSignPublicKey_ECC(
	void *hSessionHandle,
	unsigned int uiKeyIndex,
	ECCrefPublicKey *pucPublicKey)
{
	if (!pucPublicKey)
		return SDR_INARGERR;
	memcpy(pucPublicKey, eccPublicKeyBuf, sizeof(*pucPublicKey));
	return SDR_OK;
}

int SDF_ExportEncPublicKey_ECC(
	void *hSessionHandle,
	unsigned int uiKeyIndex,
	ECCrefPublicKey *pucPublicKey)
{
	if (!pucPublicKey)
		return SDR_INARGERR;
	memcpy(pucPublicKey, eccPublicKeyBuf, sizeof(*pucPublicKey));
	return SDR_OK;
}

int SDF_GenerateKeyPair_ECC(
	void *hSessionHandle,
	unsigned int uiAlgID,
	unsigned int  uiKeyBits,
	ECCrefPublicKey *pucPublicKey,
	ECCrefPrivateKey *pucPrivateKey)
{
	if (!pucPublicKey || !pucPublicKey)
		return SDR_INARGERR;
	memcpy(pucPublicKey, eccPublicKeyBuf, sizeof(*pucPublicKey));
	memcpy(pucPrivateKey, eccPrivateKeyBuf, sizeof(*pucPrivateKey));
	return SDR_OK;
}

int SDF_GenerateKeyWithIPK_ECC(
	void *hSessionHandle,
	unsigned int uiIPKIndex,
	unsigned int uiKeyBits,
	ECCCipher *pucKey,
	void **phKeyHandle)
{
	if (!phKeyHandle || !(*phKeyHandle))
		return SDR_INARGERR;
	*phKeyHandle = keyHandle;
	return SDR_OK;
}

int SDF_GenerateKeyWithEPK_ECC(
	void *hSessionHandle,
	unsigned int uiKeyBits,
	unsigned int uiAlgID,
	ECCrefPublicKey *pucPublicKey,
	ECCCipher *pucKey,
	void **phKeyHandle)
{
	if (!phKeyHandle || !(*phKeyHandle))
		return SDR_INARGERR;
	*phKeyHandle = keyHandle;
	return SDR_OK;
}

int SDF_ImportKeyWithISK_ECC(
	void *hSessionHandle,
	unsigned int uiISKIndex,
	ECCCipher *pucKey,
	void **phKeyHandle)
{
	if (!phKeyHandle || !(*phKeyHandle))
		return SDR_INARGERR;
	*phKeyHandle = keyHandle;
	return SDR_OK;
}

/* 6.3.14 */
int SDF_GenerateAgreementDataWithECC(
	void *hSessionHandle,
	unsigned int uiISKIndex,
	unsigned int uiKeyBits,
	unsigned char *pucSponsorID,
	unsigned int uiSponsorIDLength,
	ECCrefPublicKey *pucSponsorPublicKey,
	ECCrefPublicKey *pucSponsorTmpPublicKey,
	void **phAgreementHandle)
{
	// any output public key ?
	if (!phAgreementHandle || !(*phAgreementHandle))
		return SDR_INARGERR;
	*phAgreementHandle = agreementHandle;
	return SDR_OK;
}

/* 6.3.15 */
int SDF_GenerateKeyWithECC(
	void *hSessionHandle,
	unsigned char *pucResponseID,
	unsigned int uiResponseIDLength,
	ECCrefPublicKey *pucResponsePublicKey,
	ECCrefPublicKey *pucResponseTmpPublicKey,
	void *hAgreementHandle,
	void **phKeyHandle)
{
	if (!phKeyHandle || !(*phKeyHandle))
		return SDR_INARGERR;
	*phKeyHandle = keyHandle;
	return SDR_OK;
}

/* 6.3.16 */
int SDF_GenerateAgreementDataAndKeyWithECC(
	void *hSessionHandle,
	unsigned int uiISKIndex,
	unsigned int uiKeyBits,
	unsigned char *pucResponseID,
	unsigned int uiResponseIDLength,
	unsigned char *pucSponsorID,
	unsigned int uiSponsorIDLength,
	ECCrefPublicKey *pucSponsorPublicKey,
	ECCrefPublicKey *pucSponsorTmpPublicKey,
	ECCrefPublicKey *pucResponsePublicKey,
	ECCrefPublicKey *pucResponseTmpPublicKey,
	void **phKeyHandle)
{
	// any output
	if (!phKeyHandle || !(*phKeyHandle))
		return SDR_INARGERR;
	*phKeyHandle = keyHandle;
	return SDR_OK;
}

int SDF_ExchangeDigitEnvelopeBaseOnECC(
	void *hSessionHandle,
	unsigned int uiKeyIndex,
	unsigned int uiAlgID,
	ECCrefPublicKey *pucPublicKey,
	ECCCipher *pucEncDataIn,
	ECCCipher *pucEncDataOut)
{
	return SDR_OK;
}

int SDF_GenerateKeyWithKEK(
	void *hSessionHandle,
	unsigned int uiKeyBits,
	unsigned int uiAlgID,
	unsigned int uiKEKIndex,
	unsigned char *pucKey,
	unsigned int *puiKeyLength,
	void **phKeyHandle)
{
	if (!phKeyHandle || !(*phKeyHandle))
		return SDR_INARGERR;
	*phKeyHandle = keyHandle;
	return SDR_OK;
}

int SDF_ImportKeyWithKEK(
	void *hSessionHandle,
	unsigned int uiAlgID,
	unsigned int uiKEKIndex,
	unsigned char *pucKey,
	unsigned int uiKeyLength,
	void **phKeyHandle)
{
	if (!phKeyHandle || !(*phKeyHandle))
		return SDR_INARGERR;
	*phKeyHandle = keyHandle;
	return SDR_OK;
}

int SDF_DestroyKey(
	void *hSessionHandle,
	void *hKeyHandle)
{
	return SDR_OK;
}

int SDF_ExternalPublicKeyOperation_RSA(
	void *hSessionHandle,
	RSArefPublicKey *pucPublicKey,
	unsigned char *pucDataInput,
	unsigned int uiInputLength,
	unsigned char *pucDataOutput,
	unsigned int *puiOutputLength)
{
	if (!puiOutputLength)
		return SDR_INARGERR;
	*puiOutputLength = 2048/8;
	return SDR_OK;
}

int SDF_ExternalPrivateKeyOperation_RSA(
	void *hSessionHandle,
	RSArefPrivateKey *pucPrivateKey,
	unsigned char *pucDataInput,
	unsigned int uiInputLength,
	unsigned char *pucDataOutput,
	unsigned int *puiOutputLength)
{
	if (!puiOutputLength)
		return SDR_INARGERR;
	*puiOutputLength = 2048/8;
	return SDR_OK;
}

int SDF_InternalPrivateKeyOperation_RSA(
	void *hSessionHandle,
	unsigned int uiKeyIndex,
	unsigned char *pucDataInput,
	unsigned int uiInputLength,
	unsigned char *pucDataOutput,
	unsigned int *puiOutputLength)
{
	if (!puiOutputLength)
		return SDR_INARGERR;
	*puiOutputLength = 2048/8;
	return SDR_OK;
}

int SDF_ExternalVerify_ECC(
	void *hSessionHandle,
	unsigned int uiAlgID,
	ECCrefPublicKey *pucPublicKey,
	unsigned char *pucDataInput,
	unsigned int uiInputLength,
	ECCSignature *pucSignature)
{
	return SDR_OK;
}

int SDF_InternalSign_ECC(
	void *hSessionHandle,
	unsigned int uiISKIndex,
	unsigned char *pucData,
	unsigned int uiDataLength,
	ECCSignature *pucSignature)
{
	return SDR_OK;
}

int SDF_InternalVerify_ECC(
	void *hSessionHandle,
	unsigned int uiIPKIndex,
	unsigned char *pucData,
	unsigned int uiDataLength,
	ECCSignature *pucSignature)
{
	return SDR_OK;
}

int SDF_ExternalEncrypt_ECC(
	void *hSessionHandle,
	unsigned int uiAlgID,
	ECCrefPublicKey *pucPublicKey,
	unsigned char *pucData,
	unsigned int uiDataLength,
	ECCCipher *pucEncData)
{
	if (!pucEncData)
		return SDR_INARGERR;
	pucEncData->L = uiDataLength;
	return SDR_OK;
}

int SDF_Encrypt(
	void *hSessionHandle,
	void *hKeyHandle,
	unsigned int uiAlgID,
	unsigned char *pucIV,
	unsigned char *pucData,
	unsigned int uiDataLength,
	unsigned char *pucEncData,
	unsigned int *puiEncDataLength)
{
	if (!puiEncDataLength)
		return SDR_INARGERR;
	*puiEncDataLength = uiDataLength;
	return SDR_OK;
}

int SDF_Decrypt(
	void *hSessionHandle,
	void *hKeyHandle,
	unsigned int uiAlgID,
	unsigned char *pucIV,
	unsigned char *pucEncData,
	unsigned int uiEncDataLength,
	unsigned char *pucData,
	unsigned int *puiDataLength)
{
	if (!puiDataLength)
		return SDR_INARGERR;
	*puiDataLength = uiEncDataLength;
	return SDR_OK;
}

int SDF_CalculateMAC(
	void *hSessionHandle,
	void *hKeyHandle,
	unsigned int uiAlgID,
	unsigned char *pucIV,
	unsigned char *pucData,
	unsigned int uiDataLength,
	unsigned char *pucMAC,
	unsigned int *puiMACLength)
{
	if (!puiMACLength)
		return SDR_INARGERR;
	*puiMACLength = 16; /* CBC-MAC length */
	return SDR_OK;
}

int SDF_HashInit(
	void *hSessionHandle,
	unsigned int uiAlgID,
	ECCrefPublicKey *pucPublicKey,
	unsigned char *pucID,
	unsigned int uiIDLength)
{
	return SDR_OK;
}

int SDF_HashUpdate(
	void *hSessionHandle,
	unsigned char *pucData,
	unsigned int uiDataLength)
{
	return SDR_OK;
}

int SDF_HashFinal(void *hSessionHandle,
	unsigned char *pucHash,
	unsigned int *puiHashLength)
{
	if (!puiHashLength)
		return SDR_INARGERR;
	*puiHashLength = 32;
	return SDR_OK;
}

int SDF_CreateFile(
	void *hSessionHandle,
	unsigned char *pucFileName,
	unsigned int uiNameLen,
	unsigned int uiFileSize)
{
	return SDR_OK;
}

int SDF_ReadFile(
	void *hSessionHandle,
	unsigned char *pucFileName,
	unsigned int uiNameLen,
	unsigned int uiOffset,
	unsigned int *puiReadLength,
	unsigned char *pucBuffer)
{
	if (!puiReadLength)
		return SDR_INARGERR;
	return SDR_OK;
}

int SDF_WriteFile(
	void *hSessionHandle,
	unsigned char *pucFileName,
	unsigned int uiNameLen,
	unsigned int uiOffset,
	unsigned int uiWriteLength,
	unsigned char *pucBuffer)
{
	return SDR_OK;
}

int SDF_DeleteFile(
	void *hSessionHandle,
	unsigned char *pucFileName,
	unsigned int uiNameLen)
{
	return SDR_OK;
}
