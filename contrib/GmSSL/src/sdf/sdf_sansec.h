/*
 *  Copyright 2014-2022 The GmSSL Project. All Rights Reserved.
 *
 *  Licensed under the Apache License, Version 2.0 (the License); you may
 *  not use this file except in compliance with the License.
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 */

#ifndef SDFUTIL_SDF_SANSEC_H
#define SDFUTIL_SDF_SANSEC_H

#include "../sgd.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SANSEC_SM1		(SGD_SM1)
#define SANSEC_SM1_ECB		(SANSEC_SM1|SGD_ECB)
#define SANSEC_SM1_CBC		(SANSEC_SM1|SGD_CBC)
#define SANSEC_SM1_CFB		(SANSEC_SM1|SGD_CFB)
#define SANSEC_SM1_OFB		(SANSEC_SM1|SGD_OFB)
#define SANSEC_SM1_MAC		(SANSEC_SM1|SGD_MAC)

#define SANSEC_SM4		0x00002000
#define SANSEC_SM4_ECB		(SANSEC_SM4|SGD_ECB)
#define SANSEC_SM4_CBC		(SANSEC_SM4|SGD_CBC)
#define SANSEC_SM4_CFB		(SANSEC_SM4|SGD_CFB)
#define SANSEC_SM4_OFB		(SANSEC_SM4|SGD_OFB)
#define SANSEC_SM4_MAC		(SANSEC_SM4|SGD_MAC)

#define SANSEC_SSF33		(SGD_SSF33)
#define SANSEC_SSF33_ECB	(SANSEC_SSF33|SGD_ECB)
#define SANSEC_SSF33_CBC	(SANSEC_SSF33|SGD_CBC)
#define SANSEC_SSF33_CFB	(SANSEC_SSF33|SGD_CFB)
#define SANSEC_SSF33_OFB	(SANSEC_SSF33|SGD_OFB)
#define SANSEC_SSF33_MAC	(SANSEC_SSF33|SGD_MAC)

#define SANSEC_AES		0x00000400
#define SANSEC_AES_ECB		(SANSEC_AES|SGD_ECB)
#define SANSEC_AES_CBC		(SANSEC_AES|SGD_CBC)
#define SANSEC_AES_CFB		(SANSEC_AES|SGD_CFB)
#define SANSEC_AES_OFB		(SANSEC_AES|SGD_OFB)
#define SANSEC_AES_MAC		(SANSEC_AES|SGD_MAC)

#define SANSEC_DES		0x00004000
#define SANSEC_DES_ECB		(SANSEC_DES|SGD_ECB)
#define SANSEC_DES_CBC		(SANSEC_DES|SGD_CBC)
#define SANSEC_DES_CFB		(SANSEC_DES|SGD_CFB)
#define SANSEC_DES_OFB		(SANSEC_DES|SGD_OFB)
#define SANSEC_DES_MAC		(SANSEC_DES|SGD_MAC)

#define SANSEC_3DES		0x00000800
#define SANSEC_3DES_ECB		(SANSEC_3DES|SGD_ECB)
#define SANSEC_3DES_CBC		(SANSEC_3DES|SGD_CBC)
#define SANSEC_3DES_CFB		(SANSEC_3DES|SGD_CFB)
#define SANSEC_3DES_OFB		(SANSEC_3DES|SGD_OFB)
#define SANSEC_3DES_MAC		(SANSEC_3DES|SGD_MAC)

#define SANSEC_SM3		(SGD_SM3)
#define SANSEC_SHA1		(SGD_SHA1)
#define SANSEC_SHA256		(SGD_SHA256)
#define SANSEC_SHA512		0x00000008
#define SANSEC_SHA384		0x00000010
#define SANSEC_SHA224		0x00000020
#define SANSEC_MD5		0x00000080

#define SANSEC_RSA		(SGD_RSA)
#define SANSEC_RSA_SIGN		(SGD_RSA_SIGN)
#define SANSEC_RSA_ENC		0x00010200
#define SANSEC_SM2		(SGD_SM2)
#define SANSEC_SM2_1		(SGD_SM2_1)
#define SANSEC_SM2_2		(SGD_SM2_2)
#define SANSEC_SM2_3		(SGD_SM2_3)

#define SANSEC_BASE				(SDR_BASE + 0x00010000)
#define SANSEC_INVALID_USER			(SANSEC_BASE + 0x00000001)
#define SANSEC_INVALID_AUTHENCODE		(SANSEC_BASE + 0x00000002)
#define SANSEC_PROTOCOL_VERSION_ERROR		(SANSEC_BASE + 0x00000003)
#define SANSEC_INVALID_COMMAND			(SANSEC_BASE + 0x00000004)
#define SANSEC_INVALID_PARAMETERS		(SANSEC_BASE + 0x00000005)
#define SANSEC_FILE_ALREADY_EXIST		(SANSEC_BASE + 0x00000006)
#define SANSEC_SYNC_ERROR			(SANSEC_BASE + 0x00000007)
#define SANSEC_SYNC_LOGIN_ERROR			(SANSEC_BASE + 0x00000008)
#define SANSEC_SOCKET_TIMEOUT			(SANSEC_BASE + 0x00000100)
#define SANSEC_CONNECT_ERROR			(SANSEC_BASE + 0x00000101)
#define SANSEC_SET_SOCKET_OPTION_ERROR		(SANSEC_BASE + 0x00000102)
#define SANSEC_SOCKET_SEND_ERROR		(SANSEC_BASE + 0x00000104)
#define SANSEC_SOCKET_RECV_ERROR		(SANSEC_BASE + 0x00000105)
#define SANSEC_SOCKET_RECV_0			(SANSEC_BASE + 0x00000106)
#define SANSEC_SEM_TIMEOUT			(SANSEC_BASE + 0x00000200)
#define SANSEC_NO_AVAILABLE_HSM			(SANSEC_BASE + 0x00000201)
#define SANSEC_NO_AVAILABLE_CSM			(SANSEC_BASE + 0x00000202)
#define SANSEC_CONFIG_ERROR			(SANSEC_BASE + 0x00000301)
#define SANSEC_CARD_BASE			(SDR_BASE + 0x00020000)
#define SANSEC_CARD_UNKNOW_ERROR		(SANSEC_CARD_BASE + 0x00000001)
#define SANSEC_CARD_NOT_SUPPORTED		(SANSEC_CARD_BASE + 0x00000002)
#define SANSEC_CARD_COMMMUCATION_FAILED		(SANSEC_CARD_BASE + 0x00000003)
#define SANSEC_CARD_HARDWARE_FAILURE		(SANSEC_CARD_BASE + 0x00000004)
#define SANSEC_CARD_OPEN_DEVICE_FAILED		(SANSEC_CARD_BASE + 0x00000005)
#define SANSEC_CARD_OPEN_SESSION_FAILED		(SANSEC_CARD_BASE + 0x00000006)
#define SANSEC_CARD_PRIVATE_KEY_ACCESS_DENYED	(SANSEC_CARD_BASE + 0x00000007)
#define SANSEC_CARD_KEY_NOT_EXIST		(SANSEC_CARD_BASE + 0x00000008)
#define SANSEC_CARD_ALGOR_NOT_SUPPORTED		(SANSEC_CARD_BASE + 0x00000009)
#define SANSEC_CARD_ALG_MODE_NOT_SUPPORTED	(SANSEC_CARD_BASE + 0x00000010)
#define SANSEC_CARD_PUBLIC_KEY_OPERATION_ERROR	(SANSEC_CARD_BASE + 0x00000011)
#define SANSEC_CARD_PRIVATE_KEY_OPERATION_ERROR	(SANSEC_CARD_BASE + 0x00000012)
#define SANSEC_CARD_SIGN_ERROR			(SANSEC_CARD_BASE + 0x00000013)
#define SANSEC_CARD_VERIFY_ERROR		(SANSEC_CARD_BASE + 0x00000014)
#define SANSEC_CARD_SYMMETRIC_ALGOR_ERROR	(SANSEC_CARD_BASE + 0x00000015)
#define SANSEC_CARD_STEP_ERROR			(SANSEC_CARD_BASE + 0x00000016)
#define SANSEC_CARD_FILE_SIZE_ERROR		(SANSEC_CARD_BASE + 0x00000017)
#define SANSEC_CARD_FILE_NOT_EXIST		(SANSEC_CARD_BASE + 0x00000018)
#define SANSEC_CARD_FILE_OFFSET_ERROR		(SANSEC_CARD_BASE + 0x00000019)
#define SANSEC_CARD_KEY_TYPE_ERROR		(SANSEC_CARD_BASE + 0x00000020)
#define SANSEC_CARD_KEY_ERROR			(SANSEC_CARD_BASE + 0x00000021)
#define SANSEC_CARD_BUFFER_TOO_SMALL		(SANSEC_CARD_BASE + 0x00000101)
#define SANSEC_CARD_DATA_PADDING_ERROR		(SANSEC_CARD_BASE + 0x00000102)
#define SANSEC_CARD_DATA_SIZE			(SANSEC_CARD_BASE + 0x00000103)
#define SANSEC_CARD_CRYPTO_NOT_INITED		(SANSEC_CARD_BASE + 0x00000104)
#define SANSEC_CARD_MANAGEMENT_DENYED		(SANSEC_CARD_BASE + 0x00001001)
#define SANSEC_CARD_OPERATION_DENYED		(SANSEC_CARD_BASE + 0x00001002)
#define SANSEC_CARD_DEVICE_STATUS_ERROR		(SANSEC_CARD_BASE + 0x00001003)
#define SANSEC_CARD_LOGIN_ERROR			(SANSEC_CARD_BASE + 0x00001011)
#define SANSEC_CARD_USERID_ERROR		(SANSEC_CARD_BASE + 0x00001012)
#define SANSEC_CARD_PARAMENT_ERROR		(SANSEC_CARD_BASE + 0x00001013)
#define SANSEC_CARD_MANAGEMENT_DENYED_05	(SANSEC_CARD_BASE + 0x00000801)
#define SANSEC_CARD_OPERATION_DENYED_05		(SANSEC_CARD_BASE + 0x00000802)
#define SANSEC_CARD_DEVICE_STATUS_ERROR_05	(SANSEC_CARD_BASE + 0x00000803)
#define SANSEC_CARD_LOGIN_ERROR_05		(SANSEC_CARD_BASE + 0x00000811)
#define SANSEC_CARD_USERID_ERROR_05		(SANSEC_CARD_BASE + 0x00000812)
#define SANSEC_CARD_PARAMENT_ERROR_05		(SANSEC_CARD_BASE + 0x00000813)
#define SANSEC_CARD_READER_BASE			(SDR_BASE + 0x00030000)
#define SANSEC_CARD_READER_PIN_ERROR		(SANSEC_CARD_READER_BASE + 0x000063CE)
#define SANSEC_CARD_READER_NO_CARD		(SANSEC_CARD_READER_BASE + 0x0000FF01)
#define SANSEC_CARD_READER_CARD_INSERT		(SANSEC_CARD_READER_BASE + 0x0000FF02)
#define SANSEC_CARD_READER_CARD_INSERT_TYPE 	(SANSEC_CARD_READER_BASE + 0x0000FF03)

#pragma pack(1)
typedef struct {
	unsigned int  clength;
	unsigned char x[ECCref_MAX_LEN];
	unsigned char y[ECCref_MAX_LEN];
	unsigned char C[136];
	unsigned char M[ECCref_MAX_LEN];
} SANSEC_ECCCipher;
#pragma pack()

#ifdef __cplusplus
}
#endif
#endif
